
from xtquant import xtdata as xt_data
def download_data():
    xt_data.download_history_data2(
        stock_list=["600000.SH"],   
        period="1d",
        start_time="20250715145700",
        end_time="20250716093100"
    )

    result = xt_data.get_local_data(
        field_list=[],
        stock_list=["600000.SH"],
        period="1d",
        start_time="20250715145700",
        end_time="20250716093100",
        dividend_type="front"
    )

    return result

if __name__ == "__main__":
    import pandas as pd
    pd.set_option('display.max_columns', None)  # 显示所有列
    pd.set_option('display.width', None)  # 设置宽度为无限制
    pd.set_option('display.max_rows', None)  # 显示所有行
    result = download_data()
    df = pd.DataFrame(result["600000.SH"])
    #print(df.head(300))
    print(df.loc['20250715145700':'20250716093100'])
