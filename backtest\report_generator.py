#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
回测报告生成器，用于生成HTML和PDF格式的回测报告
"""

import json
import os
import sys
from datetime import datetime
from typing import Dict

import pandas as pd
from jinja2 import BaseLoader, Environment

# 添加项目根目录到系统路径
root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, root_path)

from backtest.performance import PerformanceAnalyzer  # noqa: E402
from utils.logger import get_unified_logger  # noqa: E402


# 创建一个自定义的Jinja2环境和过滤器
def format_money(value, decimals=2):
    """金额格式化过滤器，将数字格式化为货币形式"""
    if value is None:
        return "N/A"
    try:
        value_float = float(value)
        return f"{value_float:,.{decimals}f}"
    except (ValueError, TypeError):
        return str(value)


def format_percent(value, decimals=2):
    """百分比格式化过滤器，将小数格式化为百分比形式"""
    if value is None:
        return "N/A"
    try:
        value_float = float(value)
        return f"{value_float:.{decimals}f}%"
    except (ValueError, TypeError):
        return str(value)


def create_jinja_env():
    """创建带有自定义过滤器的Jinja2环境"""
    env = Environment(loader=BaseLoader())
    env.filters['format_money'] = format_money
    env.filters['format_percent'] = format_percent
    return env


class ReportGenerator:
    """
    回测报告生成器，用于生成HTML和PDF格式的回测报告
    """
    
    def __init__(self, **kwargs):
        """
        初始化报告生成器
        
        Args:
            **kwargs: 参数字典，可包含：
                - output_dir: 输出目录
                - template_dir: 模板目录
                - logger: 日志记录器
        """
        self.output_dir = kwargs.get("output_dir", "./output/reports")
        self.template_dir = kwargs.get("template_dir", "./backtest/templates")
        self.logger = kwargs.get("logger", get_unified_logger("report_generator"))
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 性能分析器
        self.performance_analyzer = PerformanceAnalyzer(
            logger=self.logger,
            output_dir=self.output_dir
        )
        
        # 创建Jinja2环境
        self.jinja_env = create_jinja_env()
    
    def generate_html_report(self, backtest_results: Dict, strategy_name: str,
                             include_trades: bool = True) -> str:
        """
        生成HTML格式的回测报告
        
        Args:
            backtest_results: 回测结果字典，包含equity_curve, trades等
            strategy_name: 策略名称
            include_trades: 是否包含交易明细
            
        Returns:
            HTML报告的保存路径
        """
        self.logger.info(f"生成HTML回测报告: {strategy_name}")
        
        # 提取回测结果
        equity_curve = backtest_results.get("equity_curve", pd.DataFrame())
        trades = backtest_results.get("trades", [])
        performance = backtest_results.get("performance", None)
        benchmark = backtest_results.get("benchmark", None)
        parameters = backtest_results.get("parameters", {})
        
        # 如果没有性能指标，则计算
        if performance is None:
            performance = self.performance_analyzer.analyze(equity_curve, trades)
        
        # 创建报告目录
        report_time = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_dir_name = f"{strategy_name.replace(' ', '_')}_{report_time}"
        report_dir = os.path.join(self.output_dir, report_dir_name)
        os.makedirs(report_dir, exist_ok=True)
        
        # 生成图表
        self._generate_charts(
            equity_curve, trades, benchmark, report_dir, strategy_name
        )
        
        # 保存数据文件
        self._save_data_files(
            equity_curve, trades, performance, parameters, report_dir
        )
        
        # 生成HTML报告
        html_path = self._create_html_report(
            equity_curve, trades, performance, benchmark, 
            parameters, report_dir, strategy_name, include_trades
        )
        
        self.logger.info(f"HTML报告已生成: {html_path}")
        return html_path
    
    def generate_pdf_report(self, html_path: str) -> str:
        """
        从HTML生成PDF报告（需要安装wkhtmltopdf）
        
        Args:
            html_path: HTML报告路径
            
        Returns:
            PDF报告路径
        """
        try:
            import pdfkit
            self.logger.info("从HTML生成PDF报告...")
            
            pdf_path = html_path.replace(".html", ".pdf")
            
            # 使用pdfkit将HTML转换为PDF
            options = {
                'page-size': 'A4',
                'margin-top': '15mm',
                'margin-right': '15mm',
                'margin-bottom': '15mm',
                'margin-left': '15mm',
                'encoding': "UTF-8",
                'no-outline': None
            }
            
            pdfkit.from_file(html_path, pdf_path, options=options)
            self.logger.info(f"PDF报告已生成: {pdf_path}")
            return pdf_path
            
        except ImportError:
            self.logger.warning("无法生成PDF报告：缺少pdfkit库或wkhtmltopdf")
            return ""
        except Exception as e:
            self.logger.error(f"生成PDF报告失败: {e}")
            return ""
    
    def _generate_charts(
        self, equity_curve, trades, benchmark, report_dir, strategy_name
    ):
        """生成图表"""
        if not equity_curve.empty:
            # 绘制权益曲线
            self.performance_analyzer.plot_equity_curve(
                equity_curve,
                title=f"{strategy_name}收益曲线",
                benchmark=benchmark
            )
            
            # 绘制月度收益热图
            self.performance_analyzer.plot_monthly_returns(
                equity_curve,
                title=f"{strategy_name}月度收益"
            )
            
            # 绘制回撤分布
            self.performance_analyzer.plot_drawdown_distribution(
                equity_curve,
                title=f"{strategy_name}回撤分布"
            )
            
            # 移动图表到报告目录
            for img_file in os.listdir(self.performance_analyzer.output_dir):
                chart_types = [
                    "equity_curve",
                    "monthly_returns",
                    "drawdown_distribution"
                ]
                if (img_file.endswith(".png") and 
                        any(chart in img_file for chart in chart_types)):
                    src = os.path.join(
                        self.performance_analyzer.output_dir, img_file
                    )
                    dst = os.path.join(report_dir, img_file.split("_")[-1])
                    try:
                        # 如果目标文件已存在，先删除
                        if os.path.exists(dst):
                            os.remove(dst)
                        os.rename(src, dst)
                    except Exception as e:
                        self.logger.warning(f"移动图表文件失败: {e}")
                        # 尝试复制而不是重命名
                        try:
                            import shutil
                            shutil.copy2(src, dst)
                        except Exception as e2:
                            self.logger.error(f"无法复制图表文件: {e2}")
    
    def _save_data_files(
        self, equity_curve, trades, performance, parameters, report_dir
    ):
        """保存数据文件"""
        # 保存权益曲线
        if not equity_curve.empty:
            equity_curve.to_csv(os.path.join(report_dir, "equity_curve.csv"))
        
        # 保存交易记录
        if trades:
            pd.DataFrame(trades).to_csv(
                os.path.join(report_dir, "trades.csv"), index=False
            )
        
        # 保存性能指标
        if performance:
            pd.DataFrame([performance]).to_csv(
                os.path.join(report_dir, "performance.csv"), index=False
            )
        
        # 保存策略参数
        if parameters:
            with open(os.path.join(report_dir, "parameters.json"), "w") as f:
                json.dump(parameters, f, indent=4)
    
    def _create_html_report(
        self, equity_curve, trades, performance, benchmark, 
        parameters, report_dir, strategy_name, include_trades
    ):
        """创建HTML报告"""
        # 生成基础统计数据
        stats = self._generate_stats_data(equity_curve, trades, performance)
        
        # 生成月度收益表格数据
        monthly_returns_table = self._generate_monthly_returns_table(equity_curve)
        
        # 格式化参数
        formatted_params = self._format_parameters(parameters)
        
        # 读取HTML模板
        template = self._get_report_template()
        
        # 生成交易记录HTML
        trades_html = self._generate_trades_html(trades) if include_trades else ""
        
        # 渲染模板
        has_benchmark = benchmark is not None and not benchmark.empty
        html_content = template.render(
            strategy_name=strategy_name,
            report_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            stats=stats,
            monthly_returns=monthly_returns_table,
            parameters=formatted_params,
            trades_html=trades_html,
            has_benchmark=has_benchmark
        )
        
        # 保存HTML文件
        html_path = os.path.join(report_dir, "report.html")
        with open(html_path, "w", encoding="utf-8") as f:
            f.write(html_content)
        
        return html_path
    
    def _get_report_template(self):
        """获取报告模板"""
        # 尝试从文件加载模板
        template_path = os.path.join(self.template_dir, "report_template.html")
        if os.path.exists(template_path):
            try:
                with open(template_path, "r", encoding="utf-8") as f:
                    template_str = f.read()
                return self.jinja_env.from_string(template_str)
            except Exception as e:
                self.logger.error(f"加载模板失败: {e}")
        
        # 默认模板，如果未找到模板文件
        default_template = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>{{ strategy_name }} - 回测报告</title>
            <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
            <style>
                body { font-family: 'Arial', sans-serif; margin: 20px; }
                .report-header { text-align: center; margin-bottom: 30px; }
                .performance-metrics { margin-bottom: 30px; }
                .monthly-returns { margin-bottom: 30px; }
                .parameters { margin-bottom: 30px; }
                .charts { margin-bottom: 30px; }
                .trades-table { margin-bottom: 30px; }
                img { max-width: 100%; height: auto; margin-bottom: 20px; }
                .positive { color: green; }
                .negative { color: red; }
                table { width: 100%; border-collapse: collapse; }
                th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
                th { background-color: #f2f2f2; }
                tr:hover { background-color: #f5f5f5; }
            </style>
        </head>
        <body>
            <div class="container">
                <!-- 报告标题 -->
                <div class="report-header">
                    <h1>{{ strategy_name }}</h1>
                    <p>回测报告生成时间: {{ report_time }}</p>
                </div>
                
                <!-- 性能指标 -->
                <div class="performance-metrics">
                    <h2>性能指标</h2>
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>指标</th>
                                <th>值</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for key, value in stats.items() %}
                            <tr>
                                <td>{{ key }}</td>
                                <td>{{ value }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- 图表 -->
                <div class="charts">
                    <h2>图表</h2>
                    <div class="row">
                        <div class="col-md-12">
                            <h4>权益曲线</h4>
                            <img src="equity_curve.png" alt="权益曲线">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <h4>月度收益热图</h4>
                            <img src="monthly_returns.png" alt="月度收益热图">
                        </div>
                    </div>
                </div>
                
                <!-- 月度收益表格 -->
                <div class="monthly-returns">
                    <h2>月度收益</h2>
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>年份</th>
                                {% for month in range(1, 13) %}
                                <th>{{ month }}月</th>
                                {% endfor %}
                                <th>年度</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for year, months in monthly_returns.items() %}
                            <tr>
                                <td>{{ year }}</td>
                                {% for month in range(1, 13) %}
                                    {% if month in months %}
                                        {% if months[month] >= 0 %}
                                        <td class="positive">{{ months[month] }}%</td>
                                        {% else %}
                                        <td class="negative">{{ months[month] }}%</td>
                                        {% endif %}
                                    {% else %}
                                        <td>-</td>
                                    {% endif %}
                                {% endfor %}
                                {% if months.get('yearly') %}
                                    {% if months['yearly'] >= 0 %}
                                    <td class="positive">{{ months['yearly'] }}%</td>
                                    {% else %}
                                    <td class="negative">{{ months['yearly'] }}%</td>
                                    {% endif %}
                                {% else %}
                                    <td>-</td>
                                {% endif %}
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- 策略参数 -->
                <div class="parameters">
                    <h2>策略参数</h2>
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>参数</th>
                                <th>值</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for key, value in parameters.items() %}
                            <tr>
                                <td>{{ key }}</td>
                                <td>{{ value }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- 交易记录 -->
                {% if trades_html %}
                <div class="trades-table">
                    <h2>交易记录</h2>
                    {{ trades_html|safe }}
                </div>
                {% endif %}
            </div>
        </body>
        </html>
        """
        return self.jinja_env.from_string(default_template)
    
    def _generate_stats_data(self, equity_curve, trades, performance):
        """生成性能统计数据"""
        if not performance:
            return {}
        
        # 将性能指标格式化为易读的格式
        stats = {
            "总收益率": f"{performance.get('total_return', 0):.2f}%",
            "年化收益率": f"{performance.get('annual_return', 0):.2f}%",
            "夏普比率": f"{performance.get('sharpe_ratio', 0):.2f}",
            "最大回撤": f"{performance.get('max_drawdown', 0):.2f}%",
            "胜率": f"{performance.get('win_rate', 0):.2f}%",
            "盈亏比": f"{performance.get('profit_ratio', 0):.2f}",
            "平均持仓周期": f"{performance.get('avg_holding_period', 0):.1f}天"
        }
        
        return stats
    
    def _generate_monthly_returns_table(self, equity_curve):
        """生成月度收益表格数据"""
        if equity_curve.empty or 'returns_pct' not in equity_curve.columns:
            return {}
        
        # 确保索引是日期类型并设置时区
        if not isinstance(equity_curve.index, pd.DatetimeIndex):
            return {}
        
        # 计算月度收益
        try:
            # 获取月度变化
            equity_curve = equity_curve.copy()
            equity_curve['year'] = equity_curve.index.year
            equity_curve['month'] = equity_curve.index.month
            
            # 计算月度收益
            monthly_returns = equity_curve.groupby(
                ['year', 'month']
            )['returns_pct'].sum()
            
            # 计算年度收益
            yearly_returns = equity_curve.groupby('year')['returns_pct'].sum()
            
            # 生成结果字典
            result = {}
            for (year, month), value in monthly_returns.items():
                if year not in result:
                    result[year] = {}
                result[year][month] = round(value, 2)
            
            # 添加年度收益
            for year, value in yearly_returns.items():
                if year not in result:
                    result[year] = {}
                result[year]['yearly'] = round(value, 2)
            
            return result
            
        except Exception as e:
            self.logger.error(f"生成月度收益表格失败: {e}")
            return {}
    
    def _format_parameters(self, parameters):
        """格式化策略参数"""
        if not parameters:
            return {}
        
        # 简单格式化，保持原始值
        formatted = {}
        for key, value in parameters.items():
            if isinstance(value, float):
                formatted[key] = f"{value:.4f}"
            else:
                formatted[key] = str(value)
        
        return formatted
    
    def _generate_trades_html(self, trades):
        """生成交易记录HTML表格"""
        if not trades:
            return ""
        
        # 创建DataFrame并格式化日期
        df = pd.DataFrame(trades)
        
        # 生成HTML表格
        table_html = """
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>入场日期</th>
                    <th>出场日期</th>
                    <th>代码</th>
                    <th>方向</th>
                    <th>入场价</th>
                    <th>出场价</th>
                    <th>数量</th>
                    <th>收益</th>
                    <th>收益率</th>
                </tr>
            </thead>
            <tbody>
        """
        
        for _, row in df.iterrows():
            profit_class = "positive" if row.get('profit', 0) > 0 else "negative"
            direction = row.get('direction')
            direction_text = "多" if direction == 'long' else "空"
            
            table_html += f"""
            <tr>
                <td>{row.get('entry_date', '')}</td>
                <td>{row.get('exit_date', '')}</td>
                <td>{row.get('symbol', '')}</td>
                <td>{direction_text}</td>
                <td>{row.get('entry_price', 0):.2f}</td>
                <td>{row.get('exit_price', 0):.2f}</td>
                <td>{row.get('volume', 0)}</td>
                <td class="{profit_class}">{row.get('profit', 0):.2f}</td>
                <td class="{profit_class}">{row.get('profit_pct', 0):.2f}%</td>
            </tr>
            """
        
        table_html += """
            </tbody>
        </table>
        """
        
        return table_html


if __name__ == "__main__":
    # 测试代码
    from backtest.engine import BacktestEngine
    from config.settings import setup_system
    from strategy.ma_cross_strategy import MACrossStrategy

    # 初始化系统
    setup_system()
    
    # 创建回测引擎
    backtest = BacktestEngine(
        strategy=MACrossStrategy,
        strategy_params={"short_period": 5, "long_period": 20},
        start_date="20230101",
        end_date="20231231",
        period="1d",
        symbols=["000001.SZ", "600000.SH"],
        initial_capital=100000,
        commission_rate=0.0003
    )
    
    # 运行回测
    results = backtest.run_backtest()
    
    # 创建报告生成器
    report_generator = ReportGenerator(output_dir="./output/reports")
    
    # 生成HTML报告
    html_path = report_generator.generate_html_report(
        results,
        strategy_name="MA交叉策略"
    )
    
    print(f"HTML报告已生成: {html_path}")
    
    # 生成PDF报告
    pdf_path = report_generator.generate_pdf_report(html_path)
    if pdf_path:
        print(f"PDF报告已生成: {pdf_path}") 