# 下载历史数据模块使用说明

## 概述

`下载历史数据.py` 是将交互界面的下载功能移动到独立文件中的多周期下载工具，使用现有的下载系统，支持在Python文件中直接配置多个周期和时间范围，自动循环处理所有周期的数据下载。

## 主要特性

- **多周期支持**: 支持同时下载多个周期的数据（日线、1分钟、tick等）
- **Python配置**: 直接在代码中配置下载参数，简单直观
- **使用现有系统**: 调用现有的download_data函数，保持功能一致性
- **自动循环处理**: 按配置顺序自动处理所有周期，无需手动干预
- **进度显示**: 实时显示下载进度和状态
- **数据预览**: 下载完成后自动显示数据预览
- **灵活配置**: 支持自定义时间范围、显示选项等

## 配置方式

所有配置都在 `下载历史数据.py` 文件的 `if __name__ == "__main__":` 部分进行：

### 主要配置项

```python
if __name__ == "__main__":
    # ==================== 多周期下载配置 ====================

    # 不同周期的配置
    download_configs = [
        {
            "period": "1d",
            "period_name": "日线",
            "start_date": "20150101",
            "end_date": "",
            "incremental": True,
            "display_head_rows": 5,
            "display_tail_rows": 5
        },
        {
            "period": "1m",
            "period_name": "1分钟",
            "start_date": "20240101",
            "end_date": "",
            "incremental": True,
            "display_head_rows": 5,
            "display_tail_rows": 5
        },
        {
            "period": "tick",
            "period_name": "tick",
            "start_date": "20250601",
            "end_date": "",
            "incremental": True,
            "display_head_rows": 5,
            "display_tail_rows": 5
        }
    ]

    # 通用下载选项
    show_data = True          # 是否显示数据预览
    real_time_log = False     # 是否实时日志
    delay_between_periods = 2 # 周期间延时（秒）
```

### 配置说明

- **download_configs**: 下载配置列表，每个配置包含：
  - **period**: 数据周期（如'1d', '1m', 'tick'）
  - **period_name**: 周期显示名称
  - **start_date**: 开始日期（YYYYMMDD格式，空字符串表示最早可用）
  - **end_date**: 结束日期（YYYYMMDD格式，空字符串表示今天）
  - **incremental**: 是否增量更新
  - **display_head_rows**: 头部显示行数
  - **display_tail_rows**: 尾部显示行数
- **show_data**: 是否显示数据预览
- **real_time_log**: 是否实时日志
- **delay_between_periods**: 周期间延时（秒）

### 股票代码来源

系统支持两种股票代码来源方式：

1. **统一结果文件**: 从 `download_results.txt` 文件中读取股票代码和各周期的下载结果
2. **股票列表文件**: 如果结果文件不存在，系统会从 `stock_list.txt` 创建初始结果文件

### 多周期处理机制

系统采用简化的统一结果文件管理机制：

1. **统一结果文件**: 所有周期使用同一个 `download_results.txt` 文件
2. **周期分段保存**: 文件内部按周期分段保存各自的下载结果
3. **简化文件格式**: 移除冗余的股票列表部分，直接从各周期的未下载股票中读取
4. **灵活的股票管理**: 不同周期可以有不同的股票列表，提供更大的灵活性
5. **清晰的结果查看**: 可以在一个文件中查看所有周期的下载状态

## 使用方法

### 1. 准备股票代码

有两种方式准备股票代码：

**方式一：创建股票列表文件**
创建 `stock_list.txt` 文件，格式示例：
```
# 股票列表文件
# 每行一个股票代码，支持注释

# A股股票示例
000001.SZ
000002.SZ
600000.SH
600036.SH

# 期货合约示例
a00.DF
pp00.DF
```

**方式二：直接创建结果文件**
直接创建 `download_results.txt` 文件，使用统一的多周期格式。

**简化的多周期结果文件格式示例：**
```
股票下载结果 - 2025-07-17 05:45:00
========================================

========== 日线数据下载结果 ==========
下载成功的股票:

下载失败的股票:

未下载的股票:
000001.SZ
000002.SZ
600000.SH
600036.SH
a00.DF
pp00.DF

无需下载的股票:

总计股票: 6
下载成功: 0
下载失败: 0
未下载: 6
无需下载: 0
最后更新时间: 2025-07-17 05:45:00

========== 1分钟数据下载结果 ==========
下载成功的股票:

下载失败的股票:

未下载的股票:
000001.SZ
000002.SZ
600000.SH
600036.SH
a00.DF
pp00.DF

无需下载的股票:

总计股票: 6
下载成功: 0
下载失败: 0
未下载: 6
无需下载: 0
最后更新时间: 2025-07-17 05:45:00

========== tick数据下载结果 ==========
（类似格式，包含各周期的下载状态）
```

**格式说明：**
- 移除了冗余的"股票列表"部分
- 股票信息直接存储在各周期的"未下载的股票"中
- 系统从各周期的未下载股票中读取要处理的股票
- 文件格式更简洁，逻辑更清晰

### 2. 配置下载参数

编辑 `data/下载历史数据.py` 文件中的配置部分，设置：
- 要下载的周期配置
- 各周期的时间范围
- 下载选项

### 3. 运行下载程序

```bash
cd d:\quant
python data/下载历史数据.py
```

### 4. 查看下载进度

程序会自动显示：
- 当前处理的周期
- 下载进度
- 完成状态
- 数据预览（如果启用）

## 输出示例

```
🚀 开始批量下载数据，共 3 个周期
📋 统一结果文件: D:\data\download_results.txt
📋 从股票列表文件创建初始结果文件: D:\data\stock_list.txt
✅ 已创建初始结果文件，包含 6 只股票

============================================================
📈 [1/3] 开始下载 日线 数据...
⏰ 时间范围: 20150101 ~ 今天
📊 增量更新: 是
📁 结果文件: download_results.txt (周期: 1d)
============================================================
✅ 日线数据下载完成

⏳ 等待2秒后继续下载下一个周期...

============================================================
📈 [2/3] 开始下载 1分钟 数据...
⏰ 时间范围: 20240101 ~ 今天
📊 增量更新: 是
📁 结果文件: download_results_1m.txt
============================================================
✅ 1分钟数据下载完成

⏳ 等待2秒后继续下载下一个周期...
```

## 自定义配置

直接修改 `下载历史数据.py` 文件中的配置变量：

```python
# 自定义周期配置
download_configs = [
    {
        "period": "1d",
        "period_name": "日线",
        "start_date": "20200101",  # 从2020年开始
        "end_date": "",
        "incremental": True,
        "display_head_rows": 5,
        "display_tail_rows": 5
    },
    {
        "period": "1m",
        "period_name": "1分钟",
        "start_date": "20240101",  # 从2024年开始
        "end_date": "",
        "incremental": True,
        "display_head_rows": 5,
        "display_tail_rows": 5
    },
    {
        "period": "5m",  # 添加5分钟周期
        "period_name": "5分钟",
        "start_date": "20240601",
        "end_date": "",
        "incremental": True,
        "display_head_rows": 5,
        "display_tail_rows": 5
    }
]
```

## 注意事项

1. **股票代码文件**: 确保 `stock_list.txt` 或 `download_results.txt` 文件存在且包含有效的股票代码
2. **时间格式**: 时间必须使用YYYYMMDD格式
3. **网络连接**: 确保网络连接正常，数据源服务可用
4. **磁盘空间**: 确保有足够的磁盘空间存储下载的数据
5. **权限**: 确保对数据存储目录有写入权限
6. **结果文件**: 使用统一的结果文件，按周期分段保存各自的下载状态

## 故障排除

1. **股票文件不存在**: 检查 `stock_list.txt` 或 `download_results.txt` 文件是否存在
2. **下载失败**: 检查网络连接和数据源服务状态
3. **权限问题**: 检查文件和目录权限
4. **配置错误**: 检查Python配置语法是否正确
5. **文件格式错误**: 确保结果文件使用正确的多周期格式

## 与交互界面的关系

- 交互界面的"下载历史数据"功能已被移除
- 所有数据下载功能现在通过此独立文件实现
- 使用现有的 `download_data` 函数，保持功能一致性
- 交互界面专注于数据查看、管理和合成功能
