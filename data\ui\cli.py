#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
命令行接口模块

专门处理命令行参数解析和命令行模式的执行
负责将命令行参数转换为对数据操作模块的调用
"""

import argparse
import os
import sys
from typing import Optional

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from config.settings import DATA_ROOT
from utils.logger import get_unified_logger, LogTarget
from utils.data_helpers import DataHelpers
from data.core.operations import download_data, read_data, list_data, summarize_data

# 初始化
logger = get_unified_logger(__name__, enhanced=True)
helpers = DataHelpers()


def create_argument_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(description="数据管理系统")
    
    # 通用选项
    parser.add_argument("--interactive", action="store_true", help="启动交互模式")
    
    # 下载数据选项
    parser.add_argument("--download", action="store_true", help="下载数据（自动从download_results.txt文件读取股票列表）")
    parser.add_argument("--stocks", nargs="+", help="股票代码列表（⚠️ 已废弃：系统会自动从download_results.txt文件读取）")
    parser.add_argument("--period", default="tick", help="数据周期")
    parser.add_argument("--start", help="开始日期 (YYYYMMDD)")
    parser.add_argument("--end", help="结束日期 (YYYYMMDD)")
    parser.add_argument("--incremental", action="store_true", help="增量更新数据")
    
    # 查看本地数据选项
    parser.add_argument("--view-data", action="store_true", help="查看本地数据")
    parser.add_argument("--fields", nargs="+", help="要获取的字段列表")
    parser.add_argument("--lines", type=int, default=5, help="要显示的行数")
    
    # 其他选项
    parser.add_argument("--list-files", action="store_true", help="列出可用数据文件")
    parser.add_argument("--market", help="市场代码")
    parser.add_argument("--code", help="股票代码")
    parser.add_argument("--summarize", action="store_true", help="查看数据汇总")
    parser.add_argument("--storage-info", action="store_true", help="显示数据存储信息")
    parser.add_argument("--test-logging", action="store_true", help="测试日志系统")
    
    return parser


def handle_download_command(args):
    """处理下载命令"""
    # 显示文件路径信息
    from config.settings import DATA_ROOT
    import os
    download_results_file = os.path.join(DATA_ROOT, "download_results.txt")
    logger.info(f"📋 股票代码文件路径: {download_results_file}")
    logger.info("⚠️ 命令行中的 --stocks 参数已废弃")

    # 验证周期参数
    from utils.data_processor.period_handler import is_valid_period, get_native_periods
    if args.period not in get_native_periods() and not is_valid_period(args.period):
        logger.error(f"错误: '{args.period}' 不是有效的数据周期")
        return 1

    success = download_data(
        stocks=[],  # 此参数已废弃，系统会从download_results.txt文件读取
        period=args.period,
        start_date=args.start,
        end_date=args.end,
        incremental=args.incremental,
        show_data=True,
        real_time_log=True
    )

    return 0 if success else 1


def handle_view_data_command(args):
    """处理查看数据命令"""
    if not args.stocks:
        logger.error("错误: 查看数据需要指定股票代码")
        return 1
    
    # 验证周期参数
    from utils.data_processor.period_handler import is_valid_period, get_native_periods
    if args.period not in get_native_periods() and not is_valid_period(args.period):
        logger.error(f"错误: '{args.period}' 不是有效的数据周期")
        return 1
    
    try:
        data = read_data(
            symbols=args.stocks,
            period=args.period,
            start_time=args.start,
            end_time=args.end,
            fields=args.fields,
            lines=args.lines,
            quiet_console=True
        )
        
        if not data:
            logger.info("未找到符合条件的数据。")
            return 1
        else:
            logger.info("成功读取数据:")
            for symbol, df in data.items():
                if df is not None and not df.empty:
                    logger.info(f"  - {symbol}: {len(df)} 行")
            
            log_file = helpers.get_log_file_path("data_commands")
            logger.info(f"查询结果已详细记录到日志文件: {log_file}")
            return 0
    except Exception as e:
        logger.error(f"读取数据时出现错误: {str(e)}")
        return 1


def handle_list_files_command(args):
    """处理列出文件命令"""
    try:
        files = list_data(
            market=args.market,
            stock=args.code,
            period=args.period
        )
        
        if files:
            logger.info(f"找到 {len(files)} 个数据文件")
            return 0
        else:
            logger.info("未找到符合条件的数据文件。")
            return 1
    except Exception as e:
        logger.error(f"列出数据文件时出现错误: {str(e)}")
        return 1


def handle_summarize_command(args):
    """处理汇总命令"""
    try:
        summary = summarize_data()
        return 0 if summary else 1
    except Exception as e:
        logger.error(f"汇总数据时出现错误: {str(e)}")
        return 1


def handle_storage_info_command(args):
    """处理存储信息命令"""
    logger.info(f"当前数据根目录: {DATA_ROOT}")
    
    if os.path.exists(DATA_ROOT):
        try:
            size_bytes = sum(
                os.path.getsize(os.path.join(dirpath, filename))
                for dirpath, _, filenames in os.walk(DATA_ROOT)
                for filename in filenames
            )
            size_mb = size_bytes / (1024 * 1024)
            print(f"目录总大小: {size_mb:.2f} MB")
            return 0
        except Exception as e:
            logger.error(f"计算存储大小时出现错误: {str(e)}")
            return 1
    else:
        logger.error("数据目录不存在。")
        return 1


def handle_test_logging_command(args):
    """处理测试日志命令"""
    from utils.logger.examples import test_logging_system
    test_logging_system()
    return 0


def execute_command_line(args):
    """执行命令行命令"""
    if args.test_logging:
        return handle_test_logging_command(args)
    elif args.download:
        return handle_download_command(args)
    elif args.view_data:
        return handle_view_data_command(args)
    elif args.list_files:
        return handle_list_files_command(args)
    elif args.summarize:
        return handle_summarize_command(args)
    elif args.storage_info:
        return handle_storage_info_command(args)
    else:
        return None  # 表示需要进入交互模式


# 保持向后兼容的命令行处理
if __name__ == "__main__":
    # 兼容直接运行此模块的情况
    parser = create_argument_parser()
    args = parser.parse_args()
    
    result = execute_command_line(args)
    if result is not None:
        sys.exit(result)
    else:
        print("请使用 --interactive 参数启动交互模式，或使用其他命令参数。")
        parser.print_help()