#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
日志过滤器模块

提供各种日志过滤器，用于根据不同条件过滤日志记录
"""

import logging

# 导入LogTarget枚举
from utils.logger.config import LogTarget


class TargetFilter(logging.Filter):
    """
    根据目标类型过滤日志记录

    此过滤器根据日志记录中的target属性过滤日志，
    使日志只输出到指定的目标（文件、控制台或两者）
    
    支持分别控制标题和正文的输出位置：
    - 通过title_target属性控制标题的输出位置
    - 通过content_target属性控制正文的输出位置
    """

    def __init__(self, target_type: int, default_target: int = LogTarget.FILE):
        """
        初始化过滤器

        Args:
            target_type: 目标类型，可以是LogTarget.FILE或LogTarget.CONSOLE
            default_target: 默认目标类型，未指定target时使用，默认为LogTarget.FILE
        """
        super().__init__()
        self.target_type = target_type
        self.default_target = default_target
        
    def filter(self, record: logging.LogRecord) -> bool:
        """
        过滤日志记录

        Args:
            record: 日志记录对象

        Returns:
            bool: 如果日志应该被输出到当前目标，则返回True，否则返回False
        """
        # 添加当前处理器类型属性，供格式化器使用
        record.current_handler_type = self.target_type
        
        # 检查是否使用新的参数格式（title_target和content_target）
        has_title_target = hasattr(record, 'title_target')
        has_content_target = hasattr(record, 'content_target')
        
        # 如果同时使用了title_target和content_target
        if has_title_target or has_content_target:
            # 获取标题和正文的目标
            title_target = getattr(record, 'title_target', self.default_target)
            content_target = getattr(record, 'content_target', self.default_target)
            
            # 尝试将字符串转换为数字
            if isinstance(title_target, str):
                title_target = LogTarget.from_code(title_target)
            elif not isinstance(title_target, int) or title_target is None:
                title_target = self.default_target
                
            if isinstance(content_target, str):
                content_target = LogTarget.from_code(content_target)
            elif not isinstance(content_target, int) or content_target is None:
                content_target = self.default_target
            
            # 检查目标是否匹配
            if self.target_type == LogTarget.FILE:
                # 文件目标：标题或正文的目标必须是FILE或BOTH
                return (title_target in (LogTarget.FILE, LogTarget.BOTH) or 
                        content_target in (LogTarget.FILE, LogTarget.BOTH))
            elif self.target_type == LogTarget.CONSOLE:
                # 控制台目标：标题或正文的目标必须是CONSOLE或BOTH
                return (title_target in (LogTarget.CONSOLE, LogTarget.BOTH) or 
                        content_target in (LogTarget.CONSOLE, LogTarget.BOTH))
            else:
                # 未知目标类型，默认允许
                return True
        
        # 使用旧的参数格式（target）
        # 检查记录是否有target属性
        if not hasattr(record, 'target') or getattr(record, 'target') is None:
            # 如果没有指定target或target为None，使用默认目标
            return self.target_type == self.default_target
            
        # 获取记录的目标
        record_target = getattr(record, 'target', self.default_target)
        
        # 尝试将字符串转换为数字
        if isinstance(record_target, str):
            record_target = LogTarget.from_code(record_target)
        elif not isinstance(record_target, int) or record_target is None:
            # 如果不是整数也不是字符串，或者是None，使用默认目标
            return self.target_type == self.default_target
            
        # 检查目标是否匹配
        if self.target_type == LogTarget.FILE:
            # 文件目标：记录目标必须是FILE或BOTH
            return record_target in (LogTarget.FILE, LogTarget.BOTH)
        elif self.target_type == LogTarget.CONSOLE:
            # 控制台目标：记录目标必须是CONSOLE或BOTH
            return record_target in (LogTarget.CONSOLE, LogTarget.BOTH)
        else:
            # 未知目标类型，默认允许
            return True


class LevelFilter(logging.Filter):
    """
    级别过滤器
    
    根据日志记录的级别过滤日志，只允许指定级别及以上的日志通过
    """
    
    def __init__(self, min_level=logging.NOTSET, max_level=logging.CRITICAL):
        """
        初始化级别过滤器
        
        Args:
            min_level: 最小级别（含），低于此级别的日志将被过滤掉
            max_level: 最大级别（含），高于此级别的日志将被过滤掉
        """
        super().__init__()
        self.min_level = min_level
        self.max_level = max_level
        
    def filter(self, record):
        """
        过滤日志记录
        
        Args:
            record: 日志记录对象
            
        Returns:
            bool: 如果日志记录的级别在指定范围内，则返回True，否则返回False
        """
        # 检查日志级别是否在指定范围内
        return self.min_level <= record.levelno <= self.max_level


class PrefixFilter(logging.Filter):
    """
    前缀过滤器
    
    根据日志记录的前缀过滤日志，只允许包含指定前缀的日志通过
    """
    
    def __init__(self, prefix=None):
        """
        初始化前缀过滤器
        
        Args:
            prefix: 要匹配的前缀，如果为None则不进行过滤
        """
        super().__init__()
        self.prefix = prefix
        
    def filter(self, record):
        """
        过滤日志记录
        
        Args:
            record: 日志记录对象
            
        Returns:
            bool: 如果日志记录的消息包含指定前缀，则返回True，否则返回False
        """
        if self.prefix is None:
            return True
            
        # 获取日志消息
        message = record.getMessage()
        
        # 检查消息是否以指定前缀开头
        return message.startswith(self.prefix)


class ModuleFilter(logging.Filter):
    """
    模块过滤器
    
    根据日志记录的模块名称过滤日志，只允许来自指定模块的日志通过
    """
    
    def __init__(self, module_name=None):
        """
        初始化模块过滤器
        
        Args:
            module_name: 要匹配的模块名称，如果为None则不进行过滤
        """
        super().__init__()
        self.module_name = module_name
        
    def filter(self, record):
        """
        过滤日志记录
        
        Args:
            record: 日志记录对象
            
        Returns:
            bool: 如果日志记录来自指定模块，则返回True，否则返回False
        """
        if self.module_name is None:
            return True
            
        # 获取日志记录的模块名称
        logger_name = record.name
        
        # 检查模块名称是否匹配
        # 支持前缀匹配，例如：module_name="data"会匹配"data.submodule"
        return (logger_name == self.module_name or 
                logger_name.startswith(f"{self.module_name}.")) 