---
description: 
globs: "*"
alwaysApply: true
---
# Python 编码规范

本文档定义了项目中Python代码的编写规范和最佳实践，旨在保持代码的一致性、可读性和可维护性。

## 1. 基本设计原则

- **功能模块化设计**：便于开发管理和代码复用
- **最小化功能粒度**：模块和函数应设计为执行单一、明确的任务，保持短小精悍，易于理解、测试和AI处理
- **增强代码复用性**：优先设计和使用高度解耦、职责清晰的模块
- **`utils`模块优先原则**: 
    - 在开发或优化功能时，优先检查并使用`D:\quant\utils`目录下的现有通用模块。
    - 如果计划开发的功能具有通用潜力，应优先考虑在`D:\quant\utils`目录中将其实现为可复用的通用模块，然后再集成到具体业务模块中。
    - 向`D:\quant\utils`目录添加新功能时，需仔细评估其归类，尽量整合到功能相似的现有文件中，或创建逻辑清晰的新文件，避免`utils`目录结构混乱和功能冗余。
    - 在判断`D:\quant\utils`目录中是否存在某个通用功能时，必须基于对该目录下**实际文件内容**的实时检查，而非仅仅依赖可能过时的文档。文档仅作为参考，代码文件是最终依据。
- **避免重复造轮子**：在实现新功能前，应检查项目中（特别是`utils`目录）是否已存在可复用的模块或函数，避免不必要的重复开发
- **推荐项目打包安装**：对于提供命令行工具、包含多模块或期望高度复用的项目，建议通过`pyproject.toml`将其配置为可安装的包
- **处理大数据实时反馈**：在处理大数据、大任务等需要等待的节点，要实时反馈进度，避免用户长时间等待而无反馈
- **临时脚本管理**：所有的临时Python脚本都要加上 **test_**前缀，以示区分，在使用完后应删除，避免项目中留有太多临时文件
- **按需开发**：不要擅自添加、删除或修改非任务需求相关的其他功能
- **代码简洁规范**：代码生成要简洁规范，主要功能不改变的前提下，避免冗余代码无限制增加
- **避免基础语法错误**：生成代码和修复bug后严格检查缩进问题，避免**SyntaxError: invalid syntax**等低级问题
- **文件组织一致性**：同一个模块的文件，除了缓存文件，应放在相同目录中，避免文件混乱
- **结构一致性**：确保所有if-else结构的缩进一致，保持结构清晰
- **代码质量自检**：生成代码后，先对代码质量做检查，确保无linter等错误
- **菜单默认值**：所有终端交互式菜单选项及其子菜单都应设置默认值
- **文档同步更新**：当项目代码或功能发生变动时，必须同步更新相关文档，确保文档始终反映最新的项目状态和功能
- **缓存文件位置**：所有缓存文件夹请放在项目根目录之外
- **统一使用UTF-8编码**：所有源代码文件、配置文件和日志文件必须统一使用UTF-8编码，以确保在不同操作系统和编辑环境下正确显示中文内容，有效解决中文乱码问题。即使在Windows环境下也必须使用UTF-8而非默认的GBK/CP936编码，特别是在日志记录和文件读写操作中。

## 2. 命名规范

### 2.1 变量命名
- 变量名使用小写字母，单词之间用下划线连接（snake_case）
- 变量名应当具有描述性，避免使用单字母（除了循环计数器）
```python
# 正确
user_name = "John"
total_count = 0

# 错误
userName = "John"
n = 0  # 除非在循环中使用
```

### 2.2 常量命名
- 常量使用大写字母，单词之间用下划线连接
```python
MAX_CONNECTIONS = 100
DEFAULT_TIMEOUT = 30
```

### 2.3 函数命名
- 函数名使用小写字母，单词之间用下划线连接
- 函数名应当是动词或动词短语
```python
def calculate_total():
    pass

def get_user_info():
    pass
```

### 2.4 类命名
- 类名使用驼峰命名法（CamelCase）
- 每个单词的首字母大写
```python
class UserAccount:
    pass

class DataProcessor:
    pass
```

## 3. 代码结构规范

### 3.1 导入规范
- 导入顺序：标准库 > 第三方库 > 本地模块
- 每组之间空一行
- 避免使用 `from module import *`
```python
import os
import sys

import numpy as np
import pandas as pd

from .utils import helper
from .config import settings
```

### 3.2 函数定义
- 每个函数必须有文档字符串
- 明确指定参数类型和返回类型
```python
def process_data(data: pd.DataFrame, threshold: float = 0.5) -> pd.DataFrame:
    """
    处理输入的数据框。

    Args:
        data: 输入的数据框
        threshold: 过滤阈值，默认0.5

    Returns:
        处理后的数据框
    """
    # 函数实现
    pass
```

### 3.3 类定义
- 类应当遵循单一职责原则
- 使用特殊方法（如`__init__`、`__str__`等）增强类的功能
- 类方法和静态方法使用适当的装饰器
```python
class DataProcessor:
    """数据处理类，负责对输入数据进行处理和转换。"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化数据处理器。
        
        Args:
            config: 配置参数字典
        """
        self.config = config
        
    @classmethod
    def from_file(cls, config_path: str) -> 'DataProcessor':
        """
        从配置文件创建实例。
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            数据处理器实例
        """
        config = load_config(config_path)
        return cls(config)
    
    @staticmethod
    def validate_input(data: pd.DataFrame) -> bool:
        """
        验证输入数据是否有效。
        
        Args:
            data: 输入数据
            
        Returns:
            数据是否有效
        """
        return not data.empty and all(col in data.columns for col in REQUIRED_COLUMNS)
```

## 4. 错误处理规范

### 4.1 异常处理
- 始终使用具体的异常类型而不是捕获所有异常
- 异常处理块要尽可能小
```python
# 正确
try:
    value = int(user_input)
except ValueError:
    print("请输入有效的数字")

# 错误
try:
    # 大量代码
    pass
except Exception:  # 不要捕获所有异常
    pass
```

### 4.2 断言使用
- 用于验证函数的前置条件和后置条件
- 不要用于处理可恢复的错误情况
```python
def calculate_percentage(total: int, part: int) -> float:
    assert total > 0, "总数必须大于0"
    assert part >= 0, "部分值必须大于等于0"
    return (part / total) * 100
```

## 5. 代码质量保证

### 5.1 类型注解
- 使用类型注解提高代码可读性和可维护性
- 复杂类型使用 typing 模块
```python
from typing import List, Dict, Optional

def get_user_data(user_id: int) -> Optional[Dict[str, any]]:
    pass

def process_items(items: List[str]) -> None:
    pass
```

### 5.2 注释规范
- 注释应该解释为什么，而不是什么
- 复杂算法必须有详细注释
```python
# 正确
# 使用二分查找而不是线性搜索以提高大数据集的搜索效率
def binary_search(items: List[int], target: int) -> int:
    pass

# 错误
# 这个函数计算总和
def calculate_sum(numbers):
    pass
```

### 5.3 代码修改原则
- 修改前先理解代码的整体结构和实现逻辑
- 保持原有的编码风格和命名规范
- 最小化修改范围，仅修改必要部分
- 确保修改不破坏现有功能
- 添加必要的注释解释修改原因和实现方式

### 5.4 质量保证措施
- 编写代码前先查看相似功能的现有实现
- 使用与项目一致的代码格式化工具
- 提交前运行代码静态检查工具
- 确保没有未使用的导入和变量
- 遵循DRY原则，避免重复代码
- 使用适当的数据结构和算法提高效率

## 6. 性能优化规范

### 6.1 数据结构选择
- 根据使用场景选择合适的数据结构
- 避免不必要的数据复制
```python
# 正确：使用集合进行成员检查
valid_users = set(users)
if user in valid_users:
    pass

# 错误：使用列表进行频繁的成员检查
valid_users = list(users)
if user in valid_users:
    pass
```

### 6.2 循环优化
- 使用列表推导式替代简单循环
- 避免在循环中修改迭代对象
```python
# 正确
squares = [x**2 for x in range(10)]

# 错误
squares = []
for x in range(10):
    squares.append(x**2)
```

## 7. 测试规范

### 7.1 单元测试
- 每个函数都应该有对应的单元测试
- 测试应该覆盖边界条件和异常情况
```python
def test_calculate_percentage():
    assert calculate_percentage(100, 50) == 50.0
    with pytest.raises(AssertionError):
        calculate_percentage(0, 10)
```

### 7.2 文档测试
- 在文档字符串中包含可执行的示例
```python
def add_numbers(a: int, b: int) -> int:
    """
    返回两个数字的和。

    >>> add_numbers(1, 2)
    3
    >>> add_numbers(-1, 1)
    0
    """
    return a + b
```

## 8. 模块导入与路径处理

### 8.1 项目根目录导入方式
为解决Python模块导入问题，**所有**导入本地模块的Python文件必须在文件顶部添加以下代码：

```python
import os
import sys

# 将项目根目录添加到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 然后使用绝对导入方式导入模块
from data.fetcher.module import Class
# 而不是使用相对导入
# from ..data.fetcher.module import Class
```

### 8.2 导入顺序规范

导入语句应按以下顺序排列：

1. 标准库导入
2. 第三方库导入
3. 添加项目根目录到sys.path
4. 本地模块导入

每组之间用一个空行分隔。

### 8.3 特殊情况处理

#### 8.3.1 在项目根目录的文件中

如果Python文件位于项目根目录，使用单次`dirname`即可：

```python
# 当前文件在项目根目录时
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
```

#### 8.3.2 在深层嵌套目录中

对于位于多层嵌套目录的文件，使用多次`dirname`操作：

```python
# 对于深度为3的目录结构，如 project/dir1/dir2/dir3/file.py
# 获取项目根目录
project_root = os.path.abspath(__file__)
for _ in range(3):  # 向上3级
    project_root = os.path.dirname(project_root)
sys.path.insert(0, project_root)
```

### 8.4 模块别名规范

- **一致性**：同一个模块在整个项目中应使用统一的别名
- **社区习惯**：使用广泛接受的别名（如`pd`表示pandas，`np`表示numpy）
- **项目专属别名**：项目特有的模块可定义特定别名，但须在项目中保持一致

```python
# 标准别名示例
import numpy as np
import pandas as pd
from matplotlib import pyplot as plt

# 项目专属别名示例
from xtquant import xtdata as xt_data
```

## 9. 终端显示与交互规范

### 9.1 终端显示规范
终端交互提示应清晰、一致并提供合理的默认值：

```python
# 正确格式
请输入要下载的周期(例如: tick, 1m, 5m, 15m, 30m, 1h, 1d，默认为tick):
请输入开始时间(格式: YYYYMMDD，默认为自动检测):
请输入结束时间(格式: YYYYMMDD，默认为当前日期):
是否启用增量更新? (y/n，默认为y):
请输入要显示的行数 (默认为5，0表示不显示数据):
请输入数据保存目录 (默认为d:\data):
```

### 9.2 print打印规范
- 优先合并打印语句，避免分段打印同一信息
```python
# 正确
print("标准方式创建失败，尝试替代方法，或重新选择其他方法创建")

# 错误
print("标准方式创建失败，尝试替代方法")
print("或重新选择其他方法创建")
```

## 10. 依赖管理规范

### 10.1 基本原则与AI职责
- **核心依赖文件**: 项目的所有Python依赖项必须在项目根目录下的 `requirements.txt` 文件中明确列出并进行版本控制。
- **Python版本**: 项目应指定一个统一的Python版本进行开发，所有依赖项应确保与该商定版本兼容。当前项目约定的Python版本应在`requirements.txt`文件的注释中或项目主README中明确指出。
- **Conda环境**: 推荐在 Conda 环境中进行开发和管理依赖。
- **AI助手职责**:
    - 在代码生成或修改过程中，如果引入了新的库或移除了现有库的依赖，AI助手**必须**识别这些变化。
    - AI助手应主动提醒用户这些依赖项的变化，并解释引入新依赖的理由或移除旧依赖的影响。
    - 在获得用户确认后，AI助手**必须**协助更新 `requirements.txt` 文件，确保其准确反映项目当前所需的依赖及其版本。
    - AI助手在更新 `requirements.txt` 时，应遵循本规范中的版本管理和文件格式要求（见 10.2 和 10.3）。
- **定期检查与更新**:
    - **兼容性检查**: 定期（例如每周）检查 `requirements.txt` 文件中的包是否与项目约定的 Python 版本（其具体版本号在`requirements.txt`文件的注释中或项目主README中指明）兼容。
    - **版本更新**: 定期（例如每周）检查 `requirements.txt` 中列出的依赖是否有推荐的稳定新版本。在提议更新前，AI助手或开发者应评估新版本的兼容性、必要性以及潜在风险，并征得用户或团队同意后方可更新。

### 10.2 `requirements.txt` 文件格式与内容
- **精确版本指定**: 必须使用 `==` 操作符精确指定每个依赖包的版本，例如 `numpy==2.2.5`。这有助于确保在不同环境和时间点构建项目时依赖的一致性。
- **避免版本范围**: 严格禁止在 `requirements.txt` 中使用 `>=`、`<=`、`~=` 或类似的范围限定符，以防止无意的自动升级导致兼容性问题。
- **注释规范**:
    - 可以在 `requirements.txt` 文件中使用 `#` 开头添加注释。
    - 对于有特殊安装步骤、特定版本选择原因或不常用库，建议添加注释说明。
    - 示例：
      ```
      # utils
      requests==2.32.3
      packaging==25.0

      # data analysis
      numpy==2.2.5
      pandas==2.2.3

      # plotting
      matplotlib==3.10.1
      seaborn==0.13.2

      # TA-Lib 需要预编译库，请参考官方文档进行安装
      # ta-lib==0.6.3 # 版本号仅为示例
      ```
- **文件组织**: 建议按字母顺序或功能模块对 `requirements.txt` 中的包进行分组和排序，以提高可读性和可维护性。

### 10.3 依赖安装与管理命令
- **标准安装命令**: 使用 `pip install -r requirements.txt` 命令来安装 `requirements.txt` 文件中列出的所有项目依赖。
- **单个包安装规范** (主要供AI助手逐步安装、用户手动安装或调试时参考):
    - **逐个安装**: 推荐逐个安装包，而不是一次性用一个 `pip install` 命令安装多个包。这有助于清晰地显示每个包的安装进度、日志和可能出现的错误。
    - **进度反馈**: AI助手在执行安装时，应设法反馈每个包的安装状态（例如，通过打印信息或总结安装结果）。
    - **示例**:
      ```bash
      pip install numpy # AI应反馈安装进度/结果
      pip install pandas # AI应反馈安装进度/结果
      ```
- **依赖检查与更新工具** (可选辅助):
    - 可以使用如 `pip list --outdated` 查看可更新的包。
    - 工具如 `pip-tools` (通过 `pip-compile` 管理 `requirements.in` 生成 `requirements.txt`) 或 `pip-upgrade` 可以辅助管理依赖。
    - 即使使用这些工具，最终生成的 `requirements.txt` 文件也必须符合本规范10.2中定义的格式（特别是精确版本指定）。

### 10.4 特殊依赖处理
- **预编译或系统依赖**: 对于那些不能通过标准 `pip install <package_name>` 直接从PyPI成功安装的库（例如，需要额外系统库、手动下载预编译的wheel文件，或者像 `TA-Lib` 这样有复杂构建过程的库），必须在 `requirements.txt` 中通过注释清晰地说明其特殊的安装方法、先决条件或指向相关安装指南的链接。
  ```
  # TA-Lib requires system libraries. Refer to official installation guide:
  # https://mrjbq7.github.io/ta-lib/install.html
  # Ensure build-essential, python3-dev, and talib-dev (or equivalent) are installed on Linux,
  # or download appropriate wheel for Windows.
  # ta-lib==0.4.28  # (Comment out if direct pip install is problematic, version for reference)
  ```
- **私有源或Git仓库依赖**: 如果项目依赖于私有PyPI源或直接从Git仓库安装的包，应在 `requirements.txt` 中使用 `pip` 支持的相应格式，并可能需要注释说明访问权限或配置。

## 11. 文件编码与国际化

### 11.1 文件编码标准
- **统一使用UTF-8编码**：所有源代码文件、配置文件、数据文件和日志文件必须统一使用UTF-8编码，不使用系统默认编码（如Windows下的GBK/CP936）
- **在文件头部明确声明UTF-8编码**：Python源代码文件应在头部包含编码声明
  ```python
  #!/usr/bin/env python
  # -*- coding: utf-8 -*-
  ```
- **避免使用编码相关的魔法数字或假设**：不要在代码中假设特定的编码环境

### 11.2 日志文件编码规则
- **日志文件必须使用UTF-8编码**：所有日志文件必须强制指定UTF-8编码，确保日志在任何环境下都可以正确显示中文
  ```python
  # 示例：在日志处理中指定UTF-8编码
  file_handler = logging.FileHandler(log_file, encoding='utf-8')
  ```
- **禁止使用系统默认编码**：特别是在Windows系统上，不要使用默认的编码（通常为GBK或CP936），而是显式指定UTF-8
- **读写操作中的编码处理**：对文件的读写操作必须显式指定UTF-8编码
  ```python
  # 文件读取示例
  with open('file.txt', 'r', encoding='utf-8') as f:
      content = f.read()
      
  # 文件写入示例
  with open('output.txt', 'w', encoding='utf-8') as f:
      f.write('包含中文的内容')
  ```

### 11.3 跨平台编码兼容性
- **使用字符串而非字节**：优先处理Unicode字符串而非字节序列
- **编码转换**：需要处理不同编码时，先解码为Unicode再编码为目标编码
  ```python
  # 处理可能是GBK编码的数据
  try:
      unicode_text = binary_data.decode('utf-8')
  except UnicodeDecodeError:
      try:
          unicode_text = binary_data.decode('gbk')
      except UnicodeDecodeError:
          # 处理编码识别失败的情况
          unicode_text = binary_data.decode('utf-8', errors='replace')
  
  # 统一转换为UTF-8
  utf8_text = unicode_text.encode('utf-8').decode('utf-8')
  ```

### 11.4 中文显示与处理
- **终端输出中文**：确保终端环境支持UTF-8，特别是在Windows系统下
  ```python
  # 在Windows控制台中启用UTF-8显示
  import sys
  if sys.platform == 'win32':
      # 可能需要先设置控制台代码页
      import subprocess
      subprocess.run(['chcp', '65001'], shell=True)
  ```
- **中文字符宽度处理**：处理中文等宽字符时，考虑到其在终端中的实际显示宽度
  ```python
  def get_display_width(text):
      """计算字符串在终端中的显示宽度，中文字符占用2个单位宽度"""
      width = 0
      for char in text:
          # 中文、日文、韩文等宽度为2
          if '\u4e00' <= char <= '\u9fff' or '\u3000' <= char <= '\u30ff' or '\uac00' <= char <= '\ud7af':
              width += 2
          else:
              width += 1
      return width
  ```
- **文件名与路径中的中文**：处理包含中文的文件路径时，确保使用正确的编码
  ```python
  # 使用Path对象处理中文路径
  from pathlib import Path
  
  # 创建包含中文的路径
  path = Path("数据") / "用户信息.txt"
  
  # 读取文件
  if path.exists():
      with open(path, 'r', encoding='utf-8') as f:
          content = f.read()
  ```

### 11.5 编码问题诊断与修复
- **编码问题诊断**：当遇到编码相关错误时，使用以下方法诊断
  1. 检查文件的实际编码：使用专用工具如`chardet`检测文件编码
  2. 查看文件的BOM标记
  3. 尝试使用不同编码打开文件并观察结果
- **乱码修复策略**：
  1. 确认源文件的实际编码
  2. 使用正确的编码读取文件
  3. 将内容转换为Unicode
  4. 使用UTF-8保存文件
  ```python
  import chardet
  
  # 检测文件编码
  with open('file.txt', 'rb') as f:
      raw_data = f.read()
      result = chardet.detect(raw_data)
      encoding = result['encoding']
  
  # 使用检测到的编码读取内容
  with open('file.txt', 'r', encoding=encoding) as f:
      content = f.read()
  
  # 以UTF-8保存
  with open('file_utf8.txt', 'w', encoding='utf-8') as f:
      f.write(content)
  ```

### 11.6 编码相关的库与工具推荐
- **chardet**：用于自动检测文本编码
- **ftfy**：修复文本中的Unicode字符问题
- **unicodedata**：标准库模块，处理Unicode数据
- **PyICU**：强大的国际化组件，支持Unicode字符处理
- **pyuca**：Unicode排序算法实现，用于正确排序各种语言的字符串
