#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据展示与文本格式化工具模块

提供数据格式化展示、表格生成、文本美化和终端输出控制等功能，
支持各种数据类型的美观展示，如表格、图表、文本报告、进度条等。
"""

# 从表格模块导入
from utils.data_display.table import (
    dataframe_to_text_table,
    write_dataframe_to_file,
    create_console_table,
    print_summary_statistics
)

# 从文本模块导入
from utils.data_display.text import (
    get_display_width,
    format_column,
    truncate_text,
    wrap_text,
    print_boxed_text,
    create_text_box,
    animate_text,
    highlight_text
)

# 从格式化模块导入其他函数
from utils.data_display.formatting import (
    format_number,
    format_percentage,
    format_float_smart  # 直接从formatting模块导入format_float_smart
)

# 从控制台模块导入
from utils.data_display.console import (
    print_progress_bar,
    print_dict_as_table,
    show_menu
)

# 导出所有接口
__all__ = [
    # 表格相关
    'dataframe_to_text_table',
    'write_dataframe_to_file',
    'create_console_table',
    'print_summary_statistics',
    
    # 文本相关
    'get_display_width',
    'format_column',
    'truncate_text',
    'wrap_text',
    'print_boxed_text',
    'create_text_box',
    'animate_text',
    'highlight_text',
    
    # 格式化相关
    'format_float_smart',
    'format_number',
    'format_percentage',
    
    # 控制台相关
    'print_progress_bar',
    'print_dict_as_table',
    'show_menu'
]