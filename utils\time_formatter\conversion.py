#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
时间转换功能模块

提供时间戳与日期时间对象相互转换的功能
"""

import os
import sys
from datetime import datetime
from typing import Union
import pandas as pd
import pytz

# 将项目根目录添加到Python路径
root_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, root_path)


def timestamp_to_datetime(
    timestamp: Union[int, float],
    unit: str = 'ms',
    tz: str = 'Asia/Shanghai'
) -> datetime:
    """
    将时间戳转换为带时区的datetime对象 - 重定向到新模块的简化实现

    Args:
        timestamp: 时间戳
        unit: 时间戳单位，可选值: 's'(秒)、'ms'(毫秒)
        tz: 时区名称（保持兼容性，但实际使用本地时区）

    Returns:
        datetime对象（本地时区）

    Note:
        此函数已重定向到新的极简实现，不再使用复杂的时区处理
    """
    # 重定向到新模块的简化实现
    from utils.time_utils import simple_ms_to_datetime, s_to_datetime

    if unit == 's':
        return s_to_datetime(timestamp)
    else:  # 默认为毫秒
        return simple_ms_to_datetime(timestamp)


def datetime_to_timestamp(dt: datetime, unit: str = 'ms') -> Union[int, float]:
    """
    将datetime对象转换为时间戳 - 重定向到新模块的简化实现

    Args:
        dt: datetime对象
        unit: 时间戳单位，可选值: 's'(秒)、'ms'(毫秒)

    Returns:
        时间戳

    Note:
        此函数已重定向到新的极简实现，不再使用复杂的时区处理
    """
    # 重定向到新模块的简化实现
    from utils.time_utils import datetime_to_ms, datetime_to_s

    if unit == 's':
        return datetime_to_s(dt)
    elif unit == 'ms':
        return datetime_to_ms(dt)
    else:
        raise ValueError(f"不支持的时间戳单位: {unit}，请使用 's' 或 'ms'")


def standardize_timestamp_series(
    series: pd.Series, 
    tz: str = 'Asia/Shanghai'
) -> pd.Series:
    """
    将时间戳Series标准化为datetime64[ns, tz]类型
    
    Args:
        series: 输入的Series，可能是时间戳或时间字符串
        tz: 时区名称
        
    Returns:
        标准化后的Series
    """
    # 使用新模块的简化实现，避免pd.to_datetime的时区问题
    try:
        # 检查是否为时间戳
        if series.dtype in ['int64', 'float64'] and len(series) > 0 and series.iloc[0] > 1e10:
            # 毫秒时间戳
            from utils.time_utils import fast_ms_to_datetime_index
            result = pd.Series(fast_ms_to_datetime_index(series), index=series.index)
        elif series.dtype in ['int64', 'float64']:
            # 秒时间戳
            from utils.time_utils import s_to_datetime_index
            result = pd.Series(s_to_datetime_index(series), index=series.index)
        else:
            # 字符串或其他格式，发出警告但仍使用pd.to_datetime
            import warnings
            warnings.warn("字符串时间转换可能有时区问题，建议使用明确的时间戳格式", UserWarning)
            result = smart_to_datetime(series)

        return result
    except:
        # 如果转换失败，返回原始数据
        return series


def format_date_to_int(date_obj: Union[datetime, str]) -> int:
    """
    将日期对象或字符串转换为整数格式(YYYYMMDD)
    
    Args:
        date_obj: 日期对象或字符串
        
    Returns:
        整数格式的日期
    """
    import re
    from utils.time_formatter.parsing import parse_datetime
    from utils.smart_time_converter import smart_to_datetime

    if isinstance(date_obj, str):
        # 尝试解析字符串
        try:
            date_obj = parse_datetime(date_obj)
        except ValueError:
            # 如果已经是YYYYMMDD格式的字符串，直接转换为整数
            if re.match(r'^\d{8}$', date_obj):
                return int(date_obj)
            raise ValueError(f"无法解析日期字符串: {date_obj}")
    
    # 转换为整数
    return int(date_obj.strftime("%Y%m%d"))


def format_int_to_date(date_int: int) -> datetime:
    """
    将整数格式的日期(YYYYMMDD)转换为日期对象
    
    Args:
        date_int: 整数格式的日期
        
    Returns:
        日期对象
    """
    date_str = str(date_int)
    if len(date_str) != 8:
        raise ValueError(f"无效的日期整数格式: {date_int}，应为YYYYMMDD")
    
    year = int(date_str[:4])
    month = int(date_str[4:6])
    day = int(date_str[6:8])
    
    return datetime(year, month, day)