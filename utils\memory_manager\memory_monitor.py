"""
内存监控模块 - 提供系统和进程级别的内存使用监控功能
"""

import os
import time
import threading
import psutil
from typing import Dict, List, Callable
from datetime import datetime

from ..logger import get_unified_logger

# 获取logger实例
logger = get_unified_logger("memory_monitor")


class MemoryMonitor:
    """
    内存监控类，用于监控系统和当前进程的内存使用情况
    
    功能:
    1. 实时监控内存使用情况
    2. 定时检查内存状态
    3. 记录内存使用历史
    4. 提供内存使用统计信息
    """
    
    def __init__(
        self, 
        check_interval: int = 30,
        history_size: int = 100,
        auto_start: bool = False
    ):
        """
        初始化内存监控器
        
        Args:
            check_interval: 自动检查间隔时间(秒)，默认30秒
            history_size: 历史记录保留条数，默认100条
            auto_start: 是否自动启动监控线程，默认False
        """
        # 当前进程
        self._process = psutil.Process(os.getpid())
        
        # 配置参数
        self._check_interval = check_interval
        self._history_size = history_size
        
        # 监控状态
        self._is_monitoring = False
        self._monitor_thread = None
        
        # 历史记录
        self._memory_history = []
        
        # 回调函数列表
        self._callbacks = []
        
        # 自动启动
        if auto_start:
            self.start_monitoring()
            
        logger.debug(f"内存监控器初始化完成，进程ID: {os.getpid()}")
    
    def get_memory_info(self) -> Dict:
        """
        获取当前内存使用情况
        
        Returns:
            包含内存使用详情的字典
        """
        # 系统内存信息
        sys_memory = psutil.virtual_memory()
        
        # 当前进程内存信息
        process_memory = self._process.memory_info()
        
        # 组装结果
        memory_info = {
            # 时间戳
            'timestamp': datetime.now(),
            
            # 系统内存信息(单位: 字节)
            'system': {
                'total': sys_memory.total,
                'available': sys_memory.available,
                'used': sys_memory.used,
                'free': sys_memory.free,
                'percent': sys_memory.percent,
            },
            
            # 进程内存信息(单位: 字节)
            'process': {
                'rss': process_memory.rss,  # 物理内存
                'vms': process_memory.vms,  # 虚拟内存
                'percent': self._process.memory_percent(),
            }
        }
        
        return memory_info
    
    def check_memory(self) -> Dict:
        """
        检查当前内存状态并记录历史
        
        Returns:
            当前内存信息
        """
        memory_info = self.get_memory_info()
        
        # 添加到历史记录
        self._add_to_history(memory_info)
        
        # 触发回调
        self._trigger_callbacks(memory_info)
        
        # 记录日志
        self._log_memory_info(memory_info)
        
        return memory_info
    
    def _add_to_history(self, memory_info: Dict) -> None:
        """
        将内存信息添加到历史记录
        
        Args:
            memory_info: 内存信息字典
        """
        self._memory_history.append(memory_info)
        
        # 限制历史记录大小
        if len(self._memory_history) > self._history_size:
            self._memory_history.pop(0)
    
    def _log_memory_info(self, memory_info: Dict) -> None:
        """
        记录内存使用日志
        
        Args:
            memory_info: 内存信息字典
        """
        sys_info = memory_info['system']
        proc_info = memory_info['process']
        
        # 转换为MB便于阅读
        sys_used_mb = sys_info['used'] / (1024 * 1024)
        sys_total_mb = sys_info['total'] / (1024 * 1024)
        proc_rss_mb = proc_info['rss'] / (1024 * 1024)
        
        # 格式化日志信息，避免行过长
        sys_percent = sys_info['percent']
        proc_percent = proc_info['percent']
        sys_mem_str = f"系统: {sys_used_mb:.1f}MB/{sys_total_mb:.1f}MB ({sys_percent}%)"
        proc_mem_str = f"进程: {proc_rss_mb:.1f}MB ({proc_percent:.1f}%)"
        logger.debug(f"内存状态 - {sys_mem_str}, {proc_mem_str}")
    
    def get_history(self) -> List[Dict]:
        """
        获取内存使用历史记录
        
        Returns:
            内存历史记录列表
        """
        return self._memory_history.copy()
    
    def get_memory_trend(self, minutes: int = 5) -> Dict:
        """
        分析内存使用趋势
        
        Args:
            minutes: 分析最近多少分钟的数据，默认5分钟
            
        Returns:
            趋势分析结果
        """
        if not self._memory_history:
            return {'status': 'no_data'}
        
        # 计算时间范围
        now = datetime.now()
        time_threshold = now.timestamp() - (minutes * 60)
        
        # 筛选时间范围内的记录
        recent_records = [
            record for record in self._memory_history
            if record['timestamp'].timestamp() >= time_threshold
        ]
        
        if not recent_records:
            return {'status': 'no_recent_data'}
        
        # 计算进程内存变化率
        if len(recent_records) >= 2:
            first_record = recent_records[0]
            last_record = recent_records[-1]
            
            first_rss = first_record['process']['rss']
            last_rss = last_record['process']['rss']
            
            # 计算时间差(秒)
            time_diff = (
                last_record['timestamp'] - first_record['timestamp']
            ).total_seconds()
            
            if time_diff > 0:
                # 计算每秒内存增长(字节/秒)
                growth_rate = (last_rss - first_rss) / time_diff
                
                # 转换为MB/分钟便于理解
                growth_rate_mb_min = (growth_rate * 60) / (1024 * 1024)
                
                return {
                    'status': 'ok',
                    'growth_rate_bytes_sec': growth_rate,
                    'growth_rate_mb_min': growth_rate_mb_min,
                    'is_increasing': growth_rate > 0,
                    'first_timestamp': first_record['timestamp'],
                    'last_timestamp': last_record['timestamp'],
                    'duration_seconds': time_diff
                }
        
        return {'status': 'insufficient_data'}
    
    def register_callback(self, callback: Callable[[Dict], None]) -> None:
        """
        注册内存检查回调函数
        
        Args:
            callback: 回调函数，接收内存信息字典作为参数
        """
        if callback not in self._callbacks:
            self._callbacks.append(callback)
            logger.debug(f"已注册内存监控回调函数: {callback.__name__}")
    
    def unregister_callback(self, callback: Callable[[Dict], None]) -> None:
        """
        注销内存检查回调函数
        
        Args:
            callback: 之前注册的回调函数
        """
        if callback in self._callbacks:
            self._callbacks.remove(callback)
            logger.debug(f"已注销内存监控回调函数: {callback.__name__}")
    
    def _trigger_callbacks(self, memory_info: Dict) -> None:
        """
        触发所有注册的回调函数
        
        Args:
            memory_info: 内存信息字典
        """
        for callback in self._callbacks:
            try:
                callback(memory_info)
            except Exception as e:
                logger.error(f"执行内存监控回调函数出错: {e}")
    
    def _monitoring_loop(self) -> None:
        """
        监控线程的主循环
        """
        logger.info(f"内存监控线程已启动，检查间隔: {self._check_interval}秒")
        
        while self._is_monitoring:
            try:
                self.check_memory()
            except Exception as e:
                logger.error(f"内存监控检查出错: {e}")
            
            # 等待下一次检查
            time.sleep(self._check_interval)
        
        logger.info("内存监控线程已停止")
    
    def start_monitoring(self) -> bool:
        """
        启动定时监控线程
        
        Returns:
            是否成功启动
        """
        if self._is_monitoring:
            logger.warning("内存监控线程已在运行中")
            return False
        
        self._is_monitoring = True
        self._monitor_thread = threading.Thread(
            target=self._monitoring_loop,
            daemon=True
        )
        self._monitor_thread.start()
        
        return True
    
    def stop_monitoring(self) -> bool:
        """
        停止定时监控线程
        
        Returns:
            是否成功停止
        """
        if not self._is_monitoring:
            logger.warning("内存监控线程未在运行")
            return False
        
        self._is_monitoring = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=1.0)
            self._monitor_thread = None
        
        return True
    
    def set_check_interval(self, seconds: int) -> None:
        """
        设置检查间隔时间
        
        Args:
            seconds: 间隔秒数
        """
        if seconds < 1:
            raise ValueError("检查间隔必须大于等于1秒")
        
        self._check_interval = seconds
        logger.debug(f"内存监控检查间隔已设置为: {seconds}秒")
    
    def set_history_size(self, size: int) -> None:
        """
        设置历史记录保留条数
        
        Args:
            size: 历史记录条数
        """
        if size < 1:
            raise ValueError("历史记录条数必须大于等于1")
        
        old_size = self._history_size
        self._history_size = size
        
        # 如果新的大小更小，则裁剪历史记录
        if size < old_size and len(self._memory_history) > size:
            self._memory_history = self._memory_history[-size:]
        
        logger.debug(f"内存监控历史记录条数已设置为: {size}")
    
    def clear_history(self) -> None:
        """
        清空历史记录
        """
        self._memory_history.clear()
        logger.debug("内存监控历史记录已清空")
    
    def get_memory_summary(self) -> Dict:
        """
        获取内存使用摘要信息
        
        Returns:
            内存使用摘要字典
        """
        current_info = self.get_memory_info()
        trend_info = self.get_memory_trend()
        
        # 计算系统内存信息(转换为MB)
        sys_info = current_info['system']
        sys_total_mb = sys_info['total'] / (1024 * 1024)
        sys_used_mb = sys_info['used'] / (1024 * 1024)
        sys_available_mb = sys_info['available'] / (1024 * 1024)
        
        # 计算进程内存信息(转换为MB)
        proc_info = current_info['process']
        proc_rss_mb = proc_info['rss'] / (1024 * 1024)
        proc_vms_mb = proc_info['vms'] / (1024 * 1024)
        
        # 组装摘要信息
        summary = {
            'timestamp': current_info['timestamp'],
            'system': {
                'total_mb': sys_total_mb,
                'used_mb': sys_used_mb,
                'available_mb': sys_available_mb,
                'usage_percent': sys_info['percent'],
            },
            'process': {
                'rss_mb': proc_rss_mb,
                'vms_mb': proc_vms_mb,
                'usage_percent': proc_info['percent'],
            },
            'trend': trend_info
        }
        
        return summary 