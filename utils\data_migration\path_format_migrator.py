#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
复权数据路径格式迁移工具

将复权数据从旧格式迁移到新格式：
旧格式：D:\\data\\adjusted\\SH\\600000\\front\\1m\\2025.parquet
新格式：D:\\data\\adjusted\\front\\SH\\600000\\1m\\2025.parquet

功能特性：
1. 批量扫描和迁移复权数据
2. 并行处理提高迁移效率
3. 完整性验证确保数据安全
4. 详细日志记录迁移过程
5. 支持迁移回滚机制

作者: Augment AI
日期: 2025-01-29
"""

import os
import shutil
import json
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import pandas as pd

from utils.logger import get_unified_logger, LogTarget

# 获取统一日志记录器
logger = get_unified_logger()


class PathFormatMigrator:
    """复权数据路径格式迁移器"""
    
    def __init__(self, data_root: str, backup_dir: Optional[str] = None):
        """
        初始化迁移器
        
        Args:
            data_root: 数据根目录
            backup_dir: 备份目录，默认为data_root/migration_backup
        """
        self.data_root = Path(data_root)
        self.backup_dir = Path(backup_dir) if backup_dir else self.data_root / "migration_backup"
        self.migration_log_file = self.backup_dir / f"migration_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # 创建备份目录
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        # 迁移统计
        self.migration_stats = {
            "total_files": 0,
            "migrated_files": 0,
            "failed_files": 0,
            "start_time": None,
            "end_time": None,
            "errors": []
        }
        
        logger.info(LogTarget.FILE, f"路径格式迁移器初始化完成")
        logger.info(LogTarget.FILE, f"数据根目录: {self.data_root}")
        logger.info(LogTarget.FILE, f"备份目录: {self.backup_dir}")
    
    def scan_old_format_files(self) -> List[Dict[str, str]]:
        """
        扫描旧格式的复权数据文件
        
        Returns:
            文件信息列表，每个元素包含文件路径和元数据
        """
        logger.info(LogTarget.FILE, "开始扫描旧格式复权数据文件...")
        
        old_files = []
        adjusted_dir = self.data_root / "adjusted"
        
        if not adjusted_dir.exists():
            logger.warning(LogTarget.FILE, f"复权数据目录不存在: {adjusted_dir}")
            return old_files
        
        # 扫描旧格式：adjusted/{market}/{code}/{adj_type}/{period}/*.parquet
        for market_dir in adjusted_dir.iterdir():
            if not market_dir.is_dir():
                continue
                
            market = market_dir.name
            logger.debug(LogTarget.FILE, f"扫描市场目录: {market}")
            
            for code_dir in market_dir.iterdir():
                if not code_dir.is_dir():
                    continue
                    
                code = code_dir.name
                
                for adj_type_dir in code_dir.iterdir():
                    if not adj_type_dir.is_dir() or adj_type_dir.name not in ["front", "back"]:
                        continue
                        
                    adj_type = adj_type_dir.name
                    
                    for period_dir in adj_type_dir.iterdir():
                        if not period_dir.is_dir():
                            continue
                            
                        period = period_dir.name
                        
                        # 查找parquet文件
                        for parquet_file in period_dir.glob("*.parquet"):
                            file_info = {
                                "old_path": str(parquet_file),
                                "market": market,
                                "code": code,
                                "adj_type": adj_type,
                                "period": period,
                                "filename": parquet_file.name,
                                "size": parquet_file.stat().st_size,
                                "mtime": parquet_file.stat().st_mtime
                            }
                            old_files.append(file_info)
        
        logger.info(LogTarget.FILE, f"扫描完成，找到 {len(old_files)} 个旧格式复权数据文件")
        self.migration_stats["total_files"] = len(old_files)
        
        return old_files
    
    def generate_new_path(self, file_info: Dict[str, str]) -> str:
        """
        根据文件信息生成新格式路径
        
        Args:
            file_info: 文件信息字典
            
        Returns:
            新格式的文件路径
        """
        # 新格式：adjusted/{adj_type}/{market}/{code}/{period}/{filename}
        new_path = self.data_root / "adjusted" / file_info["adj_type"] / file_info["market"] / file_info["code"] / file_info["period"] / file_info["filename"]
        return str(new_path)
    
    def migrate_single_file(self, file_info: Dict[str, str]) -> Dict[str, any]:
        """
        迁移单个文件
        
        Args:
            file_info: 文件信息字典
            
        Returns:
            迁移结果字典
        """
        try:
            old_path = Path(file_info["old_path"])
            new_path = Path(self.generate_new_path(file_info))
            
            # 创建新目录
            new_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 验证源文件存在
            if not old_path.exists():
                raise FileNotFoundError(f"源文件不存在: {old_path}")
            
            # 检查目标文件是否已存在
            if new_path.exists():
                logger.warning(LogTarget.FILE, f"目标文件已存在，将覆盖: {new_path}")
            
            # 复制文件（而不是移动，保留原文件作为备份）
            shutil.copy2(old_path, new_path)
            
            # 验证复制结果
            if not new_path.exists():
                raise RuntimeError(f"文件复制失败: {new_path}")
            
            # 验证文件大小
            if new_path.stat().st_size != old_path.stat().st_size:
                raise RuntimeError(f"文件大小不匹配: {old_path.stat().st_size} != {new_path.stat().st_size}")
            
            # 验证数据完整性（读取parquet文件）
            try:
                old_df = pd.read_parquet(old_path)
                new_df = pd.read_parquet(new_path)
                
                if len(old_df) != len(new_df):
                    raise RuntimeError(f"数据行数不匹配: {len(old_df)} != {len(new_df)}")
                    
                if list(old_df.columns) != list(new_df.columns):
                    raise RuntimeError(f"数据列不匹配")
                    
            except Exception as e:
                raise RuntimeError(f"数据完整性验证失败: {e}")
            
            result = {
                "success": True,
                "old_path": str(old_path),
                "new_path": str(new_path),
                "file_size": new_path.stat().st_size,
                "error": None
            }
            
            logger.debug(LogTarget.FILE, f"文件迁移成功: {old_path} -> {new_path}")
            return result
            
        except Exception as e:
            error_msg = f"文件迁移失败: {file_info['old_path']} - {e}"
            logger.error(LogTarget.FILE, error_msg)
            
            result = {
                "success": False,
                "old_path": file_info["old_path"],
                "new_path": self.generate_new_path(file_info),
                "file_size": 0,
                "error": str(e)
            }
            
            return result
    
    def migrate_all_files(self, file_list: List[Dict[str, str]], max_workers: int = 4) -> Dict[str, any]:
        """
        批量迁移所有文件
        
        Args:
            file_list: 文件信息列表
            max_workers: 最大并发数
            
        Returns:
            迁移结果统计
        """
        logger.info(LogTarget.FILE, f"开始批量迁移 {len(file_list)} 个文件，并发数: {max_workers}")
        
        self.migration_stats["start_time"] = datetime.now().isoformat()
        migration_results = []
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有迁移任务
            future_to_file = {executor.submit(self.migrate_single_file, file_info): file_info 
                             for file_info in file_list}
            
            # 处理完成的任务
            for future in as_completed(future_to_file):
                file_info = future_to_file[future]
                try:
                    result = future.result()
                    migration_results.append(result)
                    
                    if result["success"]:
                        self.migration_stats["migrated_files"] += 1
                    else:
                        self.migration_stats["failed_files"] += 1
                        self.migration_stats["errors"].append(result["error"])
                        
                    # 进度报告
                    completed = len(migration_results)
                    if completed % 100 == 0 or completed == len(file_list):
                        progress = (completed / len(file_list)) * 100
                        logger.info(LogTarget.FILE, f"迁移进度: {completed}/{len(file_list)} ({progress:.1f}%)")
                        
                except Exception as e:
                    error_msg = f"处理文件时发生异常: {file_info['old_path']} - {e}"
                    logger.error(LogTarget.FILE, error_msg)
                    self.migration_stats["failed_files"] += 1
                    self.migration_stats["errors"].append(error_msg)
        
        self.migration_stats["end_time"] = datetime.now().isoformat()
        
        # 保存迁移日志
        self._save_migration_log(migration_results)
        
        logger.info(LogTarget.FILE, f"批量迁移完成")
        logger.info(LogTarget.FILE, f"成功: {self.migration_stats['migrated_files']}")
        logger.info(LogTarget.FILE, f"失败: {self.migration_stats['failed_files']}")
        
        return {
            "migration_stats": self.migration_stats,
            "migration_results": migration_results
        }
    
    def _save_migration_log(self, migration_results: List[Dict[str, any]]):
        """保存迁移日志"""
        try:
            log_data = {
                "migration_stats": self.migration_stats,
                "migration_results": migration_results,
                "timestamp": datetime.now().isoformat()
            }
            
            with open(self.migration_log_file, 'w', encoding='utf-8') as f:
                json.dump(log_data, f, indent=2, ensure_ascii=False)
                
            logger.info(LogTarget.FILE, f"迁移日志已保存: {self.migration_log_file}")
            
        except Exception as e:
            logger.error(LogTarget.FILE, f"保存迁移日志失败: {e}")

    def cleanup_old_files(self, migration_results: List[Dict[str, any]], confirm: bool = False) -> Dict[str, int]:
        """
        清理旧格式文件

        Args:
            migration_results: 迁移结果列表
            confirm: 是否确认删除，False时只是模拟

        Returns:
            清理统计结果
        """
        logger.info(LogTarget.FILE, f"开始清理旧格式文件，确认删除: {confirm}")

        cleanup_stats = {
            "total_files": 0,
            "deleted_files": 0,
            "failed_deletions": 0,
            "errors": []
        }

        # 只清理迁移成功的文件
        successful_migrations = [r for r in migration_results if r["success"]]
        cleanup_stats["total_files"] = len(successful_migrations)

        for result in successful_migrations:
            old_path = Path(result["old_path"])

            try:
                if old_path.exists():
                    if confirm:
                        old_path.unlink()
                        logger.debug(LogTarget.FILE, f"已删除旧文件: {old_path}")
                        cleanup_stats["deleted_files"] += 1
                    else:
                        logger.debug(LogTarget.FILE, f"模拟删除旧文件: {old_path}")
                        cleanup_stats["deleted_files"] += 1
                else:
                    logger.warning(LogTarget.FILE, f"旧文件不存在: {old_path}")

            except Exception as e:
                error_msg = f"删除文件失败: {old_path} - {e}"
                logger.error(LogTarget.FILE, error_msg)
                cleanup_stats["failed_deletions"] += 1
                cleanup_stats["errors"].append(error_msg)

        # 清理空目录
        if confirm:
            self._cleanup_empty_directories()

        logger.info(LogTarget.FILE, f"清理完成，删除文件: {cleanup_stats['deleted_files']}")
        return cleanup_stats

    def _cleanup_empty_directories(self):
        """清理空目录"""
        logger.info(LogTarget.FILE, "开始清理空目录...")

        adjusted_dir = self.data_root / "adjusted"
        if not adjusted_dir.exists():
            return

        # 从最深层开始清理
        for root, dirs, files in os.walk(adjusted_dir, topdown=False):
            root_path = Path(root)

            # 跳过根目录
            if root_path == adjusted_dir:
                continue

            try:
                # 如果目录为空，删除它
                if not any(root_path.iterdir()):
                    root_path.rmdir()
                    logger.debug(LogTarget.FILE, f"已删除空目录: {root_path}")
            except Exception as e:
                logger.debug(LogTarget.FILE, f"删除空目录失败: {root_path} - {e}")

    def rollback_migration(self, migration_log_file: str) -> Dict[str, int]:
        """
        回滚迁移操作

        Args:
            migration_log_file: 迁移日志文件路径

        Returns:
            回滚统计结果
        """
        logger.info(LogTarget.FILE, f"开始回滚迁移操作，日志文件: {migration_log_file}")

        rollback_stats = {
            "total_files": 0,
            "rolled_back_files": 0,
            "failed_rollbacks": 0,
            "errors": []
        }

        try:
            # 读取迁移日志
            with open(migration_log_file, 'r', encoding='utf-8') as f:
                log_data = json.load(f)

            migration_results = log_data.get("migration_results", [])
            successful_migrations = [r for r in migration_results if r["success"]]
            rollback_stats["total_files"] = len(successful_migrations)

            # 删除新格式文件
            for result in successful_migrations:
                new_path = Path(result["new_path"])

                try:
                    if new_path.exists():
                        new_path.unlink()
                        logger.debug(LogTarget.FILE, f"已删除新文件: {new_path}")
                        rollback_stats["rolled_back_files"] += 1
                    else:
                        logger.warning(LogTarget.FILE, f"新文件不存在: {new_path}")

                except Exception as e:
                    error_msg = f"删除新文件失败: {new_path} - {e}"
                    logger.error(LogTarget.FILE, error_msg)
                    rollback_stats["failed_rollbacks"] += 1
                    rollback_stats["errors"].append(error_msg)

            # 清理新格式的空目录
            self._cleanup_new_format_empty_directories()

            logger.info(LogTarget.FILE, f"回滚完成，删除新文件: {rollback_stats['rolled_back_files']}")

        except Exception as e:
            error_msg = f"回滚操作失败: {e}"
            logger.error(LogTarget.FILE, error_msg)
            rollback_stats["errors"].append(error_msg)

        return rollback_stats

    def _cleanup_new_format_empty_directories(self):
        """清理新格式的空目录"""
        logger.info(LogTarget.FILE, "开始清理新格式空目录...")

        adjusted_dir = self.data_root / "adjusted"
        if not adjusted_dir.exists():
            return

        # 清理新格式目录结构：adjusted/{adj_type}/{market}/{code}/{period}
        for adj_type_dir in adjusted_dir.iterdir():
            if not adj_type_dir.is_dir() or adj_type_dir.name not in ["front", "back"]:
                continue

            # 从最深层开始清理
            for root, dirs, files in os.walk(adj_type_dir, topdown=False):
                root_path = Path(root)

                try:
                    # 如果目录为空，删除它
                    if not any(root_path.iterdir()):
                        root_path.rmdir()
                        logger.debug(LogTarget.FILE, f"已删除空目录: {root_path}")
                except Exception as e:
                    logger.debug(LogTarget.FILE, f"删除空目录失败: {root_path} - {e}")


def main():
    """主函数，演示迁移工具的使用"""
    from config.settings import DATA_ROOT

    # 初始化迁移器
    migrator = PathFormatMigrator(DATA_ROOT)

    # 扫描旧格式文件
    old_files = migrator.scan_old_format_files()

    if not old_files:
        logger.info(LogTarget.FILE, "没有找到需要迁移的文件")
        return

    # 执行迁移
    migration_result = migrator.migrate_all_files(old_files, max_workers=4)

    # 打印统计结果
    stats = migration_result["migration_stats"]
    logger.info(LogTarget.FILE, f"迁移统计:")
    logger.info(LogTarget.FILE, f"  总文件数: {stats['total_files']}")
    logger.info(LogTarget.FILE, f"  成功迁移: {stats['migrated_files']}")
    logger.info(LogTarget.FILE, f"  迁移失败: {stats['failed_files']}")

    # 如果迁移成功，询问是否清理旧文件
    if stats["migrated_files"] > 0:
        logger.info(LogTarget.FILE, "迁移完成，可以选择清理旧格式文件")
        # 注意：实际使用时需要用户确认
        # cleanup_result = migrator.cleanup_old_files(migration_result["migration_results"], confirm=True)


if __name__ == "__main__":
    main()
