#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
跨日期数据分区修复验证测试

验证修复后的系统能够正确处理跨日期数据分区问题。
"""

import pandas as pd
import numpy as np
from datetime import datetime
import sys
import os
import tempfile
import shutil

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data.storage.unified_data_saver import save_data_unified, SaveStrategy
from utils.smart_timestamp_processor import extract_partition_timestamp


def test_cross_date_error_detection():
    """测试跨日期数据错误检测"""
    print("=== 测试跨日期数据错误检测 ===")
    
    # 创建跨日期测试数据
    cross_date_data = pd.DataFrame({
        'time': [
            1752562262000,  # 2025-07-15 14:51:02
            1752629698000   # 2025-07-16 09:34:58
        ],
        'price': [13.53, 13.45]
    })
    
    try:
        # 尝试提取单一分区时间戳，应该抛出异常
        timestamp = extract_partition_timestamp(cross_date_data, "tick", force_single_partition=False)
        print("❌ 未检测到跨日期数据错误")
        return False
    except ValueError as e:
        print(f"✅ 正确检测到跨日期数据错误: {e}")
        return True
    except Exception as e:
        print(f"❌ 意外错误: {e}")
        return False


def test_force_single_partition():
    """测试强制单分区模式"""
    print("\n=== 测试强制单分区模式 ===")
    
    # 创建跨日期测试数据
    cross_date_data = pd.DataFrame({
        'time': [
            1752562262000,  # 2025-07-15 14:51:02
            1752629698000   # 2025-07-16 09:34:58
        ],
        'price': [13.53, 13.45]
    })
    
    try:
        # 强制单分区模式，应该返回主要日期
        timestamp = extract_partition_timestamp(cross_date_data, "tick", force_single_partition=True)
        print(f"强制单分区时间戳: {timestamp}")
        
        if timestamp == "20250715":
            print("✅ 强制单分区模式测试通过")
            return True
        else:
            print("❌ 强制单分区模式测试失败")
            return False
    except Exception as e:
        print(f"❌ 强制单分区模式测试失败: {e}")
        return False


def test_multi_partition_strategy():
    """测试多分区策略自动选择"""
    print("\n=== 测试多分区策略自动选择 ===")
    
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 创建跨日期测试数据
        cross_date_data = pd.DataFrame({
            'time': [
                1752562262000,  # 2025-07-15 14:51:02
                1752562265000,  # 2025-07-15 14:51:05
                1752629698000,  # 2025-07-16 09:34:58
                1752629701000   # 2025-07-16 09:35:01
            ],
            'lastPrice': [13.53, 13.54, 13.45, 13.46],
            'volume': [100, 200, 300, 400]
        })
        
        print(f"测试数据行数: {len(cross_date_data)}")
        
        # 使用AUTO策略，应该自动选择多分区策略
        result = save_data_unified(
            df=cross_date_data,
            data_root=temp_dir,
            symbol="600000.SH",
            period="tick",
            strategy=SaveStrategy.AUTO,
            parallel=True,
            data_type="adjusted",
            adj_type="front"
        )
        
        print(f"保存结果: {result.success}")
        print(f"使用策略: {result.strategy_used.value}")
        print(f"保存分区数: {len(result.saved_partitions)}")
        
        if result.success:
            print("保存的分区:")
            for date_str, path in result.saved_partitions.items():
                print(f"  {date_str}: {os.path.basename(path)}")
                
                # 验证文件是否存在
                if os.path.exists(path):
                    saved_data = pd.read_parquet(path)
                    print(f"    数据行数: {len(saved_data)}")
                else:
                    print(f"    文件不存在")
            
            # 验证是否使用了多分区策略
            if result.strategy_used.value == "multi" and len(result.saved_partitions) == 2:
                expected_dates = ["20250715", "20250716"]
                actual_dates = list(result.saved_partitions.keys())
                
                if all(date in actual_dates for date in expected_dates):
                    print("✅ 多分区策略自动选择测试通过")
                    return True
                else:
                    print(f"❌ 分区日期不正确，期望: {expected_dates}, 实际: {actual_dates}")
                    return False
            else:
                print(f"❌ 策略选择错误，期望: multi, 实际: {result.strategy_used.value}")
                return False
        else:
            print(f"❌ 保存失败: {result.error_message}")
            return False
            
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)


def test_single_date_data():
    """测试单日期数据处理"""
    print("\n=== 测试单日期数据处理 ===")
    
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 创建单日期测试数据
        single_date_data = pd.DataFrame({
            'time': [1752562262000, 1752562265000, 1752562268000],  # 都是7月15日
            'price': [13.53, 13.54, 13.55]
        })
        
        # 使用AUTO策略
        result = save_data_unified(
            df=single_date_data,
            data_root=temp_dir,
            symbol="600000.SH",
            period="tick",
            strategy=SaveStrategy.AUTO
        )
        
        print(f"单日期数据策略: {result.strategy_used.value}")
        print(f"分区数: {len(result.saved_partitions)}")
        
        if result.success and len(result.saved_partitions) == 1:
            date_key = list(result.saved_partitions.keys())[0]
            if date_key == "20250715":
                print("✅ 单日期数据处理测试通过")
                return True
            else:
                print(f"❌ 日期错误，期望: 20250715, 实际: {date_key}")
                return False
        else:
            print("❌ 单日期数据处理测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        return False
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)


def test_strategy_priority():
    """测试策略优先级"""
    print("\n=== 测试策略优先级 ===")
    
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 创建跨日期数据
        cross_date_data = pd.DataFrame({
            'time': [1752562262000, 1752629698000],
            'price': [13.53, 13.45]
        })
        
        # 强制使用SINGLE_PARTITION策略
        result = save_data_unified(
            df=cross_date_data,
            data_root=temp_dir,
            symbol="600000.SH",
            period="tick",
            strategy=SaveStrategy.SINGLE_PARTITION
        )
        
        print(f"强制单分区策略结果: {result.strategy_used.value}")
        print(f"保存成功: {result.success}")
        print(f"分区数: {len(result.saved_partitions)}")
        if not result.success:
            print(f"错误信息: {result.error_message}")

        if result.success and result.strategy_used.value == "single" and len(result.saved_partitions) >= 1:
            print("✅ 策略优先级测试通过")
            return True
        else:
            print("❌ 策略优先级测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        return False
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)


def run_all_tests():
    """运行所有测试"""
    print("开始跨日期数据分区修复验证测试...")
    
    tests = [
        test_cross_date_error_detection,
        test_force_single_partition,
        test_multi_partition_strategy,
        test_single_date_data,
        test_strategy_priority
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试执行失败: {e}")
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！跨日期数据分区修复成功！")
        return True
    else:
        print("⚠️ 部分测试失败")
        return False


if __name__ == "__main__":
    run_all_tests()
