# 每周检查一次项目中所有依赖库是否与Python 3.12.9兼容，是否检查依赖库版本更新，请先询问用户
# 确认 requirements.txt 中列出的依赖是否都已是最新的可用版本
# 批量更新requirements.txt中的包：pip-upgrade requirements.txt
# 批量安装requirements.txt中的包：pip install -r requirements.txt
# 激活环境：conda activate quant_py_3.12.9（conda activate 环境名称）




numpy==2.2.5
pandas==2.2.3
matplotlib==3.10.1
seaborn==0.13.2
jupyterlab==4.4.1
scikit-learn==1.6.1
pyarrow==20.0.0
fastparquet==2024.11.0
psutil==7.0.0
pymongo==4.12.1
SQLAlchemy==2.0.40
pytest==8.3.5
pytest==8.3.5
tqdm==4.67.1
joblib==1.4.2
numba==0.61.2
bottleneck==1.4.2
tables==3.10.2
openpyxl==3.1.5
xlrd==2.0.1
statsmodels==0.14.4
requests==2.32.3
packaging==25.0
# ta-lib需要特殊安装，不能通过pip直接安装，请参考下面命令安装说明
# ta-lib==0.6.3
