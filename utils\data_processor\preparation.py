#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据准备功能模块

提供数据集划分、日期处理、重采样等功能
"""

import os
import sys
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Set, Tuple, Union, Callable, Any
from datetime import datetime, timedelta

# 将项目根目录添加到Python路径
root_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, root_path)

# 导入日志模块
from utils.logger import get_unified_logger

# 导入智能时间转换器
from utils.smart_time_converter import smart_to_datetime

# 设置日志记录器
logger = get_unified_logger(__name__)


def ensure_datetime_index(df: pd.DataFrame, date_col: Optional[str] = None,
                         format: Optional[str] = None, inplace: bool = False) -> pd.DataFrame:
    """
    确保DataFrame使用日期时间索引
    
    Args:
        df: 输入DataFrame
        date_col: 包含日期时间的列名，如果为None则尝试使用现有索引
        format: 日期时间格式字符串，如果为None则自动推断
        inplace: 是否就地修改DataFrame，默认为False
        
    Returns:
        使用日期时间索引的DataFrame
    """
    if df.empty:
        logger.warning("输入的DataFrame为空")
        return df
    
    # 创建副本（如果不是就地操作）
    result = df if inplace else df.copy()
    
    # 检查是否已经有日期时间索引
    if pd.api.types.is_datetime64_any_dtype(result.index):
        logger.info("DataFrame已经使用日期时间索引")
        return result
    
    # 如果提供了日期列，使用它作为索引
    if date_col is not None:
        # 检查列是否存在
        if date_col not in result.columns:
            logger.error(f"日期列 '{date_col}' 不存在于DataFrame中")
            return df  # 返回原始DataFrame
        
        # 复制日期列以防后续需要
        original_date = result[date_col].copy()
        
        # 尝试转换为日期时间 - 使用极简方法避免时区问题
        try:
            if format is not None:
                # 使用指定格式 - 避免pd.to_datetime的时区问题
                from utils.time_utils import simple_string_to_datetime_list
                dt_index = simple_string_to_datetime_list(original_date, format)
            else:
                # 自动推断格式 - 仍需要使用pd.to_datetime，但要注意时区
                import warnings
                warnings.warn("自动推断时间格式可能有时区问题，建议指定format参数", UserWarning)
                dt_index = smart_to_datetime(original_date)
            
            # 设置日期时间索引
            result.index = dt_index
            
            # 如果日期列是索引的副本，可以选择删除它
            if date_col in result.columns and original_date.equals(result[date_col]):
                result.drop(columns=[date_col], inplace=True)
                logger.info(f"已将列 '{date_col}' 转换为日期时间索引并删除原列")
            else:
                logger.info(f"已将列 '{date_col}' 转换为日期时间索引")
                
            return result
        except Exception as e:
            logger.error(f"将列 '{date_col}' 转换为日期时间索引时出错: {str(e)}")
            return df  # 返回原始DataFrame
    
    # 尝试将现有索引转换为日期时间
    try:
        # 复制索引以防后续需要
        original_index = result.index.copy()
        
        # 尝试转换为日期时间 - 使用极简方法避免时区问题
        if format is not None:
            # 使用指定格式 - 避免pd.to_datetime的时区问题
            from utils.time_utils import simple_string_to_datetime_list
            dt_index = simple_string_to_datetime_list(original_index, format)
        else:
            # 自动推断格式 - 仍需要使用pd.to_datetime，但要注意时区
            import warnings
            warnings.warn("自动推断时间格式可能有时区问题，建议指定format参数", UserWarning)
            dt_index = smart_to_datetime(original_index)
        
        # 设置日期时间索引
        result.index = dt_index
        logger.info("已将现有索引转换为日期时间索引")
        return result
    except Exception as e:
        logger.error(f"将现有索引转换为日期时间索引时出错: {str(e)}")
        logger.warning("无法创建日期时间索引，请提供有效的日期列")
        return df  # 返回原始DataFrame


def resample_data(df: pd.DataFrame, freq: str, agg_dict: Optional[Dict[str, str]] = None,
                inplace: bool = False) -> pd.DataFrame:
    """
    重采样数据到不同的频率
    
    Args:
        df: 输入DataFrame，必须有日期时间索引
        freq: 重采样频率，如'D'（日）, 'W'（周）, 'M'（月）等
        agg_dict: 聚合方法字典，键为列名，值为聚合函数名
        inplace: 是否就地修改DataFrame，默认为False
        
    Returns:
        重采样后的DataFrame
    """
    if df.empty:
        logger.warning("输入的DataFrame为空")
        return df
    
    # 检查是否有日期时间索引
    if not pd.api.types.is_datetime64_any_dtype(df.index):
        logger.error("DataFrame必须有日期时间索引才能进行重采样")
        return df
    
    # 创建副本（如果不是就地操作）
    result = df if inplace else df.copy()
    
    # 如果没有提供聚合方法字典，为不同类型的列创建默认聚合方法
    if agg_dict is None:
        agg_dict = {}
        
        for col in result.columns:
            if pd.api.types.is_numeric_dtype(result[col]):
                # 对数值列使用这些常见聚合方法
                if 'open' in col.lower():
                    agg_dict[col] = 'first'
                elif 'high' in col.lower():
                    agg_dict[col] = 'max'
                elif 'low' in col.lower():
                    agg_dict[col] = 'min'
                elif 'close' in col.lower():
                    agg_dict[col] = 'last'
                elif 'volume' in col.lower():
                    agg_dict[col] = 'sum'
                else:
                    agg_dict[col] = 'mean'
            else:
                # 对非数值列使用first聚合方法
                agg_dict[col] = 'first'
    
    # 执行重采样
    try:
        resampled = result.resample(freq).agg(agg_dict)
        logger.info(f"已将数据重采样为频率 '{freq}'")
        return resampled
    except Exception as e:
        logger.error(f"重采样时出错: {str(e)}")
        return df  # 返回原始DataFrame


def split_train_test(df: pd.DataFrame, test_size: Union[float, int, timedelta] = 0.2,
                    validation_size: Optional[Union[float, int, timedelta]] = None,
                    shuffle: bool = False, random_state: Optional[int] = None,
                    stratify_col: Optional[str] = None) -> Tuple[pd.DataFrame, ...]:
    """
    将数据集分割为训练集和测试集（可选验证集）
    
    Args:
        df: 输入DataFrame
        test_size: 测试集大小，可以是百分比（0-1）、样本数（整数）或时间段（timedelta）
        validation_size: 验证集大小，可以是百分比、样本数或时间段，默认为None
        shuffle: 是否打乱数据，默认为False
        random_state: 随机种子，当shuffle=True时生效
        stratify_col: 分层抽样的列名，当shuffle=True时生效
        
    Returns:
        如果validation_size为None，返回(train_df, test_df)；
        否则返回(train_df, validation_df, test_df)
    """
    if df.empty:
        logger.warning("输入的DataFrame为空")
        if validation_size is None:
            return df, df  # 返回两个空DataFrame
        else:
            return df, df, df  # 返回三个空DataFrame
    
    # 根据索引类型选择分割方法
    if pd.api.types.is_datetime64_any_dtype(df.index) and isinstance(test_size, timedelta) and not shuffle:
        # 基于时间的分割
        return _split_by_time(df, test_size, validation_size)
    else:
        # 基于样本数量或百分比的分割
        from sklearn.model_selection import train_test_split
        
        # 如果test_size是timedelta但需要shuffle，转换为百分比
        if isinstance(test_size, timedelta):
            test_delta = test_size
            test_size = len(df[df.index >= (df.index.max() - test_delta)]) / len(df)
        
        # 如果validation_size是timedelta，转换为百分比
        if isinstance(validation_size, timedelta):
            val_delta = validation_size
            validation_size = len(df[df.index >= (df.index.max() - val_delta) & 
                                 df.index < (df.index.max() - test_delta)]) / len(df)
        
        # 处理分层抽样
        stratify = None
        if shuffle and stratify_col is not None:
            if stratify_col in df.columns:
                stratify = df[stratify_col]
            else:
                logger.warning(f"分层列 '{stratify_col}' 不存在于DataFrame中，不使用分层抽样")
        
        # 先划分训练集和测试集
        if validation_size is None:
            # 只划分训练集和测试集
            train_df, test_df = train_test_split(
                df, test_size=test_size, random_state=random_state,
                shuffle=shuffle, stratify=stratify
            )
            
            logger.info(f"已将数据集分割为训练集 ({len(train_df)} 样本) 和测试集 ({len(test_df)} 样本)")
            return train_df, test_df
        else:
            # 需要计算验证集在剩余样本中的比例
            if validation_size is not None:
                if isinstance(validation_size, float) and 0 < validation_size < 1:
                    # 调整验证集比例以基于整个数据集
                    validation_size_adjusted = validation_size / (1.0 - test_size)
                    if validation_size_adjusted >= 1.0:
                        logger.warning("验证集大小调整后超过了训练集大小，将使用一半的训练集作为验证集")
                        validation_size_adjusted = 0.5
                else:
                    # 验证集大小是整数，不需要调整
                    validation_size_adjusted = validation_size
            else:
                validation_size_adjusted = None
            
            # 划分训练集和测试集
            train_val_df, test_df = train_test_split(
                df, test_size=test_size, random_state=random_state,
                shuffle=shuffle, stratify=stratify
            )
            
            # 如果使用了分层抽样，需要更新分层列
            if stratify is not None:
                stratify = train_val_df[stratify_col]
            
            # 划分训练集和验证集
            train_df, val_df = train_test_split(
                train_val_df, test_size=validation_size_adjusted, 
                random_state=random_state, shuffle=shuffle, stratify=stratify
            )
            
            logger.info(f"已将数据集分割为训练集 ({len(train_df)} 样本)、"
                      f"验证集 ({len(val_df)} 样本) 和测试集 ({len(test_df)} 样本)")
            return train_df, val_df, test_df


def _split_by_time(df: pd.DataFrame, test_size: timedelta,
                 validation_size: Optional[timedelta] = None) -> Tuple[pd.DataFrame, ...]:
    """
    根据时间分割数据集
    
    Args:
        df: 输入DataFrame，必须有日期时间索引
        test_size: 测试集时间段
        validation_size: 验证集时间段，默认为None
        
    Returns:
        如果validation_size为None，返回(train_df, test_df)；
        否则返回(train_df, validation_df, test_df)
    """
    # 确保索引已排序
    df = df.sort_index()
    
    # 获取最后一个日期
    last_date = df.index.max()
    
    # 计算测试集的起始日期
    test_start = last_date - test_size
    
    # 分割测试集
    test_df = df[df.index >= test_start]
    
    if validation_size is None:
        # 不需要验证集，剩余部分全部作为训练集
        train_df = df[df.index < test_start]
        
        logger.info(f"已将数据集分割为训练集 ({len(train_df)} 样本, "
                  f"{train_df.index.min()} - {train_df.index.max()}) 和测试集 "
                  f"({len(test_df)} 样本, {test_df.index.min()} - {test_df.index.max()})")
        return train_df, test_df
    else:
        # 计算验证集的起始日期
        val_start = test_start - validation_size
        
        # 分割验证集和训练集
        val_df = df[(df.index >= val_start) & (df.index < test_start)]
        train_df = df[df.index < val_start]
        
        logger.info(f"已将数据集分割为训练集 ({len(train_df)} 样本, "
                  f"{train_df.index.min()} - {train_df.index.max()})、验证集 "
                  f"({len(val_df)} 样本, {val_df.index.min()} - {val_df.index.max()}) 和测试集 "
                  f"({len(test_df)} 样本, {test_df.index.min()} - {test_df.index.max()})")
        return train_df, val_df, test_df


def create_time_features(df: pd.DataFrame, inplace: bool = False) -> pd.DataFrame:
    """
    从日期时间索引创建时间特征
    
    Args:
        df: 输入DataFrame，必须有日期时间索引
        inplace: 是否就地修改DataFrame，默认为False
        
    Returns:
        添加时间特征后的DataFrame
    """
    if df.empty:
        logger.warning("输入的DataFrame为空")
        return df
    
    # 检查是否有日期时间索引
    if not pd.api.types.is_datetime64_any_dtype(df.index):
        logger.error("DataFrame必须有日期时间索引才能创建时间特征")
        return df
    
    # 创建副本（如果不是就地操作）
    result = df if inplace else df.copy()
    
    # 提取时间特征
    result['year'] = result.index.year
    result['month'] = result.index.month
    result['day'] = result.index.day
    result['day_of_week'] = result.index.dayofweek  # 0=周一, 6=周日
    result['day_of_year'] = result.index.dayofyear
    result['week_of_year'] = result.index.isocalendar().week
    result['is_month_end'] = result.index.is_month_end.astype(int)
    result['is_month_start'] = result.index.is_month_start.astype(int)
    result['is_quarter_end'] = result.index.is_quarter_end.astype(int)
    result['is_quarter_start'] = result.index.is_quarter_start.astype(int)
    result['is_year_end'] = result.index.is_year_end.astype(int)
    result['is_year_start'] = result.index.is_year_start.astype(int)
    
    # 如果有时间部分，添加时间特征
    if (result.index.hour != 0).any():
        result['hour'] = result.index.hour
        result['minute'] = result.index.minute
        result['is_weekend'] = result.index.dayofweek.isin([5, 6]).astype(int)
        
        # 创建一个"交易时段"特征（假设9:30-11:30, 13:00-15:00是交易时段）
        def is_trading_hours(dt):
            hour, minute = dt.hour, dt.minute
            if hour == 9 and minute >= 30:
                return True
            if hour == 10:
                return True
            if hour == 11 and minute <= 30:
                return True
            if hour == 13 or hour == 14:
                return True
            if hour == 15 and minute == 0:
                return True
            return False
        
        result['is_trading_hours'] = result.index.map(is_trading_hours).astype(int)
    
    # 创建周期性特征（sin/cos变换）
    result['month_sin'] = np.sin(2 * np.pi * result['month'] / 12)
    result['month_cos'] = np.cos(2 * np.pi * result['month'] / 12)
    result['day_of_week_sin'] = np.sin(2 * np.pi * result['day_of_week'] / 7)
    result['day_of_week_cos'] = np.cos(2 * np.pi * result['day_of_week'] / 7)
    result['day_of_year_sin'] = np.sin(2 * np.pi * result['day_of_year'] / 365)
    result['day_of_year_cos'] = np.cos(2 * np.pi * result['day_of_year'] / 365)
    
    if 'hour' in result.columns:
        result['hour_sin'] = np.sin(2 * np.pi * result['hour'] / 24)
        result['hour_cos'] = np.cos(2 * np.pi * result['hour'] / 24)
    
    logger.info("已添加时间特征")
    return result


def prepare_data_for_ml(df: pd.DataFrame, target_col: str,
                       cat_features: Optional[List[str]] = None,
                       num_features: Optional[List[str]] = None,
                       test_size: Union[float, int, timedelta] = 0.2,
                       handle_missing: bool = True,
                       scale_data: bool = True) -> Dict[str, Any]:
    """
    为机器学习准备数据
    
    处理缺失值、编码分类特征、缩放数值特征，并分割数据集
    
    Args:
        df: 输入DataFrame
        target_col: 目标列名
        cat_features: 分类特征列名列表，如果为None则自动检测
        num_features: 数值特征列名列表，如果为None则自动检测
        test_size: 测试集大小
        handle_missing: 是否处理缺失值，默认为True
        scale_data: 是否缩放数值特征，默认为True
        
    Returns:
        包含处理后数据集和变换器的字典
    """
    if df.empty:
        logger.warning("输入的DataFrame为空")
        return {
            "status": "error",
            "message": "输入的DataFrame为空"
        }
    
    # 检查目标列是否存在
    if target_col not in df.columns:
        logger.error(f"目标列 '{target_col}' 不存在于DataFrame中")
        return {
            "status": "error",
            "message": f"目标列 '{target_col}' 不存在"
        }
    
    # 创建结果字典
    result = {
        "status": "success",
        "transformers": {},
        "feature_importance": None
    }
    
    # 创建一个DataFrame的副本
    data = df.copy()
    
    # 分离特征和目标
    X = data.drop(columns=[target_col])
    y = data[target_col]
    
    # 自动检测特征类型
    if cat_features is None:
        cat_features = []
        for col in X.columns:
            # 如果是对象类型、分类类型，或者唯一值较少的数值类型，视为分类特征
            if pd.api.types.is_object_dtype(X[col]) or pd.api.types.is_categorical_dtype(X[col]) or \
               (pd.api.types.is_numeric_dtype(X[col]) and X[col].nunique() < 20):
                cat_features.append(col)
    
    if num_features is None:
        num_features = [col for col in X.columns if col not in cat_features]
    
    # 处理缺失值
    if handle_missing:
        from sklearn.impute import SimpleImputer
        
        # 处理数值特征的缺失值
        if num_features:
            num_imputer = SimpleImputer(strategy='mean')
            X[num_features] = num_imputer.fit_transform(X[num_features])
            result["transformers"]["num_imputer"] = num_imputer
        
        # 处理分类特征的缺失值
        if cat_features:
            cat_imputer = SimpleImputer(strategy='most_frequent')
            X[cat_features] = cat_imputer.fit_transform(X[cat_features])
            result["transformers"]["cat_imputer"] = cat_imputer
    
    # 编码分类特征
    if cat_features:
        from sklearn.preprocessing import OneHotEncoder
        
        # 独热编码分类特征
        encoder = OneHotEncoder(sparse=False, handle_unknown='ignore')
        encoded_cats = encoder.fit_transform(X[cat_features])
        
        # 获取编码后的特征名
        encoded_names = []
        for i, cat in enumerate(cat_features):
            for j, category in enumerate(encoder.categories_[i]):
                encoded_names.append(f"{cat}_{category}")
        
        # 创建包含编码后特征的DataFrame
        encoded_df = pd.DataFrame(encoded_cats, columns=encoded_names, index=X.index)
        
        # 将编码后的分类特征与原始数值特征合并
        X = pd.concat([X[num_features], encoded_df], axis=1)
        
        # 保存编码器
        result["transformers"]["encoder"] = encoder
    
    # 缩放数值特征
    if scale_data and num_features:
        from sklearn.preprocessing import StandardScaler
        
        scaler = StandardScaler()
        if cat_features:
            # 如果已经编码了分类特征，只缩放原始数值特征
            X[num_features] = scaler.fit_transform(X[num_features])
        else:
            # 如果没有分类特征，缩放所有特征
            X = pd.DataFrame(scaler.fit_transform(X), columns=X.columns, index=X.index)
        
        # 保存缩放器
        result["transformers"]["scaler"] = scaler
    
    # 分割数据集
    from sklearn.model_selection import train_test_split

    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=test_size, random_state=42
    )
    
    # 存储处理后的数据集
    result["X_train"] = X_train
    result["X_test"] = X_test
    result["y_train"] = y_train
    result["y_test"] = y_test
    result["feature_names"] = X.columns.tolist()
    
    logger.info(f"已为机器学习准备数据：{len(X_train)} 个训练样本，{len(X_test)} 个测试样本，"
              f"{len(X.columns)} 个特征")
    return result