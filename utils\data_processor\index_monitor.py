#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
索引格式监控模块

提供装饰器和监控工具，用于在数据读取/写入接口自动验证索引格式
确保所有数据操作都遵循YYYYMMDDHHMMSS索引格式标准
"""

import functools
import pandas as pd
from typing import Callable, Any, Optional, Union
from utils.logger import get_unified_logger
from utils.data_processor.index_manager import IndexManager

logger = get_unified_logger(__name__, enhanced=True)


def monitor_index_format(
    validate_input: bool = True,
    validate_output: bool = True,
    auto_fix: bool = True,
    time_column: str = 'time'
):
    """
    索引格式监控装饰器
    
    自动验证函数输入和输出的DataFrame索引格式
    确保所有数据操作都遵循YYYYMMDDHHMMSS标准
    
    Args:
        validate_input: 是否验证输入DataFrame的索引格式
        validate_output: 是否验证输出DataFrame的索引格式
        auto_fix: 当检测到格式错误时是否自动修复
        time_column: 用于修复索引的时间列名
        
    Returns:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            func_name = func.__name__
            
            # 验证输入DataFrame
            if validate_input:
                _validate_input_dataframes(args, kwargs, func_name, auto_fix, time_column)
            
            # 执行原函数
            result = func(*args, **kwargs)
            
            # 验证输出DataFrame
            if validate_output and isinstance(result, pd.DataFrame):
                result = _validate_output_dataframe(result, func_name, auto_fix, time_column)
            elif validate_output and isinstance(result, (list, tuple)):
                # 处理返回多个DataFrame的情况
                validated_results = []
                for item in result:
                    if isinstance(item, pd.DataFrame):
                        item = _validate_output_dataframe(item, func_name, auto_fix, time_column)
                    validated_results.append(item)
                result = type(result)(validated_results)
            
            return result
        return wrapper
    return decorator


def _validate_input_dataframes(
    args: tuple, 
    kwargs: dict, 
    func_name: str, 
    auto_fix: bool, 
    time_column: str
) -> None:
    """验证输入参数中的DataFrame索引格式"""
    # 检查位置参数中的DataFrame
    for i, arg in enumerate(args):
        if isinstance(arg, pd.DataFrame):
            if not IndexManager.validate_index_format(arg):
                logger.warning(f"函数 {func_name} 的第{i+1}个参数索引格式不正确")
                if auto_fix:
                    # 注意：这里不能直接修改args，因为tuple是不可变的
                    logger.info(f"建议在调用 {func_name} 前使用 IndexManager.ensure_proper_index() 修复索引")
                else:
                    logger.error(f"函数 {func_name} 输入数据索引格式错误，请修复后重试")
    
    # 检查关键字参数中的DataFrame
    for key, value in kwargs.items():
        if isinstance(value, pd.DataFrame):
            if not IndexManager.validate_index_format(value):
                logger.warning(f"函数 {func_name} 的参数 {key} 索引格式不正确")
                if auto_fix:
                    # 可以修改kwargs中的值
                    kwargs[key] = IndexManager.ensure_proper_index(value, time_column=time_column)
                    logger.info(f"已自动修复参数 {key} 的索引格式")
                else:
                    logger.error(f"函数 {func_name} 参数 {key} 索引格式错误，请修复后重试")


def _validate_output_dataframe(
    df: pd.DataFrame, 
    func_name: str, 
    auto_fix: bool, 
    time_column: str
) -> pd.DataFrame:
    """验证输出DataFrame的索引格式"""
    if not IndexManager.validate_index_format(df):
        logger.warning(f"函数 {func_name} 的输出索引格式不正确")
        if auto_fix:
            df = IndexManager.ensure_proper_index(df, time_column=time_column)
            logger.info(f"已自动修复函数 {func_name} 输出的索引格式")
        else:
            logger.error(f"函数 {func_name} 输出索引格式错误")
    else:
        logger.debug(f"函数 {func_name} 输出索引格式正确")
    
    return df


class IndexFormatMonitor:
    """索引格式监控器类"""
    
    def __init__(self, auto_fix: bool = True, time_column: str = 'time'):
        """
        初始化监控器
        
        Args:
            auto_fix: 是否自动修复索引格式错误
            time_column: 用于修复索引的时间列名
        """
        self.auto_fix = auto_fix
        self.time_column = time_column
        self.violation_count = 0
        self.fix_count = 0
    
    def check_dataframe(self, df: pd.DataFrame, context: str = "") -> pd.DataFrame:
        """
        检查DataFrame的索引格式
        
        Args:
            df: 要检查的DataFrame
            context: 上下文信息
            
        Returns:
            验证/修复后的DataFrame
        """
        if df is None or df.empty:
            return df
        
        context_str = f"[{context}] " if context else ""
        
        if not IndexManager.validate_index_format(df):
            self.violation_count += 1
            logger.warning(f"{context_str}检测到索引格式违规 (总计: {self.violation_count})")
            
            if self.auto_fix:
                df = IndexManager.ensure_proper_index(df, time_column=self.time_column)
                self.fix_count += 1
                logger.info(f"{context_str}已自动修复索引格式 (总计: {self.fix_count})")
            else:
                logger.error(f"{context_str}索引格式错误，需要手动修复")
        else:
            logger.debug(f"{context_str}索引格式正确")
        
        return df
    
    def get_statistics(self) -> dict:
        """获取监控统计信息"""
        return {
            "violation_count": self.violation_count,
            "fix_count": self.fix_count,
            "auto_fix_enabled": self.auto_fix,
            "time_column": self.time_column
        }
    
    def reset_statistics(self) -> None:
        """重置统计信息"""
        self.violation_count = 0
        self.fix_count = 0


# 创建全局监控器实例
global_index_monitor = IndexFormatMonitor()


def validate_dataframe_index(df: pd.DataFrame, context: str = "") -> pd.DataFrame:
    """
    便捷函数：验证DataFrame索引格式
    
    Args:
        df: 要验证的DataFrame
        context: 上下文信息
        
    Returns:
        验证/修复后的DataFrame
    """
    return global_index_monitor.check_dataframe(df, context)


def get_monitor_statistics() -> dict:
    """获取全局监控器统计信息"""
    return global_index_monitor.get_statistics()


def reset_monitor_statistics() -> None:
    """重置全局监控器统计信息"""
    global_index_monitor.reset_statistics()
