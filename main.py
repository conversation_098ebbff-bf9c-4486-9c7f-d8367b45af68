#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
量化交易系统主入口文件
用于管理和调用各个模块的功能
"""

import argparse
import importlib
import os
import sys
from datetime import datetime
from typing import Any, Optional

# 将项目根目录添加到系统路径
root_path = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, root_path)

# 创建日志目录
log_dir = os.path.join(root_path, "logs")
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

# 首先调用统一日志配置函数，确保在任何日志输出前配置好日志系统
try:
    from utils.logger import setup_unified_logging, clean_old_log_files, LogTarget
    from config.settings import (
        LOG_LEVEL, DEBUG_LOG_ENABLED, DEBUG_LOG_LEVEL,
        DEBUG_LOG_MAX_SIZE, DEBUG_LOG_BACKUP_COUNT,
        DEBUG_LOG_DAYS_TO_KEEP, DEBUG_LOG_FORMAT
    )
    
    setup_unified_logging(
        log_level=LOG_LEVEL,
        default_target=LogTarget.FILE,
        use_table_formatter=True,  # 使用表格式Markdown格式化器
        module_width=30,  # 设置模块名显示宽度为30
        print_init_message=True,  # 只在主程序中打印初始化消息
        # 使用配置文件中的debug日志配置
        enable_debug_log=DEBUG_LOG_ENABLED,
        debug_log_level=DEBUG_LOG_LEVEL,
        debug_log_max_size=DEBUG_LOG_MAX_SIZE,
        debug_log_backup_count=DEBUG_LOG_BACKUP_COUNT,
        debug_log_days_to_keep=DEBUG_LOG_DAYS_TO_KEEP,
        debug_log_format=DEBUG_LOG_FORMAT
    )
    
    # 清理旧的日志文件（保留7天）
    try:
        deleted_count = clean_old_log_files(days_to_keep=7)
        if deleted_count > 0:
            print(f"已清理 {deleted_count} 个旧日志文件")
    except Exception as e:
        print(f"清理旧日志文件时出错: {e}")
except ImportError:
    print("警告: 无法导入统一日志配置模块")

# # 导入项目配置和工具 -- 旧版遗留，SYSTEM_CONFIG不再使用
# from config.settings import SYSTEM_CONFIG  # noqa: E402


def setup_system() -> None:
    """初始化系统环境"""
    # 日志系统已在程序启动时初始化，这里不再重复初始化
    pass


def display_main_menu() -> str:
    """
    显示主菜单并获取用户选择
    
    Returns:
        用户的选择
    """
    print("\n" + "=" * 50)
    print("量化交易系统 - 主菜单")
    print("=" * 50)
    print("1. 数据管理")
    print("2. 策略管理")
    print("3. 回测系统")
    print("4. 实盘交易")
    print("5. 风险控制")
    print("6. 系统设置")
    print("0. 退出系统")
    print("=" * 50)
    
    choice = input("请输入您的选择 [0-6] 默认(1): ").strip() or "1"
    return choice


def load_module(module_path: str) -> Any:
    """
    动态加载模块
    
    Args:
        module_path: 模块路径，例如 'data.data_main'
        
    Returns:
        加载的模块
    """
    try:
        module = importlib.import_module(module_path)
        return module
    except ImportError as e:
        print(f"无法加载模块 {module_path}: {e}")
        return None


def call_module_function(
    module_path: str, 
    function_name: Optional[str] = None, 
    **kwargs
) -> Any:
    """
    调用指定模块的函数
    
    Args:
        module_path: 模块路径
        function_name: 函数名，如果为None则调用模块的主函数
        **kwargs: 传递给函数的参数
        
    Returns:
        函数的返回值
    """
    module = load_module(module_path)
    if not module:
        print(f"模块 {module_path} 加载失败")
        return None
    
    # 如果未指定函数名，则尝试调用main函数或默认处理函数
    if not function_name:
        if hasattr(module, "main"):
            function = getattr(module, "main")
        elif hasattr(module, "run"):
            function = getattr(module, "run")
        elif hasattr(module, "display_menu"):
            function = getattr(module, "display_menu")
        else:
            print(f"模块 {module_path} 没有默认入口函数")
            return None
    else:
        if hasattr(module, function_name):
            function = getattr(module, function_name)
        else:
            print(f"模块 {module_path} 没有函数 {function_name}")
            return None
    
    try:
        return function(**kwargs)
    except Exception as e:
        print(f"调用函数 {function_name or '默认函数'} 失败: {e}")
        return None


def handle_data_management() -> None:
    """处理数据管理功能"""
    call_module_function("data.data_main")


def handle_strategy_management() -> None:
    """处理策略管理功能"""
    # 加载策略管理模块
    print("\n" + "=" * 50)
    print("策略管理")
    print("=" * 50)
    print("1. 策略列表")
    print("2. 创建策略")
    print("3. 编辑策略")
    print("4. 参数优化")
    print("5. 策略组合")
    print("0. 返回主菜单")
    print("=" * 50)
    
    choice = input("请输入您的选择 [0-5] 默认(1): ").strip() or "1"
    
    if choice == "0":
        return
    elif choice == "1":
        print("\n已有策略列表:")
        strategies_dir = os.path.join(root_path, "strategy", "strategies")
        if os.path.exists(strategies_dir):
            strategies = [
                f for f in os.listdir(strategies_dir) 
                if f.endswith('.py') and not f.startswith('__')
            ]
            for i, strategy in enumerate(strategies, 1):
                print(f"{i}. {strategy[:-3]}")
        else:
            print("没有找到策略目录")
    else:
        print("此功能正在开发中...")


def handle_backtest_system() -> None:
    """处理回测系统功能"""
    print("\n" + "=" * 50)
    print("回测系统")
    print("=" * 50)
    print("1. 运行回测")
    print("2. 查看回测结果")
    print("3. 回测报告")
    print("4. 批量回测")
    print("0. 返回主菜单")
    print("=" * 50)
    
    choice = input("请输入您的选择 [0-4] 默认(1): ").strip() or "1"
    
    if choice == "0":
        return
    elif choice == "1":
        strategy_name = input("请输入策略名称(例如: ma_cross): ").strip()
        if not strategy_name:
            print("策略名称不能为空")
            return
            
        symbols = input(
            "请输入交易品种代码(多个代码用逗号分隔，例如: 000001.SZ,600000.SH): "
        ).strip()
        if not symbols:
            print("交易品种不能为空")
            return
            
        start_date = input(
            "请输入回测开始日期(格式: YYYYMMDD，默认为20220101): "
        ).strip() or "20220101"
        end_date = input(
            "请输入回测结束日期(格式: YYYYMMDD，默认为当前日期): "
        ).strip() or datetime.now().strftime("%Y%m%d")
        
        # 构建回测命令
        cmd = [
            sys.executable,
            os.path.join(root_path, "run_backtest.py"),
            f"--strategy={strategy_name}",
            f"--symbols={symbols}",
            f"--start-date={start_date}",
            f"--end-date={end_date}"
        ]
        
        # 是否显示图表
        if input("是否显示图表? (y/n，默认为y): ").strip().lower() != "n":
            cmd.append("--plot")
        
        # 执行回测命令
        print(f"执行回测命令: {' '.join(cmd)}")
        try:
            import subprocess
            subprocess.run(cmd)
        except Exception as e:
            print(f"执行回测失败: {e}")
    else:
        print("此功能正在开发中...")


def handle_trading_system() -> None:
    """处理实盘交易功能"""
    print("\n" + "=" * 50)
    print("实盘交易")
    print("=" * 50)
    print("1. 账户管理")
    print("2. 手动交易")
    print("3. 策略交易")
    print("4. 持仓监控")
    print("5. 订单管理")
    print("0. 返回主菜单")
    print("=" * 50)
    
    choice = input("请输入您的选择 [0-5] 默认(1): ").strip() or "1"
    
    if choice == "0":
        return
    else:
        print("此功能正在开发中...")


def handle_risk_control() -> None:
    """处理风险控制功能"""
    print("\n" + "=" * 50)
    print("风险控制")
    print("=" * 50)
    print("1. 风控参数设置")
    print("2. 风险报告")
    print("3. 预警设置")
    print("0. 返回主菜单")
    print("=" * 50)
    
    choice = input("请输入您的选择 [0-3] 默认(1): ").strip() or "1"
    
    if choice == "0":
        return
    else:
        print("此功能正在开发中...")


def handle_system_settings() -> None:
    """处理系统设置功能"""
    print("\n" + "=" * 50)
    print("系统设置")
    print("=" * 50)
    print("1. 数据路径设置")
    print("2. 日志设置")
    print("3. 性能优化")
    print("4. API设置")
    print("0. 返回主菜单")
    print("=" * 50)
    
    choice = input("请输入您的选择 [0-4] 默认(1): ").strip() or "1"
    
    if choice == "0":
        return
    elif choice == "1":
        current_path = os.environ.get("DATA_ROOT_PATH", "未设置")
        print(f"当前数据路径: {current_path}")
        new_path = input("请输入新的数据路径(留空保持不变): ").strip()
        if new_path:
            os.environ["DATA_ROOT_PATH"] = new_path
            print(f"数据路径已更新为: {new_path}")
    else:
        print("此功能正在开发中...")


def parse_args() -> argparse.Namespace:
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="量化交易系统")
    parser.add_argument("-m", "--module", help="要直接运行的模块名")
    parser.add_argument("-f", "--function", help="要直接运行的函数名")
    return parser.parse_args()


def main() -> None:
    """主函数"""
    # 设置系统环境
    setup_system()
    
    # 获取日志记录器
    from utils.logger import get_unified_logger
    logger = get_unified_logger("main")
    logger.info("启动量化交易系统")
    
    # 解析命令行参数
    args = parse_args()
    
    # 如果指定了直接运行的模块，则直接运行
    if args.module:
        logger.info(f"直接运行模块: {args.module}")
        call_module_function(args.module, args.function)
        return
    
    # 主循环
    while True:
        choice = display_main_menu()
        
        if choice == "0":
            logger.info("退出系统")
            print("感谢使用量化交易系统，再见！")
            break
        elif choice == "1":
            logger.info("进入数据管理")
            handle_data_management()
        elif choice == "2":
            logger.info("进入策略管理")
            handle_strategy_management()
        elif choice == "3":
            logger.info("进入回测系统")
            handle_backtest_system()
        elif choice == "4":
            logger.info("进入实盘交易")
            handle_trading_system()
        elif choice == "5":
            logger.info("进入风险控制")
            handle_risk_control()
        elif choice == "6":
            logger.info("进入系统设置")
            handle_system_settings()
        else:
            print("无效的选择，请重新输入")


# 命令行入口点函数 - 用于pyproject.toml中定义的命令行脚本
def data_cli():
    """数据管理命令行入口点"""
    import sys
    setup_system()
    if len(sys.argv) > 1 and sys.argv[1] == "--help":
        print("用法: quant-data [选项]")
        print("\n选项:")
        print("  --interactive, -i    启动交互式界面")
        print("  --non-interactive    非交互式模式")
        print("  --output-format=FORMAT    输出格式 (text或json)")
        print("  --help               显示此帮助信息")
        sys.exit(0)
    handle_data_management()

def backtest_cli():
    """回测系统命令行入口点"""
    import sys
    setup_system()
    if len(sys.argv) > 1 and sys.argv[1] == "--help":
        print("用法: quant-backtest [选项]")
        print("\n选项:")
        print("  --strategy=STRATEGY  策略名称")
        print("  --symbols=SYMBOLS    交易品种代码，多个用逗号分隔")
        print("  --start-date=DATE    回测开始日期 (YYYYMMDD)")
        print("  --end-date=DATE      回测结束日期 (YYYYMMDD)")
        print("  --plot               显示图表")
        print("  --output=PATH        输出结果到指定路径")
        print("  --non-interactive    非交互式模式")
        print("  --output-format=FORMAT    输出格式 (text或json)")
        print("  --help               显示此帮助信息")
        sys.exit(0)
    handle_backtest_system()

def strategy_cli():
    """策略管理命令行入口点"""
    import sys
    setup_system()
    if len(sys.argv) > 1 and sys.argv[1] == "--help":
        print("用法: quant-strategy [选项]")
        print("\n选项:")
        print("  --list               列出可用策略")
        print("  --create=NAME        创建新策略")
        print("  --edit=NAME          编辑策略")
        print("  --optimize=NAME      优化策略参数")
        print("  --non-interactive    非交互式模式")
        print("  --output-format=FORMAT    输出格式 (text或json)")
        print("  --help               显示此帮助信息")
        sys.exit(0)
    handle_strategy_management()

def trade_cli():
    """交易系统命令行入口点"""
    import sys
    setup_system()
    if len(sys.argv) > 1 and sys.argv[1] == "--help":
        print("用法: quant-trade [选项]")
        print("\n选项:")
        print("  --account=ACCOUNT    账户名称")
        print("  --order=ORDER        执行订单")
        print("  --status             查看交易状态")
        print("  --position           查看持仓")
        print("  --non-interactive    非交互式模式")
        print("  --output-format=FORMAT    输出格式 (text或json)")
        print("  --help               显示此帮助信息")
        sys.exit(0)
    handle_trading_system()

def risk_cli():
    """风险控制命令行入口点"""
    import sys
    setup_system()
    if len(sys.argv) > 1 and sys.argv[1] == "--help":
        print("用法: quant-risk [选项]")
        print("\n选项:")
        print("  --check              检查风险")
        print("  --limits             设置风险限制")
        print("  --report             生成风险报告")
        print("  --non-interactive    非交互式模式")
        print("  --output-format=FORMAT    输出格式 (text或json)")
        print("  --help               显示此帮助信息")
        sys.exit(0)
    handle_risk_control()

def monitor_cli():
    """监控系统命令行入口点"""
    import sys
    setup_system()
    if len(sys.argv) > 1 and sys.argv[1] == "--help":
        print("用法: quant-monitor [选项]")
        print("\n选项:")
        print("  --system             监控系统状态")
        print("  --performance        监控性能")
        print("  --alert              配置告警")
        print("  --non-interactive    非交互式模式")
        print("  --output-format=FORMAT    输出格式 (text或json)")
        print("  --help               显示此帮助信息")
        sys.exit(0)
    print("监控系统功能正在开发中...")

def config_cli():
    """配置管理命令行入口点"""
    import sys
    setup_system()
    if len(sys.argv) > 1 and sys.argv[1] == "--help":
        print("用法: quant-config [选项]")
        print("\n选项:")
        print("  --get=KEY            获取配置项")
        print("  --set=KEY=VALUE      设置配置项")
        print("  --list               列出所有配置")
        print("  --reset              重置配置")
        print("  --non-interactive    非交互式模式")
        print("  --output-format=FORMAT    输出格式 (text或json)")
        print("  --help               显示此帮助信息")
        sys.exit(0)
    handle_system_settings()

def main_cli():
    """主程序命令行入口点"""
    setup_system()
    main()


# 如果直接运行该文件
if __name__ == "__main__":
    main()
