#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据命令兼容接口

提供向后兼容的API接口，将调用转发到新的模块架构
保持原有的函数签名和行为，确保现有代码不受影响
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入新的操作模块
from data.core.operations import (
    download_data as _download_data,
    read_data as _read_data,
    list_data as _list_data,
    summarize_data as _summarize_data,
    synthesize_data as _synthesize_data,
    standardize_stock_codes as _standardize_stock_codes
)

# 导入辅助模块
from utils.data_helpers import DataHelpers
from utils.logger import get_unified_logger, LogTarget
from utils.time_formatter.parsing import parse_multi_format_date
from utils.time_formatter.validation import get_default_period_dates

# 初始化
logger = get_unified_logger(__name__, enhanced=True)
helpers = DataHelpers()


# 导出所有公共函数，保持原有API兼容性
def download_data(*args, **kwargs):
    """下载股票数据 - 兼容接口"""
    return _download_data(*args, **kwargs)


def read_data(*args, **kwargs):
    """读取数据 - 兼容接口"""
    return _read_data(*args, **kwargs)


def list_data(*args, **kwargs):
    """列出本地可用的数据文件 - 兼容接口"""
    return _list_data(*args, **kwargs)


def summarize_data(*args, **kwargs):
    """汇总数据目录中的可用数据 - 兼容接口"""
    return _summarize_data(*args, **kwargs)


def synthesize_data(*args, **kwargs):
    """从本地数据合成新周期数据 - 兼容接口"""
    return _synthesize_data(*args, **kwargs)


def standardize_stock_codes(*args, **kwargs):
    """标准化股票代码 - 兼容接口"""
    return _standardize_stock_codes(*args, **kwargs)


def parse_date(date_str):
    """解析日期字符串，支持多种格式 - 兼容接口"""
    return parse_multi_format_date(date_str)


def get_log_file_path(logger_name):
    """获取日志文件路径 - 兼容接口"""
    return helpers.get_log_file_path(logger_name)


# 保持命令行兼容性
if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="数据命令工具")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")

    # download 命令
    download_parser = subparsers.add_parser("download", help="下载历史行情数据")
    download_parser.add_argument("--stocks", "-s", required=True, help="股票代码，多个用逗号分隔")
    download_parser.add_argument("--period", "-p", default="1d", help="数据周期")
    download_parser.add_argument("--start-date", help="开始日期 (YYYYMMDD)")
    download_parser.add_argument("--end-date", help="结束日期 (YYYYMMDD)")
    download_parser.add_argument("--no-incremental", action="store_false", dest="incremental", help="关闭增量更新")
    download_parser.add_argument("--dividend", "-d", default="front", choices=["front", "back", "none"], help="除权类型")
    download_parser.add_argument("--dir", default=None, help="数据保存目录")
    download_parser.add_argument("--no-show", action="store_false", dest="show", help="不显示数据")

    # read 命令
    read_parser = subparsers.add_parser("read", help="读取本地历史行情数据")
    read_parser.add_argument("--symbols", "-s", required=True, help="股票代码，多个用逗号分隔")
    read_parser.add_argument("--period", "-p", default="1d", help="数据周期")
    read_parser.add_argument("--start-date", help="开始日期 (YYYYMMDD)")
    read_parser.add_argument("--end-date", help="结束日期 (YYYYMMDD)")
    read_parser.add_argument("--columns", "-c", help="需要的列，多个用逗号分隔")
    read_parser.add_argument("--dir", default=None, help="数据目录")

    # list 命令
    list_parser = subparsers.add_parser("list", help="列出本地可用的数据文件")
    list_parser.add_argument("--dir", default=None, help="数据目录")
    list_parser.add_argument("--market", "-m", help="市场代码")
    list_parser.add_argument("--stock", "-s", help="股票代码")
    list_parser.add_argument("--period", "-p", help="数据周期")

    # summary 命令
    summary_parser = subparsers.add_parser("summary", help="汇总数据统计信息")
    summary_parser.add_argument("--dir", default=None, help="数据目录")

    # convert 命令
    convert_parser = subparsers.add_parser("convert", help="合成自定义周期数据")
    convert_parser.add_argument("--symbols", "-s", required=True, help="股票代码，多个用逗号分隔")
    convert_parser.add_argument("--source", default="1m", help="源数据周期，默认为1m")
    convert_parser.add_argument("--target", "-t", required=True, help="目标周期，如3m, 2h等")
    convert_parser.add_argument("--start-date", help="开始日期 (YYYYMMDD)")
    convert_parser.add_argument("--end-date", help="结束日期 (YYYYMMDD)")
    convert_parser.add_argument("--dir", default=None, help="数据目录")
    convert_parser.add_argument("--rows", type=int, default=5, help="显示的行数")

    args = parser.parse_args()

    if args.command == "download":
        stocks = [s.strip() for s in args.stocks.split(",")]
        download_data(
            stocks=stocks,
            period=args.period,
            start_date=args.start_date,
            end_date=args.end_date,
            incremental=args.incremental,
            dividend_type=args.dividend,
            data_dir=args.dir,
            show_data=args.show,
        )
    elif args.command == "read":
        symbols = [s.strip() for s in args.symbols.split(",")]
        columns = None
        if args.columns:
            columns = [c.strip() for c in args.columns.split(",")]
        read_data(
            data_root=args.dir,
            symbols=symbols,
            period=args.period,
            start_time=args.start_date,
            end_time=args.end_date,
            fields=columns
        )
    elif args.command == "list":
        list_data(data_dir=args.dir, market=args.market, stock=args.stock, period=args.period)
    elif args.command == "summary":
        summarize_data(data_dir=args.dir)
    elif args.command == "convert":
        symbols = [s.strip() for s in args.symbols.split(",")]
        synthesize_data(
            symbols=symbols,
            source_period=args.source,
            target_period=args.target,
            start_date=args.start_date,
            end_date=args.end_date,
            data_dir=args.dir,
            display_rows=args.rows
        )
    else:
        parser.print_help()