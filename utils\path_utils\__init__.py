#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
路径工具模块

提供文件和目录操作、路径管理的功能，包括：
1. 文件的读写、复制、移动和删除
2. 目录的创建、删除、复制和比较
3. 路径的标准化、解析和管理
4. 项目结构的获取和操作
"""

import os
import sys

# 将项目根目录添加到Python路径
root_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, root_path)

# 导入各个子模块的功能
from utils.path_utils.file_operations import (
    ensure_dir_exists,
    copy_file,
    move_file,
    delete_file,
    clean_directory,
    read_file,
    write_file,
    read_json,
    write_json,
    read_pickle,
    write_pickle,
    read_csv,
    write_csv
)

from utils.path_utils.directory_operations import (
    create_directory,
    delete_directory,
    copy_directory,
    move_directory,
    compare_directories,
    directory_stats,
    get_latest_modified_files
)

from utils.path_utils.path_management import (
    get_root_dir,
    get_project_dir,
    get_project_structure,
    normalize_path,
    is_path_inside,
    join_paths,
    get_file_extension,
    list_files,
    find_files_by_content,
    get_parent_dirs,
    resolve_relative_path
)

# 导出所有公共接口
__all__ = [
    # 文件操作
    'ensure_dir_exists',
    'copy_file',
    'move_file',
    'delete_file',
    'clean_directory',
    'read_file',
    'write_file',
    'read_json',
    'write_json',
    'read_pickle',
    'write_pickle',
    'read_csv',
    'write_csv',
    
    # 目录操作
    'create_directory',
    'delete_directory',
    'copy_directory',
    'move_directory',
    'compare_directories',
    'directory_stats',
    'get_latest_modified_files',
    
    # 路径管理
    'get_root_dir',
    'get_project_dir',
    'get_project_structure',
    'normalize_path',
    'is_path_inside',
    'join_paths',
    'get_file_extension',
    'list_files',
    'find_files_by_content',
    'get_parent_dirs',
    'resolve_relative_path'
]