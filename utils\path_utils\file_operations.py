#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
文件操作功能模块

提供文件的读取、写入、拷贝、移动等基本操作
"""

import os
import sys
import shutil
import json
import pickle
import csv
from typing import Any, Dict, List, Optional, Union, BinaryIO, TextIO, Tuple
from pathlib import Path
from utils.logger import get_unified_logger, LogTarget

# 将项目根目录添加到Python路径
root_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, root_path)

# 设置日志记录器
logger = get_unified_logger(__name__, enhanced=True)


def ensure_dir_exists(dir_path: Union[str, Path]) -> str:
    """
    确保目录存在，如果不存在则创建

    Args:
        dir_path: 目录路径

    Returns:
        目录的绝对路径
    """
    dir_path = os.path.abspath(os.path.expanduser(str(dir_path)))
    if not os.path.exists(dir_path):
        logger.info(LogTarget.FILE, f"创建目录: {dir_path}")
        os.makedirs(dir_path, exist_ok=True)
    return dir_path


def copy_file(src: Union[str, Path], dst: Union[str, Path], overwrite: bool = False) -> bool:
    """
    复制文件

    Args:
        src: 源文件路径
        dst: 目标文件路径
        overwrite: 是否覆盖已存在的文件，默认为False

    Returns:
        操作是否成功
    """
    src = os.path.abspath(os.path.expanduser(str(src)))
    dst = os.path.abspath(os.path.expanduser(str(dst)))
    
    # 检查源文件是否存在
    if not os.path.isfile(src):
        logger.error(f"源文件不存在: {src}")
        return False
    
    # 检查目标文件是否已存在
    if os.path.exists(dst) and not overwrite:
        logger.warning(f"目标文件已存在，未启用覆盖: {dst}")
        return False
    
    # 确保目标目录存在
    dst_dir = os.path.dirname(dst)
    ensure_dir_exists(dst_dir)
    
    try:
        shutil.copy2(src, dst)
        logger.info(f"已复制文件: {src} -> {dst}")
        return True
    except Exception as e:
        logger.error(f"复制文件失败: {str(e)}")
        return False


def move_file(src: Union[str, Path], dst: Union[str, Path], overwrite: bool = False) -> bool:
    """
    移动文件

    Args:
        src: 源文件路径
        dst: 目标文件路径
        overwrite: 是否覆盖已存在的文件，默认为False

    Returns:
        操作是否成功
    """
    src = os.path.abspath(os.path.expanduser(str(src)))
    dst = os.path.abspath(os.path.expanduser(str(dst)))
    
    # 检查源文件是否存在
    if not os.path.isfile(src):
        logger.error(f"源文件不存在: {src}")
        return False
    
    # 检查目标文件是否已存在
    if os.path.exists(dst) and not overwrite:
        logger.warning(f"目标文件已存在，未启用覆盖: {dst}")
        return False
    
    # 确保目标目录存在
    dst_dir = os.path.dirname(dst)
    ensure_dir_exists(dst_dir)
    
    try:
        shutil.move(src, dst)
        logger.info(f"已移动文件: {src} -> {dst}")
        return True
    except Exception as e:
        logger.error(f"移动文件失败: {str(e)}")
        return False


def delete_file(file_path: Union[str, Path], silent: bool = False) -> bool:
    """
    删除文件

    Args:
        file_path: 文件路径
        silent: 是否静默模式（不记录日志），默认为False

    Returns:
        操作是否成功
    """
    file_path = os.path.abspath(os.path.expanduser(str(file_path)))
    
    # 检查文件是否存在
    if not os.path.isfile(file_path):
        if not silent:
            logger.warning(f"文件不存在，无需删除: {file_path}")
        return True  # 文件不存在视为删除成功
    
    try:
        os.remove(file_path)
        if not silent:
            logger.info(f"已删除文件: {file_path}")
        return True
    except Exception as e:
        if not silent:
            logger.error(f"删除文件失败: {str(e)}")
        return False


def clean_directory(dir_path: Union[str, Path], file_pattern: Optional[str] = None,
                  recursive: bool = False, exclude: Optional[List[str]] = None) -> int:
    """
    清理目录，删除符合条件的文件

    Args:
        dir_path: 目录路径
        file_pattern: 文件名匹配模式（可使用通配符），如果为None则匹配所有文件
        recursive: 是否递归处理子目录，默认为False
        exclude: 要排除的文件或目录列表，默认为None

    Returns:
        删除的文件数量
    """
    import fnmatch
    
    dir_path = os.path.abspath(os.path.expanduser(str(dir_path)))
    exclude = exclude or []
    
    # 检查目录是否存在
    if not os.path.isdir(dir_path):
        logger.error(f"目录不存在: {dir_path}")
        return 0
    
    deleted_count = 0
    
    try:
        if recursive:
            # 递归遍历目录
            for root, dirs, files in os.walk(dir_path, topdown=True):
                # 修改dirs列表以排除指定目录（必须在topdown=True时进行）
                dirs[:] = [d for d in dirs if d not in exclude]
                
                # 处理当前目录中的文件
                for filename in files:
                    if filename in exclude:
                        continue
                    
                    if file_pattern is None or fnmatch.fnmatch(filename, file_pattern):
                        file_path = os.path.join(root, filename)
                        if delete_file(file_path, silent=True):
                            deleted_count += 1
        else:
            # 仅处理当前目录
            for item in os.listdir(dir_path):
                if item in exclude:
                    continue
                
                item_path = os.path.join(dir_path, item)
                if os.path.isfile(item_path):
                    if file_pattern is None or fnmatch.fnmatch(item, file_pattern):
                        if delete_file(item_path, silent=True):
                            deleted_count += 1
        
        logger.info(f"已清理目录 {dir_path}，删除了 {deleted_count} 个文件")
        return deleted_count
    except Exception as e:
        logger.error(f"清理目录失败: {str(e)}")
        return deleted_count


def read_file(file_path: Union[str, Path], encoding: str = 'utf-8') -> Optional[str]:
    """
    读取文本文件内容

    Args:
        file_path: 文件路径
        encoding: 文件编码，默认为utf-8

    Returns:
        文件内容，如果读取失败则返回None
    """
    file_path = os.path.abspath(os.path.expanduser(str(file_path)))
    
    # 检查文件是否存在
    if not os.path.isfile(file_path):
        logger.error(f"文件不存在: {file_path}")
        return None
    
    try:
        with open(file_path, 'r', encoding=encoding) as f:
            content = f.read()
        return content
    except Exception as e:
        logger.error(f"读取文件失败: {file_path}, 错误: {str(e)}")
        return None


def write_file(file_path: Union[str, Path], content: str, encoding: str = 'utf-8',
             mode: str = 'w', make_dirs: bool = True) -> bool:
    """
    写入内容到文本文件

    Args:
        file_path: 文件路径
        content: 要写入的内容
        encoding: 文件编码，默认为utf-8
        mode: 文件打开模式，默认为'w'（覆盖），可选'a'（追加）
        make_dirs: 是否自动创建父目录，默认为True

    Returns:
        操作是否成功
    """
    file_path = os.path.abspath(os.path.expanduser(str(file_path)))
    
    # 确保父目录存在
    if make_dirs:
        dir_path = os.path.dirname(file_path)
        ensure_dir_exists(dir_path)
    
    try:
        with open(file_path, mode, encoding=encoding) as f:
            f.write(content)
        logger.debug(f"已写入文件: {file_path}")
        return True
    except Exception as e:
        logger.error(f"写入文件失败: {file_path}, 错误: {str(e)}")
        return False


def read_json(file_path: Union[str, Path], encoding: str = 'utf-8') -> Optional[Dict]:
    """
    读取JSON文件

    Args:
        file_path: 文件路径
        encoding: 文件编码，默认为utf-8

    Returns:
        JSON数据（字典），如果读取失败则返回None
    """
    file_path = os.path.abspath(os.path.expanduser(str(file_path)))
    
    # 检查文件是否存在
    if not os.path.isfile(file_path):
        logger.error(f"JSON文件不存在: {file_path}")
        return None
    
    try:
        with open(file_path, 'r', encoding=encoding) as f:
            data = json.load(f)
        return data
    except json.JSONDecodeError as e:
        logger.error(f"JSON解析错误: {file_path}, 位置: {e.pos}, 错误: {e.msg}")
        return None
    except Exception as e:
        logger.error(f"读取JSON文件失败: {file_path}, 错误: {str(e)}")
        return None


def write_json(file_path: Union[str, Path], data: Dict, encoding: str = 'utf-8',
              indent: int = 4, ensure_ascii: bool = False, make_dirs: bool = True) -> bool:
    """
    写入数据到JSON文件

    Args:
        file_path: 文件路径
        data: 要写入的数据（字典）
        encoding: 文件编码，默认为utf-8
        indent: 缩进空格数，默认为4
        ensure_ascii: 是否确保ASCII编码，默认为False（允许非ASCII字符）
        make_dirs: 是否自动创建父目录，默认为True

    Returns:
        操作是否成功
    """
    file_path = os.path.abspath(os.path.expanduser(str(file_path)))
    
    # 确保父目录存在
    if make_dirs:
        dir_path = os.path.dirname(file_path)
        ensure_dir_exists(dir_path)
    
    try:
        with open(file_path, 'w', encoding=encoding) as f:
            json.dump(data, f, indent=indent, ensure_ascii=ensure_ascii)
        logger.debug(f"已写入JSON文件: {file_path}")
        return True
    except Exception as e:
        logger.error(f"写入JSON文件失败: {file_path}, 错误: {str(e)}")
        return False


def read_pickle(file_path: Union[str, Path]) -> Any:
    """
    读取Pickle文件

    Args:
        file_path: 文件路径

    Returns:
        Pickle数据，如果读取失败则返回None
    """
    file_path = os.path.abspath(os.path.expanduser(str(file_path)))
    
    # 检查文件是否存在
    if not os.path.isfile(file_path):
        logger.error(f"Pickle文件不存在: {file_path}")
        return None
    
    try:
        with open(file_path, 'rb') as f:
            data = pickle.load(f)
        return data
    except Exception as e:
        logger.error(f"读取Pickle文件失败: {file_path}, 错误: {str(e)}")
        return None


def write_pickle(file_path: Union[str, Path], data: Any, make_dirs: bool = True) -> bool:
    """
    写入数据到Pickle文件

    Args:
        file_path: 文件路径
        data: 要写入的数据
        make_dirs: 是否自动创建父目录，默认为True

    Returns:
        操作是否成功
    """
    file_path = os.path.abspath(os.path.expanduser(str(file_path)))
    
    # 确保父目录存在
    if make_dirs:
        dir_path = os.path.dirname(file_path)
        ensure_dir_exists(dir_path)
    
    try:
        with open(file_path, 'wb') as f:
            pickle.dump(data, f)
        logger.debug(f"已写入Pickle文件: {file_path}")
        return True
    except Exception as e:
        logger.error(f"写入Pickle文件失败: {file_path}, 错误: {str(e)}")
        return False


def read_csv(file_path: Union[str, Path], encoding: str = 'utf-8', delimiter: str = ',',
           header: bool = True) -> Optional[List[List[str]]]:
    """
    读取CSV文件

    Args:
        file_path: 文件路径
        encoding: 文件编码，默认为utf-8
        delimiter: 分隔符，默认为逗号
        header: 是否包含标题行，默认为True

    Returns:
        CSV数据（二维列表），如果读取失败则返回None
    """
    file_path = os.path.abspath(os.path.expanduser(str(file_path)))
    
    # 检查文件是否存在
    if not os.path.isfile(file_path):
        logger.error(f"CSV文件不存在: {file_path}")
        return None
    
    try:
        data = []
        with open(file_path, 'r', encoding=encoding, newline='') as f:
            reader = csv.reader(f, delimiter=delimiter)
            if header:
                headers = next(reader)
                data.append(headers)
            for row in reader:
                data.append(row)
        return data
    except Exception as e:
        logger.error(f"读取CSV文件失败: {file_path}, 错误: {str(e)}")
        return None


def write_csv(file_path: Union[str, Path], data: List[List[str]], encoding: str = 'utf-8',
            delimiter: str = ',', make_dirs: bool = True) -> bool:
    """
    写入数据到CSV文件

    Args:
        file_path: 文件路径
        data: 要写入的数据（二维列表）
        encoding: 文件编码，默认为utf-8
        delimiter: 分隔符，默认为逗号
        make_dirs: 是否自动创建父目录，默认为True

    Returns:
        操作是否成功
    """
    file_path = os.path.abspath(os.path.expanduser(str(file_path)))
    
    # 确保父目录存在
    if make_dirs:
        dir_path = os.path.dirname(file_path)
        ensure_dir_exists(dir_path)
    
    try:
        with open(file_path, 'w', encoding=encoding, newline='') as f:
            writer = csv.writer(f, delimiter=delimiter)
            writer.writerows(data)
        logger.debug(f"已写入CSV文件: {file_path}")
        return True
    except Exception as e:
        logger.error(f"写入CSV文件失败: {file_path}, 错误: {str(e)}")
        return False