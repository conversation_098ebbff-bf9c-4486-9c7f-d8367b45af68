# 时间格式化模块 (Time Formatter)

时间格式化模块提供了完整的时间处理功能，包括时间转换、格式化、交易时间判断等。

## 最新更新 (2025-07-23)

🚀 **日期提取模块批量日志优化完成**：
- 智能批量日志控制：每100个目录输出一次进度日志
- 汇总日志功能：操作结束时输出处理总数、成功率、日期范围等信息
- 性能大幅提升：将2664条单独日志减少到2-10条汇总信息，日志减少99%以上
- 功能完整性保持：所有原有功能不变，只优化日志输出策略
- 新增批量操作接口：`start_batch_operation()` 和 `end_batch_operation()`

## 历史更新 (2025-01-16)

🚀 **统一时间转换架构重构完成**：
- 新增统一时间转换接口模块 (unified_converter.py)
- 解决时间转换函数命名不一致问题
- 建立标准化的错误处理机制
- 性能优化：50000个时间戳转换仅需0.202秒
- 完整的单元测试覆盖
- 向后兼容性支持

## 历史更新 (2025-07-15)

🚀 **向量化时间判断架构重构完成**：
- 性能提升141倍（22秒 → 0.156秒）
- 支持大批量数据处理（百万级别）
- 完全重构的向量化时间判断框架
- 内置性能监控和缓存机制

## 模块结构

```
utils/time_formatter/
├── __init__.py                    # 模块初始化
├── README.md                      # 本文档
├── conversion.py                  # 时间转换功能
├── formatting.py                  # 时间格式化
├── parsing.py                     # 时间解析
├── unified_converter.py          # 统一时间转换接口 🚀 推荐使用
├── trading_time.py               # 交易时间判断 ⭐
├── vectorized_time_judge.py      # 向量化时间判断 🚀
├── date_extraction.py            # 统一日期提取模块 🆕
└── validation.py                 # 时间验证
```

## 主要功能

- 多格式日期时间解析
- 日期格式验证
- 交易日识别和调整
- 默认日期计算
- **交易时间判断和处理**
- **统一时间转换接口（推荐使用）**
- **统一日期提取功能（🆕 v2.0新增）**

## 空日期处理

系统支持使用空字符串表示特殊的日期值：
- 空的开始日期: 表示使用数据源最早的可用数据
- 空的结束日期: 表示使用最新的可用数据（通常是当前日期）

当遇到空日期时，系统会跳过交易日调整，直接将空字符串传递给底层数据源，由数据源决定实际使用的日期范围。

## 使用示例

```python
from utils.time_formatter import (
    parse_multi_format_date,
    validate_date_format,
    is_trading_day,
    adjust_to_trading_day,
    get_start_end_date,
    get_default_period_dates,
    # 新增的日期提取功能
    extract_date_from_path,
    extract_timestamp_from_data,
    sort_files_by_date,
    get_latest_file_by_date,
    get_earliest_file_by_date
)

# 解析多种格式的日期
dt = parse_multi_format_date("2023-01-01")
dt = parse_multi_format_date("20230101")
dt = parse_multi_format_date("2023/01/01")

# 验证日期格式
is_valid = validate_date_format("20230101")

# 判断是否为交易日
is_trade_day = is_trading_day("20230101")

# 调整到最近的交易日
next_trade_day = adjust_to_trading_day("20230101", direction="forward")
prev_trade_day = adjust_to_trading_day("20230101", direction="backward")

# 空日期处理
next_trade_day = adjust_to_trading_day("", direction="forward")  # 返回空字符串，不进行调整

# 获取默认的开始和结束日期
start_date, end_date = get_default_period_dates("1d")

## unified_converter.py（推荐使用）

### 统一时间转换接口

**设计目标：**
- 统一项目中所有时间转换操作
- 解决函数命名不一致问题
- 提供标准化的错误处理机制
- 优化批量转换性能
- 保持向后兼容性

**主要特性：**
- 类型安全的时间转换
- 详细的错误信息和日志记录
- 使用 `datetime.fromtimestamp()` 确保本地时区正确性
- 完整的单元测试覆盖
- 向后兼容的别名函数

**重要修复（2025-01-17）：**
- 修复了批量转换中的8小时偏移问题
- 统一使用 `datetime.fromtimestamp()` 方法处理时区
- 移除了有问题的手动时区调整逻辑
- 修复了时间转换验证函数，使用 `time.mktime()` 确保与 `fromtimestamp()` 的一致性
- 彻底解决了时间转换验证失败的问题
- 确保所有时间转换结果的准确性

### 推荐使用方式

```python
from utils.time_formatter.unified_converter import (
    ms_to_datetime,
    ms_to_datetime_index,
    s_to_datetime,
    s_to_datetime_index,
    datetime_to_ms,
    datetime_to_s,
    TimeConversionError
)

# 单个时间戳转换
timestamp = 1749517140500
dt = ms_to_datetime(timestamp)
print(dt)  # 2025-06-10 08:59:00.500000

# 批量转换（高性能）
timestamps = [1749517140500, 1749517200000, 1749517260000]
dt_index = ms_to_datetime_index(timestamps)  # 向量化转换，性能优异

# 反向转换
ms_timestamp = datetime_to_ms(dt)
s_timestamp = datetime_to_s(dt)

# 异常处理
try:
    result = ms_to_datetime(invalid_timestamp)
except TimeConversionError as e:
    print(f"转换失败: {e}")
```

### 性能表现

- **50000个时间戳转换仅需0.202秒**
- **转换速度: ~247,000个/秒**
- **内存效率优化的向量化操作**
- **支持百万级别数据处理**

### 向后兼容性

为保持向后兼容，仍支持旧的函数名：

```python
# 这些函数仍然可用，但推荐使用新的统一接口
from utils.time_formatter.unified_converter import (
    _convert_ms_timestamp_to_datetime,  # 等同于 ms_to_datetime
    convert_ms_timestamp_to_datetimeindex,  # 等同于 ms_to_datetime_index
    convert_s_timestamp_to_datetimeindex   # 等同于 s_to_datetime_index
)
```

## 故障排除指南

### 常见问题及解决方案

#### 1. NameError: name '_convert_ms_timestamp_to_datetime' is not defined

**问题原因：** 缺少必要的导入语句

**解决方案：**
```python
# 方案1：使用统一转换器（推荐）
from utils.time_formatter.unified_converter import ms_to_datetime

# 方案2：使用向后兼容的别名
from utils.time_formatter.unified_converter import _convert_ms_timestamp_to_datetime
```

#### 2. 时间转换结果偏移8小时

**问题原因：** 使用了smart_to_datetime的UTC时间处理

**解决方案：** 使用统一转换器，自动处理本地时区
```python
# 错误方式
df.index = smart_to_datetime(timestamps, unit='ms')  # UTC时间

# 正确方式
from utils.time_formatter.unified_converter import ms_to_datetime_index
df.index = ms_to_datetime_index(timestamps)  # 本地时区
```

#### 3. TimeConversionError异常

**问题原因：** 输入的时间戳格式不正确或超出范围

**解决方案：**
```python
try:
    result = ms_to_datetime(timestamp)
except TimeConversionError as e:
    print(f"转换失败: {e}")
    # 检查时间戳格式和范围
```

#### 4. 批量转换性能问题

**问题原因：** 使用了循环转换而非向量化操作

**解决方案：**
```python
# 低效方式
results = [ms_to_datetime(ts) for ts in timestamps]

# 高效方式
result = ms_to_datetime_index(timestamps)  # 向量化转换
```

### 最佳实践

1. **优先使用统一转换器**：使用 `unified_converter` 模块的函数
2. **批量转换使用向量化**：使用 `*_to_datetime_index` 函数
3. **异常处理**：捕获 `TimeConversionError` 异常
4. **性能监控**：对大批量数据转换进行性能测试
5. **类型检查**：使用类型提示确保输入正确

### 迁移指南

从旧的时间转换函数迁移到统一转换器：

```python
# 旧方式 → 新方式
_convert_ms_timestamp_to_datetime(ts) → ms_to_datetime(ts)
convert_ms_timestamp_to_datetimeindex(tss) → ms_to_datetime_index(tss)
convert_s_timestamp_to_datetimeindex(tss) → s_to_datetime_index(tss)
```

# 交易时间判断（新增功能）
from utils.time_formatter import (
    is_valid_trading_time,
    get_last_valid_trading_time,
    detect_symbol_type,
    detect_futures_category
)

# 判断是否为有效交易时间
dt = datetime(2023, 1, 1, 10, 30)  # 2023年1月1日 10:30
is_trading = is_valid_trading_time(dt, 'stock')  # A股交易时间判断
is_trading = is_valid_trading_time(dt, 'futures', 'most')  # 期货交易时间判断

# 获取最后一个有效交易时间
last_time = get_last_valid_trading_time(dt, 'stock')

# 自动检测品种类型
symbol_type = detect_symbol_type('000001.SZ')  # 返回 'stock'
symbol_type = detect_symbol_type('rb2501.SF')  # 返回 'futures'
futures_cat = detect_futures_category('au2501.SF')  # 返回 'precious'

# 判断期货品种是否有夜盘交易
has_night = has_night_trading('rb2501.SF')  # 返回 True（螺纹钢有夜盘）
has_night = has_night_trading('wr2501.SF')  # 返回 False（线材无夜盘）
```

## 交易时间规则

### A股交易时间
- 连续竞价：9:30-11:30, 13:00-15:00
- 集合竞价：9:15-9:30（开盘）, 14:57-15:00（收盘）

### 期货交易时间
- 日盘：9:00-11:30（10:15-10:30休息）, 13:30-15:00
- 夜盘（按品种分类）：
  - 大部分品种：21:00-23:00
  - 有色金属：21:00-01:00
  - 贵金属：21:00-02:30
  - 日盘品种：无夜盘交易（线材、锰硅、硅铁、纤维板、胶合板、尿素、小麦、稻类、菜籽、鸡蛋、苹果、红枣等）

### 期货品种分类
- **'most'**: 大部分品种（螺纹钢、橡胶、热轧卷板、沥青等）
- **'metals'**: 有色金属（铜、铝、镍、铅、锌、锡、不锈钢）
- **'precious'**: 贵金属（黄金、白银、原油）
- **'day_only'**: 日盘品种（线材、锰硅、硅铁、纤维板、胶合板、尿素、小麦、稻类、菜籽、鸡蛋、苹果、红枣、花生、生猪）

### 特殊时间处理
- **夜盘品种9:00处理**: 对于有夜盘的期货品种，9:00:00被识别为休盘时间，在数据重采样时会向后合并到9:01
- **日盘品种9:00处理**: 对于只有日盘的期货品种，9:00:00是集合竞价结束时间，数据会被保留

## 🆕 统一日期提取功能 (v2.0)

### 功能特点
- **多格式支持**: 支持YYYYMMDD.parquet、YYYY.parquet、年/月/日目录结构等
- **智能缓存**: 自动缓存提取结果，提升重复查询性能
- **批量处理**: 支持批量文件日期提取，优化大量文件处理
- **统一接口**: 替代项目中所有重复的日期提取逻辑
- **🚀 批量日志优化**: 智能控制日志输出，避免大量重复日志影响性能

### 使用示例

```python
# 从文件路径提取日期
date_str = extract_date_from_path("D:/data/SF/rb00/tick/2025/07/22.parquet")
print(date_str)  # "20250722000000"

# 从DataFrame提取时间戳
import pandas as pd
df = pd.DataFrame({'time': [1737158400000], 'value': [100]})
timestamp = extract_timestamp_from_data(df)
print(timestamp)  # "20250118080000"

# 文件排序和查找
files = [
    "D:/data/SF/rb00/tick/2025/07/20.parquet",
    "D:/data/SF/rb00/tick/2025/07/22.parquet",
    "D:/data/SF/rb00/tick/2025/07/21.parquet"
]

# 按日期排序
sorted_files = sort_files_by_date(files)

# 获取最新/最早文件
latest = get_latest_file_by_date(files)
earliest = get_earliest_file_by_date(files)

# 批量处理（性能优化）
from utils.time_formatter import extract_dates_from_paths_batch
batch_results = extract_dates_from_paths_batch(files)

# 缓存管理
from utils.time_formatter import clear_date_extraction_cache, get_cache_stats
stats = get_cache_stats()
print(f"缓存统计: {stats}")
clear_date_extraction_cache()  # 清理缓存释放内存

# 批量日志控制（🚀 v2.1新增）
from utils.time_formatter.date_extraction import start_batch_operation, end_batch_operation

# 开始批量操作
start_batch_operation("大批量文件扫描")

# 执行大量日期提取操作
for file_path in large_file_list:
    date_str = extract_date_from_path(file_path)
    # 每100个文件会自动输出一次进度日志

# 结束批量操作，输出汇总日志
end_batch_operation("大批量文件扫描")
# 输出示例：大批量文件扫描完成: 共处理 2664 个目录, 成功提取 2664 个日期 (100.0%), 日期范围: 20140709 ~ 20250624
```

## 注意事项

1. **时区处理**: 所有时间转换都基于本地时区，避免了UTC时区的复杂性
2. **性能优化**: 使用向量化操作处理大批量数据，内置智能缓存机制
3. **错误处理**: 提供详细的错误信息和日志记录
4. **向后兼容**: 保持与旧版本的接口兼容性
5. **内存管理**: 大量文件处理后建议清理缓存释放内存

## 更新日志

### v2.1.0 (2025-07-23) 🚀
- 🚀 批量日志优化：智能控制日志输出，避免大量重复日志
- ⚡ 性能大幅提升：将2664条单独日志减少到2-10条汇总信息，日志减少99%以上
- 📊 新增批量操作接口：`start_batch_operation()` 和 `end_batch_operation()`
- 📈 进度日志：每100个目录输出一次进度，大批量操作可视化
- 📋 汇总日志：操作结束时输出处理总数、成功率、日期范围等详细信息

### v2.0.0 (2025-07-22) 🆕
- 🚀 新增统一日期提取模块
- ⚡ 智能缓存机制，提升重复查询性能
- 📁 支持多种文件路径格式的日期提取
- 🔄 统一项目中所有重复的日期处理逻辑
- 📊 批量处理功能，优化大量文件处理性能

### v1.3.0 (2025-07-15)
- 🚀 新增向量化时间判断功能
- ⚡ 性能提升141倍
- 🔧 完善的缓存机制
- 📊 内置性能监控

### v1.2.0 (2025-07-10)
- 新增统一时间转换接口
- 优化毫秒时间戳处理
- 增强错误处理机制

### v1.1.0 (2025-07-05)
- 新增交易时间判断功能
- 支持多品种交易时间规则
- 优化日期解析性能

### v1.0.0 (2025-07-01)
- 初始版本发布
- 基础时间转换功能
- 日期格式验证

### 中金所交易时间
- 连续竞价：9:30-11:30, 13:00-15:00
- 集合竞价：9:25-9:30
```