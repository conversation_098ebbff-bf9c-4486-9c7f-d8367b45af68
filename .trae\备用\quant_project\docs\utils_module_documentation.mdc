---
description: 
globs: "*"
alwaysApply: true
---
# 工具模块(utils)详细文档

## 概述

utils模块提供了一系列通用工具函数，用于支持项目中的各个模块。这些工具包括日志记录、性能指标计算、数据展示、文本格式化、文本解析、路径处理和输入处理等。本文档详细介绍utils模块中的主要组件及其功能。

## 主要组件

utils模块包含以下主要组件：

| 文件名 | 描述 |
|-----|-----|
| `logger.py`           | 日志记录工具                     |
| `metrics.py`          | 金融和回测性能指标计算工具       |
| `data_display.py`     | 数据展示与文本格式化工具         |
| `text_parser.py`      | 文本解析工具                     |
| `time_formatter.py`   | 时间处理和格式化工具             |
| `path_utils.py`       | 路径处理与文件操作工具           |
| `input_handler.py`    | 用户输入处理工具                 |
| `factory_pattern.py`  | 工厂模式实现工具                 |

下面我们将详细介绍utils模块的主要工具。

## 日志记录工具 (logger.py)

`logger.py` 提供统一的日志记录功能，确保项目中所有日志使用一致的格式和处理方式。

### 主要功能

#### 1. 获取日志记录器

```python
def get_logger(name: str, level: str = "info", log_file: Optional[str] = None) -> logging.Logger
```

获取或创建指定名称的日志记录器。

**参数**:
- `name`: 日志记录器名称
- `level`: 日志级别 (debug, info, warning, error, critical)，默认为info
- `log_file`: 日志文件路径，可选。如果提供，日志将同时输出到文件和控制台。

**返回值**:
- `logging.Logger`: 配置好的日志记录器实例。

#### 2. 记录函数执行时间 (装饰器)

```python
def log_execution_time(logger: Optional[logging.Logger] = None, level: str = "info") -> Callable
```

一个装饰器，用于记录被装饰函数的执行时间。

**参数**:
- `logger`: 用于记录时间的日志记录器。如果为None，将使用名为 'performance' 的记录器。
- `level`: 记录执行时间的日志级别，默认为info。

**返回值**:
- `Callable`: 装饰器函数。

**示例**:
```python
from utils.logger import get_logger, log_execution_time
import time

logger = get_logger('my_module')

@log_execution_time(logger=logger, level='debug')
def some_long_task():
    logger.info("任务开始")
    time.sleep(2)
    logger.info("任务结束")

some_long_task() 
# 输出类似: DEBUG:performance:函数 some_long_task 执行时间: 2.00XX 秒
```

## 金融和回测性能指标计算工具 (metrics.py)

`metrics.py` 包含用于计算各种金融和回测性能指标的函数。

### 主要功能

#### 1. 收益率计算

```python
def calculate_returns(prices: Union[pd.Series, np.ndarray], log_returns: bool = False) -> pd.Series
```

计算收益率序列。

**参数**:
- `prices`: 价格序列
- `log_returns`: 是否计算对数收益率，默认为False

**返回值**:
- `pd.Series`: 收益率序列

#### 2. 夏普比率计算

```python
def calculate_sharpe_ratio(returns: pd.Series, risk_free_rate: float = 0.0, periods_per_year: int = 252) -> float
```

计算夏普比率。

**参数**:
- `returns`: 收益率序列
- `risk_free_rate`: 无风险利率，年化，默认为0
- `periods_per_year`: 一年的周期数，默认为252（交易日）

**返回值**:
- `float`: 夏普比率

#### 3. 最大回撤计算

```python
def calculate_max_drawdown(returns: pd.Series) -> Tuple[float, pd.Timestamp, pd.Timestamp]
```

计算最大回撤及其发生的时间段。

**参数**:
- `returns`: 收益率序列

**返回值**:
- `Tuple[float, pd.Timestamp, pd.Timestamp]`: (最大回撤, 峰值时间, 谷值时间)

#### 4. 综合性能指标计算

```python
def calculate_metrics(returns: pd.Series, benchmark_returns: Optional[pd.Series] = None, 
                      risk_free_rate: float = 0.0, periods_per_year: int = 252) -> Dict[str, float]
```

计算回测结果的综合性能指标。

**参数**:
- `returns`: 策略收益率序列
- `benchmark_returns`: 基准收益率序列（可选）
- `risk_free_rate`: 无风险利率，年化，默认为0
- `periods_per_year`: 一年的周期数

**返回值**:
- `Dict[str, float]`: 包含各种性能指标的字典

**示例**:
```python
from utils.metrics import calculate_metrics
import pandas as pd
import numpy as np

# 生成一年的随机收益率
dates = pd.date_range(start='2022-01-01', periods=252, freq='B')
returns = pd.Series(np.random.normal(0.0005, 0.01, len(dates)), index=dates)
benchmark_returns = pd.Series(np.random.normal(0.0003, 0.012, len(dates)), index=dates)

# 计算性能指标
metrics = calculate_metrics(returns, benchmark_returns)
print(metrics)
```

## 数据展示与文本格式化工具 (data_display.py)

`data_display.py`提供了一系列用于数据展示、文本格式化和表格生成的工具函数，特别是针对终端环境下的数据展示。该模块整合了原`formatter.py`的功能。

### 主要功能

#### 1. 文本显示宽度计算

```python
def get_display_width(text: Any) -> int
```

计算字符串在等宽字体终端中的显示宽度。中文字符、全角字符等占用2个单位宽度，英文字母、数字等占用1个单位宽度。

**参数**:
- `text`: 要计算宽度的文本内容(可以是任何类型，会被转换为字符串)

**返回值**:
- `int`: 文本在等宽字体下的显示宽度

**示例**:
```python
from utils.data_display import get_display_width

width1 = get_display_width("Hello")         # 返回5
width2 = get_display_width("你好")          # 返回4 (每个中文字符占2个宽度)
width3 = get_display_width("Hello你好")     # 返回9 (5个英文字符 + 2个中文字符*2)
```

#### 2. 列文本格式化

```python
def format_column(text: Any, width: int, is_last_column: bool = False) -> str
```

根据文本的实际显示宽度，确保列对齐。

**参数**:
- `text`: 要显示的文本
- `width`: 列的目标宽度
- `is_last_column`: 是否为行中的最后一列，影响是否添加尾部空格

**返回值**:
- `str`: 格式化后的文本，确保显示宽度与其他列对齐

#### 3. DataFrame转文本表格

```python
def dataframe_to_text_table(
    df: pd.DataFrame,
    title: Optional[str] = None,
    include_index: bool = True,
    max_rows: Optional[int] = None,
    max_cols: Optional[int] = None,
    float_precision: int = 4,
    highlight_max: Optional[List[str]] = None,
    highlight_min: Optional[List[str]] = None,
    column_width: Optional[Dict[str, int]] = None
) -> str
```

将DataFrame转换为格式化的文本表格，适合在终端中显示。

**参数**:
- `df`: 要转换的DataFrame
- `title`: 表格标题
- `include_index`: 是否包含索引列
- `max_rows`: 最大显示行数
- `max_cols`: 最大显示列数
- `float_precision`: 浮点数精度
- `highlight_max`: 要高亮最大值的列
- `highlight_min`: 要高亮最小值的列
- `column_width`: 列宽度定制

**返回值**:
- `str`: 格式化后的文本表格

**示例**:
```python
from utils.data_display import dataframe_to_text_table
import pandas as pd

# 创建示例DataFrame
df = pd.DataFrame({
    'Code': ['000001.SZ', '600000.SH'],
    'Name': ['平安银行', '浦发银行'],
    'Price': [15.23, 8.45],
    'Change': [0.05, -0.02]
})

# 转换为文本表格
table_text = dataframe_to_text_table(
    df=df,
    title="股票行情",
    include_index=False,
    float_precision=2
)
print(table_text)
```

#### 4. 进度条显示

```python
def print_progress_bar(
    iteration: int,
    total: int,
    prefix: str = '',
    suffix: str = '',
    decimals: int = 1,
    length: int = 50,
    fill: str = '█',
    print_end: str = '\r'
) -> None
```

在终端中显示进度条，用于显示长时间运行任务的进度。

**参数**:
- `iteration`: 当前迭代次数
- `total`: 总迭代次数
- `prefix`: 前缀字符串
- `suffix`: 后缀字符串
- `decimals`: 百分比小数位数
- `length`: 进度条长度
- `fill`: 进度条填充字符
- `print_end`: 打印结束字符

**示例**:
```python
from utils.data_display import print_progress_bar
import time

# 示例循环
total = 100
for i in range(total + 1):
    print_progress_bar(i, total, prefix='处理进度:', suffix='完成', length=50)
    time.sleep(0.1)
```

## 文本解析工具 (text_parser.py)

`text_parser.py`提供了解析特定格式文本输入的工具函数。

### 主要功能

#### 1. 解析合约代码输入

```python
def parse_instrument_input(text_input: str) -> List[str]
```

解析包含合约代码的输入文本，支持带序号、逗号分隔、空格分隔等多种格式。

**参数**:
- `text_input`: 用户输入的包含合约代码的文本。

**返回值**:
- `List[str]`: 提取的合约代码列表，已去重。

**示例**:
```python
from utils.text_parser import parse_instrument_input

text = """
1. 600000.SH
2. 000001.SZ, 300059.SZ
   IF2406 IH2406
"""
instruments = parse_instrument_input(text)
print(instruments) 
# Output: ['600000.SH', '000001.SZ', '300059.SZ', 'IF2406', 'IH2406']
```

#### 2. 解析板块名称输入

```python
def parse_sector_input(text_input: str) -> List[str]
```

解析包含板块名称的输入文本，支持带序号、逗号分隔、空格分隔等多种格式。

**参数**:
- `text_input`: 用户输入的包含板块名称的文本。

**返回值**:
- `List[str]`: 提取的板块名称列表，已去重。

**示例**:
```python
from utils.text_parser import parse_sector_input

text = """
1 上期所
2, 中金所 大商所
   郑商所
"""
sectors = parse_sector_input(text)
print(sectors)
# Output: ['上期所', '中金所', '大商所', '郑商所']
```

## 时间处理工具 (time_formatter.py)

`time_formatter.py` 提供了一系列用于时间戳转换、日期处理和格式化的函数。

### 主要功能

#### 1. 时间戳转换

```python
def timestamp_to_datetime(
    timestamp: Union[int, float], 
    unit: str = 'ms', 
    tz: str = 'Asia/Shanghai'
) -> datetime
```

将时间戳转换为带时区的datetime对象。

**参数**:
- `timestamp`: 时间戳
- `unit`: 时间戳单位，可选值: 's'(秒)、'ms'(毫秒)、'us'(微秒)、'ns'(纳秒)
- `tz`: 时区名称

**返回值**:
- `datetime`: 带时区的datetime对象

```python
def datetime_to_timestamp(dt: datetime, unit: str = 'ms') -> Union[int, float]
```

将datetime对象转换为时间戳。

**参数**:
- `dt`: datetime对象
- `unit`: 时间戳单位，可选值: 's'(秒)、'ms'(毫秒)、'us'(微秒)、'ns'(纳秒)

**返回值**:
- `Union[int, float]`: 转换后的时间戳

**示例**:
```python
from utils.time_formatter import timestamp_to_datetime, datetime_to_timestamp
from datetime import datetime

# 时间戳转datetime
dt = timestamp_to_datetime(1651324800000, unit='ms', tz='Asia/Shanghai')
print(dt)  # 2022-05-01 00:00:00+08:00

# datetime转时间戳
now = datetime.now()
ts = datetime_to_timestamp(now, unit='ms')
print(ts)  # 毫秒级时间戳
```

#### 2. 日期格式化

```python
def format_datetime(dt: datetime, format_str: str = '%Y-%m-%d %H:%M:%S') -> str
```

格式化datetime对象为字符串。

**参数**:
- `dt`: datetime对象
- `format_str`: 格式字符串

**返回值**:
- `str`: 格式化后的日期字符串

```python
def format_time_duration(seconds: float) -> str
```

将秒数格式化为人类可读的时间持续时间。

**参数**:
- `seconds`: 秒数

**返回值**:
- `str`: 格式化后的时间持续时间字符串，如"2小时30分15秒"

## 路径处理工具 (path_utils.py)

`path_utils.py` 提供了一系列用于路径处理、文件操作和标准化的函数，确保项目中所有路径操作的一致性和安全性。

### 主要功能

#### 1. 路径标准化与构建

```python
def normalize_path(path: str) -> str
```

标准化路径格式，确保路径分隔符正确，并移除多余的分隔符。

**参数**:
- `path`: 输入路径

**返回值**:
- `str`: 标准化后的路径

```python
def join_paths(*paths: str) -> str
```

连接多个路径部分，确保结果是标准化的。

**参数**:
- `*paths`: 路径部分列表

**返回值**:
- `str`: 连接后的标准化路径

#### 2. 数据路径管理

```python
def build_data_path(
    exchange: str,
    symbol: str,
    period: str,
    data_root: Optional[str] = None,
    create_dirs: bool = True,
    file_ext: str = "parquet"
) -> str
```

构建标准数据文件路径，遵循项目规定的格式。

**参数**:
- `exchange`: 交易所代码，如SH、SZ
- `symbol`: 股票或期货代码
- `period`: 数据周期，如tick、1m、1d
- `data_root`: 数据根目录，默认从配置中获取
- `create_dirs`: 是否创建目录结构
- `file_ext`: 文件扩展名

**返回值**:
- `str`: 构建的数据文件路径

**示例**:
```python
from utils.path_utils import build_data_path

# 构建数据文件路径
file_path = build_data_path(
    exchange="SZ",
    symbol="000001",
    period="1d",
    data_root="D:\\data"
)
print(file_path)  # 输出: D:\data\SZ\000001\1d.parquet
```

#### 3. 目录和文件操作

```python
def ensure_dir_exists(directory: str) -> str
```

确保目录存在，如果不存在则创建。

**参数**:
- `directory`: 目录路径

**返回值**:
- `str`: 确保存在后的目录路径

```python
def find_data_files(
    data_root: str,
    exchange: Optional[str] = None,
    symbol: Optional[str] = None,
    period: Optional[str] = None,
    file_ext: str = "parquet"
) -> List[str]
```

在数据目录中查找符合条件的数据文件。

**参数**:
- `data_root`: 数据根目录
- `exchange`: 交易所代码过滤
- `symbol`: 股票或期货代码过滤
- `period`: 数据周期过滤
- `file_ext`: 文件扩展名

**返回值**:
- `List[str]`: 符合条件的数据文件路径列表

## 用户输入处理工具 (input_handler.py)

`input_handler.py` 提供了各类用户输入处理函数，包括命令行参数解析、交互式输入等。

### 主要功能

#### 1. 命令行参数解析

```python
def setup_argument_parser(description: str, subparsers_help: Optional[str] = None) -> argparse.ArgumentParser
```

设置标准化的命令行参数解析器。

**参数**:
- `description`: 程序描述
- `subparsers_help`: 子命令帮助信息

**返回值**:
- `argparse.ArgumentParser`: 配置好的参数解析器

#### 2. 交互式输入处理

```python
def get_input_with_default(
    prompt: str,
    default: Optional[str] = None,
    validator: Optional[Callable[[str], bool]] = None,
    error_message: str = "输入无效，请重新输入"
) -> str
```

获取用户输入，支持默认值和输入验证。

**参数**:
- `prompt`: 提示信息
- `default`: 默认值
- `validator`: 验证函数
- `error_message`: 输入无效时的错误信息

**返回值**:
- `str`: 用户输入或默认值

**示例**:
```python
from utils.input_handler import get_input_with_default

# 获取用户输入的日期，默认为当前日期
date_str = get_input_with_default(
    prompt="请输入日期(格式: YYYYMMDD):",
    default="20250101",
    validator=lambda x: re.match(r'^\d{8}$', x) is not None,
    error_message="日期格式不正确，请输入8位数字(YYYYMMDD)"
)
print(f"使用日期: {date_str}")
```

## 工厂模式实现工具 (factory_pattern.py)

`factory_pattern.py` 提供通用的工厂模式实现，支持对象的注册、创建和管理，适用于策略、数据源、算法等多种类型对象的统一创建接口。

### 主要功能

#### 1. 通用工厂类

```python
class Factory:
    """通用工厂类，支持对象的注册、创建和管理"""
    
    @classmethod
    def register(cls, name: str, creator: Callable, *args, **kwargs) -> None:
        """注册一个创建器到工厂"""
        pass
        
    @classmethod
    def create(cls, name: str, *args, **kwargs) -> Any:
        """通过名称创建对象实例"""
        pass
        
    @classmethod
    def get_registered_names(cls) -> List[str]:
        """获取所有已注册的对象名称列表"""
        pass
```

**示例**:
```python
from utils.factory_pattern import Factory

# 定义一些策略类
class MACrossStrategy:
    def __init__(self, fast_period=5, slow_period=20):
        self.fast_period = fast_period
        self.slow_period = slow_period
        
    def process(self, data):
        print(f"MA Cross Strategy processing with {self.fast_period}/{self.slow_period}")

class RsiStrategy:
    def __init__(self, period=14, upper=70, lower=30):
        self.period = period
        self.upper = upper
        self.lower = lower
        
    def process(self, data):
        print(f"RSI Strategy processing with period={self.period}")

# 注册策略到工厂
Factory.register("ma_cross", MACrossStrategy)
Factory.register("rsi", RsiStrategy)

# 根据名称创建策略
strategy1 = Factory.create("ma_cross", fast_period=10, slow_period=30)
strategy2 = Factory.create("rsi", period=21)

# 获取所有已注册的策略名称
strategy_names = Factory.get_registered_names()
print(f"可用策略: {strategy_names}")

# 使用策略
strategy1.process(None)  # MA Cross Strategy processing with 10/30
strategy2.process(None)  # RSI Strategy processing with period=21
```

以上是utils模块的主要组件和功能介绍。这些工具为项目提供了强大而灵活的支持，确保代码的高度复用和一致性。通过合理使用这些工具，可以显著提高开发效率和代码质量。

