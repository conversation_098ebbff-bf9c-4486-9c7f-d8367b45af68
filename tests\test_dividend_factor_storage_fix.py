#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试修复后的复权因子存储管理器
"""

import os
import sys
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from utils.data_processor.adjustment.dividend_factor_storage import DividendFactorStorage
from config.settings import DATA_ROOT

def test_dividend_factor_storage_fix():
    """测试修复后的复权因子存储管理器"""
    
    print("🧪 测试修复后的复权因子存储管理器")
    print(f"📁 数据根目录: {DATA_ROOT}")
    
    # 测试1：初始化存储管理器
    print("\n📊 测试1：初始化存储管理器")
    storage = DividendFactorStorage()
    
    print(f"✅ 存储管理器初始化成功")
    print(f"   数据根目录: {storage.data_root}")
    print(f"   使用统一路径管理器工厂动态获取路径管理器")
    
    # 测试2：获取存储路径
    print("\n📊 测试2：获取存储路径")
    test_symbol = "600000.SH"
    storage_path = storage.get_storage_path(test_symbol)
    
    print(f"✅ 存储路径获取成功")
    print(f"   股票代码: {test_symbol}")
    print(f"   存储路径: {storage_path}")
    
    # 验证路径格式是否正确
    expected_parts = ["raw", "SH", "600000", "dividend_factors.parquet"]
    path_parts = storage_path.parts
    
    if all(part in path_parts for part in expected_parts):
        print("✅ 路径格式正确，符合统一路径管理器规范")
    else:
        print("❌ 路径格式不正确")
        print(f"   期望包含: {expected_parts}")
        print(f"   实际路径: {path_parts}")
        return False
    
    # 测试3：验证路径不再硬编码
    print("\n📊 测试3：验证路径不再硬编码")
    
    # 检查是否还包含旧的硬编码路径
    old_hardcoded_path = "data/dividend_factors"
    if old_hardcoded_path in str(storage_path):
        print(f"❌ 仍然包含硬编码路径: {old_hardcoded_path}")
        return False
    else:
        print("✅ 已移除硬编码路径，使用统一路径管理器")
    
    # 测试4：测试不同股票代码的路径
    print("\n📊 测试4：测试不同股票代码的路径")
    test_symbols = ["000001.SZ", "600000.SH", "300001.SZ"]
    
    for symbol in test_symbols:
        path = storage.get_storage_path(symbol)
        print(f"   {symbol}: {path}")
        
        # 验证路径包含正确的交易所和代码
        code, exchange = symbol.split('.')
        if code in str(path) and exchange in str(path):
            print(f"   ✅ {symbol} 路径格式正确")
        else:
            print(f"   ❌ {symbol} 路径格式错误")
            return False
    
    # 测试5：测试存储信息统计
    print("\n📊 测试5：测试存储信息统计")
    storage_info = storage.get_storage_info()
    
    print(f"✅ 存储信息获取成功")
    print(f"   数据根目录: {storage_info.get('data_root')}")
    print(f"   文件总数: {storage_info.get('total_files', 0)}")
    print(f"   总大小: {storage_info.get('total_size_mb', 0)} MB")
    print(f"   股票列表: {storage_info.get('stock_list', [])[:5]}...")  # 只显示前5个
    
    # 验证存储信息不再使用旧的storage_dir
    if 'storage_dir' in storage_info:
        print("❌ 存储信息仍然包含旧的storage_dir字段")
        return False
    elif 'data_root' in storage_info:
        print("✅ 存储信息使用新的data_root字段")
    
    # 测试6：测试自定义数据根目录
    print("\n📊 测试6：测试自定义数据根目录")
    with tempfile.TemporaryDirectory() as temp_dir:
        custom_storage = DividendFactorStorage(data_root=temp_dir)
        custom_path = custom_storage.get_storage_path("000001.SZ")
        
        if temp_dir in str(custom_path):
            print("✅ 自定义数据根目录生效")
            print(f"   自定义路径: {custom_path}")
        else:
            print("❌ 自定义数据根目录未生效")
            return False
    
    print("\n🎉 所有测试通过！复权因子存储管理器修复成功！")
    print("\n📋 修复要点总结:")
    print("   ✅ 移除硬编码路径 'data/dividend_factors'")
    print("   ✅ 使用统一路径管理器获取标准化路径")
    print("   ✅ 路径格式: {DATA_ROOT}/raw/{exchange}/{code}/dividend_factors.parquet")
    print("   ✅ 遵循DRY原则，避免重复实现")
    print("   ✅ 符合项目常见问题文档的指导原则")
    
    return True

if __name__ == "__main__":
    test_dividend_factor_storage_fix()
