# 全局进程池模块

## 简介

全局进程池模块提供了一个全局共享的进程池实现，用于优化并行处理任务。相比于为每个任务创建新的进程池，全局进程池可以更高效地管理系统资源，避免内存占用持续增长的问题。

## 主要功能

- **全局单例进程池**：确保整个应用程序中只有一个进程池实例
- **资源自动管理**：进程池会在应用程序退出时自动关闭
- **任务提交接口**：提供简单易用的任务提交接口
- **资源监控**：支持获取进程池统计信息
- **内存优化**：有效控制内存使用，避免内存泄漏

## 使用方法

### 基本用法

```python
from utils.multiprocessing.global_process_pool import GlobalProcessPool

# 获取全局进程池实例
pool = GlobalProcessPool.get_instance()

# 定义要执行的函数
def my_task(x):
    return x * x

# 使用map执行任务
results = pool.map(my_task, [1, 2, 3, 4, 5])
print(results)  # [1, 4, 9, 16, 25]

# 使用完毕后关闭进程池（通常不需要手动调用，程序结束时会自动关闭）
# pool.close()
```

### 异步执行

```python
from utils.multiprocessing.global_process_pool import GlobalProcessPool

# 获取全局进程池实例
pool = GlobalProcessPool.get_instance()

# 定义要执行的函数
def my_task(x):
    return x * x

# 定义回调函数
def on_complete(result):
    print(f"任务完成，结果: {result}")

# 异步执行任务
async_result = pool.apply_async(my_task, args=(10,), callback=on_complete)

# 等待结果
result = async_result.get()
print(f"最终结果: {result}")  # 最终结果: 100
```

## 配置参数

在 `config/settings.py` 中可以配置以下参数：

```python
# 是否启用全局进程池
ENABLE_GLOBAL_PROCESS_POOL = True

# 全局进程池最大工作进程数
# None表示使用CPU核心数，建议设置为CPU核心数或更小以避免资源争用
GLOBAL_POOL_MAX_WORKERS = 8

# 全局进程池任务分块大小
# 较大的值可以减少任务调度开销，但可能导致负载不均衡
GLOBAL_POOL_CHUNKSIZE = 10
```

## 与标准进程池的区别

1. **资源管理**：全局进程池在整个应用程序生命周期中只创建一次，避免了重复创建和销毁进程的开销
2. **内存控制**：内存使用有上限，不会随着处理的任务数量无限增长
3. **统一接口**：提供与标准 `multiprocessing.Pool` 兼容的接口，便于迁移现有代码
4. **自动清理**：程序退出时自动清理资源，避免资源泄漏

## 应用场景

全局进程池特别适用于以下场景：

1. **批量数据处理**：需要处理大量数据，如股票历史数据下载和处理
2. **CPU密集型任务**：需要充分利用多核CPU进行计算密集型任务
3. **资源受限环境**：在内存或CPU资源有限的环境中运行的应用
4. **长时间运行的应用**：需要长时间运行且频繁执行并行任务的应用

## 注意事项

1. 全局进程池是单例模式，整个应用中共享一个实例
2. 进程池中的工作进程数应根据系统CPU核心数和内存情况合理设置
3. 对于非常短的任务，进程池的调度开销可能超过并行处理带来的收益
4. 在处理完大批量任务后，可以考虑手动调用 `close()` 方法释放资源 