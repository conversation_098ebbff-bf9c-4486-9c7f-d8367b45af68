#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
均线交叉策略，一个简单的双均线交叉策略示例
当短期均线上穿长期均线时买入，当短期均线下穿长期均线时卖出
"""

import os
import sys

import numpy as np
import pandas as pd

# 添加项目根目录到系统路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from strategy.strategy_template import SimpleStrategyTemplate


class MACrossStrategy(SimpleStrategyTemplate):
    """
    均线交叉策略
    简单的双均线交叉策略，当短期均线上穿长期均线时买入，当短期均线下穿长期均线时卖出
    
    参数:
        short_period: 短期均线周期，默认为 5
        long_period: 长期均线周期，默认为 20
        position_ratio: 仓位比例，默认为 0.3 (30%)
    """
    
    def init(self, **kwargs):
        """
        初始化策略参数
        
        Args:
            **kwargs: 策略参数
        """
        # 设置默认参数
        self.short_period = kwargs.get("short_period", 5)
        self.long_period = kwargs.get("long_period", 20)
        self.position_ratio = kwargs.get("position_ratio", 0.3)
        
        # 记录均线参数
        self.logger.info(f"均线周期设置: 短期={self.short_period}, 长期={self.long_period}")
        self.logger.info(f"仓位比例设置: {self.position_ratio * 100}%")
    
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算均线指标
        
        Args:
            data: 价格数据DataFrame
            
        Returns:
            添加了均线指标的DataFrame
        """
        df = data.copy()
        
        # 计算短期和长期均线
        df[f"ma{self.short_period}"] = df["close"].rolling(window=self.short_period).mean()
        df[f"ma{self.long_period}"] = df["close"].rolling(window=self.long_period).mean()
        
        # 计算均线差值
        df["ma_diff"] = df[f"ma{self.short_period}"] - df[f"ma{self.long_period}"]
        
        # 计算均线差值的变化，用于判断交叉
        df["ma_diff_prev"] = df["ma_diff"].shift(1)
        
        return df
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        生成交易信号
        
        Args:
            data: 带有均线指标的DataFrame
            
        Returns:
            添加了交易信号的DataFrame
        """
        df = data.copy()
        
        # 初始化信号
        df["signal"] = 0
        
        # 计算信号，至少需要有long_period+1个数据点
        if len(df) <= self.long_period + 1:
            return df
        
        # 移除NaN值
        df = df.dropna(subset=[f"ma{self.short_period}", f"ma{self.long_period}", "ma_diff", "ma_diff_prev"])
        
        for i in range(1, len(df)):
            # 金叉：短期均线从下方穿过长期均线
            if df["ma_diff"].iloc[i] > 0 and df["ma_diff_prev"].iloc[i] <= 0:
                df["signal"].iloc[i] = 1
            # 死叉：短期均线从上方穿过长期均线
            elif df["ma_diff"].iloc[i] < 0 and df["ma_diff_prev"].iloc[i] >= 0:
                df["signal"].iloc[i] = -1
        
        return df
    
    def on_bar(self, context) -> dict:
        """
        K线数据事件回调
        
        Args:
            context: 策略上下文
            
        Returns:
            操作指令字典
        """
        # 调用父类方法处理基本逻辑
        return super().on_bar(context)


if __name__ == "__main__":
    # 策略测试代码
    from datetime import datetime, timedelta

    import matplotlib.pyplot as plt

    from data.data_source_manager import download_history_data, get_local_data

    # 测试参数
    test_symbol = "600000.SH"  # 浦发银行
    end_date = datetime.now().strftime("%Y%m%d")
    start_date = (datetime.now() - timedelta(days=365)).strftime("%Y%m%d")
    
    # 下载数据
    print(f"下载测试数据: {test_symbol}, {start_date} 至 {end_date}")
    success = download_history_data(
        stock_list=[test_symbol],
        period="1d",
        start_time=start_date,
        end_time=end_date
    )
    
    if not success:
        print("数据下载失败，退出测试")
        sys.exit(1)
    
    # 获取数据
    data = get_local_data(
        stock_list=[test_symbol],
        period="1d",
        start_time=start_date,
        end_time=end_date
    )
    
    if not data or test_symbol not in data:
        print("未找到测试数据，退出测试")
        sys.exit(1)
    
    # 创建策略实例
    strategy = MACrossStrategy(short_period=5, long_period=20)
    
    # 计算指标和信号
    df = data[test_symbol].copy()
    df = strategy.calculate_indicators(df)
    df = strategy.generate_signals(df)
    
    # 打印信号
    signal_df = df[df["signal"] != 0].copy()
    print("\n策略信号:")
    print(signal_df[["datetime", "close", f"ma5", f"ma20", "signal"]])
    
    # 绘制结果
    plt.figure(figsize=(12, 8))
    
    # 绘制收盘价和均线
    plt.subplot(2, 1, 1)
    plt.plot(df.index, df["close"], label="收盘价")
    plt.plot(df.index, df[f"ma5"], label=f"MA{strategy.short_period}")
    plt.plot(df.index, df[f"ma20"], label=f"MA{strategy.long_period}")
    
    # 绘制买卖点
    buy_points = df[df["signal"] > 0]
    sell_points = df[df["signal"] < 0]
    plt.scatter(buy_points.index, buy_points["close"], color="green", marker="^", s=100, label="买入信号")
    plt.scatter(sell_points.index, sell_points["close"], color="red", marker="v", s=100, label="卖出信号")
    
    plt.title(f"{test_symbol} 均线交叉策略 ({start_date} - {end_date})")
    plt.legend()
    plt.grid(True)
    
    # 绘制均线差
    plt.subplot(2, 1, 2)
    plt.plot(df.index, df["ma_diff"], label="均线差值")
    plt.axhline(y=0, color="red", linestyle="-", alpha=0.3)
    plt.scatter(buy_points.index, buy_points["ma_diff"], color="green", marker="^", s=100)
    plt.scatter(sell_points.index, sell_points["ma_diff"], color="red", marker="v", s=100)
    plt.title("均线差值")
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.show()
    
    # 计算策略收益率
    initial_cash = 100000.0
    current_cash = initial_cash
    position = 0
    trades = []
    
    # 模拟交易
    for i in range(len(df)):
        if df["signal"].iloc[i] > 0 and position == 0:  # 买入信号
            # 计算可买入股数（按100股为1手）
            price = df["close"].iloc[i]
            shares = int((current_cash * 0.95) / (price * 100)) * 100
            if shares > 0:
                cost = shares * price * (1 + 0.0003)  # 考虑手续费
                current_cash -= cost
                position = shares
                trades.append({
                    "date": df.index[i],
                    "action": "买入",
                    "price": price,
                    "shares": shares,
                    "cost": cost,
                    "cash": current_cash
                })
                print(f"买入: {df.index[i]}, 价格: {price:.2f}, 数量: {shares}, 剩余资金: {current_cash:.2f}")
                
        elif df["signal"].iloc[i] < 0 and position > 0:  # 卖出信号
            price = df["close"].iloc[i]
            proceeds = position * price * (1 - 0.0003)  # 考虑手续费
            current_cash += proceeds
            trades.append({
                "date": df.index[i],
                "action": "卖出",
                "price": price,
                "shares": position,
                "proceeds": proceeds,
                "cash": current_cash
            })
            print(f"卖出: {df.index[i]}, 价格: {price:.2f}, 数量: {position}, 剩余资金: {current_cash:.2f}")
            position = 0
    
    # 平仓未平的仓位
    if position > 0:
        price = df["close"].iloc[-1]
        proceeds = position * price * (1 - 0.0003)
        current_cash += proceeds
        trades.append({
            "date": df.index[-1],
            "action": "结束平仓",
            "price": price,
            "shares": position,
            "proceeds": proceeds,
            "cash": current_cash
        })
        position = 0
    
    # 计算收益率
    final_value = current_cash
    total_return = (final_value - initial_cash) / initial_cash * 100
    print(f"\n初始资金: {initial_cash:.2f}")
    print(f"最终资金: {final_value:.2f}")
    print(f"总收益率: {total_return:.2f}%")
    print(f"交易次数: {len(trades)}")
    
    # 打印策略参数
    print(f"\n策略参数:")
    print(f"短期均线周期: {strategy.short_period}")
    print(f"长期均线周期: {strategy.long_period}")
    print(f"仓位比例: {strategy.position_ratio * 100}%") 