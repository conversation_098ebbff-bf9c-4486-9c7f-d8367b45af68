---
description: 
globs: "*"
alwaysApply: true
---
# Windows PowerShell命令执行规则

本文档规定了在Windows环境下使用PowerShell执行命令的规范和最佳实践，确保命令执行的一致性和可靠性。

## ⚠️ 高优先级规则警告

**以下规则是AI助手在处理Windows PowerShell命令时的强制性规则，必须在每次命令生成前检查：**

1. **命令连接符规则**：
   - ❌ **严禁使用** `&&` 或 `||` 连接多个命令
   - ✅ **必须使用** `;` (分号) 连接多个命令
   - 示例：`command1; command2` ✓  而不是 `command1 && command2` ✗

2. **路径格式规则**：
   - ❌ **严禁使用** `/d:/path` 或 `/d/path` 格式的Unix风格路径
   - ✅ **必须使用** `D:\path` 或 `d:\path` 格式的Windows风格路径
   - 示例：`cd D:\quant` ✓  而不是 `cd /d:/quant` ✗

3. **命令工具选择规则**：
   - ❌ **严禁使用** `ls`, `cat`, `grep` 等Unix命令
   - ✅ **必须使用** `Get-ChildItem`, `Get-Content`, `Select-String` 等PowerShell命令
   - 示例：`Get-Content file.txt` ✓  而不是 `cat file.txt` ✗

4. **路径检查规则**：
   - **在执行命令前检查当前工作目录** (使用 `<last_terminal_cwd>` 信息)
   - **避免不必要的目录切换**，如果已经在正确的目录，无需再使用 `cd` 命令

5. **执行命令前的强制检查流程**：
   - 确认当前操作环境是Windows PowerShell
   - 检查生成的命令是否符合上述所有规则
   - 简化命令结构，避免过于复杂的单行命令

**在调用任何 `run_terminal_cmd` 工具前，必须对照上述规则进行严格检查。**

## 1. 基本原则

- 识别当前环境是Windows时，**禁止**使用Unix风格的管道和命令组合
- PowerShell不支持`&&`连接多个命令，应使用`;`（分号）替代
- 避免使用`cat`、`grep`、`ls`等Unix命令，使用PowerShell原生命令
- 对于复杂的shell脚本，推荐创建`.ps1`脚本文件，而不是直接执行长命令

## 2. 命令替代对照表

| Unix命令 | PowerShell替代 | 说明 |
|---|---|---|
| `cat file.txt` | `Get-Content file.txt` | 查看文件内容 |
| `command \| cat` | `command \| Out-String` | 将命令输出作为字符串 |
| `command \| grep pattern` | `command \| Select-String pattern` | 过滤包含模式的行 |
| `ls` | `Get-ChildItem` 或 `dir` | 列出目录内容 |
| `command1 && command2` | `command1; command2` | 顺序执行多个命令 |
| `command \| head` | `command \| Select-Object -First 10` | 显示前10行 |
| `command \| tail` | `command \| Select-Object -Last 10` | 显示后10行 |
| `echo text > file.txt` | `Set-Content -Path file.txt -Value "text"` | 写入文本到文件 |
| `echo text >> file.txt` | `Add-Content -Path file.txt -Value "text"` | 追加文本到文件 |
| `find . -name "*.py"` | `Get-ChildItem -Recurse -Filter "*.py"` | 递归查找文件 |
| `rm -rf directory` | `Remove-Item -Recurse -Force directory` | 递归强制删除目录 |
| `mkdir -p path/to/dir` | `New-Item -ItemType Directory -Path "path/to/dir" -Force` | 创建目录及其父目录 |
| `touch file.txt` | `New-Item -ItemType File -Path "file.txt"` | 创建空文件 |
| `chmod +x file.sh` | 不适用 | Windows权限模型不同 |
| `ps -ef` | `Get-Process` | 列出进程 |
| `kill -9 PID` | `Stop-Process -Id PID -Force` | 强制终止进程 |
| `mv file1 file2` | `Move-Item -Path file1 -Destination file2` | 移动文件或重命名 |
| `cp file1 file2` | `Copy-Item -Path file1 -Destination file2` | 复制文件 |
| `ln -s target link` | `New-Item -ItemType SymbolicLink -Path link -Target target` | 创建符号链接 |
| `which command` | `Get-Command command` | 查找命令位置 |
| `export VAR=value` | `$env:VAR = "value"` | 设置环境变量 |
| `echo $VAR` | `Write-Host $env:VAR` | 显示环境变量 |

## 3. 常见问题解决方案

### 3.1 输出重定向到文件

```powershell
# 正确方式
python script.py > temp.txt
Get-Content temp.txt

# 避免使用
python script.py | cat  # 错误用法
```

### 3.2 使用变量捕获输出

```powershell
# 正确方式
$output = python -c "print('hello')"
Write-Host $output

# 避免使用
output=$(python -c "print('hello')")  # 错误用法
```

### 3.3 创建临时脚本文件

```powershell
# 正确方式
"import sys; print('hello')" | Out-File -Encoding utf8 temp.py
python temp.py

# 避免使用
echo "import sys; print('hello')" > temp.py  # 错误用法
```

### 3.4 分页查看大量输出

```powershell
# 正确方式
command | Out-Host -Paging

# 避免使用
command | more  # 错误用法
command | less  # 错误用法
```

### 3.5 处理路径中的特殊字符

```powershell
# 正确方式 - 使用引号包裹包含空格的路径
Get-Content "C:\Path with spaces\file.txt"

# 正确方式 - 使用反引号转义特殊字符
Get-Content C:\Path`[with`]brackets\file.txt
```

### 3.6 命令嵌套

```powershell
# 正确方式
$date = (Get-Date).ToString("yyyy-MM-dd")
Write-Host "Today is $date"

# 避免使用
date=`date +%Y-%m-%d`  # 错误用法
```

## 4. PowerShell执行策略处理

由于PowerShell默认可能禁止运行脚本，需要了解如何处理执行策略：

```powershell
# 查看当前执行策略
Get-ExecutionPolicy

# 暂时绕过执行策略（单次运行）
PowerShell -ExecutionPolicy Bypass -File script.ps1

# 为当前用户设置执行策略（需要管理员权限）
Set-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy RemoteSigned
```

## 5. 路径处理最佳实践

### 5.1 路径分隔符

```powershell
# 正确方式 - 使用标准Windows路径分隔符
$path = "C:\Users\<USER>\Documents"

# 正确方式 - PowerShell也支持正斜杠
$path = "C:/Users/<USER>/Documents"

# 避免使用反斜杠与其他字符组合可能导致的转义问题
$path = "C:\Users\<USER>\temp"  # 这里\n会被解释为换行符
```

### 5.2 路径连接

```powershell
# 正确方式 - 使用Join-Path命令
$fullPath = Join-Path -Path "C:\Users" -ChildPath "Username"

# 正确方式 - 字符串拼接时注意分隔符
$fullPath = "C:\Users\<USER>\path` 或 `d:\path` (Windows路径格式)
  - 示例：`cd D:\project\scripts` ✓

- [必检] **命令工具检查**：
  - ❌ 是否使用了 `ls`、`cat`、`grep` 等Unix命令？
  - ✓ 替换为对应的PowerShell命令
  - 示例：使用 `Get-ChildItem` 代替 `ls` ✓

- [必检] **当前工作目录检查**：
  - 根据 `<last_terminal_cwd>` 信息，当前目录是否已经是目标目录？
  - 如果是，则无需 `cd` 命令
  - 示例：当前已在 `D:\quant`，直接运行 `python script.py` ✓

- [必检] **命令复杂度检查**：
  - 命令是否过于复杂？考虑拆分为多个简单命令
  - 单行命令应当简洁明了，避免嵌套过多操作

### 6.2 PowerShell命令生成正确示例

**✓ 正确的PowerShell命令示例：**

```powershell
# 单一命令
python script.py

# 多个命令（使用分号连接）
cd D:\projects; python script.py; Write-Host "完成"

# 捕获命令输出
$result = python -c "print('hello')"
Write-Host $result

# 条件执行
if (Test-Path "file.txt") { Get-Content "file.txt" } else { Write-Host "文件不存在" }

# 循环执行
1..5 | ForEach-Object { python script.py -i $_ }
```

**❌ 错误的PowerShell命令示例：**

```powershell
# 错误：使用&&连接命令
cd /d:/projects && python script.py  # ❌ 双重错误：错误的路径格式和&&

# 错误：使用Unix命令
ls -la | grep "py"  # ❌ 应使用: Get-ChildItem | Select-String "py"

# 错误：使用Unix风格变量赋值
result=$(python -c "print('hello')")  # ❌ 应使用: $result = python -c "print('hello')"
```

### 6.3 执行效率最佳实践

- 执行Python命令时，优先使用变量捕获输出
- 长命令输出使用 `Out-File` 保存后查看
- 对需要管道处理的命令，使用PowerShell原生管道方式
- 执行多命令序列使用 `;` 分隔，不要使用 `&&`
- 需要交互式查看大量输出时，使用 `| Out-Host -Paging` 替代 `| more` 或 `| less`
- 在单行命令中使用 `$(...)`进行命令嵌套，替代反引号

### 6.4 命令执行前的最终确认

在执行任何PowerShell命令前，AI助手应执行以下最终确认步骤：

1. 重新检查命令是否符合所有PowerShell语法规则
2. 确认命令中不含任何Unix风格的语法或命令
3. 验证路径格式是否符合Windows标准
4. 确保命令的执行环境与意图相符
5. 检查命令执行的权限要求是否满足 