# 数据处理架构迁移指南

## 🎯 迁移概述

本指南帮助用户从旧的集成架构迁移到新的分离架构。新架构将复权和周期转换功能完全分离，提供更清晰的模块边界和更好的可维护性。

## 📋 迁移检查清单

### ✅ 迁移前准备
- [ ] 备份现有代码和数据
- [ ] 了解新架构的设计理念
- [ ] 确认当前使用的功能模块
- [ ] 制定迁移计划和时间表

### ✅ 迁移过程
- [ ] 更新函数调用接口
- [ ] 修改复权处理逻辑
- [ ] 调整周期转换代码
- [ ] 测试新的处理流程
- [ ] 验证数据处理结果

### ✅ 迁移后验证
- [ ] 功能完整性测试
- [ ] 性能对比验证
- [ ] 错误处理测试
- [ ] 文档更新完成

## 🔄 主要变更说明

### 1. 函数接口变更

#### `synthesize_from_local_data` 函数
**旧接口**:
```python
synthesize_from_local_data(
    symbols=symbols,
    source_period=source_period,
    target_period=target_period,
    start_time=start_time,
    end_time=end_time,
    dividend_type="front"  # 复权参数
)
```

**新接口**:
```python
synthesize_from_local_data(
    symbols=symbols,
    source_period=source_period,
    target_period=target_period,
    start_time=start_time,
    end_time=end_time
    # 移除了 dividend_type 参数
)
```

#### `read_partitioned_data_vectorized` 函数
**旧接口**:
```python
read_partitioned_data_vectorized(
    data_root=data_root,
    symbol=symbol,
    period=period,
    dividend_type="front"  # 复权参数
)
```

**新接口**:
```python
read_partitioned_data_vectorized(
    data_root=data_root,
    symbol=symbol,
    period=period
    # 移除了 dividend_type 参数
)
```

#### `synthesize_data` 函数
**旧接口**:
```python
synthesize_data(
    symbols=symbols,
    source_period=source_period,
    target_period=target_period,
    dividend_type="front"  # 复权参数
)
```

**新接口**:
```python
synthesize_data(
    symbols=symbols,
    source_period=source_period,
    target_period=target_period
    # 移除了 dividend_type 参数
)
```

### 2. 处理流程变更

#### 旧流程（集成方式）
```python
# 旧方式：在周期转换中集成复权
result = synthesize_data(
    symbols=["000001.SZ"],
    source_period="1m",
    target_period="5m",
    dividend_type="front"  # 一步完成复权+周期转换
)
```

#### 新流程（分离方式）
```python
# 新方式1：使用流程编排器（推荐）
from data.processing.data_flow_orchestrator import create_data_flow_orchestrator

orchestrator = create_data_flow_orchestrator()
result = orchestrator.execute_full_pipeline(
    symbols=["000001.SZ"],
    source_period="1m",
    target_period="5m",
    dividend_type="front"
)

# 新方式2：手动分步处理
from data.processing.adjustment_pipeline import create_adjustment_pipeline
from data.processing.period_synthesis_pipeline import create_period_synthesis_pipeline

# 步骤1：复权处理
adj_pipeline = create_adjustment_pipeline()
adj_result = (adj_pipeline
    .load_raw_data(data_root, symbol, "1m")
    .apply_adjustment("front")
    .save_adjusted_data()
    .execute())

# 步骤2：周期转换（使用复权后的数据）
syn_pipeline = create_period_synthesis_pipeline()
syn_result = (syn_pipeline
    .load_source_data(data_root, symbol, "1m")  # 加载复权数据
    .synthesize_period("5m")
    .save_synthesized_data()
    .execute())
```

## 📝 具体迁移步骤

### 步骤1：更新导入语句
```python
# 新增导入
from data.processing.data_flow_orchestrator import create_data_flow_orchestrator
from data.processing.adjustment_pipeline import create_adjustment_pipeline
from data.processing.period_synthesis_pipeline import create_period_synthesis_pipeline
```

### 步骤2：替换旧的集成调用
**迁移前**:
```python
# 旧代码
result = synthesize_data(
    symbols=stock_list,
    source_period="1m",
    target_period="5m",
    dividend_type="front"
)
```

**迁移后**:
```python
# 新代码（推荐使用编排器）
orchestrator = create_data_flow_orchestrator()
result = orchestrator.execute_full_pipeline(
    symbols=stock_list,
    source_period="1m",
    target_period="5m",
    dividend_type="front"
)
```

### 步骤3：更新批量处理脚本
**迁移前**:
```python
# 旧的批量合成脚本
from data.core.operations import synthesize_data

for config in synthesis_configs:
    result = synthesize_data(
        symbols=stock_list,
        source_period=config["source_period"],
        target_period=config["target_period"],
        dividend_type=config["dividend_type"]
    )
```

**迁移后**:
```python
# 新的批量合成脚本
from data.processing.data_flow_orchestrator import create_data_flow_orchestrator

orchestrator = create_data_flow_orchestrator()
for config in synthesis_configs:
    result = orchestrator.execute_full_pipeline(
        symbols=stock_list,
        source_period=config["source_period"],
        target_period=config["target_period"],
        dividend_type=config["dividend_type"]
    )
```

### 步骤4：更新数据读取代码
**迁移前**:
```python
# 旧代码：读取复权数据
df = read_partitioned_data_vectorized(
    data_root=data_root,
    symbol=symbol,
    period=period,
    dividend_type="front"
)
```

**迁移后**:
```python
# 新代码：分别处理复权和读取
# 方式1：使用复权管道
adj_pipeline = create_adjustment_pipeline()
adj_result = (adj_pipeline
    .load_raw_data(data_root, symbol, period)
    .apply_adjustment("front")
    .execute())

# 方式2：直接读取已保存的复权数据
df = read_partitioned_data_vectorized(
    data_root=data_root,
    symbol=symbol,
    period=period
)
```

## ⚠️ 注意事项

### 1. 数据兼容性
- 新架构只读取原始数据，不自动应用复权
- 如需复权数据，必须使用复权管道处理
- 已保存的复权数据仍可正常读取

### 2. 性能影响
- 分离架构可能增加处理步骤
- 但提供了更好的缓存和优化机会
- 总体性能可能有所提升

### 3. 错误处理
- 新架构提供更详细的错误信息
- 错误定位更加精确
- 支持分步骤的错误恢复

### 4. 向后兼容
- 旧的函数接口仍然存在，但移除了复权功能
- 建议尽快迁移到新架构
- 提供了完整的迁移工具和指南

## 🧪 测试验证

### 1. 功能测试
```python
# 测试复权功能
adj_pipeline = create_adjustment_pipeline()
adj_result = (adj_pipeline
    .load_raw_data(data_root, "000001.SZ", "1d")
    .apply_adjustment("front")
    .validate_adjustment()
    .execute())

# 测试周期转换功能
syn_pipeline = create_period_synthesis_pipeline()
syn_result = (syn_pipeline
    .load_source_data(data_root, "000001.SZ", "1m")
    .synthesize_period("5m")
    .validate_synthesis()
    .execute())

# 测试完整流程
orchestrator = create_data_flow_orchestrator()
full_result = orchestrator.execute_full_pipeline(
    symbols=["000001.SZ"],
    source_period="1m",
    target_period="5m",
    dividend_type="front",
    validate_results=True
)
```

### 2. 性能测试
```python
import time

# 测试处理时间
start_time = time.time()
result = orchestrator.execute_full_pipeline(
    symbols=stock_list,
    source_period="1m",
    target_period="5m",
    dividend_type="front"
)
end_time = time.time()

print(f"处理耗时: {end_time - start_time:.2f}秒")
print(f"处理统计: {orchestrator.get_flow_statistics()}")
```

## 🎉 迁移完成

完成迁移后，您将获得：

1. **更清晰的代码结构**: 复权和周期转换功能完全分离
2. **更好的可维护性**: 模块职责单一，易于理解和修改
3. **更强的扩展性**: 基于管道的架构易于扩展新功能
4. **更好的错误处理**: 分步骤的错误定位和恢复
5. **更灵活的组合**: 支持多种处理模式和流程编排

如有任何迁移问题，请参考新架构文档或联系技术支持。
