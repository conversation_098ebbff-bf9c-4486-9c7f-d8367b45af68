#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据处理流程编排器

负责编排复权和周期转换的数据处理流程，实现模块职责分离。
基于独立的复权管道和周期转换管道，提供统一的流程编排接口。

核心特性：
- 流程编排：先复权处理，再周期转换
- 模块分离：复权和周期转换完全独立
- 数据传递：管道间的数据流转
- 错误处理：完整的错误处理和恢复机制
- 进度跟踪：全流程的进度监控

使用示例：
```python
from data.processing.data_flow_orchestrator import DataFlowOrchestrator

# 创建编排器
orchestrator = DataFlowOrchestrator()

# 执行完整流程：复权 + 周期转换
result = orchestrator.execute_full_pipeline(
    symbols=["000001.SZ"],
    source_period="1m",
    target_period="5m",
    dividend_type="front",
    data_root=DATA_ROOT
)
```

作者: AI Assistant
创建时间: 2025-08-05
版本: 1.0.0
"""

import time
from typing import Dict, List, Optional, Any, Union
import pandas as pd
from dataclasses import dataclass
from enum import Enum

from data.processing.adjustment_pipeline import AdjustmentDataPipeline, create_adjustment_pipeline
from data.processing.period_synthesis_pipeline import PeriodSynthesisDataPipeline, create_period_synthesis_pipeline
from utils.logger import get_unified_logger, LogTarget
from config.settings import DATA_ROOT

logger = get_unified_logger(__name__)


class FlowStage(Enum):
    """流程阶段枚举"""
    INITIALIZATION = "initialization"
    ADJUSTMENT = "adjustment"
    PERIOD_SYNTHESIS = "period_synthesis"
    VALIDATION = "validation"
    COMPLETION = "completion"


@dataclass
class FlowResult:
    """流程执行结果"""
    success: bool
    stage: FlowStage
    data: Optional[Union[pd.DataFrame, Dict[str, pd.DataFrame]]]
    statistics: Dict[str, Any]
    error_message: Optional[str] = None


class DataFlowOrchestrator:
    """
    数据处理流程编排器
    
    负责编排复权和周期转换的完整数据处理流程
    """
    
    def __init__(self, enable_parallel: bool = True):
        """
        初始化数据处理流程编排器
        
        Args:
            enable_parallel: 是否启用并行处理
        """
        self.enable_parallel = enable_parallel
        self.flow_statistics = {
            'total_flows': 0,
            'successful_flows': 0,
            'failed_flows': 0,
            'total_flow_time': 0.0,
            'stage_times': {}
        }
        logger.info("数据处理流程编排器初始化完成")
    
    def execute_full_pipeline(self, symbols: Union[str, List[str]], 
                             source_period: str, target_period: str,
                             dividend_type: str = "front",
                             data_root: Optional[str] = None,
                             start_time: Optional[str] = None,
                             end_time: Optional[str] = None,
                             show_data: bool = True,
                             validate_results: bool = True) -> FlowResult:
        """
        执行完整的数据处理流程：复权 + 周期转换
        
        Args:
            symbols: 股票代码或股票代码列表
            source_period: 源数据周期
            target_period: 目标周期
            dividend_type: 复权类型，"front"（前复权）、"back"（后复权）、"none"（原始数据）
            data_root: 数据根目录
            start_time: 开始时间
            end_time: 结束时间
            show_data: 是否显示数据预览
            validate_results: 是否验证结果
            
        Returns:
            FlowResult: 流程执行结果
        """
        flow_start_time = time.time()
        self.flow_statistics['total_flows'] += 1
        
        # 标准化输入参数
        if isinstance(symbols, str):
            symbols = [symbols]
        
        data_root = data_root or DATA_ROOT
        
        logger.info(f"开始执行完整数据处理流程: {len(symbols)} 只股票 {source_period} -> {target_period} {dividend_type}")
        
        try:
            # 阶段1: 初始化
            stage_start_time = time.time()
            logger.debug("流程阶段1: 初始化")
            
            # 记录阶段时间
            self._record_stage_time(FlowStage.INITIALIZATION, time.time() - stage_start_time)
            
            # 阶段2: 复权处理（如果需要）
            if dividend_type != "none":
                adjustment_result = self._execute_adjustment_stage(
                    symbols, source_period, dividend_type, data_root, start_time, end_time
                )
                
                if not adjustment_result.success:
                    return adjustment_result
                
                # 使用复权后的数据作为周期转换的输入
                adjusted_data = adjustment_result.data
            else:
                # 如果不需要复权，直接使用原始数据
                logger.info("跳过复权处理，直接使用原始数据")
                adjusted_data = None  # 将在周期转换阶段直接加载原始数据
            
            # 阶段3: 周期转换处理
            synthesis_result = self._execute_synthesis_stage(
                symbols, source_period, target_period, data_root, 
                start_time, end_time, adjusted_data
            )
            
            if not synthesis_result.success:
                return synthesis_result
            
            # 阶段4: 验证（如果需要）
            if validate_results:
                validation_result = self._execute_validation_stage(synthesis_result.data)
                if not validation_result.success:
                    logger.warning("数据验证失败，但继续完成流程")
            
            # 阶段5: 完成
            flow_end_time = time.time()
            total_flow_time = flow_end_time - flow_start_time
            
            self.flow_statistics['successful_flows'] += 1
            self.flow_statistics['total_flow_time'] += total_flow_time
            
            # 显示数据预览（如果需要）
            if show_data and synthesis_result.data is not None:
                self._display_results(synthesis_result.data, symbols, source_period, target_period, dividend_type)
            
            logger.info(f"完整数据处理流程执行成功，耗时: {total_flow_time:.2f}秒")
            
            return FlowResult(
                success=True,
                stage=FlowStage.COMPLETION,
                data=synthesis_result.data,
                statistics={
                    'total_symbols': len(symbols),
                    'source_period': source_period,
                    'target_period': target_period,
                    'dividend_type': dividend_type,
                    'total_time': total_flow_time,
                    'stage_times': self.flow_statistics['stage_times']
                }
            )
            
        except Exception as e:
            self.flow_statistics['failed_flows'] += 1
            logger.error(f"完整数据处理流程执行失败: {e}")
            
            return FlowResult(
                success=False,
                stage=FlowStage.COMPLETION,
                data=None,
                statistics=self.flow_statistics,
                error_message=str(e)
            )
    
    def execute_adjustment_only(self, symbols: Union[str, List[str]], 
                               period: str, dividend_type: str = "front",
                               data_root: Optional[str] = None,
                               start_time: Optional[str] = None,
                               end_time: Optional[str] = None) -> FlowResult:
        """
        仅执行复权处理流程
        
        Args:
            symbols: 股票代码或股票代码列表
            period: 数据周期
            dividend_type: 复权类型
            data_root: 数据根目录
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            FlowResult: 流程执行结果
        """
        if isinstance(symbols, str):
            symbols = [symbols]
        
        data_root = data_root or DATA_ROOT
        
        logger.info(f"开始执行复权处理流程: {len(symbols)} 只股票 {period} {dividend_type}")
        
        return self._execute_adjustment_stage(
            symbols, period, dividend_type, data_root, start_time, end_time
        )
    
    def execute_synthesis_only(self, symbols: Union[str, List[str]], 
                              source_period: str, target_period: str,
                              data_root: Optional[str] = None,
                              start_time: Optional[str] = None,
                              end_time: Optional[str] = None) -> FlowResult:
        """
        仅执行周期转换流程
        
        Args:
            symbols: 股票代码或股票代码列表
            source_period: 源数据周期
            target_period: 目标周期
            data_root: 数据根目录
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            FlowResult: 流程执行结果
        """
        if isinstance(symbols, str):
            symbols = [symbols]
        
        data_root = data_root or DATA_ROOT
        
        logger.info(f"开始执行周期转换流程: {len(symbols)} 只股票 {source_period} -> {target_period}")
        
        return self._execute_synthesis_stage(
            symbols, source_period, target_period, data_root, start_time, end_time, None
        )

    # ==================== 内部实现方法 ====================

    def _execute_adjustment_stage(self, symbols: List[str], period: str, dividend_type: str,
                                 data_root: str, start_time: Optional[str],
                                 end_time: Optional[str]) -> FlowResult:
        """执行复权处理阶段"""
        stage_start_time = time.time()
        logger.debug(f"流程阶段2: 复权处理 {dividend_type}")

        try:
            # 创建复权处理管道
            adjustment_pipeline = create_adjustment_pipeline(self.enable_parallel)

            if len(symbols) == 1:
                # 单只股票处理
                symbol = symbols[0]
                result = (adjustment_pipeline
                         .load_raw_data(data_root, symbol, period, start_time, end_time)
                         .apply_adjustment(dividend_type)
                         .save_adjusted_data()
                         .execute())
            else:
                # 批量处理
                result = (adjustment_pipeline
                         .load_batch_raw_data(data_root, symbols, period, start_time, end_time)
                         .apply_batch_adjustment(dividend_type)
                         .save_batch_adjusted_data()
                         .execute())

            stage_time = time.time() - stage_start_time
            self._record_stage_time(FlowStage.ADJUSTMENT, stage_time)

            logger.info(f"复权处理阶段完成，耗时: {stage_time:.2f}秒")

            return FlowResult(
                success=True,
                stage=FlowStage.ADJUSTMENT,
                data=result,
                statistics={'stage_time': stage_time}
            )

        except Exception as e:
            logger.error(f"复权处理阶段失败: {e}")
            return FlowResult(
                success=False,
                stage=FlowStage.ADJUSTMENT,
                data=None,
                statistics={},
                error_message=str(e)
            )

    def _execute_synthesis_stage(self, symbols: List[str], source_period: str, target_period: str,
                                data_root: str, start_time: Optional[str], end_time: Optional[str],
                                adjusted_data: Optional[Union[pd.DataFrame, Dict[str, pd.DataFrame]]]) -> FlowResult:
        """执行周期转换阶段"""
        stage_start_time = time.time()
        logger.debug(f"流程阶段3: 周期转换 {source_period} -> {target_period}")

        try:
            # 创建周期转换处理管道
            synthesis_pipeline = create_period_synthesis_pipeline(self.enable_parallel)

            if len(symbols) == 1:
                # 单只股票处理
                symbol = symbols[0]

                if adjusted_data is not None and isinstance(adjusted_data, pd.DataFrame):
                    # 使用已复权的数据（需要临时保存后再加载）
                    # 这里简化处理，直接使用管道的数据传递功能
                    synthesis_pipeline.data = adjusted_data
                    result = (synthesis_pipeline
                             .synthesize_period(target_period)
                             .save_synthesized_data()
                             .execute())
                else:
                    # 直接从存储加载原始数据
                    result = (synthesis_pipeline
                             .load_source_data(data_root, symbol, source_period, start_time, end_time)
                             .synthesize_period(target_period)
                             .save_synthesized_data()
                             .execute())
            else:
                # 批量处理
                if adjusted_data is not None and isinstance(adjusted_data, dict):
                    # 使用已复权的批量数据
                    synthesis_pipeline.data = adjusted_data
                    result = (synthesis_pipeline
                             .synthesize_batch_period(target_period)
                             .save_batch_synthesized_data()
                             .execute())
                else:
                    # 直接从存储加载原始数据
                    result = (synthesis_pipeline
                             .load_batch_source_data(data_root, symbols, source_period, start_time, end_time)
                             .synthesize_batch_period(target_period)
                             .save_batch_synthesized_data()
                             .execute())

            stage_time = time.time() - stage_start_time
            self._record_stage_time(FlowStage.PERIOD_SYNTHESIS, stage_time)

            logger.info(f"周期转换阶段完成，耗时: {stage_time:.2f}秒")

            return FlowResult(
                success=True,
                stage=FlowStage.PERIOD_SYNTHESIS,
                data=result,
                statistics={'stage_time': stage_time}
            )

        except Exception as e:
            logger.error(f"周期转换阶段失败: {e}")
            return FlowResult(
                success=False,
                stage=FlowStage.PERIOD_SYNTHESIS,
                data=None,
                statistics={},
                error_message=str(e)
            )

    def _execute_validation_stage(self, data: Union[pd.DataFrame, Dict[str, pd.DataFrame]]) -> FlowResult:
        """执行验证阶段"""
        stage_start_time = time.time()
        logger.debug("流程阶段4: 数据验证")

        try:
            # 这里可以添加更复杂的验证逻辑
            validation_passed = True
            validation_issues = []

            if isinstance(data, pd.DataFrame):
                if data.empty:
                    validation_passed = False
                    validation_issues.append("数据为空")
            elif isinstance(data, dict):
                empty_symbols = [symbol for symbol, df in data.items() if df.empty]
                if empty_symbols:
                    validation_issues.append(f"以下股票数据为空: {empty_symbols}")

            stage_time = time.time() - stage_start_time
            self._record_stage_time(FlowStage.VALIDATION, stage_time)

            logger.debug(f"数据验证阶段完成，耗时: {stage_time:.2f}秒")

            return FlowResult(
                success=validation_passed,
                stage=FlowStage.VALIDATION,
                data=data,
                statistics={
                    'stage_time': stage_time,
                    'validation_issues': validation_issues
                }
            )

        except Exception as e:
            logger.error(f"数据验证阶段失败: {e}")
            return FlowResult(
                success=False,
                stage=FlowStage.VALIDATION,
                data=None,
                statistics={},
                error_message=str(e)
            )

    def _display_results(self, data: Union[pd.DataFrame, Dict[str, pd.DataFrame]],
                        symbols: List[str], source_period: str, target_period: str,
                        dividend_type: str) -> None:
        """显示处理结果"""
        try:
            print(f"\n📊 数据处理流程完成预览:")
            print(f"股票代码: {', '.join(symbols)}")
            print(f"周期转换: {source_period} -> {target_period}")
            print(f"复权类型: {dividend_type}")

            if isinstance(data, pd.DataFrame):
                print(f"数据行数: {len(data)}")
                print(f"时间范围: {data.index[0]} ~ {data.index[-1]}")
                print(f"数据列: {list(data.columns)}")
            elif isinstance(data, dict):
                total_rows = sum(len(df) for df in data.values() if not df.empty)
                print(f"总数据行数: {total_rows}")
                print(f"处理股票数: {len(data)}")

                for symbol, df in data.items():
                    if not df.empty:
                        print(f"  {symbol}: {len(df)} 行")

        except Exception as e:
            logger.warning(f"显示结果失败: {e}")

    def _record_stage_time(self, stage: FlowStage, stage_time: float) -> None:
        """记录阶段耗时"""
        stage_name = stage.value
        if stage_name not in self.flow_statistics['stage_times']:
            self.flow_statistics['stage_times'][stage_name] = []

        self.flow_statistics['stage_times'][stage_name].append(stage_time)

    def get_flow_statistics(self) -> Dict[str, Any]:
        """获取流程统计信息"""
        return self.flow_statistics.copy()


def create_data_flow_orchestrator(enable_parallel: bool = True) -> DataFlowOrchestrator:
    """
    创建数据处理流程编排器的工厂函数

    Args:
        enable_parallel: 是否启用并行处理

    Returns:
        DataFlowOrchestrator: 数据处理流程编排器实例
    """
    return DataFlowOrchestrator(enable_parallel=enable_parallel)
