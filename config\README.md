# 配置模块 (Config)

## 概述
配置模块提供系统级别的配置管理功能。经过2025-08-02重构，现在采用统一配置架构。

## 🔧 配置重构说明 (2025-08-02)

### 重构目标
- **遵循DRY原则**：删除所有重复配置文件
- **统一配置源**：所有配置集中在settings.py中管理
- **简化访问**：直接导入配置常量，无需复杂管理器
- **核心指导思维**：删除后备方案，保持代码简洁

### 文件结构
```
config/
├── settings.py                    # ✅ 唯一配置文件
├── symbols.py                     # ✅ 股票代码配置
├── *.backup                       # 📁 备份文件
├── system_config.json             # ❌ 已删除（重复）
├── data_source_config.py          # ❌ 已删除（重复）
└── utils/config/config_manager.py # ❌ 已删除（重复）
```

### 配置类别
- **数据存储配置**：DATA_ROOT, DATA_PATH_TEMPLATE, SUPPORTED_PERIODS
- **日志配置**：LOG_LEVEL, LOG_DIR, DEBUG_LOG_*
- **数据处理配置**：DATA_VALIDATION_LEVEL, ERROR_HANDLING_STRATEGY
- **时间转换配置**：TIME_CONVERTER_*, DEFAULT_TIMEZONE
- **系统配置**：ENABLE_MULTIPROCESSING, MAX_WORKERS
- **缓存配置**：CACHE_DIR, ADJUSTED_CACHE_CONFIG

### 使用方式
```python
# ✅ 正确方式：直接从settings导入
from config.settings import LOG_LEVEL, DATA_ROOT, DEFAULT_TIMEZONE

# ❌ 旧方式：使用配置管理器（已删除）
# from utils.config.config_manager import config_manager
```

### 重构效果
- **配置文件数量**：从12个减少到4个（删除8个重复文件）
- **DRY违反**：完全消除日志、时间、数据处理配置的重复定义
- **维护成本**：大幅降低，配置修改只需在一个地方进行
- **代码复杂度**：显著简化，无需复杂的配置管理器

## 文件说明

### settings.py
全局设置文件，包含所有系统配置：
- 数据存储路径和格式
- 日志级别和输出配置
- 数据处理和验证参数
- 时间转换器配置
- 系统性能参数
- 缓存管理配置

### symbols.py
股票代码配置文件，包含：
- 股票代码列表
- 市场分类信息
- 交易所映射关系

## 迁移指南

### 从旧配置系统迁移
如果你的代码中使用了已删除的配置文件，请按以下方式更新：

```python
# 旧方式 → 新方式

# 1. ConfigManager
# from utils.config.config_manager import config_manager
# config = config_manager.get_config()
from config.settings import DATA_VALIDATION_LEVEL, AUTO_FIX_TYPES

# 2. 时间转换器配置
# from utils.time_converter_config import get_manager
# config = get_manager()
from config.settings import TIME_CONVERTER_CACHE_SIZE, DEFAULT_TIMEZONE

# 3. 系统配置JSON
# 直接从settings.py导入对应的配置常量
from config.settings import DEBUG_LEVEL, ENABLE_PERFORMANCE_MONITORING
```

### 配置验证
运行以下测试确保迁移成功：
```bash
python tests/test_config_unification.py
```

## 最佳实践

1. **统一导入**：所有配置都从config.settings导入
2. **避免硬编码**：使用配置常量而不是硬编码值
3. **文档更新**：修改配置时同步更新相关文档
4. **测试验证**：配置修改后运行测试确保系统正常
