#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据获取基类模块

定义所有数据源必须实现的接口
"""

import pandas as pd
from typing import Dict, List, Optional, Any, Union
from abc import ABC, abstractmethod


class BaseFetcher(ABC):
    """
    数据获取器基类
    
    所有数据源实现必须继承此类并实现其抽象方法
    """
    
    @abstractmethod
    def download_history_data(self, 
                              symbols: List[str],
                              period: str,
                              start_time: str,
                              end_time: str,
                              **kwargs) -> Dict[str, pd.DataFrame]:
        """
        下载历史数据
        
        Args:
            symbols: 股票代码列表
            period: 数据周期
            start_time: 开始时间
            end_time: 结束时间
            kwargs: 额外参数
            
        Returns:
            Dict[str, pd.DataFrame]: 股票代码到数据的映射
        """
        pass
    
    @abstractmethod
    def get_local_data(self,
                       symbols: List[str],
                       period: str,
                       start_time: Optional[str] = None,
                       end_time: Optional[str] = None,
                       **kwargs) -> Dict[str, pd.DataFrame]:
        """
        获取本地数据
        
        Args:
            symbols: 股票代码列表
            period: 数据周期
            start_time: 开始时间，默认为None表示从最早可用数据开始
            end_time: 结束时间，默认为None表示到最新数据
            kwargs: 额外参数
            
        Returns:
            Dict[str, pd.DataFrame]: 股票代码到数据的映射
        """
        pass 