#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
日志模块

提供日志记录功能，支持将日志输出到控制台和文件
"""

import os
import sys

# 将项目根目录添加到Python路径
root_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, root_path)

# 导入子模块
from utils.logger.config import (  # noqa: E402
    LOG_LEVELS,
    LogTarget,
    get_log_file_path,
    list_log_files,
    get_all_loggers
)

from utils.logger.formatters import (  # noqa: E402
    EnhancedFormatter,
    TableMarkdownFormatter,
    get_formatter
)

from utils.logger.filters import (  # noqa: E402
    TargetFilter,
    PrefixFilter,
    ModuleFilter
)

from utils.logger.handlers import (  # noqa: E402
    RotatingFileHandlerWithCompression
)

from utils.logger.manager import (  # noqa: E402
    setup_unified_logging,
    get_unified_logger,
    clean_old_log_files
)

# 导出所有公共接口
__all__ = [
    # 配置相关
    'LOG_LEVELS',
    'LogTarget',
    'get_log_file_path',
    'list_log_files',
    'get_all_loggers',
    
    # 格式化器
    'EnhancedFormatter',   # 推荐使用的格式化器
    'TableMarkdownFormatter',  # 新的表格式Markdown格式化器
    'get_formatter',
    
    # 过滤器
    'TargetFilter',
    'PrefixFilter',
    'ModuleFilter',
    
    # 处理器
    'RotatingFileHandlerWithCompression',
    
    # 管理器
    'setup_unified_logging',
    'get_unified_logger',
    'clean_old_log_files'
]
