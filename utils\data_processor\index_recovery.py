#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
索引格式错误恢复模块

提供索引格式错误的检测、诊断和恢复功能
支持多种恢复策略和数据备份机制
"""

import pandas as pd
import numpy as np
from typing import Optional, Dict, List, Tuple, Any, Union
from datetime import datetime
import json
from pathlib import Path
import shutil

from utils.logger import get_unified_logger
from utils.data_processor.index_manager import IndexManager
from utils.smart_time_converter import smart_to_datetime

logger = get_unified_logger(__name__, enhanced=True)


class IndexRecoveryError(Exception):
    """索引恢复错误异常"""
    pass


class IndexRecoveryStrategy:
    """索引恢复策略枚举"""
    
    TIME_COLUMN = "time_column"           # 使用time列重建索引
    BACKUP_RESTORE = "backup_restore"     # 从备份恢复
    MANUAL_REPAIR = "manual_repair"       # 手动修复
    DATA_REGENERATE = "data_regenerate"   # 重新生成数据


class IndexRecoveryManager:
    """索引格式错误恢复管理器"""
    
    def __init__(self, backup_dir: Optional[str] = None):
        """
        初始化恢复管理器
        
        Args:
            backup_dir: 备份目录路径
        """
        self.backup_dir = Path(backup_dir or "data/index_backups")
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        self.recovery_history = []
        
        logger.info(f"索引恢复管理器初始化完成，备份目录: {self.backup_dir}")
    
    def diagnose_index_issues(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        诊断DataFrame的索引问题
        
        Args:
            df: 要诊断的DataFrame
            
        Returns:
            诊断报告字典
        """
        if df is None or df.empty:
            return {"status": "empty", "issues": [], "recommendations": []}
        
        issues = []
        recommendations = []
        
        # 检查索引类型
        index_type = type(df.index).__name__
        if index_type == "RangeIndex":
            issues.append("索引为数字序列（RangeIndex），应该是时间戳格式")
            recommendations.append("使用time列重建索引")
        
        # 检查索引格式
        if not IndexManager.validate_index_format(df):
            issues.append("索引格式不符合YYYYMMDDHHMMSS标准")
            recommendations.append("使用IndexManager.ensure_proper_index()修复")
        
        # 检查time列存在性
        has_time_column = 'time' in df.columns
        if not has_time_column:
            issues.append("缺少time列，无法重建索引")
            recommendations.append("从其他数据源获取时间信息")
        
        # 检查索引重复
        has_duplicates = df.index.duplicated().any()
        if has_duplicates:
            duplicate_count = df.index.duplicated().sum()
            issues.append(f"索引存在{duplicate_count}个重复值")
            recommendations.append("去除重复索引或使用groupby聚合")
        
        # 检查索引单调性
        is_monotonic = df.index.is_monotonic_increasing
        if not is_monotonic:
            issues.append("索引未按时间顺序排序")
            recommendations.append("使用sort_index()排序")
        
        # 生成诊断报告
        diagnosis = {
            "timestamp": datetime.now().isoformat(),
            "status": "issues_found" if issues else "healthy",
            "index_type": index_type,
            "index_length": len(df.index),
            "has_time_column": has_time_column,
            "has_duplicates": has_duplicates,
            "is_monotonic": is_monotonic,
            "issues": issues,
            "recommendations": recommendations,
            "suggested_strategy": self._suggest_recovery_strategy(issues, has_time_column)
        }
        
        logger.info(f"索引诊断完成，发现{len(issues)}个问题")
        return diagnosis
    
    def _suggest_recovery_strategy(self, issues: List[str], has_time_column: bool) -> str:
        """根据问题建议恢复策略"""
        if not issues:
            return "no_action_needed"
        
        if has_time_column:
            return IndexRecoveryStrategy.TIME_COLUMN
        else:
            return IndexRecoveryStrategy.BACKUP_RESTORE
    
    def create_backup(self, df: pd.DataFrame, identifier: str) -> str:
        """
        创建DataFrame备份
        
        Args:
            df: 要备份的DataFrame
            identifier: 备份标识符
            
        Returns:
            备份文件路径
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"{identifier}_{timestamp}.parquet"
            backup_path = self.backup_dir / backup_filename
            
            # 保存备份
            df.to_parquet(backup_path, compression='snappy')
            
            # 记录备份信息
            backup_info = {
                "timestamp": datetime.now().isoformat(),
                "identifier": identifier,
                "backup_path": str(backup_path),
                "data_shape": df.shape,
                "index_type": type(df.index).__name__
            }
            
            info_path = backup_path.with_suffix('.json')
            with open(info_path, 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, indent=2, ensure_ascii=False)
            
            logger.info(f"已创建数据备份: {backup_path}")
            return str(backup_path)
            
        except Exception as e:
            logger.error(f"创建备份失败: {e}")
            raise IndexRecoveryError(f"备份创建失败: {e}")
    
    def recover_from_time_column(self, df: pd.DataFrame, time_column: str = 'time') -> pd.DataFrame:
        """
        从time列恢复索引格式
        
        Args:
            df: 要恢复的DataFrame
            time_column: 时间列名
            
        Returns:
            恢复后的DataFrame
        """
        try:
            if time_column not in df.columns:
                raise IndexRecoveryError(f"DataFrame中不存在{time_column}列")
            
            logger.info(f"开始从{time_column}列恢复索引格式")
            
            # 使用IndexManager的标准方法
            recovered_df = IndexManager.ensure_proper_index(df, time_column=time_column)
            
            # 验证恢复结果
            if IndexManager.validate_index_format(recovered_df):
                logger.info("索引格式恢复成功")
                return recovered_df
            else:
                raise IndexRecoveryError("索引格式恢复失败，结果仍不符合标准")
                
        except Exception as e:
            logger.error(f"从{time_column}列恢复索引失败: {e}")
            raise IndexRecoveryError(f"索引恢复失败: {e}")
    
    def recover_from_backup(self, backup_path: str) -> pd.DataFrame:
        """
        从备份文件恢复数据
        
        Args:
            backup_path: 备份文件路径
            
        Returns:
            恢复的DataFrame
        """
        try:
            backup_file = Path(backup_path)
            if not backup_file.exists():
                raise IndexRecoveryError(f"备份文件不存在: {backup_path}")
            
            logger.info(f"从备份恢复数据: {backup_path}")
            
            # 读取备份数据
            recovered_df = pd.read_parquet(backup_path)
            
            # 验证恢复的数据
            if IndexManager.validate_index_format(recovered_df):
                logger.info("从备份恢复数据成功")
                return recovered_df
            else:
                logger.warning("备份数据的索引格式也不正确，尝试修复")
                return IndexManager.ensure_proper_index(recovered_df, time_column='time')
                
        except Exception as e:
            logger.error(f"从备份恢复数据失败: {e}")
            raise IndexRecoveryError(f"备份恢复失败: {e}")
    
    def auto_recover(self, df: pd.DataFrame, identifier: str = "auto_recovery") -> Tuple[pd.DataFrame, Dict]:
        """
        自动恢复索引格式
        
        Args:
            df: 要恢复的DataFrame
            identifier: 恢复标识符
            
        Returns:
            (恢复后的DataFrame, 恢复报告)
        """
        try:
            # 1. 诊断问题
            diagnosis = self.diagnose_index_issues(df)
            
            if diagnosis["status"] == "healthy":
                logger.info("数据索引格式正常，无需恢复")
                return df, diagnosis
            
            # 2. 创建备份
            backup_path = self.create_backup(df, identifier)
            
            # 3. 根据建议策略进行恢复
            strategy = diagnosis["suggested_strategy"]
            
            if strategy == IndexRecoveryStrategy.TIME_COLUMN:
                recovered_df = self.recover_from_time_column(df)
            else:
                # 如果无法从time列恢复，尝试其他方法
                logger.warning("无法从time列恢复，尝试使用IndexManager标准方法")
                recovered_df = IndexManager.ensure_proper_index(df, time_column='time')
            
            # 4. 验证恢复结果
            if IndexManager.validate_index_format(recovered_df):
                recovery_report = {
                    "status": "success",
                    "strategy_used": strategy,
                    "backup_path": backup_path,
                    "original_issues": diagnosis["issues"],
                    "recovery_timestamp": datetime.now().isoformat()
                }
                
                # 记录恢复历史
                self.recovery_history.append(recovery_report)
                
                logger.info("自动恢复成功")
                return recovered_df, recovery_report
            else:
                raise IndexRecoveryError("自动恢复失败，结果仍不符合标准")
                
        except Exception as e:
            logger.error(f"自动恢复失败: {e}")
            recovery_report = {
                "status": "failed",
                "error": str(e),
                "recovery_timestamp": datetime.now().isoformat()
            }
            return df, recovery_report
    
    def list_backups(self, identifier: Optional[str] = None) -> List[Dict]:
        """
        列出可用的备份文件
        
        Args:
            identifier: 可选的标识符过滤
            
        Returns:
            备份文件信息列表
        """
        backups = []
        
        try:
            for json_file in self.backup_dir.glob("*.json"):
                with open(json_file, 'r', encoding='utf-8') as f:
                    backup_info = json.load(f)
                
                if identifier is None or backup_info.get("identifier") == identifier:
                    backups.append(backup_info)
            
            # 按时间戳排序
            backups.sort(key=lambda x: x.get("timestamp", ""), reverse=True)
            
        except Exception as e:
            logger.error(f"列出备份文件失败: {e}")
        
        return backups
    
    def cleanup_old_backups(self, days: int = 7) -> int:
        """
        清理旧的备份文件
        
        Args:
            days: 保留天数
            
        Returns:
            清理的文件数量
        """
        try:
            cutoff_time = datetime.now().timestamp() - (days * 24 * 3600)
            cleaned_count = 0
            
            for backup_file in self.backup_dir.glob("*.parquet"):
                if backup_file.stat().st_mtime < cutoff_time:
                    # 删除备份文件和对应的信息文件
                    backup_file.unlink()
                    info_file = backup_file.with_suffix('.json')
                    if info_file.exists():
                        info_file.unlink()
                    cleaned_count += 1
            
            logger.info(f"清理了{cleaned_count}个旧备份文件")
            return cleaned_count
            
        except Exception as e:
            logger.error(f"清理备份文件失败: {e}")
            return 0
    
    def get_recovery_history(self) -> List[Dict]:
        """获取恢复历史记录"""
        return self.recovery_history.copy()


# 创建全局恢复管理器实例
global_recovery_manager = IndexRecoveryManager()


def diagnose_dataframe_index(df: pd.DataFrame) -> Dict[str, Any]:
    """便捷函数：诊断DataFrame索引问题"""
    return global_recovery_manager.diagnose_index_issues(df)


def auto_recover_dataframe(df: pd.DataFrame, identifier: str = "auto") -> Tuple[pd.DataFrame, Dict]:
    """便捷函数：自动恢复DataFrame索引格式"""
    return global_recovery_manager.auto_recover(df, identifier)
