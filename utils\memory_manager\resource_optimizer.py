"""
资源优化模块 - 提供内存优化和资源管理功能
"""

import os
import gc
import sys
import time
import psutil
import weakref
from typing import Dict, List, Set, Callable, Optional, Any

# 导入正确的logger模块
from ..logger import get_unified_logger, LogTarget

# 获取logger实例
logger = get_unified_logger("resource_optimizer")


class ResourceOptimizer:
    """
    资源优化器类
    
    提供内存优化和资源管理功能，包括垃圾回收触发、缓存清理、大型对象检测等
    """
    
    def __init__(self):
        """初始化资源优化器"""
        # 当前进程
        self._process = psutil.Process(os.getpid())
        
        # 对象引用计数器
        self._object_refs: Dict[int, weakref.ref] = {}
        
        # 大型对象阈值(字节)
        self._large_object_threshold = 10 * 1024 * 1024  # 10MB
        
        # 缓存对象注册表
        self._cache_registry: Dict[str, Callable[[], None]] = {}
        
        logger.debug(LogTarget.FILE, "资源优化器初始化完成")
    
    def trigger_garbage_collection(self, full: bool = False) -> Dict:
        """
        触发垃圾回收
        
        Args:
            full: 是否执行完整垃圾回收，默认为False
            
        Returns:
            垃圾回收结果统计
        """
        # 记录回收前内存使用
        before_mem = self._process.memory_info().rss
        
        # 记录开始时间
        start_time = time.time()
        
        # 获取回收前的对象计数
        before_counts = gc.get_count()
        
        if full:
            # 执行完整垃圾回收(所有代)
            collected = gc.collect()
        else:
            # 只执行0代垃圾回收
            collected = gc.collect(0)
        
        # 获取回收后的对象计数
        after_counts = gc.get_count()
        
        # 记录回收后内存使用
        after_mem = self._process.memory_info().rss
        
        # 计算内存变化
        mem_diff = before_mem - after_mem
        mem_diff_mb = mem_diff / (1024 * 1024)
        
        # 计算耗时
        elapsed = time.time() - start_time
        
        # 记录日志
        if mem_diff > 0:
            logger.info(
                LogTarget.FILE,
                f"垃圾回收完成: 回收 {collected} 个对象, 释放 {mem_diff_mb:.2f}MB 内存, "
                f"耗时 {elapsed:.3f}秒"
            )
        else:
            logger.debug(
                LogTarget.FILE,
                f"垃圾回收完成: 回收 {collected} 个对象, 内存无变化或增加 "
                f"{-mem_diff_mb:.2f}MB, 耗时 {elapsed:.3f}秒"
            )
        
        # 返回统计信息
        return {
            'collected': collected,
            'before_memory': before_mem,
            'after_memory': after_mem,
            'memory_diff': mem_diff,
            'memory_diff_mb': mem_diff_mb,
            'before_counts': before_counts,
            'after_counts': after_counts,
            'elapsed': elapsed,
            'full_collection': full
        }
    
    def register_cache_cleaner(self, name: str, cleaner_func: Callable[[], None]) -> bool:
        """
        注册缓存清理函数
        
        Args:
            name: 缓存名称
            cleaner_func: 清理函数，无参数，无返回值
            
        Returns:
            是否成功注册
        """
        if name in self._cache_registry:
            logger.warning(LogTarget.FILE, f"缓存清理函数已存在: {name}")
            return False
        
        self._cache_registry[name] = cleaner_func
        logger.debug(LogTarget.FILE, f"已注册缓存清理函数: {name}")
        return True
    
    def unregister_cache_cleaner(self, name: str) -> bool:
        """
        注销缓存清理函数
        
        Args:
            name: 缓存名称
            
        Returns:
            是否成功注销
        """
        if name not in self._cache_registry:
            logger.warning(LogTarget.FILE, f"缓存清理函数不存在: {name}")
            return False
        
        del self._cache_registry[name]
        logger.debug(LogTarget.FILE, f"已注销缓存清理函数: {name}")
        return True
    
    def clean_all_caches(self) -> Dict[str, bool]:
        """
        清理所有注册的缓存
        
        Returns:
            清理结果字典，键为缓存名称，值为是否成功清理
        """
        results = {}
        
        for name, cleaner_func in self._cache_registry.items():
            try:
                cleaner_func()
                results[name] = True
                logger.debug(LogTarget.FILE, f"已清理缓存: {name}")
            except Exception as e:
                results[name] = False
                logger.error(LogTarget.FILE, f"清理缓存出错 [{name}]: {e}")
        
        # 记录总体结果
        success_count = sum(1 for success in results.values() if success)
        total_count = len(results)
        
        if total_count > 0:
            logger.info(
                LogTarget.FILE,
                f"缓存清理完成: {success_count}/{total_count} 个缓存成功清理"
            )
        else:
            logger.debug(LogTarget.FILE, "没有注册的缓存需要清理")
        
        return results
    
    def clean_cache(self, name: str) -> bool:
        """
        清理指定的缓存
        
        Args:
            name: 缓存名称
            
        Returns:
            是否成功清理
        """
        if name not in self._cache_registry:
            logger.warning(LogTarget.FILE, f"缓存清理函数不存在: {name}")
            return False
        
        try:
            self._cache_registry[name]()
            logger.debug(LogTarget.FILE, f"已清理缓存: {name}")
            return True
        except Exception as e:
            logger.error(LogTarget.FILE, f"清理缓存出错 [{name}]: {e}")
            return False
    
    def get_registered_caches(self) -> List[str]:
        """
        获取所有注册的缓存名称
        
        Returns:
            缓存名称列表
        """
        return list(self._cache_registry.keys())
    
    def detect_large_objects(self, threshold_mb: Optional[float] = None) -> List[Dict]:
        """
        检测大型对象
        
        Args:
            threshold_mb: 大型对象阈值(MB)，默认使用实例设置的阈值
            
        Returns:
            大型对象信息列表
        """
        # 设置阈值
        if threshold_mb is not None:
            threshold = int(threshold_mb * 1024 * 1024)
        else:
            threshold = self._large_object_threshold
        
        # 触发垃圾回收，确保引用计数准确
        gc.collect()
        
        # 获取所有对象
        large_objects = []
        all_objects = gc.get_objects()
        
        # 记录开始时间
        start_time = time.time()
        
        # 检查每个对象的大小
        for obj in all_objects:
            try:
                # 获取对象大小
                obj_size = sys.getsizeof(obj)
                
                # 如果是容器类型，递归计算内部对象大小
                if isinstance(obj, (list, tuple, dict, set)):
                    obj_size = self._get_deep_size(obj)
                
                # 如果超过阈值，记录对象信息
                if obj_size >= threshold:
                    large_objects.append({
                        'type': type(obj).__name__,
                        'size': obj_size,
                        'size_mb': obj_size / (1024 * 1024),
                        'id': id(obj),
                        'repr': self._safe_repr(obj)
                    })
            except Exception:
                # 忽略无法检查大小的对象
                pass
        
        # 按大小排序
        large_objects.sort(key=lambda x: x['size'], reverse=True)
        
        # 计算耗时
        elapsed = time.time() - start_time
        
        # 记录日志
        if large_objects:
            total_size_mb = sum(obj['size'] for obj in large_objects) / (1024 * 1024)
            logger.info(
                LogTarget.FILE,
                f"检测到 {len(large_objects)} 个大型对象，总大小 {total_size_mb:.2f}MB，"
                f"耗时 {elapsed:.3f}秒"
            )
        else:
            logger.debug(
                LogTarget.FILE,
                f"未检测到大于 {threshold / (1024 * 1024):.2f}MB 的大型对象，"
                f"耗时 {elapsed:.3f}秒"
            )
        
        return large_objects
    
    def _safe_repr(self, obj: Any) -> str:
        """
        安全地获取对象的字符串表示
        
        Args:
            obj: 任意对象
            
        Returns:
            对象的字符串表示
        """
        try:
            # 尝试获取简短表示
            repr_str = repr(obj)
            
            # 如果太长，截断
            if len(repr_str) > 100:
                repr_str = repr_str[:97] + "..."
            
            return repr_str
        except Exception:
            return f"<无法表示的 {type(obj).__name__} 对象>"
    
    def _get_deep_size(self, obj: Any, seen: Optional[Set] = None) -> int:
        """
        递归计算对象的深层大小
        
        Args:
            obj: 要计算大小的对象
            seen: 已经计算过的对象ID集合，防止循环引用
            
        Returns:
            对象的总大小(字节)
        """
        # 初始化已见对象集合
        if seen is None:
            seen = set()
        
        # 获取对象ID
        obj_id = id(obj)
        
        # 如果已经计算过，返回0
        if obj_id in seen:
            return 0
        
        # 标记为已计算
        seen.add(obj_id)
        
        # 获取对象自身大小
        size = sys.getsizeof(obj)
        
        # 处理容器类型
        if isinstance(obj, dict):
            size += sum(
                self._get_deep_size(k, seen) + self._get_deep_size(v, seen)
                for k, v in obj.items()
            )
        elif isinstance(obj, (list, tuple, set, frozenset)):
            size += sum(self._get_deep_size(item, seen) for item in obj)
        
        return size
    
    def set_large_object_threshold(self, threshold_mb: float) -> None:
        """
        设置大型对象阈值
        
        Args:
            threshold_mb: 阈值(MB)
        """
        self._large_object_threshold = int(threshold_mb * 1024 * 1024)
        logger.debug(
            LogTarget.FILE,
            f"大型对象阈值已设置为: {threshold_mb:.2f}MB"
        )
    
    def optimize_memory(self, level: str = "normal") -> Dict:
        """
        执行内存优化
        
        Args:
            level: 优化级别，可选值为 "light", "normal", "aggressive"
            
        Returns:
            优化结果统计
        """
        # 记录开始时间
        start_time = time.time()
        
        # 记录开始内存使用
        before_mem = self._process.memory_info().rss
        
        # 执行优化措施
        steps_taken = []
        
        # 1. 清理所有缓存
        cache_results = self.clean_all_caches()
        steps_taken.append({
            'step': 'clean_caches',
            'success': any(cache_results.values()),
            'details': cache_results
        })
        
        # 2. 根据优化级别执行垃圾回收
        if level == "light":
            # 轻量级：只回收年轻代(第0代)
            gc_result = self.trigger_garbage_collection(full=False)
        else:
            # 普通和激进级别：完整回收
            gc_result = self.trigger_garbage_collection(full=True)
        
        steps_taken.append({
            'step': 'garbage_collection',
            'success': True,
            'details': gc_result
        })
        
        # 3. 激进级别：尝试释放未使用的内存回操作系统
        if level == "aggressive":
            try:
                # 在某些系统上可能不可用
                if hasattr(psutil, 'Process'):
                    # 尝试调用系统特定的内存整理函数
                    if sys.platform == 'win32':
                        # Windows平台
                        import ctypes
                        ctypes.windll.kernel32.SetProcessWorkingSetSize(
                            -1, -1, -1
                        )
                        steps_taken.append({
                            'step': 'release_memory_to_os_windows',
                            'success': True
                        })
                    elif sys.platform == 'linux':
                        # Linux平台
                        os.system('echo 1 > /proc/sys/vm/drop_caches')
                        steps_taken.append({
                            'step': 'release_memory_to_os_linux',
                            'success': True
                        })
            except Exception as e:
                steps_taken.append({
                    'step': 'release_memory_to_os',
                    'success': False,
                    'error': str(e)
                })
        
        # 记录结束内存使用
        after_mem = self._process.memory_info().rss
        
        # 计算内存变化
        mem_diff = before_mem - after_mem
        mem_diff_mb = mem_diff / (1024 * 1024)
        
        # 计算耗时
        elapsed = time.time() - start_time
        
        # 记录日志
        if mem_diff > 0:
            logger.info(
                LogTarget.FILE,
                f"内存优化完成 (级别: {level}): 释放 {mem_diff_mb:.2f}MB 内存, "
                f"耗时 {elapsed:.3f}秒"
            )
        else:
            logger.debug(
                LogTarget.FILE,
                f"内存优化完成 (级别: {level}): 内存无变化或增加 {-mem_diff_mb:.2f}MB, "
                f"耗时 {elapsed:.3f}秒"
            )
        
        # 返回优化结果
        return {
            'level': level,
            'before_memory': before_mem,
            'after_memory': after_mem,
            'memory_diff': mem_diff,
            'memory_diff_mb': mem_diff_mb,
            'elapsed': elapsed,
            'steps': steps_taken
        }
    
    def track_object(self, obj: Any, name: Optional[str] = None) -> int:
        """
        跟踪对象引用
        
        Args:
            obj: 要跟踪的对象
            name: 对象名称，可选
            
        Returns:
            对象ID
        """
        obj_id = id(obj)
        
        # 创建弱引用
        ref = weakref.ref(obj)
        self._object_refs[obj_id] = ref
        
        # 记录日志
        obj_name = name or f"{type(obj).__name__}_{obj_id}"
        obj_size = sys.getsizeof(obj)
        obj_size_mb = obj_size / (1024 * 1024)
        
        logger.debug(
            LogTarget.FILE,
            f"开始跟踪对象: {obj_name}, ID: {obj_id}, 大小: {obj_size_mb:.2f}MB"
        )
        
        return obj_id
    
    def check_tracked_objects(self) -> Dict[int, bool]:
        """
        检查跟踪的对象是否仍然存在
        
        Returns:
            对象ID到存在状态的映射
        """
        results = {}
        
        for obj_id, ref in list(self._object_refs.items()):
            # 检查对象是否仍然存在
            obj = ref()
            exists = obj is not None
            
            results[obj_id] = exists
            
            # 如果对象已被回收，从跟踪列表中移除
            if not exists:
                del self._object_refs[obj_id]
                logger.debug(LogTarget.FILE, f"对象已被回收: ID {obj_id}")
        
        return results
    
    def get_tracked_objects_info(self) -> List[Dict]:
        """
        获取所有跟踪对象的信息
        
        Returns:
            对象信息列表
        """
        results = []
        
        for obj_id, ref in list(self._object_refs.items()):
            # 获取对象
            obj = ref()
            
            # 如果对象仍然存在
            if obj is not None:
                # 获取对象大小
                try:
                    obj_size = sys.getsizeof(obj)
                    if isinstance(obj, (list, tuple, dict, set)):
                        obj_size = self._get_deep_size(obj)
                except Exception:
                    obj_size = -1
                
                results.append({
                    'id': obj_id,
                    'type': type(obj).__name__,
                    'size': obj_size,
                    'size_mb': obj_size / (1024 * 1024) if obj_size >= 0 else -1,
                    'repr': self._safe_repr(obj)
                })
            else:
                # 对象已被回收，从跟踪列表中移除
                del self._object_refs[obj_id]
        
        return results
    
    def untrack_object(self, obj_id: int) -> bool:
        """
        停止跟踪对象
        
        Args:
            obj_id: 对象ID
            
        Returns:
            是否成功停止跟踪
        """
        if obj_id not in self._object_refs:
            logger.warning(LogTarget.FILE, f"未跟踪的对象ID: {obj_id}")
            return False
        
        del self._object_refs[obj_id]
        logger.debug(LogTarget.FILE, f"已停止跟踪对象: ID {obj_id}")
        return True
    
    def get_memory_usage_by_type(self) -> Dict[str, Dict]:
        """
        获取按类型分组的内存使用情况
        
        Returns:
            类型名称到内存使用统计的映射
        """
        # 触发垃圾回收，确保引用计数准确
        gc.collect()
        
        # 获取所有对象
        all_objects = gc.get_objects()
        
        # 按类型分组统计
        type_stats = {}
        
        for obj in all_objects:
            try:
                # 获取类型名称
                type_name = type(obj).__name__
                
                # 获取对象大小
                obj_size = sys.getsizeof(obj)
                
                # 更新统计
                if type_name in type_stats:
                    type_stats[type_name]['count'] += 1
                    type_stats[type_name]['size'] += obj_size
                else:
                    type_stats[type_name] = {
                        'count': 1,
                        'size': obj_size
                    }
            except Exception:
                # 忽略无法处理的对象
                pass
        
        # 转换为结果格式
        result = {}
        for type_name, stats in type_stats.items():
            result[type_name] = {
                'count': stats['count'],
                'size': stats['size'],
                'size_mb': stats['size'] / (1024 * 1024)
            }
        
        return result
    
    def suggest_optimization(self, memory_info: Dict) -> List[Dict]:
        """
        根据内存信息提供优化建议
        
        Args:
            memory_info: 内存信息字典
            
        Returns:
            优化建议列表
        """
        suggestions = []
        
        # 检查系统内存使用率
        sys_percent = memory_info.get('system', {}).get('percent', 0)
        if sys_percent > 90:
            suggestions.append({
                'level': 'critical',
                'message': f"系统内存使用率过高 ({sys_percent}%)，建议立即执行激进优化",
                'action': 'optimize_memory',
                'params': {'level': 'aggressive'}
            })
        elif sys_percent > 80:
            suggestions.append({
                'level': 'warning',
                'message': f"系统内存使用率较高 ({sys_percent}%)，建议执行标准优化",
                'action': 'optimize_memory',
                'params': {'level': 'normal'}
            })
        
        # 检查进程内存增长趋势
        trend = memory_info.get('trend', {})
        if trend.get('status') == 'ok' and trend.get('is_increasing', False):
            growth_rate = trend.get('growth_rate_mb_min', 0)
            if growth_rate > 100:
                suggestions.append({
                    'level': 'critical',
                    'message': f"内存增长速度过快 ({growth_rate:.2f}MB/分钟)，建议检查内存泄漏",
                    'action': 'detect_large_objects',
                    'params': {}
                })
            elif growth_rate > 50:
                suggestions.append({
                    'level': 'warning',
                    'message': f"内存增长速度较快 ({growth_rate:.2f}MB/分钟)，建议清理缓存",
                    'action': 'clean_all_caches',
                    'params': {}
                })
        
        # 检查进程内存使用
        proc_rss_mb = memory_info.get('process', {}).get('rss', 0) / (1024 * 1024)
        if proc_rss_mb > 1000:  # 超过1GB
            suggestions.append({
                'level': 'warning',
                'message': f"进程内存使用较高 ({proc_rss_mb:.2f}MB)，建议检查大型对象",
                'action': 'detect_large_objects',
                'params': {}
            })
        
        return suggestions 