#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据字段类型分类器

提供完整的字段类型识别和分类功能，确保复权计算只对正确的字段类型进行处理。
遵循"宁可报错也不掩盖bug"的原则，严格区分价格字段和数量字段。

设计原则：
1. 明确的字段类型定义，避免模糊分类
2. 支持多种数据源的字段变体
3. 提供详细的分类日志和验证
4. 可扩展的字段类型系统
"""

from enum import Enum
from typing import List, Dict, Set, Optional, Union
import pandas as pd
import re
import hashlib
from utils.logger import get_unified_logger, LogTarget

logger = get_unified_logger(__name__)


class FieldType(Enum):
    """字段类型枚举"""
    PRICE_FIELD = "price"           # 价格字段，需要复权
    VOLUME_FIELD = "volume"         # 数量字段，不需要复权
    TIME_FIELD = "time"             # 时间字段，不需要复权
    RATIO_FIELD = "ratio"           # 比率字段，不需要复权
    COUNT_FIELD = "count"           # 计数字段，不需要复权
    UNKNOWN_FIELD = "unknown"       # 未知字段，需要人工确认


class FieldTypeClassifier:
    """字段类型分类器"""

    def __init__(self):
        """初始化字段类型分类器"""
        self._init_field_mappings()
        # 添加缓存机制
        self._classification_cache = {}
        self._cache_hits = 0
        self._cache_misses = 0
        logger.debug(LogTarget.FILE, "字段类型分类器初始化完成")

    def _get_dataframe_hash(self, df: pd.DataFrame) -> str:
        """
        计算DataFrame列结构的哈希值，用于缓存

        Args:
            df: DataFrame

        Returns:
            str: 哈希值
        """
        # 使用列名和数据类型生成哈希
        columns_info = []
        for col in df.columns:
            dtype_str = str(df[col].dtype)
            columns_info.append(f"{col}:{dtype_str}")

        columns_str = "|".join(sorted(columns_info))
        return hashlib.md5(columns_str.encode()).hexdigest()

    def _init_field_mappings(self):
        """初始化字段类型映射规则"""
        
        # 价格字段 - 需要复权调整
        self.price_fields = {
            # 基础价格字段
            'open', 'high', 'low', 'close', 'lastPrice', 'price',
            'lastClose', 'preClose', 'prevClose', 'settlement',
            'settlementPrice', 'lastSettlementPrice',
            
            # 委托价格字段
            'bidPrice', 'askPrice', 'bid', 'ask',
            'bid1', 'bid2', 'bid3', 'bid4', 'bid5',
            'ask1', 'ask2', 'ask3', 'ask4', 'ask5',
            
            # 其他价格相关字段
            'vwap', 'avgPrice', 'upperLimit', 'lowerLimit',
            'openInt', 'interest'  # 持仓相关价格
        }
        
        # 数量字段 - 不需要复权
        self.volume_fields = {
            # 基础数量字段
            'volume', 'vol', 'amount', 'turnover',
            'pvolume', 'pamount',  # 原始成交量/额
            
            # 委托数量字段
            'bidVol', 'askVol', 'bidVolume', 'askVolume',
            'bid1Vol', 'bid2Vol', 'bid3Vol', 'bid4Vol', 'bid5Vol',
            'ask1Vol', 'ask2Vol', 'ask3Vol', 'ask4Vol', 'ask5Vol',
            
            # 其他数量字段
            'totalVolume', 'totalAmount', 'totalTurnover',
            'openInterest', 'position'  # 持仓数量
        }
        
        # 时间字段 - 不需要复权
        self.time_fields = {
            'time', 'timestamp', 'datetime', 'date',
            'timetag', 'timeStamp', 'tradingDay'
        }
        
        # 比率字段 - 不需要复权
        self.ratio_fields = {
            'pe', 'pb', 'ps', 'pcf',  # 估值比率
            'roe', 'roa', 'margin',   # 财务比率
            'change', 'pctchange', 'changerate',  # 涨跌幅
            'turnoverrate', 'amplitude',  # 换手率、振幅
            'stockstatus', 'status', 'state'  # 状态字段
        }
        
        # 计数字段 - 不需要复权
        self.count_fields = {
            'transactionNum', 'tradeCount', 'dealCount',
            'buyCount', 'sellCount', 'neutralCount',
            'tickCount', 'orderCount'
        }
        
        logger.debug(LogTarget.FILE, f"字段映射初始化完成:")
        logger.debug(LogTarget.FILE, f"  价格字段: {len(self.price_fields)} 个")
        logger.debug(LogTarget.FILE, f"  数量字段: {len(self.volume_fields)} 个")
        logger.debug(LogTarget.FILE, f"  时间字段: {len(self.time_fields)} 个")
        logger.debug(LogTarget.FILE, f"  比率字段: {len(self.ratio_fields)} 个")
        logger.debug(LogTarget.FILE, f"  计数字段: {len(self.count_fields)} 个")
    
    def classify_field(self, field_name: str) -> FieldType:
        """
        分类单个字段类型
        
        Args:
            field_name: 字段名称
            
        Returns:
            FieldType: 字段类型
        """
        field_lower = field_name.lower()
        
        # 直接匹配
        if field_lower in self.price_fields:
            return FieldType.PRICE_FIELD
        elif field_lower in self.volume_fields:
            return FieldType.VOLUME_FIELD
        elif field_lower in self.time_fields:
            return FieldType.TIME_FIELD
        elif field_lower in self.ratio_fields:
            return FieldType.RATIO_FIELD
        elif field_lower in self.count_fields:
            return FieldType.COUNT_FIELD
        
        # 模式匹配
        field_type = self._pattern_match_field(field_name)
        if field_type != FieldType.UNKNOWN_FIELD:
            return field_type
        
        # 未知字段
        logger.warning(LogTarget.FILE, f"未知字段类型: {field_name}")
        return FieldType.UNKNOWN_FIELD
    
    def _pattern_match_field(self, field_name: str) -> FieldType:
        """
        使用模式匹配识别字段类型
        
        Args:
            field_name: 字段名称
            
        Returns:
            FieldType: 字段类型
        """
        field_lower = field_name.lower()
        
        # 价格相关模式
        price_patterns = [
            r'.*price.*', r'.*bid.*', r'.*ask.*',
            r'.*open.*', r'.*high.*', r'.*low.*', r'.*close.*',
            r'.*settlement.*', r'.*limit.*'
        ]
        
        for pattern in price_patterns:
            if re.match(pattern, field_lower):
                # 排除数量相关的价格字段
                if any(vol_word in field_lower for vol_word in ['vol', 'volume', 'amount', 'count']):
                    continue
                return FieldType.PRICE_FIELD
        
        # 数量相关模式
        volume_patterns = [
            r'.*vol.*', r'.*volume.*', r'.*amount.*',
            r'.*turnover.*', r'.*position.*'
        ]
        
        for pattern in volume_patterns:
            if re.match(pattern, field_lower):
                return FieldType.VOLUME_FIELD
        
        # 时间相关模式
        time_patterns = [
            r'.*time.*', r'.*date.*', r'.*timestamp.*'
        ]
        
        for pattern in time_patterns:
            if re.match(pattern, field_lower):
                return FieldType.TIME_FIELD
        
        # 比率相关模式
        ratio_patterns = [
            r'.*rate.*', r'.*ratio.*', r'.*pct.*',
            r'.*change.*', r'.*percent.*'
        ]
        
        for pattern in ratio_patterns:
            if re.match(pattern, field_lower):
                return FieldType.RATIO_FIELD
        
        # 计数相关模式
        count_patterns = [
            r'.*count.*', r'.*num.*', r'.*number.*'
        ]
        
        for pattern in count_patterns:
            if re.match(pattern, field_lower):
                return FieldType.COUNT_FIELD
        
        return FieldType.UNKNOWN_FIELD
    
    def classify_dataframe_fields(self, df: pd.DataFrame, use_cache: bool = True) -> Dict[str, FieldType]:
        """
        分类DataFrame中所有字段的类型

        Args:
            df: 待分类的DataFrame
            use_cache: 是否使用缓存机制

        Returns:
            Dict[str, FieldType]: 字段名到字段类型的映射
        """
        # 检查缓存
        if use_cache:
            df_hash = self._get_dataframe_hash(df)
            if df_hash in self._classification_cache:
                self._cache_hits += 1
                cached_result = self._classification_cache[df_hash]
                # 只在首次缓存命中时输出日志，避免日志噪音
                if self._cache_hits == 1:
                    logger.debug(LogTarget.FILE, f"字段分类缓存机制已启用")
                return cached_result
            else:
                self._cache_misses += 1

        # 执行分类
        field_types = {}

        for column in df.columns:
            field_type = self.classify_field(column)
            field_types[column] = field_type

        # 只在首次分类时输出详细DEBUG日志
        if not use_cache or self._cache_misses == 1:
            logger.debug(LogTarget.FILE, f"执行字段分类，共 {len(df.columns)} 个字段")
            for column, field_type in field_types.items():
                logger.debug(LogTarget.FILE, f"字段 '{column}' 分类为: {field_type.value}")

        # 统计分类结果
        type_counts = {}
        for field_type in field_types.values():
            type_counts[field_type] = type_counts.get(field_type, 0) + 1

        # 只在首次分类时输出统计信息
        if not use_cache or self._cache_misses == 1:
            logger.info(LogTarget.FILE, f"字段分类完成，共 {len(df.columns)} 个字段:")
            for field_type, count in type_counts.items():
                logger.info(LogTarget.FILE, f"  {field_type.value}: {count} 个")

        # 保存到缓存
        if use_cache:
            self._classification_cache[df_hash] = field_types

        return field_types
    
    def get_fields_by_type(self, df: pd.DataFrame, field_type: FieldType) -> List[str]:
        """
        获取指定类型的字段列表
        
        Args:
            df: DataFrame
            field_type: 目标字段类型
            
        Returns:
            List[str]: 指定类型的字段名列表
        """
        field_types = self.classify_dataframe_fields(df)
        return [field for field, ftype in field_types.items() if ftype == field_type]
    
    def get_adjustment_fields(self, df: pd.DataFrame) -> List[str]:
        """
        获取需要复权调整的字段列表
        
        Args:
            df: DataFrame
            
        Returns:
            List[str]: 需要复权的字段名列表
        """
        return self.get_fields_by_type(df, FieldType.PRICE_FIELD)
    
    def validate_field_classification(self, df: pd.DataFrame) -> bool:
        """
        验证字段分类的合理性
        
        Args:
            df: DataFrame
            
        Returns:
            bool: 分类是否合理
        """
        field_types = self.classify_dataframe_fields(df)
        
        # 检查是否有未知字段
        unknown_fields = [field for field, ftype in field_types.items() 
                         if ftype == FieldType.UNKNOWN_FIELD]
        
        if unknown_fields:
            logger.warning(LogTarget.FILE, f"发现 {len(unknown_fields)} 个未知字段: {unknown_fields}")
            logger.warning(LogTarget.FILE, "建议手动确认这些字段的类型并更新分类规则")
            return False
        
        # 检查是否有价格字段
        price_fields = self.get_fields_by_type(df, FieldType.PRICE_FIELD)
        if not price_fields:
            logger.warning(LogTarget.FILE, "未发现任何价格字段，可能存在分类错误")
            return False
        
        logger.info(LogTarget.FILE, "字段分类验证通过")
        return True

    def get_cache_stats(self) -> Dict[str, int]:
        """
        获取缓存统计信息

        Returns:
            Dict[str, int]: 缓存统计信息
        """
        return {
            "cache_hits": self._cache_hits,
            "cache_misses": self._cache_misses,
            "cache_size": len(self._classification_cache),
            "hit_rate": self._cache_hits / (self._cache_hits + self._cache_misses) if (self._cache_hits + self._cache_misses) > 0 else 0
        }

    def clear_cache(self):
        """清空缓存"""
        self._classification_cache.clear()
        self._cache_hits = 0
        self._cache_misses = 0
        logger.debug(LogTarget.FILE, "字段分类缓存已清空")


# 全局分类器实例
field_classifier = FieldTypeClassifier()
