"""
复权数据处理模块

该模块提供完整的复权数据处理功能，包括：
1. 复权因子存储管理（基础数据，永久存储）
2. 前复权计算引擎（基于xtquant算法）
3. 复权价格缓存管理（派生数据，智能缓存）
4. 复权数据合成器（统一接口）

架构设计：
- 基础数据层：原始价格数据 + 复权因子数据（永久存储）
- 派生数据层：复权价格数据 + 技术指标数据（智能缓存）
- 数据管理层：统一访问接口，自动选择存储或缓存策略

使用示例：
    # 推荐使用统一接口
    from utils.data_processor.adjustment import adjustment_synthesizer

    # 合成前复权数据
    adjusted_data = adjustment_synthesizer.synthesize_adjusted_data(
        symbol="000001.SZ",
        price_data=raw_data,
        dividend_type="front"
    )

    # 直接使用底层组件
    from utils.data_processor.adjustment import dividend_factor_storage
    factors = dividend_factor_storage.query_dividend_factors("000001.SZ")
"""

from .dividend_factor_storage import DividendFactorStorage, dividend_factor_storage
from .forward_adjustment_engine import ForwardAdjustmentEngine, forward_adjustment_engine
from .adjustment_synthesizer import AdjustmentSynthesizer, adjustment_synthesizer

__all__ = [
    'DividendFactorStorage',
    'dividend_factor_storage',
    'ForwardAdjustmentEngine',
    'forward_adjustment_engine',
    'AdjustmentSynthesizer',
    'adjustment_synthesizer',
]

__version__ = '2.2.0'
