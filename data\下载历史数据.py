#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
历史数据下载模块

将交互界面的下载功能移动到此文件中，支持多周期配置
"""

import os
import sys
import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from data.core.operations import download_data
from utils.logger import get_unified_logger, LogTarget

logger = get_unified_logger(__name__)


def _create_initial_result_file(stock_list_file: str, result_file_path: str):
    """从股票列表文件创建初始结果文件（完整多周期格式）"""
    try:
        # 读取股票列表
        with open(stock_list_file, 'r', encoding='utf-8') as f:
            stock_lines = f.readlines()

        # 解析股票代码 - 使用专业文本解析工具处理带注释的代码
        from utils.text_parser import parse_stock_code_input

        stocks = []
        for line in stock_lines:
            line = line.strip()
            if line and not line.startswith('#'):
                # 使用专业解析函数处理可能包含注释的股票代码
                parsed_codes = parse_stock_code_input(line)
                stocks.extend(parsed_codes)

        # 定义支持的周期
        periods = [
            {"code": "1d", "name": "日线"},
            {"code": "1m", "name": "1分钟"},
            {"code": "tick", "name": "tick"}
        ]

        # 创建简化的多周期初始结果文件
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        with open(result_file_path, 'w', encoding='utf-8') as f:
            f.write(f"股票下载结果 - {timestamp}\n")
            f.write("="*40 + "\n\n")

            # 为每个周期创建初始的下载结果分段
            for period in periods:
                f.write(f"========== {period['name']}数据下载结果 ==========\n")

                # 写入各部分（初始状态：所有股票都是未下载）
                sections = [
                    ("下载成功的股票:", []),
                    ("下载失败的股票:", []),
                    ("未下载的股票:", sorted(stocks)),
                    ("无需下载的股票:", [])
                ]

                for title, stock_list in sections:
                    f.write(f"{title}\n")
                    for stock in stock_list:
                        f.write(f"{stock}\n")
                    f.write("\n")

                # 写入统计信息
                total = len(stocks)
                f.write(f"总计股票: {total}\n")
                f.write(f"下载成功: 0\n")
                f.write(f"下载失败: 0\n")
                f.write(f"未下载: {total}\n")
                f.write(f"无需下载: 0\n")
                f.write(f"最后更新时间: {timestamp}\n\n")

        print(f"✅ 已创建简化的多周期初始结果文件，包含 {len(stocks)} 只股票")
        print(f"📊 已为 {len(periods)} 个周期初始化下载状态（无冗余股票列表）")

    except Exception as e:
        print(f"❌ 创建初始结果文件失败: {e}")


def main():
    """主函数"""
    # ==================== 多周期下载配置 ====================
    """
    """
    # 不同周期的配置
    download_configs = [

        {
            "period": "1d",
            "period_name": "日线",
            "start_date": "",
            "end_date": "",
            "incremental": True,
            "display_head_rows": 5,
            "display_tail_rows": 5
        },
        {
            "period": "1m",
            "period_name": "1分钟",
            "start_date": "",
            "end_date": "",
            "incremental": True,
            "display_head_rows": 5,
            "display_tail_rows": 5
        },
        {
            "period": "tick",
            "period_name": "tick",
            "start_date": "",
            "end_date": "",
            "incremental": True,
            "display_head_rows": 5,
            "display_tail_rows": 5
        }
    ]

    # 通用下载选项
    show_data = True          # 是否显示数据预览
    real_time_log = False     # 是否实时日志
    delay_between_periods = 2 # 周期间延时（秒）

    # 股票代码来源配置
    result_file = "download_results.txt"  # 统一结果文件

    # ==================== 开始多周期下载 ====================

    total_periods = len(download_configs)
    print(f"\n🚀 开始批量下载数据，共 {total_periods} 个周期")

    # 显示股票代码文件路径信息
    from config.settings import DATA_ROOT
    result_file_path = os.path.join(DATA_ROOT, result_file)
    print(f"📋 统一结果文件: {result_file_path}")

    # 检查并创建初始结果文件
    if not os.path.exists(result_file_path):
        # 尝试从stock_list.txt创建初始结果文件
        stock_list_file = os.path.join(DATA_ROOT, "stock_list.txt")
        if os.path.exists(stock_list_file):
            print(f"📋 从股票列表文件创建初始结果文件: {stock_list_file}")
            _create_initial_result_file(stock_list_file, result_file_path)
        else:
            print(f"❌ 结果文件和股票列表文件都不存在")
            print(f"请创建 {stock_list_file} 文件并添加股票代码，或直接创建 {result_file_path}")
            return

    # 多周期下载循环
    for i, config in enumerate(download_configs, 1):
        period = config["period"]
        period_name = config["period_name"]
        start_date = config["start_date"]
        end_date = config["end_date"]
        incremental = config["incremental"]
        display_head_rows = config["display_head_rows"]
        display_tail_rows = config["display_tail_rows"]

        print(f"\n{'='*60}")
        print(f"📈 [{i}/{total_periods}] 开始下载 {period_name} 数据...")
        print(f"⏰ 时间范围: {start_date} ~ {end_date or '今天'}")
        print(f"📊 增量更新: {'是' if incremental else '否'}")
        print(f"📁 结果文件: {result_file} (周期: {period})")
        print(f"{'='*60}")

        # 调用现有的下载函数，使用统一的结果文件
        result = download_data(
            stocks=[],  # 此参数已废弃，系统会从结果文件读取
            period=period,
            start_date=start_date,
            end_date=end_date,
            incremental=incremental,
            show_data=show_data,
            real_time_log=real_time_log,
            display_head_rows=display_head_rows,
            display_tail_rows=display_tail_rows,
            result_file=result_file_path  # 使用统一的结果文件
        )

        # 处理结果
        if result:
            print(f"✅ {period_name}数据下载完成")
            logger.info(LogTarget.FILE, f"{period_name}数据下载完成")
        else:
            print(f"❌ {period_name}数据下载失败")
            logger.error(LogTarget.FILE, f"{period_name}数据下载失败")

        # 添加延时，避免请求过于频繁
        if i < total_periods:  # 最后一个周期不需要延时
            import time
            print(f"⏳ 等待{delay_between_periods}秒后继续下载下一个周期...")
            time.sleep(delay_between_periods)

    # 下载完成总结
    print(f"\n🎉 {'='*60}")
    print(f"🎉 所有数据下载完成！")
    print(f"📊 已下载周期: {', '.join([config['period_name'] for config in download_configs])}")
    print(f"📅 各周期时间范围:")
    for config in download_configs:
        start_date = config['start_date'] or "最早可用"
        end_date = config['end_date'] or "今天"
        print(f"   - {config['period_name']}: {start_date} ~ {end_date}")
    print(f"🎉 {'='*60}")


if __name__ == "__main__":
    main()
