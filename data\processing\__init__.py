#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一高性能数据处理模块

提供统一的数据处理接口和标准，包括：
- 向量化数据读取
- 统一数据处理框架
- 数据处理管道
- 性能监控和优化

核心特性：
- 基于DuckDB的高性能计算引擎
- 向量化操作，性能提升5-10倍
- 统一的API接口，简化使用
- 自动性能监控和优化建议

使用示例：
```python
from data.processing import load_data, create_pipeline

# 直接加载数据（向量化）
df = load_data(data_root, symbol, period)

# 使用管道处理数据
result = (create_pipeline()
    .load_data(data_root, symbol, period)
    .filter({'price': lambda x: x > 100})
    .aggregate(['date'], {'price': ['mean', 'max']})
    .execute())
```

作者: AI Assistant
创建时间: 2025-07-15
版本: 1.0.0
"""

# 导入核心组件
from data.processing.unified_data_framework import (
    UnifiedDataFramework,
    get_unified_framework
)

from data.processing.data_pipeline import (
    DataPipeline,
    create_pipeline,
    OperationType
)

from data.storage.vectorized_reader import (
    VectorizedDataReader,
    StreamingDataReader,
    get_vectorized_reader,
    read_partitioned_data_vectorized,
    read_partitioned_data_async
)

from data.processing.duckdb_diagnostics import (
    DuckDBDiagnostics,
    run_duckdb_diagnostics
)

# 便捷函数
def load_data(data_root: str, symbol: str, period: str,
              start_time: str = None,
              end_time: str = None,
              columns: list = None):
    """
    统一的数据加载接口（使用DuckDB向量化读取）

    Args:
        data_root: 数据根目录
        symbol: 股票代码
        period: 数据周期
        start_time: 开始时间
        end_time: 结束时间
        columns: 要读取的列

    Returns:
        pd.DataFrame: 加载的数据

    Raises:
        RuntimeError: 当数据加载失败时
    """
    framework = get_unified_framework()
    return framework.load_data(
        data_root, symbol, period, start_time, end_time, columns
    )


def execute_sql(sql: str, data=None):
    """
    执行SQL查询（便捷函数）
    
    Args:
        sql: SQL查询语句
        data: 可选的DataFrame数据
        
    Returns:
        pd.DataFrame: 查询结果
    """
    framework = get_unified_framework()
    return framework.execute_sql(sql, data)


def get_performance_stats():
    """
    获取整体性能统计信息
    
    Returns:
        dict: 性能统计信息
    """
    framework = get_unified_framework()
    reader = get_vectorized_reader()
    
    return {
        'framework_stats': framework.get_performance_stats(),
        'reader_stats': reader.get_performance_stats()
    }


def clear_all_cache():
    """
    清空所有缓存
    """
    framework = get_unified_framework()
    reader = get_vectorized_reader()
    
    framework.clear_cache()
    reader.clear_cache()


# 版本信息
__version__ = "1.0.0"
__author__ = "AI Assistant"

# 导出的公共接口
__all__ = [
    # 核心类
    'UnifiedDataFramework',
    'DataPipeline',
    'VectorizedDataReader',
    'StreamingDataReader',
    
    # 工厂函数
    'get_unified_framework',
    'get_vectorized_reader',
    'create_pipeline',
    
    # 便捷函数
    'load_data',
    'execute_sql',
    'read_partitioned_data_vectorized',
    'read_partitioned_data_async',

    # 工具函数
    'get_performance_stats',
    'clear_all_cache',

    # 调试工具
    'DuckDBDiagnostics',
    'run_duckdb_diagnostics',

    # 枚举
    'OperationType'
]
