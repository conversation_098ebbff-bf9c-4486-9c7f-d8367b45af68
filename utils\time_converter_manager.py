#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能时间转换器统一管理模块

提供全局配置、监控、统计和管理功能，实现真正的统一管理。
"""

import datetime
from typing import Dict, Any, List
from config.settings import (
    TIME_CONVERTER_CACHE_SIZE, TIME_CONVERTER_ENABLE_MONITORING,
    TIME_CONVERTER_ENABLE_WARNINGS, TIME_CONVERTER_BATCH_THRESHOLD,
    TIME_CONVERTER_MAX_CACHE_AGE
)
from utils.smart_time_converter import get_conversion_stats, reset_conversion_stats


class TimeConverterUnifiedManager:
    """智能时间转换器统一管理器"""

    def __init__(self):
        # 使用统一配置而不是独立的配置管理器
        self.config = {
            'cache_size': TIME_CONVERTER_CACHE_SIZE,
            'enable_monitoring': TIME_CONVERTER_ENABLE_MONITORING,
            'enable_warnings': TIME_CONVERTER_ENABLE_WARNINGS,
            'batch_threshold': TIME_CONVERTER_BATCH_THRESHOLD,
            'max_cache_age': TIME_CONVERTER_MAX_CACHE_AGE
        }
        self.start_time = datetime.datetime.now()
        self.replacement_log = []
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        # 获取转换统计
        conversion_stats = get_conversion_stats()
        
        # 获取性能统计
        performance_stats = get_performance_stats()
        
        # 计算运行时间
        uptime = datetime.datetime.now() - self.start_time
        
        return {
            'system_info': {
                'start_time': self.start_time,
                'uptime': str(uptime),
                'status': 'active'
            },
            'conversion_stats': conversion_stats,
            'performance_stats': performance_stats,
            'config': self.config_manager.get_config()
        }
    
    def get_usage_summary(self) -> Dict[str, Any]:
        """获取使用情况摘要"""
        stats = get_conversion_stats()
        
        if stats['total_calls'] == 0:
            return {
                'status': 'no_usage',
                'message': '尚未有转换调用'
            }
        
        # 计算使用分布
        total_calls = stats['total_calls']
        usage_distribution = {
            'millisecond_timestamps': {
                'count': stats['ms_conversions'],
                'percentage': (stats['ms_conversions'] / total_calls) * 100
            },
            'second_timestamps': {
                'count': stats['s_conversions'], 
                'percentage': (stats['s_conversions'] / total_calls) * 100
            },
            'string_times': {
                'count': stats['string_conversions'],
                'percentage': (stats['string_conversions'] / total_calls) * 100
            },
            'fallback_conversions': {
                'count': stats['fallback_conversions'],
                'percentage': (stats['fallback_conversions'] / total_calls) * 100
            }
        }
        
        # 计算错误率
        error_rate = (stats['errors'] / total_calls) * 100 if total_calls > 0 else 0
        
        return {
            'status': 'active',
            'total_conversions': total_calls,
            'error_rate': error_rate,
            'usage_distribution': usage_distribution,
            'most_used_type': max(usage_distribution.keys(), 
                                key=lambda k: usage_distribution[k]['count'])
        }
    
    def get_health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 测试基本功能
            from utils.smart_time_converter import smart_to_datetime
            
            # 测试毫秒时间戳
            test_ms = smart_to_datetime([**********000])
            
            # 测试秒时间戳
            test_s = smart_to_datetime([**********])
            
            # 测试字符串
            test_str = smart_to_datetime(['20250118080000'])
            
            # 获取统计信息
            stats = get_conversion_stats()
            
            health_status = {
                'overall_status': 'healthy',
                'components': {
                    'smart_converter': 'ok',
                    'millisecond_conversion': 'ok',
                    'second_conversion': 'ok', 
                    'string_conversion': 'ok',
                    'statistics_tracking': 'ok'
                },
                'test_results': {
                    'ms_test': str(test_ms[0]),
                    'second_test': str(test_s[0]),
                    'string_test': str(test_str[0])
                },
                'error_count': stats['errors']
            }
            
            return health_status
            
        except Exception as e:
            return {
                'overall_status': 'unhealthy',
                'error': str(e),
                'components': {
                    'smart_converter': 'error'
                }
            }
    
    def generate_comprehensive_report(self) -> str:
        """生成综合报告"""
        system_status = self.get_system_status()
        usage_summary = self.get_usage_summary()
        health_check = self.get_health_check()
        
        report = f"""
智能时间转换器综合管理报告
{'='*60}

系统状态:
  启动时间: {system_status['system_info']['start_time']}
  运行时间: {system_status['system_info']['uptime']}
  系统状态: {system_status['system_info']['status']}

使用情况摘要:
  总转换次数: {usage_summary.get('total_conversions', 0):,}
  错误率: {usage_summary.get('error_rate', 0):.2f}%
  最常用类型: {usage_summary.get('most_used_type', 'N/A')}

类型分布:"""
        
        if 'usage_distribution' in usage_summary:
            for type_name, info in usage_summary['usage_distribution'].items():
                report += f"\n  {type_name}: {info['count']} 次 ({info['percentage']:.1f}%)"
        
        report += f"""

健康检查:
  整体状态: {health_check['overall_status']}
  错误计数: {health_check.get('error_count', 0)}

组件状态:"""
        
        if 'components' in health_check:
            for component, status in health_check['components'].items():
                report += f"\n  {component}: {status}"
        
        report += f"""

测试结果:"""
        if 'test_results' in health_check:
            for test_name, result in health_check['test_results'].items():
                report += f"\n  {test_name}: {result}"
        
        # 添加性能报告
        try:
            perf_report = get_performance_report()
            report += f"\n\n{perf_report}"
        except:
            report += "\n\n性能报告: 暂无数据"
        
        report += f"""

配置信息:
  默认时区: {system_status['config'].get('default_timezone', 'N/A')}
  缓存大小: {system_status['config'].get('cache_size', 'N/A')}
  监控启用: {system_status['config'].get('enable_monitoring', 'N/A')}
  调试模式: {system_status['config'].get('debug_mode', 'N/A')}

报告生成时间: {datetime.datetime.now()}
"""
        
        return report
    
    def update_global_config(self, **kwargs):
        """更新全局配置"""
        self.config_manager.update_config(**kwargs)
        
        # 记录配置更改
        self.replacement_log.append({
            'timestamp': datetime.datetime.now(),
            'action': 'config_update',
            'changes': kwargs
        })
    
    def reset_all_statistics(self):
        """重置所有统计信息"""
        reset_conversion_stats()
        self.config_manager.reset_stats()
        
        # 记录重置操作
        self.replacement_log.append({
            'timestamp': datetime.datetime.now(),
            'action': 'statistics_reset'
        })
    
    def get_replacement_log(self) -> List[Dict]:
        """获取操作日志"""
        return self.replacement_log.copy()
    
    def export_configuration(self) -> Dict[str, Any]:
        """导出配置"""
        return {
            'config': self.config_manager.get_config(),
            'statistics': get_conversion_stats(),
            'export_time': datetime.datetime.now().isoformat()
        }
    
    def import_configuration(self, config_data: Dict[str, Any]):
        """导入配置"""
        if 'config' in config_data:
            for key, value in config_data['config'].items():
                self.config_manager.update_config(**{key: value})
        
        # 记录导入操作
        self.replacement_log.append({
            'timestamp': datetime.datetime.now(),
            'action': 'config_import',
            'source': config_data.get('export_time', 'unknown')
        })


# 全局统一管理器实例
_unified_manager = TimeConverterUnifiedManager()


def get_unified_manager() -> TimeConverterUnifiedManager:
    """获取全局统一管理器"""
    return _unified_manager


def get_system_status() -> Dict[str, Any]:
    """获取系统状态"""
    return _unified_manager.get_system_status()


def get_usage_summary() -> Dict[str, Any]:
    """获取使用摘要"""
    return _unified_manager.get_usage_summary()


def get_health_check() -> Dict[str, Any]:
    """健康检查"""
    return _unified_manager.get_health_check()


def generate_management_report() -> str:
    """生成管理报告"""
    return _unified_manager.generate_comprehensive_report()


def update_global_config(**kwargs):
    """更新全局配置"""
    _unified_manager.update_global_config(**kwargs)


def reset_all_statistics():
    """重置所有统计"""
    _unified_manager.reset_all_statistics()


# 便捷函数
def quick_status_check():
    """快速状态检查"""
    health = get_health_check()
    usage = get_usage_summary()
    
    print(f"智能转换器状态: {health['overall_status']}")
    print(f"总转换次数: {usage.get('total_conversions', 0):,}")
    print(f"错误率: {usage.get('error_rate', 0):.2f}%")
    
    if health['overall_status'] != 'healthy':
        print(f"⚠️ 发现问题: {health.get('error', '未知错误')}")
    else:
        print("✅ 系统运行正常")


if __name__ == '__main__':
    # 生成管理报告
    report = generate_management_report()
    print(report)
    
    # 快速状态检查
    print("\n" + "="*60)
    quick_status_check()
