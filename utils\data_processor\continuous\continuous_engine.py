#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
期货连续化计算引擎

负责期货连续化数据的计算处理，包括：
1. 主力连续化算法实现
2. 加权连续化算法实现
3. 比例调整和差值调整方法
4. 连续化数据验证

连续化算法：
- 主力连续：基于主力合约切换的连续化处理
- 加权连续：基于持仓量加权的连续化处理
- 比例调整：按价格比例调整历史数据
- 差值调整：按价格差值调整历史数据
"""

import pandas as pd
import numpy as np
from typing import Optional, Dict, Any, List, Literal
from datetime import datetime

from utils.logger.manager import get_unified_logger

logger = get_unified_logger(__name__)


class ContinuousEngine:
    """期货连续化计算引擎"""
    
    def __init__(self):
        """初始化连续化计算引擎"""
        logger.info("期货连续化计算引擎初始化完成")
    
    def calculate_main_continuous(
        self, 
        price_data: pd.DataFrame, 
        continuous_factors: pd.DataFrame,
        method: Literal["ratio", "diff"] = "ratio"
    ) -> pd.DataFrame:
        """计算主力连续数据
        
        Args:
            price_data: 原始价格数据
            continuous_factors: 连续化因子数据
            method: 调整方法，"ratio"（比例调整）或"diff"（差值调整）
            
        Returns:
            主力连续化后的价格数据
        """
        try:
            if price_data.empty:
                logger.warning("价格数据为空，返回空DataFrame")
                return price_data.copy()
            
            if continuous_factors.empty:
                logger.info("连续化因子数据为空，返回原始价格数据")
                return price_data.copy()
            
            logger.debug(f"开始主力连续化计算，价格数据: {len(price_data)} 行，连续化因子: {len(continuous_factors)} 行")
            
            # 复制数据避免修改原始数据
            result = price_data.copy()
            
            if method == "ratio":
                result = self._apply_ratio_adjustment(result, continuous_factors)
            elif method == "diff":
                result = self._apply_diff_adjustment(result, continuous_factors)
            else:
                logger.error(f"不支持的连续化方法: {method}")
                return price_data.copy()
            
            logger.info(f"主力连续化计算完成，方法: {method}，处理数据量: {len(result)}")
            return result
            
        except Exception as e:
            logger.error(f"主力连续化计算失败: {e}")
            return price_data.copy()
    
    def _apply_ratio_adjustment(self, price_data: pd.DataFrame, continuous_factors: pd.DataFrame) -> pd.DataFrame:
        """应用比例调整方法
        
        Args:
            price_data: 价格数据
            continuous_factors: 连续化因子数据
            
        Returns:
            比例调整后的数据
        """
        try:
            result = price_data.copy()
            
            # 按切换日期排序
            factors_sorted = continuous_factors.sort_values('switch_date')
            
            # 对每个切换点进行比例调整
            for _, factor_row in factors_sorted.iterrows():
                switch_date = factor_row['switch_date']
                ratio_factor = factor_row['ratio_factor']
                
                # 调整切换日期之前的所有数据
                mask = result.index < switch_date
                
                # 对价格相关列进行比例调整
                price_columns = ['open', 'high', 'low', 'close', 'lastPrice']
                for col in price_columns:
                    if col in result.columns:
                        result.loc[mask, col] = result.loc[mask, col] * ratio_factor
            
            return result
            
        except Exception as e:
            logger.error(f"比例调整失败: {e}")
            return price_data
    
    def _apply_diff_adjustment(self, price_data: pd.DataFrame, continuous_factors: pd.DataFrame) -> pd.DataFrame:
        """应用差值调整方法
        
        Args:
            price_data: 价格数据
            continuous_factors: 连续化因子数据
            
        Returns:
            差值调整后的数据
        """
        try:
            result = price_data.copy()
            
            # 按切换日期排序
            factors_sorted = continuous_factors.sort_values('switch_date')
            
            # 对每个切换点进行差值调整
            for _, factor_row in factors_sorted.iterrows():
                switch_date = factor_row['switch_date']
                diff_factor = factor_row['diff_factor']
                
                # 调整切换日期之前的所有数据
                mask = result.index < switch_date
                
                # 对价格相关列进行差值调整
                price_columns = ['open', 'high', 'low', 'close', 'lastPrice']
                for col in price_columns:
                    if col in result.columns:
                        result.loc[mask, col] = result.loc[mask, col] + diff_factor
            
            return result
            
        except Exception as e:
            logger.error(f"差值调整失败: {e}")
            return price_data
    
    def calculate_weighted_continuous(
        self, 
        contracts_data: Dict[str, pd.DataFrame],
        weights_data: Dict[str, pd.DataFrame]
    ) -> pd.DataFrame:
        """计算加权连续数据
        
        Args:
            contracts_data: 各合约价格数据字典 {合约代码: 价格数据}
            weights_data: 各合约权重数据字典 {合约代码: 权重数据}
            
        Returns:
            加权连续化后的价格数据
        """
        try:
            if not contracts_data:
                logger.warning("合约数据为空，返回空DataFrame")
                return pd.DataFrame()
            
            logger.debug(f"开始加权连续化计算，合约数量: {len(contracts_data)}")
            
            # 获取所有时间索引的并集
            all_indices = set()
            for contract_data in contracts_data.values():
                all_indices.update(contract_data.index)
            
            all_indices = sorted(all_indices)
            
            # 初始化结果DataFrame
            result_columns = ['open', 'high', 'low', 'close', 'volume', 'amount']
            result = pd.DataFrame(index=all_indices, columns=result_columns)
            
            # 对每个时间点计算加权平均
            for timestamp in all_indices:
                weighted_values = {}
                total_weight = 0
                
                for contract, contract_data in contracts_data.items():
                    if timestamp in contract_data.index and contract in weights_data:
                        weight_data = weights_data[contract]
                        if timestamp in weight_data.index:
                            weight = weight_data.loc[timestamp, 'openInterest']  # 使用持仓量作为权重
                            if weight > 0:
                                for col in result_columns:
                                    if col in contract_data.columns:
                                        value = contract_data.loc[timestamp, col]
                                        if col not in weighted_values:
                                            weighted_values[col] = 0
                                        
                                        if col in ['volume', 'amount']:
                                            # 成交量和成交额直接累加
                                            weighted_values[col] += value
                                        else:
                                            # 价格按权重加权
                                            weighted_values[col] += value * weight
                                
                                total_weight += weight
                
                # 计算加权平均（价格类数据）
                if total_weight > 0:
                    for col in ['open', 'high', 'low', 'close']:
                        if col in weighted_values:
                            result.loc[timestamp, col] = weighted_values[col] / total_weight
                    
                    # 直接使用累加值（成交量和成交额）
                    for col in ['volume', 'amount']:
                        if col in weighted_values:
                            result.loc[timestamp, col] = weighted_values[col]
            
            logger.info(f"加权连续化计算完成，处理数据量: {len(result)}")
            return result
            
        except Exception as e:
            logger.error(f"加权连续化计算失败: {e}")
            return pd.DataFrame()
    
    def calculate_continuous_factors(
        self, 
        old_contract_data: pd.DataFrame,
        new_contract_data: pd.DataFrame,
        switch_date: datetime
    ) -> Dict[str, float]:
        """计算连续化因子
        
        Args:
            old_contract_data: 旧合约数据
            new_contract_data: 新合约数据
            switch_date: 切换日期
            
        Returns:
            连续化因子字典
        """
        try:
            # 获取切换日期的价格
            old_price = self._get_price_at_date(old_contract_data, switch_date)
            new_price = self._get_price_at_date(new_contract_data, switch_date)
            
            if old_price is None or new_price is None:
                logger.warning(f"无法获取切换日期 {switch_date} 的价格数据")
                return {'ratio_factor': 1.0, 'diff_factor': 0.0}
            
            # 计算比例因子和差值因子
            ratio_factor = new_price / old_price if old_price != 0 else 1.0
            diff_factor = new_price - old_price
            
            logger.debug(f"连续化因子计算完成: ratio={ratio_factor:.6f}, diff={diff_factor:.2f}")
            
            return {
                'ratio_factor': ratio_factor,
                'diff_factor': diff_factor
            }
            
        except Exception as e:
            logger.error(f"连续化因子计算失败: {e}")
            return {'ratio_factor': 1.0, 'diff_factor': 0.0}
    
    def _get_price_at_date(self, data: pd.DataFrame, target_date: datetime) -> Optional[float]:
        """获取指定日期的价格
        
        Args:
            data: 价格数据
            target_date: 目标日期
            
        Returns:
            价格值，如果没有找到则返回None
        """
        try:
            if data.empty:
                return None
            
            # 尝试直接匹配
            if target_date in data.index:
                return data.loc[target_date, 'close']
            
            # 寻找最接近的日期
            closest_date = min(data.index, key=lambda x: abs((x - target_date).total_seconds()))
            return data.loc[closest_date, 'close']
            
        except Exception as e:
            logger.error(f"获取指定日期价格失败: {e}")
            return None
