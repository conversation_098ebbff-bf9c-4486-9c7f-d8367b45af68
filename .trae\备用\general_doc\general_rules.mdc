---
description: 
globs: "*"
alwaysApply: true
---
# 通用规则

- AI助手在制定和更新所有规则时，应以通用性为主要目标。
- 特定于某个项目的具体规则或配置，应放置在该项目位于 `.cursor/rules/` 下对应的项目专属文件夹中（例如 `.cursor/rules/{项目名称}/project_specific_rules.mdc`）。
- 项目的主说明文档 (README) **已统一存放在 `.cursor/rules/quant_project/docs` 目录中** (例如 `.cursor/rules/quant_project/docs/project_main_README.mdc`)。AI助手应从此位置高效地获取项目信息作为上下文。AI规则目录外的其他规则文档和项目根目录下的配置文件本身除外。

## 文档同步与维护
- **命令行接口变更与文档更新**：当项目的命令行接口（CLI）发生任何修改（如参数增删、行为变更等）后，AI助手应主动、及时地更新 `.cursor/rules/ai_command_rules/` 目录下对应的规则文档，以确保AI调用指令的准确性。
- **项目变更与文档同步**：对于项目中任何可能影响AI助手理解或操作的变更（不仅限于CLI），都应及时维护和同步更新所有相关的规则文档（包括项目README、特定模块规范等），确保文档始终反映项目的最新状态。

## AI驱动的文档自动化更新与维护规则

本规则旨在指导AI助手实现项目文档的自动化更新与维护，确保文档与项目代码的实时同步，提升AI辅助开发的效率和准确性。

### 1. AI代码变更后的强制操作

#### 1.0 本次会话/操作批次变更记录 (Current Interaction Change Record)
- AI助手在处理每个独立的用户请求或执行一个操作序列（视为一个'会话'或'操作批次'）时，应在内部维护一个临时的、仅针对当前会话/批次的变更记录。
- 此记录应清晰、准确地列出在本次会话/批次中所有对代码或配置所做的修改（例如：修改了哪个文件、函数签名如何变化、配置项的新旧值等）。
- **这份'本次会话/操作批次变更记录'是AI执行后续文档即时更新（见1.2）的唯一信息来源。**
- 在新的会话/操作批次开始时，这份临时记录应被视为已清空或被新的内容覆盖，确保其始终只反映最近一次操作批次的变更。

#### 1.2 对应文档的即时更新
- 在AI助手对代码或配置执行了修改后，它**必须立即、主动地**依据最新的【1.0 本次会话/操作批次变更记录】中所记载的内容，并结合对当前项目实际代码状态的分析，识别并更新所有受这些最新变更直接影响的相关文档。
- AI在执行此更新时，**严禁参考**任何历史变更日志或此前任何已完成的会话/操作批次的记录。
- 更新范围包括但不限于：模块的README.md、API文档、功能说明、使用示例、`.cursor/rules/ai_command_rules/`下的命令规则文档（若命令行接口发生变化）、以及存放在 `.cursor/rules/quant_project/docs/` 目录中的对应模块文档或项目主README。
- 更新内容应准确反映代码或配置的最新状态，确保文档的实时性和准确性。例如，若函数的参数发生变更，则必须同步更新该函数在API文档和命令规则文档中的参数列表和说明。
- **当AI助手修改了配置文件（例如 `config/settings.py`, `pyproject.toml`, `requirements.txt` 等），并且这些修改直接导致了项目行为、功能默认值、依赖关系或构建部署方式的改变时，AI助手必须评估这些变更对现有说明文档（如项目主README、模块说明文档、安装指南等）的潜在影响。**
- **如果评估认为需要更新说明文档以反映配置文件的变更，AI助手应主动提出文档修改建议或直接执行更新（在获得用户确认后，如果适用交互规则），以确保文档描述与项目实际状态一致。**
- 文档更新完成后，AI助手必须向用户发出明确提示，告知已完成对相关文档的更新，并简要说明更新的文档和主要变更点。

### 2. 文档组织与命名规范

#### 2.1 集中存储
- 项目的所有核心文档（包括**项目主README文件**、模块级README、变更日志等），**必须**统一存放在 `.cursor/rules/quant_project/docs/` 目录中。
- `.cursor/rules/` 目录下的其他AI规则文件（如 `general_rules.mdc`, `ai_interaction_rules.mdc` 等，不包括 `.cursor/rules/quant_project/docs` 内的文件）和项目根目录下的配置文件（如 `pyproject.toml`, `.gitignore` 等）是此规则的例外，它们应保留在各自的约定位置。
- AI助手在创建或查找核心项目文档时，应默认操作此 `.cursor/rules/quant_project/docs/` 目录。

#### 2.2 文档命名规范
- 存放在 `@docs` 目录中的文档，其文件名**必须**以其所属的模块名（或功能领域名）作为前缀，并清晰标明文档类型。格式：`模块名_文档类型.md` (或相应格式)。
- 示例：`data_fetcher_api_reference.md`, `strategy_module_design_spec.md`, `backtest_engine_user_guide.md`。
- 对于项目级别的通用文档（不特定于某个模块），可以以 `project_` 或类似通用前缀命名，例如 `project_architecture_overview.md`。

### 3. AI执行文档更新的原则

#### 3.1 基于最新变更、当前代码状态与用户指令的更新原则
- 在AI助手根据【1.0 本次会话/操作批次变更记录】识别出因当前交互操作而需要更新的文档范围后，当生成具体的文档更新内容时，AI助手**必须**优先分析和依赖【当前项目的实际代码状态】和【用户在当前交互中的明确意图和指令】。
- **非常重要：** AI助手**严禁**仅凭历史变更记录、过时的文档内容或对话上下文来更新文档。**必须**通过实时检查项目文件（例如，读取目标文件内容）来验证代码/配置的**最新状态**，确保文档更新反映的是项目的**实际情况**，避免因信息不对称（如用户本地修改、删除文件但未更新文档）导致错误。
- AI不应简单地将"本次会话/操作批次变更记录"中的内容直接复制到文档中，而是要结合实际情况进行智能更新。
- 例如，如果"本次会话/操作批次变更记录"指示某个函数参数已更改，AI在更新文档时，应检查该函数在代码中的当前实际签名，并确保文档准确反映此签名。
- AI在更新文档时，应聚焦于准确反映由"本次会话/操作批次变更记录"所代表的、在当前交互中发生的有效代码变更，以及用户的明确指令。
- 所有由AI助手生成的文档修改建议，或计划执行的自动更新，都必须在执行前得到用户的明确确认（遵循 `.cursor/rules/ai_interaction_rules.mdc` 中关于操作确认的流程）。这为用户提供了一个审查机会，以防止基于错误理解或不完整信息的修改。

#### 3.2 保持风格一致性
- 更新文档时，应尽量保持与现有文档的风格、格式和术语一致。

#### 3.3 准确性和完整性
- 确保更新的内容准确无误，完整反映变更。如果AI对某些变更的文档影响不确定，应标记出来或请求人类协助。

#### 3.4 遵循现有规则
- 文档更新过程中，仍需遵循项目中已定义的其他相关规则，如 `ai_interaction_rules.mdc` 中关于操作确认的流程（如果适用）以及本 `general_rules.mdc` 中的通用原则。
