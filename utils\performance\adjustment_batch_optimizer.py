#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
复权数据批量处理优化器

利用新路径格式的优势，实现高效的批量复权数据处理：
1. 按复权类型批量处理
2. 智能缓存策略
3. 并行处理优化
4. 访问模式监控

作者: Augment AI
日期: 2025-01-29
"""

import os
import time
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
from collections import defaultdict
import pandas as pd

from utils.logger import get_unified_logger, LogTarget
from config.settings import DATA_ROOT

# 获取统一日志记录器
logger = get_unified_logger()


class AdjustmentBatchOptimizer:
    """复权数据批量处理优化器"""
    
    def __init__(self, data_root: str = None):
        """
        初始化优化器
        
        Args:
            data_root: 数据根目录
        """
        self.data_root = Path(data_root or DATA_ROOT)
        self.adjusted_root = self.data_root / "adjusted"
        
        # 访问统计
        self.access_stats = {
            "front": {"count": 0, "total_time": 0.0, "files": []},
            "back": {"count": 0, "total_time": 0.0, "files": []}
        }
        
        logger.info(LogTarget.FILE, f"复权数据批量优化器初始化完成: {self.data_root}")
    
    def scan_adjustment_data_by_type(self, adj_type: str) -> Dict[str, List[str]]:
        """
        按复权类型扫描数据文件
        
        Args:
            adj_type: 复权类型 ("front" 或 "back")
            
        Returns:
            按股票分组的文件列表
        """
        logger.info(LogTarget.FILE, f"扫描 {adj_type} 复权数据...")
        
        adj_type_dir = self.adjusted_root / adj_type
        if not adj_type_dir.exists():
            logger.warning(LogTarget.FILE, f"复权类型目录不存在: {adj_type_dir}")
            return {}
        
        files_by_symbol = defaultdict(list)
        
        # 新格式：adjusted/{adj_type}/{market}/{code}/{period}/*.parquet
        for market_dir in adj_type_dir.iterdir():
            if not market_dir.is_dir():
                continue
                
            market = market_dir.name
            
            for code_dir in market_dir.iterdir():
                if not code_dir.is_dir():
                    continue
                    
                code = code_dir.name
                symbol = f"{code}.{market}"
                
                for period_dir in code_dir.iterdir():
                    if not period_dir.is_dir():
                        continue
                        
                    period = period_dir.name
                    
                    # 查找parquet文件
                    for parquet_file in period_dir.glob("*.parquet"):
                        file_info = {
                            "path": str(parquet_file),
                            "period": period,
                            "size": parquet_file.stat().st_size,
                            "mtime": parquet_file.stat().st_mtime
                        }
                        files_by_symbol[symbol].append(file_info)
        
        logger.info(LogTarget.FILE, f"扫描完成，{adj_type} 复权数据: {len(files_by_symbol)} 只股票")
        return dict(files_by_symbol)
    
    def batch_load_by_adjustment_type(self, adj_type: str, symbols: Optional[List[str]] = None, 
                                    periods: Optional[List[str]] = None) -> Dict[str, pd.DataFrame]:
        """
        按复权类型批量加载数据
        
        Args:
            adj_type: 复权类型
            symbols: 股票代码列表，None表示加载所有
            periods: 周期列表，None表示加载所有
            
        Returns:
            加载的数据字典 {symbol_period: DataFrame}
        """
        start_time = time.time()
        logger.info(LogTarget.FILE, f"开始批量加载 {adj_type} 复权数据...")
        
        # 扫描可用数据
        available_data = self.scan_adjustment_data_by_type(adj_type)
        
        if not available_data:
            logger.warning(LogTarget.FILE, f"没有找到 {adj_type} 复权数据")
            return {}
        
        # 过滤数据
        if symbols:
            available_data = {s: files for s, files in available_data.items() if s in symbols}
        
        loaded_data = {}
        total_files = 0
        
        for symbol, files in available_data.items():
            for file_info in files:
                # 过滤周期
                if periods and file_info["period"] not in periods:
                    continue
                
                try:
                    df = pd.read_parquet(file_info["path"])
                    key = f"{symbol}_{file_info['period']}"
                    loaded_data[key] = df
                    total_files += 1
                    
                    logger.debug(LogTarget.FILE, f"加载成功: {key} ({len(df)} 行)")
                    
                except Exception as e:
                    logger.error(LogTarget.FILE, f"加载失败: {file_info['path']} - {e}")
        
        # 更新访问统计
        end_time = time.time()
        duration = end_time - start_time
        
        self.access_stats[adj_type]["count"] += 1
        self.access_stats[adj_type]["total_time"] += duration
        self.access_stats[adj_type]["files"].extend([f["path"] for files in available_data.values() for f in files])
        
        logger.info(LogTarget.FILE, f"批量加载完成: {total_files} 个文件，耗时 {duration:.2f} 秒")
        return loaded_data
    
    def parallel_process_by_type(self, adj_type: str, process_func, max_workers: int = 4, 
                               **kwargs) -> Dict[str, any]:
        """
        按复权类型并行处理数据
        
        Args:
            adj_type: 复权类型
            process_func: 处理函数，接收 (symbol, file_info) 参数
            max_workers: 最大并发数
            **kwargs: 传递给处理函数的额外参数
            
        Returns:
            处理结果字典
        """
        logger.info(LogTarget.FILE, f"开始并行处理 {adj_type} 复权数据，并发数: {max_workers}")
        
        # 扫描数据
        available_data = self.scan_adjustment_data_by_type(adj_type)
        
        if not available_data:
            logger.warning(LogTarget.FILE, f"没有找到 {adj_type} 复权数据")
            return {}
        
        results = {}
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有处理任务
            future_to_key = {}
            
            for symbol, files in available_data.items():
                for file_info in files:
                    key = f"{symbol}_{file_info['period']}"
                    future = executor.submit(process_func, symbol, file_info, **kwargs)
                    future_to_key[future] = key
            
            # 处理完成的任务
            for future in as_completed(future_to_key):
                key = future_to_key[future]
                try:
                    result = future.result()
                    results[key] = result
                    logger.debug(LogTarget.FILE, f"处理完成: {key}")
                except Exception as e:
                    logger.error(LogTarget.FILE, f"处理失败: {key} - {e}")
                    results[key] = {"error": str(e)}
        
        logger.info(LogTarget.FILE, f"并行处理完成: {len(results)} 个结果")
        return results
    
    def get_access_statistics(self) -> Dict[str, any]:
        """获取访问统计信息"""
        stats = {}
        
        for adj_type, data in self.access_stats.items():
            if data["count"] > 0:
                avg_time = data["total_time"] / data["count"]
                unique_files = len(set(data["files"]))
                
                stats[adj_type] = {
                    "access_count": data["count"],
                    "total_time": data["total_time"],
                    "average_time": avg_time,
                    "unique_files_accessed": unique_files,
                    "total_file_accesses": len(data["files"])
                }
            else:
                stats[adj_type] = {
                    "access_count": 0,
                    "total_time": 0.0,
                    "average_time": 0.0,
                    "unique_files_accessed": 0,
                    "total_file_accesses": 0
                }
        
        return stats
    
    def optimize_cache_strategy(self) -> Dict[str, str]:
        """
        基于访问模式优化缓存策略
        
        Returns:
            优化建议
        """
        stats = self.get_access_statistics()
        recommendations = {}
        
        for adj_type, data in stats.items():
            if data["access_count"] == 0:
                recommendations[adj_type] = "未使用，可考虑冷存储"
            elif data["average_time"] > 5.0:  # 平均访问时间超过5秒
                recommendations[adj_type] = "访问较慢，建议增加缓存"
            elif data["access_count"] > 10:  # 访问频繁
                recommendations[adj_type] = "访问频繁，建议预加载到内存"
            else:
                recommendations[adj_type] = "访问正常，保持当前策略"
        
        return recommendations
    
    def cleanup_unused_data(self, days_threshold: int = 30, dry_run: bool = True) -> Dict[str, int]:
        """
        清理未使用的复权数据
        
        Args:
            days_threshold: 天数阈值，超过此天数未访问的文件将被清理
            dry_run: 是否模拟运行
            
        Returns:
            清理统计
        """
        logger.info(LogTarget.FILE, f"开始清理未使用的复权数据，阈值: {days_threshold} 天，模拟: {dry_run}")
        
        cleanup_stats = {"front": 0, "back": 0}
        current_time = time.time()
        threshold_seconds = days_threshold * 24 * 3600
        
        for adj_type in ["front", "back"]:
            adj_type_dir = self.adjusted_root / adj_type
            if not adj_type_dir.exists():
                continue
            
            for parquet_file in adj_type_dir.rglob("*.parquet"):
                try:
                    # 检查文件最后访问时间
                    file_stat = parquet_file.stat()
                    if current_time - file_stat.st_atime > threshold_seconds:
                        if dry_run:
                            logger.debug(LogTarget.FILE, f"模拟删除: {parquet_file}")
                        else:
                            parquet_file.unlink()
                            logger.debug(LogTarget.FILE, f"已删除: {parquet_file}")
                        
                        cleanup_stats[adj_type] += 1
                        
                except Exception as e:
                    logger.error(LogTarget.FILE, f"处理文件失败: {parquet_file} - {e}")
        
        logger.info(LogTarget.FILE, f"清理完成: front={cleanup_stats['front']}, back={cleanup_stats['back']}")
        return cleanup_stats


def example_process_function(symbol: str, file_info: Dict[str, any], **kwargs) -> Dict[str, any]:
    """示例处理函数"""
    try:
        df = pd.read_parquet(file_info["path"])
        
        # 示例处理：计算基本统计信息
        stats = {
            "symbol": symbol,
            "period": file_info["period"],
            "rows": len(df),
            "columns": len(df.columns),
            "file_size": file_info["size"],
            "date_range": {
                "start": df.index.min() if not df.empty else None,
                "end": df.index.max() if not df.empty else None
            }
        }
        
        return stats
        
    except Exception as e:
        return {"error": str(e)}


if __name__ == "__main__":
    # 示例使用
    optimizer = AdjustmentBatchOptimizer()
    
    # 扫描前复权数据
    front_data = optimizer.scan_adjustment_data_by_type("front")
    print(f"前复权数据: {len(front_data)} 只股票")
    
    # 获取访问统计
    stats = optimizer.get_access_statistics()
    print(f"访问统计: {stats}")
    
    # 获取优化建议
    recommendations = optimizer.optimize_cache_strategy()
    print(f"优化建议: {recommendations}")
