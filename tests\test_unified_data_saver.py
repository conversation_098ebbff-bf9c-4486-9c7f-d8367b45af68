#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一数据保存器测试

测试统一数据保存器的功能完整性和跨日期数据分组修复效果。
"""

import pandas as pd
import numpy as np
from datetime import datetime
import sys
import os
import tempfile
import shutil

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data.storage.unified_data_saver import (
    get_unified_data_saver,
    save_data_unified,
    SaveStrategy,
    SaveConfig,
    SaveConfigs
)


def test_unified_saver_cross_date():
    """测试统一数据保存器的跨日期数据处理"""
    print("=== 测试统一数据保存器跨日期数据处理 ===")
    
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 创建跨日期测试数据（模拟复权数据场景）
        cross_date_data = pd.DataFrame({
            'time': [
                1752562262000,  # 2025-07-15 14:51:02
                1752562265000,  # 2025-07-15 14:51:05
                1752562268000,  # 2025-07-15 14:51:08
                1752629698000,  # 2025-07-16 09:34:58
                1752629701000,  # 2025-07-16 09:35:01
                1752629704000   # 2025-07-16 09:35:04
            ],
            'lastPrice': [13.53, 13.54, 13.55, 13.45, 13.46, 13.47],
            'open': [13.74, 13.74, 13.74, 13.54, 13.54, 13.54],
            'high': [13.79, 13.79, 13.79, 13.60, 13.60, 13.60],
            'low': [13.43, 13.43, 13.43, 13.45, 13.45, 13.45],
            'volume': [100, 200, 300, 400, 500, 600]
        })
        
        print(f"测试数据行数: {len(cross_date_data)}")
        
        # 使用统一数据保存器保存复权数据，强制使用多分区策略测试跨日期分组
        result = save_data_unified(
            df=cross_date_data,
            data_root=temp_dir,
            symbol="600000.SH",
            period="tick",
            strategy=SaveStrategy.MULTI_PARTITION,  # 强制使用多分区策略
            parallel=True,
            data_type="adjusted",
            adj_type="front"
        )
        
        print(f"保存结果: {result.success}")
        print(f"使用策略: {result.strategy_used.value}")
        print(f"保存分区数: {len(result.saved_partitions)}")
        print(f"处理时间: {result.processing_time:.3f}秒")
        
        if result.success:
            print("保存的分区:")
            for date_str, path in result.saved_partitions.items():
                print(f"  {date_str}: {path}")
                
                # 验证文件是否存在
                if os.path.exists(path):
                    # 读取并验证数据
                    saved_data = pd.read_parquet(path)
                    print(f"    文件存在，数据行数: {len(saved_data)}")
                else:
                    print(f"    文件不存在: {path}")
            
            # 验证是否正确分组
            expected_dates = ["20250715", "20250716"]
            actual_dates = list(result.saved_partitions.keys())
            
            if len(actual_dates) == 2 and all(date in actual_dates for date in expected_dates):
                print("✅ 跨日期数据分组测试通过")
                return True
            else:
                print(f"❌ 分组结果不正确，期望: {expected_dates}, 实际: {actual_dates}")
                return False
        else:
            print(f"❌ 保存失败: {result.error_message}")
            return False
            
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)


def test_strategy_selection():
    """测试策略自动选择"""
    print("\n=== 测试策略自动选择 ===")
    
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 测试小数据量（应该选择单分区策略）
        small_data = pd.DataFrame({
            'time': [1752562262000, 1752562265000],
            'price': [13.53, 13.54]
        })
        
        result_small = save_data_unified(
            df=small_data,
            data_root=temp_dir,
            symbol="600000.SH",
            period="1d",  # 非tick数据
            strategy=SaveStrategy.AUTO
        )
        
        print(f"小数据量策略: {result_small.strategy_used.value}")
        
        # 测试大数据量tick数据（应该选择并行策略）
        large_data = pd.DataFrame({
            'time': np.arange(1752562262000, 1752562262000 + 60000 * 3000, 3000),  # 60000条数据
            'price': np.random.uniform(13.0, 14.0, 60000)
        })
        
        result_large = save_data_unified(
            df=large_data,
            data_root=temp_dir,
            symbol="600000.SH",
            period="tick",
            strategy=SaveStrategy.AUTO,
            parallel=True
        )
        
        print(f"大数据量策略: {result_large.strategy_used.value}")
        
        if (result_small.success and result_large.success and 
            result_small.strategy_used != result_large.strategy_used):
            print("✅ 策略自动选择测试通过")
            return True
        else:
            print("❌ 策略自动选择测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        return False
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)


def test_predefined_configs():
    """测试预定义配置"""
    print("\n=== 测试预定义配置 ===")
    
    try:
        # 测试tick数据配置
        tick_config = SaveConfigs.for_tick_data(parallel=True)
        print(f"Tick数据配置: strategy={tick_config.strategy.value}, parallel={tick_config.parallel}")
        
        # 测试复权数据配置
        adj_config = SaveConfigs.for_adjusted_data("front", parallel=True)
        print(f"复权数据配置: data_type={adj_config.data_type}, adj_type={adj_config.adj_type}")
        
        # 测试大数据集配置
        large_config = SaveConfigs.for_large_dataset(max_workers=8)
        print(f"大数据集配置: strategy={large_config.strategy.value}, max_workers={large_config.max_workers}")
        
        # 测试单分区配置
        single_config = SaveConfigs.for_single_partition()
        print(f"单分区配置: strategy={single_config.strategy.value}, parallel={single_config.parallel}")
        
        print("✅ 预定义配置测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        return False


def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 测试空数据
        empty_data = pd.DataFrame()
        result_empty = save_data_unified(
            df=empty_data,
            data_root=temp_dir,
            symbol="600000.SH",
            period="tick"
        )
        
        print(f"空数据处理: success={result_empty.success}, error={result_empty.error_message}")
        
        # 测试无效配置
        try:
            invalid_config = SaveConfig(data_type="adjusted", adj_type=None)
            print("❌ 无效配置未被检测到")
            return False
        except ValueError as e:
            print(f"✅ 无效配置正确检测: {e}")
        
        if not result_empty.success and result_empty.error_message:
            print("✅ 错误处理测试通过")
            return True
        else:
            print("❌ 错误处理测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        return False
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)


def run_all_tests():
    """运行所有测试"""
    print("开始统一数据保存器测试...")
    
    tests = [
        test_unified_saver_cross_date,
        test_strategy_selection,
        test_predefined_configs,
        test_error_handling
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试执行失败: {e}")
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！统一数据保存器功能完整！")
        return True
    else:
        print("⚠️ 部分测试失败")
        return False


if __name__ == "__main__":
    run_all_tests()
