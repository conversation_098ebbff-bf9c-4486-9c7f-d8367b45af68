# 增量更新路径匹配修复说明

## 问题背景

在量化交易系统的数据合成功能中，增量更新机制存在路径匹配问题，导致复权数据的增量更新无法正常工作。

## 问题分析

### 第一阶段问题（已修复）
**问题**：增量更新检测时路径不匹配
- 检测逻辑在 `raw/` 目录查找已有数据
- 实际数据保存在 `adjusted/front/` 目录
- 导致增量更新检测失败，每次都进行全量合成

**解决方案**：修改 `period_handler.py` 中的增量检测逻辑，传递正确的复权参数

### 第二阶段问题（本次修复）
**问题**：增量更新数据读取时路径不匹配
- `ParquetStorage.incremental_update` 方法读取现有数据时未传递 `dividend_type` 参数
- `VectorizedDataReader` 硬编码使用 `data_type="raw"`
- 导致在 `raw/` 目录查找数据，而实际数据在 `adjusted/front/` 目录

## 修复方案

### 1. ParquetStorage 参数转换
- 添加 `_convert_to_dividend_type` 方法（复用 `unified_data_accessor` 实现）
- 在 `incremental_update` 方法中转换参数并传递给数据读取函数

### 2. VectorizedDataReader 路径修复
- 移除硬编码的 `data_type="raw"`
- 根据 `dividend_type` 参数动态确定 `data_type` 和 `adj_type`
- 确保文件查找使用正确的路径参数

## 修复效果

- ✅ 增量更新检测正确：能够找到已有的复权合成数据
- ✅ 数据读取路径正确：从正确的复权目录读取现有数据
- ✅ 路径参数一致：保存路径与读取路径完全匹配
- ✅ 性能提升显著：增量合成比例达到 100%

## 技术要点

### DRY 原则应用
- 复用 `unified_data_accessor.py` 中的参数转换逻辑
- 避免重复实现相同的转换功能

### 参数转换逻辑
```python
def _convert_to_dividend_type(self, data_type: str, adj_type: Optional[str]) -> str:
    if data_type == "raw":
        return "none"
    elif data_type == "adjusted":
        if adj_type == "front":
            return "front"
        elif adj_type == "back":
            return "back"
        else:
            return "front"  # 默认前复权
    else:
        return "none"  # 默认原始数据
```

### 路径匹配确保
- 保存时：`data_type="adjusted", adj_type="front"` → `adjusted/front/` 目录
- 读取时：`dividend_type="front"` → `data_type="adjusted", adj_type="front"` → `adjusted/front/` 目录

## 相关文件

- `data/storage/parquet_storage.py`：增量更新主逻辑
- `data/storage/vectorized_reader.py`：数据读取器
- `utils/data_processor/period_handler.py`：合成主流程
- `data/storage/unified_data_accessor.py`：参数转换参考实现

## 测试验证

通过 `tests/test_incremental_update_fix.py` 验证修复效果：
- 增量更新检测正常
- 数据读取路径正确
- 路径参数传递一致
- 性能提升明显
