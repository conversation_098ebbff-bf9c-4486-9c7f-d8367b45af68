#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
技术指标功能模块

提供各种技术分析指标的计算功能
"""

import os
import sys
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Set, Tuple, Union, Callable, Any

# 将项目根目录添加到Python路径
root_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, root_path)

# 导入日志模块
from utils.logger import get_unified_logger

# 设置日志记录器
logger = get_unified_logger(__name__)

# 常量定义
PRICE_COLUMNS = ['open', 'high', 'low', 'close', 'adj_close']
VOLUME_COLUMN = 'volume'


def add_moving_average(df: pd.DataFrame, windows: List[int] = [5, 10, 20, 60],
                     price_col: str = 'close', inplace: bool = False) -> pd.DataFrame:
    """
    添加简单移动平均线
    
    Args:
        df: 输入DataFrame
        windows: 窗口大小列表，默认为[5, 10, 20, 60]
        price_col: 价格列名，默认为'close'
        inplace: 是否就地修改DataFrame，默认为False
        
    Returns:
        添加移动平均线后的DataFrame
    """
    if df.empty:
        logger.warning("输入的DataFrame为空")
        return df
    
    # 检查价格列是否存在
    if price_col not in df.columns:
        logger.error(f"价格列 '{price_col}' 不存在于DataFrame中")
        return df
    
    # 创建副本（如果不是就地操作）
    result = df if inplace else df.copy()
    
    # 添加各个窗口的移动平均线
    for window in windows:
        result[f'ma_{window}'] = result[price_col].rolling(window=window).mean()
    
    logger.info(f"已添加 {len(windows)} 个移动平均线")
    return result


def add_exponential_moving_average(df: pd.DataFrame, windows: List[int] = [5, 10, 20, 60],
                                 price_col: str = 'close', inplace: bool = False) -> pd.DataFrame:
    """
    添加指数移动平均线
    
    Args:
        df: 输入DataFrame
        windows: 窗口大小列表，默认为[5, 10, 20, 60]
        price_col: 价格列名，默认为'close'
        inplace: 是否就地修改DataFrame，默认为False
        
    Returns:
        添加指数移动平均线后的DataFrame
    """
    if df.empty:
        logger.warning("输入的DataFrame为空")
        return df
    
    # 检查价格列是否存在
    if price_col not in df.columns:
        logger.error(f"价格列 '{price_col}' 不存在于DataFrame中")
        return df
    
    # 创建副本（如果不是就地操作）
    result = df if inplace else df.copy()
    
    # 添加各个窗口的指数移动平均线
    for window in windows:
        # 计算alpha参数，alpha = 2 / (span + 1)
        alpha = 2 / (window + 1)
        result[f'ema_{window}'] = result[price_col].ewm(alpha=alpha, adjust=False).mean()
    
    logger.info(f"已添加 {len(windows)} 个指数移动平均线")
    return result


def add_macd(df: pd.DataFrame, fast_period: int = 12, slow_period: int = 26,
           signal_period: int = 9, price_col: str = 'close',
           inplace: bool = False) -> pd.DataFrame:
    """
    添加MACD指标（移动平均收敛散度）
    
    Args:
        df: 输入DataFrame
        fast_period: 快线周期，默认为12
        slow_period: 慢线周期，默认为26
        signal_period: 信号线周期，默认为9
        price_col: 价格列名，默认为'close'
        inplace: 是否就地修改DataFrame，默认为False
        
    Returns:
        添加MACD指标后的DataFrame
    """
    if df.empty:
        logger.warning("输入的DataFrame为空")
        return df
    
    # 检查价格列是否存在
    if price_col not in df.columns:
        logger.error(f"价格列 '{price_col}' 不存在于DataFrame中")
        return df
    
    # 创建副本（如果不是就地操作）
    result = df if inplace else df.copy()
    
    # 计算快线EMA
    fast_alpha = 2 / (fast_period + 1)
    fast_ema = result[price_col].ewm(alpha=fast_alpha, adjust=False).mean()
    
    # 计算慢线EMA
    slow_alpha = 2 / (slow_period + 1)
    slow_ema = result[price_col].ewm(alpha=slow_alpha, adjust=False).mean()
    
    # 计算MACD线（快线EMA - 慢线EMA）
    result['macd_line'] = fast_ema - slow_ema
    
    # 计算信号线（MACD线的EMA）
    signal_alpha = 2 / (signal_period + 1)
    result['macd_signal'] = result['macd_line'].ewm(alpha=signal_alpha, adjust=False).mean()
    
    # 计算MACD柱状图（MACD线 - 信号线）
    result['macd_histogram'] = result['macd_line'] - result['macd_signal']
    
    logger.info(f"已添加MACD指标 (快线={fast_period}, 慢线={slow_period}, 信号线={signal_period})")
    return result


def add_rsi(df: pd.DataFrame, periods: List[int] = [6, 14, 24],
          price_col: str = 'close', inplace: bool = False) -> pd.DataFrame:
    """
    添加相对强弱指标（RSI）
    
    Args:
        df: 输入DataFrame
        periods: 周期列表，默认为[6, 14, 24]
        price_col: 价格列名，默认为'close'
        inplace: 是否就地修改DataFrame，默认为False
        
    Returns:
        添加RSI指标后的DataFrame
    """
    if df.empty:
        logger.warning("输入的DataFrame为空")
        return df
    
    # 检查价格列是否存在
    if price_col not in df.columns:
        logger.error(f"价格列 '{price_col}' 不存在于DataFrame中")
        return df
    
    # 创建副本（如果不是就地操作）
    result = df if inplace else df.copy()
    
    # 计算价格变化
    delta = result[price_col].diff()
    
    # 添加各个周期的RSI
    for period in periods:
        # 分离上涨和下跌
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        
        # 计算平均上涨和下跌
        avg_gain = gain.rolling(window=period).mean()
        avg_loss = loss.rolling(window=period).mean()
        
        # 计算相对强度
        rs = avg_gain / avg_loss
        
        # 计算RSI
        result[f'rsi_{period}'] = 100 - (100 / (1 + rs))
    
    logger.info(f"已添加 {len(periods)} 个RSI指标")
    return result


def add_bollinger_bands(df: pd.DataFrame, window: int = 20, num_std: float = 2.0,
                      price_col: str = 'close', inplace: bool = False) -> pd.DataFrame:
    """
    添加布林带指标
    
    Args:
        df: 输入DataFrame
        window: 移动平均窗口大小，默认为20
        num_std: 标准差倍数，默认为2.0
        price_col: 价格列名，默认为'close'
        inplace: 是否就地修改DataFrame，默认为False
        
    Returns:
        添加布林带指标后的DataFrame
    """
    if df.empty:
        logger.warning("输入的DataFrame为空")
        return df
    
    # 检查价格列是否存在
    if price_col not in df.columns:
        logger.error(f"价格列 '{price_col}' 不存在于DataFrame中")
        return df
    
    # 创建副本（如果不是就地操作）
    result = df if inplace else df.copy()
    
    # 计算中轨（简单移动平均线）
    mid_band = result[price_col].rolling(window=window).mean()
    result[f'bb_mid_{window}'] = mid_band
    
    # 计算标准差
    rolling_std = result[price_col].rolling(window=window).std()
    
    # 计算上轨（中轨 + num_std * 标准差）
    result[f'bb_upper_{window}'] = mid_band + (rolling_std * num_std)
    
    # 计算下轨（中轨 - num_std * 标准差）
    result[f'bb_lower_{window}'] = mid_band - (rolling_std * num_std)
    
    # 计算带宽（(上轨 - 下轨) / 中轨）
    result[f'bb_width_{window}'] = (result[f'bb_upper_{window}'] - result[f'bb_lower_{window}']) / mid_band
    
    # 计算%B指标（(价格 - 下轨) / (上轨 - 下轨)）
    result[f'bb_b_{window}'] = (result[price_col] - result[f'bb_lower_{window}']) / \
                          (result[f'bb_upper_{window}'] - result[f'bb_lower_{window}'])
    
    logger.info(f"已添加布林带指标 (窗口={window}, 标准差倍数={num_std})")
    return result


def add_stochastic_oscillator(df: pd.DataFrame, k_period: int = 14, d_period: int = 3,
                            inplace: bool = False) -> pd.DataFrame:
    """
    添加随机振荡器（KD指标）
    
    Args:
        df: 输入DataFrame
        k_period: %K周期，默认为14
        d_period: %D周期（%K的移动平均），默认为3
        inplace: 是否就地修改DataFrame，默认为False
        
    Returns:
        添加随机振荡器后的DataFrame
    """
    if df.empty:
        logger.warning("输入的DataFrame为空")
        return df
    
    # 检查必要的列是否存在
    required_cols = ['high', 'low', 'close']
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        logger.error(f"缺少必要的列: {missing_cols}")
        return df
    
    # 创建副本（如果不是就地操作）
    result = df if inplace else df.copy()
    
    # 计算每个周期内的最高价和最低价
    low_min = result['low'].rolling(window=k_period).min()
    high_max = result['high'].rolling(window=k_period).max()
    
    # 计算%K（快速随机指标）
    # %K = (当前收盘价 - 最低价) / (最高价 - 最低价) * 100
    result['stoch_k'] = ((result['close'] - low_min) / (high_max - low_min)) * 100
    
    # 计算%D（慢速随机指标，%K的移动平均）
    result['stoch_d'] = result['stoch_k'].rolling(window=d_period).mean()
    
    logger.info(f"已添加随机振荡器 (K周期={k_period}, D周期={d_period})")
    return result


def add_obv(df: pd.DataFrame, price_col: str = 'close', volume_col: str = 'volume',
          inplace: bool = False) -> pd.DataFrame:
    """
    添加能量潮指标（On-Balance Volume, OBV）
    
    Args:
        df: 输入DataFrame
        price_col: 价格列名，默认为'close'
        volume_col: 成交量列名，默认为'volume'
        inplace: 是否就地修改DataFrame，默认为False
        
    Returns:
        添加OBV指标后的DataFrame
    """
    if df.empty:
        logger.warning("输入的DataFrame为空")
        return df
    
    # 检查必要的列是否存在
    if price_col not in df.columns:
        logger.error(f"价格列 '{price_col}' 不存在于DataFrame中")
        return df
    if volume_col not in df.columns:
        logger.error(f"成交量列 '{volume_col}' 不存在于DataFrame中")
        return df
    
    # 创建副本（如果不是就地操作）
    result = df if inplace else df.copy()
    
    # 计算价格变化
    price_change = result[price_col].diff()
    
    # 根据价格变化确定OBV的变化方向
    obv_change = np.where(price_change > 0, result[volume_col],
                         np.where(price_change < 0, -result[volume_col], 0))
    
    # 计算累积OBV
    result['obv'] = pd.Series(obv_change).cumsum()
    
    logger.info("已添加能量潮指标 (OBV)")
    return result


def add_atr(df: pd.DataFrame, period: int = 14, inplace: bool = False) -> pd.DataFrame:
    """
    添加平均真实范围指标（Average True Range, ATR）
    
    Args:
        df: 输入DataFrame
        period: ATR计算周期，默认为14
        inplace: 是否就地修改DataFrame，默认为False
        
    Returns:
        添加ATR指标后的DataFrame
    """
    if df.empty:
        logger.warning("输入的DataFrame为空")
        return df
    
    # 检查必要的列是否存在
    required_cols = ['high', 'low', 'close']
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        logger.error(f"缺少必要的列: {missing_cols}")
        return df
    
    # 创建副本（如果不是就地操作）
    result = df if inplace else df.copy()
    
    # 计算真实范围
    result['tr1'] = result['high'] - result['low']  # 当日价格范围
    result['tr2'] = abs(result['high'] - result['close'].shift(1))  # 当日最高价与昨日收盘价的差
    result['tr3'] = abs(result['low'] - result['close'].shift(1))   # 当日最低价与昨日收盘价的差
    
    # 计算真实范围（三者中的最大值）
    result['tr'] = result[['tr1', 'tr2', 'tr3']].max(axis=1)
    
    # 计算ATR（真实范围的移动平均）
    result[f'atr_{period}'] = result['tr'].rolling(window=period).mean()
    
    # 删除临时列
    result.drop(columns=['tr1', 'tr2', 'tr3', 'tr'], inplace=True)
    
    logger.info(f"已添加平均真实范围指标 (ATR, 周期={period})")
    return result