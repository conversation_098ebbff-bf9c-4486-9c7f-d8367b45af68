
from xtquant import xtdata
import time

def do_subscribe_quote(stock_list:list, period:str):
  for i in stock_list:
    xtdata.subscribe_quote(i,period = period)
  time.sleep(1) # 等待订阅完成

def trade_calendar(stock_list:list, period:str, start_time:str, end_time:str, count:int = 0):
  data2 = xtdata.get_market_data_ex([],stock_list,period = period, start_time = start_time, end_time = end_time, count = count) # count 设置为1，使返回值只包含最新行情
  
  return data2


if __name__ == "__main__":

  start_date = "20250601"# 格式"YYYYMMDD"，开始下载的日期，date = ""时全量下载
  end_date = "" 
  period = "1d" 

  need_download = 1  # 取数据是空值时，将need_download赋值为1，确保正确下载了历史数据
  
  code_list = ["000001.SZ"] # 股票列表
  ############ 仅获取最新行情 #####################
  do_subscribe_quote(code_list,period)# 设置订阅参数，使gmd_ex取到最新行情
  count = 1 # 设置count参数，使gmd_ex仅返回最新行情数据

  data2 = trade_calendar(code_list,period,start_date,end_date,count)
  print(f"data2: {data2[code_list[0]].tail()}")


