#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间戳修复验证测试

验证重构后的时间戳处理系统是否正确解决了分区路径日期错误问题。
"""

import pandas as pd
import numpy as np
from datetime import datetime
import sys
import os
import tempfile
import shutil

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data.storage.parquet_storage import save_to_partition
from utils.path_manager import build_partitioned_path


def test_save_to_partition_with_data_extraction():
    """测试save_to_partition的数据驱动时间戳提取"""
    print("=== 测试save_to_partition数据驱动时间戳提取 ===")
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 创建测试数据（2025年7月15日的数据）
        test_data = pd.DataFrame({
            'time': [1752562262000, 1752562265000, 1752562268000],  # 2025年7月15日
            'lastPrice': [13.53, 13.54, 13.55],
            'volume': [100, 200, 300]
        })
        
        # 测试不传递timestamp参数的情况
        success = save_to_partition(
            df=test_data,
            data_root=temp_dir,
            symbol="600000.SH",
            period="tick",
            timestamp=None,  # 不传递timestamp，让系统自动提取
            data_type="adjusted",
            adj_type="front"
        )
        
        if success:
            # 检查生成的文件路径
            expected_path = os.path.join(temp_dir, "adjusted", "front", "SH", "600000", "tick", "2025", "07", "15.parquet")
            
            if os.path.exists(expected_path):
                print(f"✅ 文件保存到正确路径: {expected_path}")
                print("✅ 数据驱动时间戳提取测试通过")
                return True
            else:
                print(f"❌ 文件未保存到预期路径: {expected_path}")
                # 列出实际生成的文件
                for root, dirs, files in os.walk(temp_dir):
                    for file in files:
                        actual_path = os.path.join(root, file)
                        print(f"实际文件路径: {actual_path}")
                return False
        else:
            print("❌ 数据保存失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        return False
    finally:
        # 清理临时目录
        shutil.rmtree(temp_dir, ignore_errors=True)


def test_build_partitioned_path_with_dataframe():
    """测试build_partitioned_path的DataFrame支持"""
    print("\n=== 测试build_partitioned_path的DataFrame支持 ===")
    
    try:
        # 创建测试数据
        test_data = pd.DataFrame({
            'time': [1752562262000, 1752562265000],  # 2025年7月15日
            'price': [13.53, 13.54]
        })
        
        # 测试数据驱动的路径构建
        path = build_partitioned_path(
            symbol="600000.SH",
            period="tick",
            timestamp=None,
            data_type="adjusted",
            adj_type="front",
            data_frame=test_data
        )
        
        print(f"生成的路径: {path}")
        
        # 验证路径是否包含正确的日期
        if "2025/07/15.parquet" in path.replace("\\", "/"):
            print("✅ 路径包含正确的日期")
            print("✅ DataFrame驱动路径构建测试通过")
            return True
        else:
            print("❌ 路径日期不正确")
            return False
            
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        return False


def test_timestamp_fallback():
    """测试时间戳备选机制"""
    print("\n=== 测试时间戳备选机制 ===")
    
    try:
        # 创建没有时间列的测试数据
        test_data = pd.DataFrame({
            'price': [13.53, 13.54],
            'volume': [100, 200]
        })
        
        # 测试备选时间戳
        path = build_partitioned_path(
            symbol="600000.SH",
            period="tick",
            timestamp="20250805",  # 明确指定时间戳
            data_type="adjusted",
            adj_type="front",
            data_frame=test_data
        )
        
        print(f"备选时间戳生成的路径: {path}")
        
        # 验证路径是否使用了指定的时间戳
        if "2025/08/05.parquet" in path.replace("\\", "/"):
            print("✅ 正确使用了备选时间戳")
            print("✅ 时间戳备选机制测试通过")
            return True
        else:
            print("❌ 备选时间戳未正确使用")
            return False
            
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        return False


def test_complex_data_scenario():
    """测试复杂数据场景"""
    print("\n=== 测试复杂数据场景 ===")
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 创建跨日期的测试数据
        test_data = pd.DataFrame({
            'time': [
                1752562262000,  # 2025-07-15 14:51:02
                1752629698000   # 2025-07-16 09:34:58
            ],
            'lastPrice': [13.53, 13.45],
            'volume': [100, 200]
        })
        
        # 测试跨日期数据的处理
        success = save_to_partition(
            df=test_data,
            data_root=temp_dir,
            symbol="600000.SH",
            period="tick",
            timestamp=None,
            data_type="adjusted",
            adj_type="front"
        )
        
        if success:
            print("✅ 跨日期数据保存成功")
            
            # 检查生成的文件
            file_count = 0
            for root, dirs, files in os.walk(temp_dir):
                file_count += len(files)
                for file in files:
                    file_path = os.path.join(root, file)
                    print(f"生成文件: {file_path}")
            
            if file_count > 0:
                print("✅ 复杂数据场景测试通过")
                return True
            else:
                print("❌ 未生成任何文件")
                return False
        else:
            print("❌ 跨日期数据保存失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        return False
    finally:
        # 清理临时目录
        shutil.rmtree(temp_dir, ignore_errors=True)


def run_all_tests():
    """运行所有测试"""
    print("开始时间戳修复验证测试...")
    
    tests = [
        test_save_to_partition_with_data_extraction,
        test_build_partitioned_path_with_dataframe,
        test_timestamp_fallback,
        test_complex_data_scenario
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试执行失败: {e}")
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！时间戳修复成功！")
        return True
    else:
        print("⚠️ 部分测试失败")
        return False


if __name__ == "__main__":
    run_all_tests()
