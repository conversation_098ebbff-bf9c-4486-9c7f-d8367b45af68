#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
日志处理器功能模块

提供各种日志处理器和上下文管理器

TODO: 此模块目前尚未完成实现。计划添加以下功能：
1. 自定义的轮转文件处理器，支持更灵活的文件命名和轮转策略
2. 网络日志处理器，支持将日志发送到远程服务器
3. 数据库日志处理器，支持将日志存储到数据库
4. 上下文管理器，用于在上下文中临时修改日志级别或目标
5. 缓冲处理器，用于在内存中缓存日志，然后根据条件批量写入
6. JSON格式日志处理器，输出结构化的JSON格式日志

如果不需要这些额外功能，可以考虑删除此文件，并使用Python标准库中的日志处理器。
"""

import os
import sys
import gzip
import logging
import logging.handlers
from typing import Optional

# 将项目根目录添加到Python路径
# Get the directory of the current file (handlers.py)
current_dir = os.path.dirname(os.path.abspath(__file__))  # utils/logger
# Get the parent directory (utils)
parent_dir = os.path.dirname(current_dir)  # utils
# Get the grandparent directory (project root)
root_path = os.path.dirname(parent_dir)  # quant
sys.path.insert(0, root_path)


class RotatingFileHandlerWithCompression(logging.handlers.RotatingFileHandler):
    """
    带压缩功能的轮转文件处理器
    
    扩展了标准的RotatingFileHandler，增加了对轮转文件的自动压缩功能。
    当日志文件达到指定大小并轮转时，会自动将旧的日志文件压缩为gzip格式。
    """
    
    def __init__(
        self, 
        filename: str, 
        mode: str = 'a', 
        maxBytes: int = 0, 
        backupCount: int = 0, 
        encoding: Optional[str] = None, 
        delay: bool = False, 
        compress: bool = True
    ):
        """
        初始化带压缩功能的轮转文件处理器
        
        Args:
            filename: 日志文件路径
            mode: 文件打开模式，默认为'a'（追加）
            maxBytes: 单个日志文件的最大字节数，默认为0（不限制）
            backupCount: 保留的备份文件数量，默认为0（不保留）
            encoding: 文件编码，默认为None（使用系统默认编码）
            delay: 是否延迟打开文件，默认为False
            compress: 是否压缩轮转的日志文件，默认为True
        """
        super().__init__(
            filename, 
            mode, 
            maxBytes, 
            backupCount, 
            encoding, 
            delay
        )
        self.compress = compress
    
    def doRollover(self) -> None:
        """
        执行日志文件轮转
        
        重写父类方法，增加对轮转文件的压缩功能
        """
        # 关闭当前日志文件
        if self.stream:
            self.stream.close()
            self.stream = None
            
        # 如果备份数量为0，直接删除当前日志文件
        if self.backupCount == 0:
            os.remove(self.baseFilename)
        else:
            # 删除最老的日志文件（如果存在）
            to_delete = self.baseFilename + ".{0}.gz".format(self.backupCount)
            if os.path.exists(to_delete):
                os.remove(to_delete)
                
            # 轮转备份文件
            for i in range(self.backupCount - 1, 0, -1):
                sfn = self.baseFilename + ".{0}.gz".format(i)
                dfn = self.baseFilename + ".{0}.gz".format(i + 1)
                if os.path.exists(sfn):
                    if os.path.exists(dfn):
                        os.remove(dfn)
                    os.rename(sfn, dfn)
            
            # 重命名当前日志文件
            dfn = self.baseFilename + ".1"
            if os.path.exists(dfn):
                os.remove(dfn)
            os.rename(self.baseFilename, dfn)
            
            # 压缩轮转后的日志文件
            if self.compress:
                self._compress_log_file(dfn)
        
        # 创建新的日志文件
        if not self.delay:
            self.stream = self._open()
    
    def _compress_log_file(self, file_path: str) -> None:
        """
        压缩日志文件
        
        Args:
            file_path: 要压缩的文件路径
        """
        try:
            # 读取原始文件内容
            with open(file_path, 'rb') as f_in:
                data = f_in.read()
            
            # 压缩并写入新文件
            with gzip.open(file_path + '.gz', 'wb') as f_out:
                f_out.write(data)
            
            # 删除原始文件
            os.remove(file_path)
        except Exception as e:
            # 压缩失败时记录错误，但不影响日志系统正常运行
            sys.stderr.write(f"压缩日志文件失败: {e}\n")
