#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
迅投数据读取模块

等待 xtdata_数据补充.py 完成数据补充后，直接读取数据返回给上层调用。
主要功能：
1. 等待数据补充完成
2. 读取本地数据并返回
"""

import os
import sys
import time
from datetime import datetime, timedelta
from typing import Dict, List
import pandas as pd
from utils.smart_time_converter import smart_to_datetime

# 将项目根目录添加到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))  # data/source
parent_dir = os.path.dirname(current_dir)  # data
root_path = os.path.dirname(parent_dir)  # quant
sys.path.insert(0, root_path)

# 导入项目配置
from config.settings import DATA_ROOT  # noqa: E402

# 使用统一的日志系统
from utils.logger import get_unified_logger, LogTarget  # noqa: E402

# 获取日志记录器
logger = get_unified_logger(__name__)

# 导入迅投API
try:
    import xtquant.xtdata as xtdata  # noqa: E402
except ImportError:
    logger.error(LogTarget.FILE, "无法导入迅投API，请确保已正确安装xtquant包")
    xtdata = None


def get_local_data_last_date(symbols: List[str], period: str) -> Dict[str, str]:
    """
    获取本地数据的最后日期
    
    Args:
        symbols: 股票代码列表
        period: 数据周期
        
    Returns:
        Dict[str, str]: 股票代码到最后日期的映射
    """
    last_dates = {}
    
    for symbol in symbols:
        try:
            # 获取最后一条数据
            data = xtdata.get_local_data(
                stock_list=[symbol],
                period=period,
                count=1
            )
            
            if symbol in data and data[symbol] is not None and not data[symbol].empty:
                df = data[symbol]

                # 使用统一日期提取模块获取最后一条数据的时间
                from utils.time_formatter.date_extraction import extract_timestamp_from_data

                timestamp_str = extract_timestamp_from_data(df)
                if timestamp_str and len(timestamp_str) >= 8:
                    last_date = timestamp_str[:8]  # 只取日期部分
                    last_dates[symbol] = last_date
                    logger.info(f"股票 {symbol} 最后数据日期: {last_date}")
                else:
                    logger.warning(f"股票 {symbol} 数据中没有找到有效的时间信息")
                    continue
                
        except Exception as e:
            logger.warning(f"获取股票 {symbol} 最后数据日期时出错: {e}")
            continue
    
    return last_dates


class XTQuantDataFetcher:
    """迅投数据获取器 - 简化版"""
    
    def __init__(self, data_root: str = DATA_ROOT):
        """初始化数据获取器"""
        self.data_root = data_root
        
    def download_history_data(self,
                              symbols: List[str],
                              period: str,
                              start_time: str,
                              end_time: str,
                              dividend_type: str = "none") -> Dict[str, pd.DataFrame]:
        """
        等待数据补充完成后读取数据返回
        
        Args:
            symbols: 股票代码列表
            period: 数据周期
            start_time: 开始时间，格式 YYYYMMDD
            end_time: 结束时间，格式 YYYYMMDD
            dividend_type: 复权类型
            
        Returns:
            Dict[str, pd.DataFrame]: 数据字典
        """
        logger.info(f"等待数据补充完成: {symbols}, 周期: {period}")
        logger.info(f"时间范围: {start_time} - {end_time}")
        
        # 解析请求的结束日期
        try:
            if end_time:
                request_end_date = datetime.strptime(end_time[:8], "%Y%m%d").date()
            else:
                request_end_date = datetime.now().date()
        except ValueError:
            logger.warning(f"无法解析结束日期 {end_time}，使用当前日期")
            request_end_date = datetime.now().date()
        
        # 循环60次检查数据是否补充完成
        max_checks = 60
        check_interval = 1  # 每秒检查一次
        
        for check_count in range(1, max_checks + 1):
            logger.info(f"第{check_count}次检查数据补充状态...")
            
            # 获取本地数据最后日期
            last_dates = get_local_data_last_date(symbols, period)
            
            # 检查所有股票的数据是否都已补充完成
            all_completed = True
            for symbol in symbols:
                if symbol not in last_dates:
                    logger.info(f"股票 {symbol} 暂无本地数据")
                    all_completed = False
                    continue
                
                try:
                    last_date = datetime.strptime(last_dates[symbol], "%Y%m%d").date()
                    date_diff = (request_end_date - last_date).days
                    
                    if date_diff <= 15:
                        logger.info(f"股票 {symbol} 数据已补充完成，最后日期: {last_dates[symbol]}, 距离请求日期: {date_diff}天")
                    else:
                        logger.info(f"股票 {symbol} 数据未补充完成，最后日期: {last_dates[symbol]}, 距离请求日期: {date_diff}天")
                        all_completed = False
                        
                except ValueError:
                    logger.warning(f"无法解析股票 {symbol} 的最后日期: {last_dates[symbol]}")
                    all_completed = False
            
            # 如果所有数据都已补充完成，跳出循环
            if all_completed:
                logger.info("所有股票数据已补充完成！")
                break
            
            # 如果还未完成，等待一段时间后继续检查
            if check_count < max_checks:
                logger.info(f"等待 {check_interval} 秒后继续检查...")
                time.sleep(check_interval)
        
        # 检查完成后，读取数据
        logger.info("开始读取本地数据...")
        
        try:
            data = xtdata.get_local_data(
                stock_list=symbols,
                period=period,
                start_time=start_time,
                end_time=end_time,
                dividend_type=dividend_type
            )
            
            # 统计数据情况
            if data:
                success_count = sum(1 for df in data.values() 
                                   if df is not None and not 
                                   (isinstance(df, pd.DataFrame) and df.empty))
                logger.info(f"成功读取 {success_count}/{len(symbols)} 个股票的数据")
                
                # 显示数据行数信息
                for symbol, df in data.items():
                    if isinstance(df, pd.DataFrame) and not df.empty:
                        logger.info(f"{symbol} 数据行数: {len(df)}")
            else:
                logger.warning("未读取到任何数据")
                data = {}
            
            return data
            
        except Exception as e:
            logger.error(f"读取本地数据时发生错误: {e}", exc_info=True)
            return {}


# 创建全局实例
xtquant_fetcher = XTQuantDataFetcher()


if __name__ == "__main__":
    # 测试功能
    print("xtquant_data.py - 数据读取模块")
    print("等待 xtdata_数据补充.py 完成数据补充后读取数据")
    
    # 测试获取本地数据最后日期
    test_symbols = ["600000.SH", "000001.SZ"]
    last_dates = get_local_data_last_date(test_symbols, "1d")
    print(f"测试获取最后日期: {last_dates}")
    
    # 测试数据读取功能
    if last_dates:
        print("测试数据读取功能...")
        data = xtquant_fetcher.download_history_data(
            symbols=["600000.SH"],
            period="1d", 
            start_time="20250101",
            end_time="20250707"
        )
        if data:
            for symbol, df in data.items():
                if isinstance(df, pd.DataFrame) and not df.empty:
                    print(f"{symbol} 数据行数: {len(df)}")
                    if hasattr(df, 'index') and len(df.index) > 0:
                        print(f"数据时间范围: {df.index[0]} 到 {df.index[-1]}")
