#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
路径管理功能模块

提供项目路径管理、标准化和解析等功能
"""

import os
import sys
import platform
from typing import Dict, List, Optional, Union
from pathlib import Path
import re

# 将项目根目录添加到Python路径
root_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, root_path)

# 导入LogTarget
from utils.logger import get_unified_logger, LogTarget  # noqa: E402

# 设置日志记录器
logger = get_unified_logger(__name__, enhanced=True)

# 全局缓存根目录
_ROOT_DIR = None


def get_root_dir() -> str:
    """
    获取项目根目录的绝对路径
    
    返回项目根目录的绝对路径，如果项目结构确定，则使用缓存提高性能
    
    Returns:
        项目根目录的绝对路径
    """
    global _ROOT_DIR
    
    if _ROOT_DIR is not None:
        return _ROOT_DIR
    
    # 通过__file__确定当前文件的位置，然后向上查找到项目根目录
    curr_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 假设项目根目录中有明确的标识文件，如setup.py或pyproject.toml
    root_markers = ['setup.py', 'pyproject.toml', 'requirements.txt']
    
    # 向上最多查找5层目录
    for _ in range(5):
        # 检查标记文件是否存在
        for marker in root_markers:
            if os.path.exists(os.path.join(curr_dir, marker)):
                _ROOT_DIR = curr_dir
                return _ROOT_DIR
        
        # 向上一级目录
        parent_dir = os.path.dirname(curr_dir)
        if parent_dir == curr_dir:  # 已经到达根目录
            break
        curr_dir = parent_dir
    
    # 如果无法确定，使用当前文件所在目录的父目录的父目录
    # （即假设utils/path_utils是项目根目录的下一级）
    _ROOT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    logger.warning(
        LogTarget.FILE, 
        f"无法准确确定项目根目录，使用推测值: {_ROOT_DIR}"
    )
    
    return _ROOT_DIR


def get_project_dir(subdir: Optional[str] = None) -> str:
    """
    获取项目中指定子目录的路径
    
    Args:
        subdir: 子目录名，如果为None则返回项目根目录
        
    Returns:
        子目录的绝对路径
    """
    root_dir = get_root_dir()
    
    if subdir is None:
        return root_dir
    
    # 拼接并返回子目录的绝对路径
    subdir_path = os.path.join(root_dir, subdir)
    
    # 如果子目录不存在，记录警告但不抛出异常
    if not os.path.exists(subdir_path):
        logger.warning(LogTarget.FILE, f"项目子目录不存在: {subdir_path}")
    
    return subdir_path


def get_project_structure() -> Dict[str, List[str]]:
    """
    获取项目的目录结构
    
    Returns:
        结构字典，键为目录名，值为子文件和子目录列表
    """
    root_dir = get_root_dir()
    structure = {}
    
    for dirpath, dirnames, filenames in os.walk(root_dir):
        # 排除特定目录，如.git, __pycache__, .venv等
        dirnames[:] = [d for d in dirnames if d not in ('.git', '__pycache__', '.venv', 'venv', '.idea', '.ipynb_checkpoints')]
        
        # 获取相对于根目录的路径
        rel_path = os.path.relpath(dirpath, root_dir)
        if rel_path == '.':
            rel_path = ''
        
        # 将目录和文件添加到结构字典
        structure[rel_path] = {
            'dirs': dirnames,
            'files': filenames
        }
    
    return structure


def normalize_path(path: Union[str, Path], make_absolute: bool = True) -> str:
    """
    标准化路径
    
    处理相对路径、波浪号展开、反斜杠转换等，根据make_absolute参数决定是否转换为绝对路径
    
    Args:
        path: 原始路径
        make_absolute: 是否转换为绝对路径，默认为True
        
    Returns:
        标准化后的路径
    """
    # 转换为字符串
    path_str = str(path)
    
    # 展开用户主目录
    path_str = os.path.expanduser(path_str)
    
    # 标准化路径分隔符
    path_str = os.path.normpath(path_str)
    
    # 如果需要，转换为绝对路径
    if make_absolute and not os.path.isabs(path_str):
        # 相对于项目根目录
        path_str = os.path.join(get_root_dir(), path_str)
    
    return path_str


def is_path_inside(path: Union[str, Path], parent_path: Union[str, Path]) -> bool:
    """
    检查路径是否在指定父目录内
    
    Args:
        path: 要检查的路径
        parent_path: 父目录路径
        
    Returns:
        如果path在parent_path内则返回True，否则返回False
    """
    # 确保使用标准化的绝对路径
    norm_path = os.path.normpath(os.path.abspath(str(path)))
    norm_parent = os.path.normpath(os.path.abspath(str(parent_path)))
    
    # 在Windows上比较时忽略大小写
    if platform.system() == 'Windows':
        return norm_path.lower().startswith(norm_parent.lower() + os.sep) or norm_path.lower() == norm_parent.lower()
    else:
        return norm_path.startswith(norm_parent + os.sep) or norm_path == norm_parent


def join_paths(*paths: Union[str, Path]) -> str:
    """
    连接多个路径
    
    提供比os.path.join更灵活的路径连接，可以处理混合的绝对路径和相对路径
    
    Args:
        *paths: 要连接的路径
        
    Returns:
        连接后的路径
    """
    if not paths:
        return ""
    
    # 将所有路径转换为字符串
    str_paths = [str(path) for path in paths]
    
    # 标准化所有路径
    norm_paths = []
    for p in str_paths:
        # 如果是绝对路径，从这里开始重新构建
        if os.path.isabs(p):
            norm_paths = [p]
        else:
            # 移除前导和尾随的斜杠，除非是根目录
            if p != "/" and p != "\\":
                p = p.strip("/\\")
            if p:  # 确保不是空字符串
                norm_paths.append(p)
    
    # 连接所有路径
    if not norm_paths:
        return ""
    
    result = norm_paths[0]
    for p in norm_paths[1:]:
        result = os.path.join(result, p)
    
    return os.path.normpath(result)


def get_file_extension(file_path: Union[str, Path]) -> str:
    """
    获取文件扩展名
    
    Args:
        file_path: 文件路径
        
    Returns:
        文件扩展名（小写，不含点号）
    """
    _, ext = os.path.splitext(str(file_path))
    
    # 移除前导点号并转换为小写
    if ext.startswith('.'):
        ext = ext[1:]
    return ext.lower()


def list_files(directory: Union[str, Path], pattern: Optional[str] = None,
              recursive: bool = False, relative: bool = False) -> List[str]:
    """
    列出目录中的文件
    
    Args:
        directory: 目录路径
        pattern: 文件名匹配模式（正则表达式）
        recursive: 是否递归搜索子目录
        relative: 是否返回相对路径
        
    Returns:
        文件路径列表
    """
    # 标准化目录路径
    dir_path = normalize_path(directory)
    
    # 检查目录是否存在
    if not os.path.exists(dir_path):
        logger.error(LogTarget.FILE, f"目录不存在: {dir_path}")
        return []
    
    # 编译正则表达式
    regex = None
    if pattern:
        try:
            regex = re.compile(pattern)
        except re.error:
            # 如果正则表达式无效，尝试将其作为glob模式
            pattern = pattern.replace(".", "\\.").replace("*", ".*").replace("?", ".")
            regex = re.compile(pattern)
    
    # 存储结果
    file_list = []
    
    # 根据recursive参数选择遍历方法
    if recursive:
        for root, _, files in os.walk(dir_path):
            for file in files:
                # 检查文件是否匹配模式
                if regex is None or regex.search(file):
                    full_path = os.path.join(root, file)
                    if relative:
                        rel_path = os.path.relpath(full_path, dir_path)
                        file_list.append(rel_path)
                    else:
                        file_list.append(full_path)
    else:
        # 只遍历当前目录
        for item in os.listdir(dir_path):
            item_path = os.path.join(dir_path, item)
            if os.path.isfile(item_path):
                # 检查文件是否匹配模式
                if regex is None or regex.search(item):
                    if relative:
                        file_list.append(item)
                    else:
                        file_list.append(item_path)
    
    return file_list


def find_files_by_content(directory: Union[str, Path], content_pattern: str,
                        file_pattern: Optional[str] = None, recursive: bool = True) -> List[str]:
    """
    根据文件内容查找文件
    
    Args:
        directory: 目录路径
        content_pattern: 文件内容匹配模式（正则表达式）
        file_pattern: 文件名匹配模式（正则表达式），可选
        recursive: 是否递归搜索子目录
        
    Returns:
        匹配的文件路径列表
    """
    # 获取符合文件名模式的文件列表
    files = list_files(directory, file_pattern, recursive)
    
    # 编译内容正则表达式
    content_regex = re.compile(content_pattern)
    
    # 存储结果
    matching_files = []
    
    # 遍历文件并检查内容
    for file_path in files:
        try:
            # 尝试以文本模式读取文件
            with open(file_path, 'r', errors='ignore') as f:
                content = f.read()
                if content_regex.search(content):
                    matching_files.append(file_path)
        except Exception as e:
            logger.debug(
                LogTarget.FILE, 
                f"检查文件内容时出错: {file_path}, 错误: {str(e)}"
            )
    
    return matching_files


def get_parent_dirs(path: Union[str, Path], levels: int = 1) -> List[str]:
    """
    获取给定路径的父目录
    
    Args:
        path: 路径
        levels: 要往上查找的层数，默认为1
        
    Returns:
        父目录路径列表，从最近的父目录到最远的父目录
    """
    # 标准化路径
    norm_path = normalize_path(path)
    
    parent_dirs = []
    current_path = norm_path
    
    for _ in range(levels):
        parent_path = os.path.dirname(current_path)
        if parent_path == current_path:  # 已经到达根目录
            break
        parent_dirs.append(parent_path)
        current_path = parent_path
    
    return parent_dirs


def resolve_relative_path(base_path: Union[str, Path], relative_path: Union[str, Path]) -> str:
    """
    解析相对于基础路径的相对路径
    
    Args:
        base_path: 基础路径（文件或目录）
        relative_path: 相对路径
        
    Returns:
        解析后的绝对路径
    """
    # 标准化基础路径
    norm_base = normalize_path(base_path)
    
    # 如果基础路径是文件，则使用其所在目录
    if os.path.isfile(norm_base):
        norm_base = os.path.dirname(norm_base)
    
    # 解析相对路径
    abs_path = os.path.normpath(os.path.join(norm_base, str(relative_path)))
    
    return abs_path