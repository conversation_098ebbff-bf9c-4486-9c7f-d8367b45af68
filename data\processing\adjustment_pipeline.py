#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
复权数据处理管道

基于DataPipeline架构的独立复权数据处理管道，专门负责复权数据的处理流程。
遵循单一职责原则，与周期转换功能完全分离。

核心特性：
- 基于现有DataPipeline架构
- 专门处理复权数据合成
- 支持批量复权处理
- 链式操作和错误处理
- 进度跟踪和性能监控

使用示例：
```python
from data.processing.adjustment_pipeline import AdjustmentDataPipeline

# 单只股票复权处理
pipeline = AdjustmentDataPipeline()
result = (pipeline
    .load_raw_data(data_root, symbol, period)
    .apply_adjustment(dividend_type="front")
    .save_adjusted_data()
    .execute())

# 批量复权处理
batch_result = (pipeline
    .load_batch_raw_data(data_root, symbols, period)
    .apply_batch_adjustment(dividend_type="front")
    .save_batch_adjusted_data()
    .execute())
```

作者: AI Assistant
创建时间: 2025-08-05
版本: 1.0.0
"""

import time
from typing import Dict, List, Optional, Any, Union
import pandas as pd
from dataclasses import dataclass
from enum import Enum

from data.processing.data_pipeline import DataPipeline, OperationType, PipelineOperation
from utils.data_processor.adjustment import adjustment_synthesizer
from data.storage.vectorized_reader import read_partitioned_data_vectorized
from data.storage.unified_data_saver import save_data_unified, SaveStrategy
from utils.logger import get_unified_logger, LogTarget
from config.settings import DATA_ROOT

logger = get_unified_logger(__name__)


class AdjustmentOperationType(Enum):
    """复权操作类型枚举"""
    LOAD_RAW = "load_raw"
    LOAD_BATCH_RAW = "load_batch_raw"
    APPLY_ADJUSTMENT = "apply_adjustment"
    APPLY_BATCH_ADJUSTMENT = "apply_batch_adjustment"
    SAVE_ADJUSTED = "save_adjusted"
    SAVE_BATCH_ADJUSTED = "save_batch_adjusted"
    VALIDATE_ADJUSTMENT = "validate_adjustment"


class AdjustmentDataPipeline(DataPipeline):
    """
    复权数据处理管道
    
    专门处理复权数据的独立管道，继承自DataPipeline
    """
    
    def __init__(self, enable_parallel: bool = True):
        """
        初始化复权数据处理管道
        
        Args:
            enable_parallel: 是否启用并行处理
        """
        super().__init__(enable_parallel)
        self.adjustment_stats = {
            'total_symbols': 0,
            'successful_adjustments': 0,
            'failed_adjustments': 0,
            'total_adjustment_time': 0.0
        }
        logger.info("复权数据处理管道初始化完成")
    
    def load_raw_data(self, data_root: str, symbol: str, period: str,
                      start_time: Optional[str] = None,
                      end_time: Optional[str] = None,
                      columns: Optional[List[str]] = None) -> 'AdjustmentDataPipeline':
        """
        加载原始数据（不含复权处理）
        
        Args:
            data_root: 数据根目录
            symbol: 股票代码
            period: 数据周期
            start_time: 开始时间
            end_time: 结束时间
            columns: 要读取的列
            
        Returns:
            AdjustmentDataPipeline: 管道实例（支持链式调用）
        """
        operation = PipelineOperation(
            op_type=OperationType.LOAD,
            func=self._load_raw_data_impl,
            args=(data_root, symbol, period),
            kwargs={
                'start_time': start_time,
                'end_time': end_time,
                'columns': columns
            },
            name=f"load_raw_{symbol}_{period}"
        )
        self.operations.append(operation)
        logger.debug(f"添加原始数据加载操作: {symbol} {period}")
        return self
    
    def load_batch_raw_data(self, data_root: str, symbols: List[str], period: str,
                           start_time: Optional[str] = None,
                           end_time: Optional[str] = None,
                           columns: Optional[List[str]] = None) -> 'AdjustmentDataPipeline':
        """
        批量加载原始数据
        
        Args:
            data_root: 数据根目录
            symbols: 股票代码列表
            period: 数据周期
            start_time: 开始时间
            end_time: 结束时间
            columns: 要读取的列
            
        Returns:
            AdjustmentDataPipeline: 管道实例（支持链式调用）
        """
        operation = PipelineOperation(
            op_type=OperationType.LOAD,
            func=self._load_batch_raw_data_impl,
            args=(data_root, symbols, period),
            kwargs={
                'start_time': start_time,
                'end_time': end_time,
                'columns': columns
            },
            name=f"load_batch_raw_{len(symbols)}symbols_{period}"
        )
        self.operations.append(operation)
        logger.debug(f"添加批量原始数据加载操作: {len(symbols)} 只股票 {period}")
        return self
    
    def apply_adjustment(self, dividend_type: str = "front",
                        method: str = "ratio",
                        use_cache: bool = True) -> 'AdjustmentDataPipeline':
        """
        应用复权处理
        
        Args:
            dividend_type: 复权类型，"front"（前复权）、"back"（后复权）、"none"（原始数据）
            method: 计算方法，"ratio"（等比复权）、"standard"（标准复权）
            use_cache: 是否使用缓存
            
        Returns:
            AdjustmentDataPipeline: 管道实例（支持链式调用）
        """
        operation = PipelineOperation(
            op_type=OperationType.TRANSFORM,
            func=self._apply_adjustment_impl,
            args=(),
            kwargs={
                'dividend_type': dividend_type,
                'method': method,
                'use_cache': use_cache
            },
            name=f"apply_adjustment_{dividend_type}_{method}"
        )
        self.operations.append(operation)
        logger.debug(f"添加复权处理操作: {dividend_type} {method}")
        return self
    
    def apply_batch_adjustment(self, dividend_type: str = "front",
                              method: str = "ratio",
                              use_cache: bool = True,
                              max_workers: int = 4) -> 'AdjustmentDataPipeline':
        """
        批量应用复权处理
        
        Args:
            dividend_type: 复权类型
            method: 计算方法
            use_cache: 是否使用缓存
            max_workers: 最大并发数
            
        Returns:
            AdjustmentDataPipeline: 管道实例（支持链式调用）
        """
        operation = PipelineOperation(
            op_type=OperationType.TRANSFORM,
            func=self._apply_batch_adjustment_impl,
            args=(),
            kwargs={
                'dividend_type': dividend_type,
                'method': method,
                'use_cache': use_cache,
                'max_workers': max_workers
            },
            name=f"apply_batch_adjustment_{dividend_type}_{method}"
        )
        self.operations.append(operation)
        logger.debug(f"添加批量复权处理操作: {dividend_type} {method}")
        return self
    
    def save_adjusted_data(self, data_root: Optional[str] = None,
                          strategy: SaveStrategy = SaveStrategy.MULTI_PARTITION,
                          parallel: bool = True) -> 'AdjustmentDataPipeline':
        """
        保存复权数据
        
        Args:
            data_root: 数据根目录
            strategy: 保存策略
            parallel: 是否并行保存
            
        Returns:
            AdjustmentDataPipeline: 管道实例（支持链式调用）
        """
        operation = PipelineOperation(
            op_type=OperationType.CUSTOM,
            func=self._save_adjusted_data_impl,
            args=(),
            kwargs={
                'data_root': data_root or DATA_ROOT,
                'strategy': strategy,
                'parallel': parallel
            },
            name="save_adjusted_data"
        )
        self.operations.append(operation)
        logger.debug("添加复权数据保存操作")
        return self
    
    def save_batch_adjusted_data(self, data_root: Optional[str] = None,
                                strategy: SaveStrategy = SaveStrategy.MULTI_PARTITION,
                                parallel: bool = True) -> 'AdjustmentDataPipeline':
        """
        批量保存复权数据
        
        Args:
            data_root: 数据根目录
            strategy: 保存策略
            parallel: 是否并行保存
            
        Returns:
            AdjustmentDataPipeline: 管道实例（支持链式调用）
        """
        operation = PipelineOperation(
            op_type=OperationType.CUSTOM,
            func=self._save_batch_adjusted_data_impl,
            args=(),
            kwargs={
                'data_root': data_root or DATA_ROOT,
                'strategy': strategy,
                'parallel': parallel
            },
            name="save_batch_adjusted_data"
        )
        self.operations.append(operation)
        logger.debug("添加批量复权数据保存操作")
        return self
    
    def validate_adjustment(self, tolerance: float = 1e-6) -> 'AdjustmentDataPipeline':
        """
        验证复权数据质量
        
        Args:
            tolerance: 容差值
            
        Returns:
            AdjustmentDataPipeline: 管道实例（支持链式调用）
        """
        operation = PipelineOperation(
            op_type=OperationType.CUSTOM,
            func=self._validate_adjustment_impl,
            args=(),
            kwargs={'tolerance': tolerance},
            name="validate_adjustment"
        )
        self.operations.append(operation)
        logger.debug("添加复权数据验证操作")
        return self

    # ==================== 内部实现方法 ====================

    def _load_raw_data_impl(self, data_root: str, symbol: str, period: str,
                           start_time: Optional[str] = None,
                           end_time: Optional[str] = None,
                           columns: Optional[List[str]] = None) -> pd.DataFrame:
        """加载原始数据的内部实现"""
        try:
            logger.debug(f"加载原始数据: {symbol} {period}")

            # 使用向量化读取器加载原始数据（不含复权处理）
            df = read_partitioned_data_vectorized(
                data_root=data_root,
                symbol=symbol,
                period=period,
                start_time=start_time,
                end_time=end_time,
                columns=columns,
                dividend_type="none"  # 强制加载原始数据
            )

            if df is None or df.empty:
                raise ValueError(f"未读取到原始数据: {symbol} {period}")

            logger.info(f"原始数据加载成功: {symbol} {period} {len(df)} 行")
            return df

        except Exception as e:
            logger.error(f"原始数据加载失败: {symbol} {period} - {e}")
            raise

    def _load_batch_raw_data_impl(self, data_root: str, symbols: List[str], period: str,
                                 start_time: Optional[str] = None,
                                 end_time: Optional[str] = None,
                                 columns: Optional[List[str]] = None) -> Dict[str, pd.DataFrame]:
        """批量加载原始数据的内部实现"""
        try:
            logger.info(f"批量加载原始数据: {len(symbols)} 只股票 {period}")

            batch_data = {}
            successful_count = 0
            failed_count = 0

            for symbol in symbols:
                try:
                    df = self._load_raw_data_impl(
                        data_root, symbol, period, start_time, end_time, columns
                    )
                    batch_data[symbol] = df
                    successful_count += 1

                except Exception as e:
                    logger.warning(f"跳过加载失败的股票: {symbol} - {e}")
                    failed_count += 1
                    continue

            logger.info(f"批量原始数据加载完成: 成功 {successful_count} 只, 失败 {failed_count} 只")
            return batch_data

        except Exception as e:
            logger.error(f"批量原始数据加载失败: {e}")
            raise

    def _apply_adjustment_impl(self, data: pd.DataFrame, dividend_type: str = "front",
                              method: str = "ratio", use_cache: bool = True) -> pd.DataFrame:
        """应用复权处理的内部实现"""
        try:
            if not hasattr(self, '_current_symbol'):
                raise ValueError("缺少股票代码信息，无法进行复权处理")

            symbol = self._current_symbol
            logger.debug(f"应用复权处理: {symbol} {dividend_type} {method}")

            if dividend_type == "none":
                logger.info(f"复权类型为none，返回原始数据: {symbol}")
                return data.copy()

            # 使用复权合成器进行复权计算
            adjusted_data = adjustment_synthesizer.synthesize_adjusted_data(
                symbol=symbol,
                price_data=data,
                dividend_type=dividend_type,
                method=method,
                use_cache=use_cache
            )

            if adjusted_data is None or adjusted_data.empty:
                logger.warning(f"复权处理失败，返回原始数据: {symbol}")
                return data.copy()

            logger.info(f"复权处理成功: {symbol} {dividend_type} {len(adjusted_data)} 行")
            self.adjustment_stats['successful_adjustments'] += 1
            return adjusted_data

        except Exception as e:
            logger.error(f"复权处理失败: {e}")
            self.adjustment_stats['failed_adjustments'] += 1
            raise

    def _apply_batch_adjustment_impl(self, batch_data: Dict[str, pd.DataFrame],
                                    dividend_type: str = "front", method: str = "ratio",
                                    use_cache: bool = True, max_workers: int = 4) -> Dict[str, pd.DataFrame]:
        """批量应用复权处理的内部实现"""
        try:
            logger.info(f"批量应用复权处理: {len(batch_data)} 只股票 {dividend_type} {method}")

            # 使用复权合成器的批量处理功能
            adjusted_batch_data = adjustment_synthesizer.batch_synthesize_adjusted_data(
                stock_data=batch_data,
                dividend_type=dividend_type,
                method=method,
                use_cache=use_cache,
                max_workers=max_workers
            )

            # 统计处理结果
            successful_count = sum(1 for symbol, data in adjusted_batch_data.items()
                                 if data is not None and not data.empty)
            failed_count = len(batch_data) - successful_count

            self.adjustment_stats['successful_adjustments'] += successful_count
            self.adjustment_stats['failed_adjustments'] += failed_count

            logger.info(f"批量复权处理完成: 成功 {successful_count} 只, 失败 {failed_count} 只")
            return adjusted_batch_data

        except Exception as e:
            logger.error(f"批量复权处理失败: {e}")
            raise

    def _save_adjusted_data_impl(self, data: pd.DataFrame, data_root: str,
                                strategy: SaveStrategy, parallel: bool) -> Dict[str, Any]:
        """保存复权数据的内部实现"""
        try:
            if not hasattr(self, '_current_symbol') or not hasattr(self, '_current_period'):
                raise ValueError("缺少股票代码或周期信息，无法保存复权数据")

            symbol = self._current_symbol
            period = self._current_period
            dividend_type = getattr(self, '_current_dividend_type', 'front')

            logger.debug(f"保存复权数据: {symbol} {period} {dividend_type}")

            # 使用统一数据保存器保存复权数据
            save_result = save_data_unified(
                df=data,
                data_root=data_root,
                symbol=symbol,
                period=period,
                strategy=strategy,
                parallel=parallel,
                data_type="adjusted",
                adj_type=dividend_type
            )

            if save_result.success:
                logger.info(f"复权数据保存成功: {symbol} {period} {dividend_type}")
                return {
                    "success": True,
                    "symbol": symbol,
                    "period": period,
                    "dividend_type": dividend_type,
                    "partitions": len(save_result.saved_partitions),
                    "strategy": save_result.strategy_used.value
                }
            else:
                logger.error(f"复权数据保存失败: {symbol} {period} - {save_result.error_message}")
                return {
                    "success": False,
                    "symbol": symbol,
                    "period": period,
                    "error": save_result.error_message
                }

        except Exception as e:
            logger.error(f"复权数据保存失败: {e}")
            raise

    def _save_batch_adjusted_data_impl(self, batch_data: Dict[str, pd.DataFrame],
                                      data_root: str, strategy: SaveStrategy,
                                      parallel: bool) -> Dict[str, Any]:
        """批量保存复权数据的内部实现"""
        try:
            if not hasattr(self, '_current_period'):
                raise ValueError("缺少周期信息，无法保存批量复权数据")

            period = self._current_period
            dividend_type = getattr(self, '_current_dividend_type', 'front')

            logger.info(f"批量保存复权数据: {len(batch_data)} 只股票 {period} {dividend_type}")

            save_results = {}
            successful_count = 0
            failed_count = 0

            for symbol, data in batch_data.items():
                try:
                    if data is None or data.empty:
                        logger.warning(f"跳过空数据: {symbol}")
                        continue

                    # 临时设置当前股票信息
                    self._current_symbol = symbol

                    save_result = self._save_adjusted_data_impl(
                        data, data_root, strategy, parallel
                    )
                    save_results[symbol] = save_result

                    if save_result["success"]:
                        successful_count += 1
                    else:
                        failed_count += 1

                except Exception as e:
                    logger.error(f"保存复权数据失败: {symbol} - {e}")
                    save_results[symbol] = {
                        "success": False,
                        "symbol": symbol,
                        "error": str(e)
                    }
                    failed_count += 1

            logger.info(f"批量复权数据保存完成: 成功 {successful_count} 只, 失败 {failed_count} 只")
            return {
                "total_symbols": len(batch_data),
                "successful_saves": successful_count,
                "failed_saves": failed_count,
                "save_results": save_results
            }

        except Exception as e:
            logger.error(f"批量复权数据保存失败: {e}")
            raise

    def _validate_adjustment_impl(self, data: Union[pd.DataFrame, Dict[str, pd.DataFrame]],
                                 tolerance: float = 1e-6) -> Dict[str, Any]:
        """验证复权数据质量的内部实现"""
        try:
            logger.debug("开始验证复权数据质量")

            if isinstance(data, pd.DataFrame):
                # 单只股票数据验证
                return self._validate_single_adjustment(data, tolerance)
            elif isinstance(data, dict):
                # 批量数据验证
                return self._validate_batch_adjustment(data, tolerance)
            else:
                raise ValueError(f"不支持的数据类型: {type(data)}")

        except Exception as e:
            logger.error(f"复权数据验证失败: {e}")
            raise

    def _validate_single_adjustment(self, data: pd.DataFrame, tolerance: float) -> Dict[str, Any]:
        """验证单只股票复权数据"""
        validation_result = {
            "valid": True,
            "issues": [],
            "statistics": {}
        }

        try:
            # 检查数据完整性
            if data.empty:
                validation_result["valid"] = False
                validation_result["issues"].append("数据为空")
                return validation_result

            # 检查价格列是否存在
            price_columns = ['open', 'high', 'low', 'close']
            missing_columns = [col for col in price_columns if col not in data.columns]
            if missing_columns:
                validation_result["issues"].append(f"缺少价格列: {missing_columns}")

            # 检查价格数据的合理性
            for col in price_columns:
                if col in data.columns:
                    if (data[col] <= 0).any():
                        validation_result["issues"].append(f"{col}列存在非正值")

                    if data[col].isnull().any():
                        validation_result["issues"].append(f"{col}列存在空值")

            # 检查高开低收的逻辑关系
            if all(col in data.columns for col in price_columns):
                invalid_ohlc = (
                    (data['high'] < data['low']) |
                    (data['high'] < data['open']) |
                    (data['high'] < data['close']) |
                    (data['low'] > data['open']) |
                    (data['low'] > data['close'])
                )

                if invalid_ohlc.any():
                    validation_result["issues"].append(f"存在 {invalid_ohlc.sum()} 行OHLC逻辑错误")

            # 统计信息
            validation_result["statistics"] = {
                "total_rows": len(data),
                "date_range": f"{data.index[0]} ~ {data.index[-1]}",
                "columns": list(data.columns)
            }

            if validation_result["issues"]:
                validation_result["valid"] = False

            logger.debug(f"单只股票复权数据验证完成: {'通过' if validation_result['valid'] else '失败'}")
            return validation_result

        except Exception as e:
            logger.error(f"单只股票复权数据验证异常: {e}")
            validation_result["valid"] = False
            validation_result["issues"].append(f"验证异常: {e}")
            return validation_result

    def _validate_batch_adjustment(self, batch_data: Dict[str, pd.DataFrame],
                                  tolerance: float) -> Dict[str, Any]:
        """验证批量复权数据"""
        batch_validation_result = {
            "overall_valid": True,
            "total_symbols": len(batch_data),
            "valid_symbols": 0,
            "invalid_symbols": 0,
            "symbol_results": {}
        }

        try:
            for symbol, data in batch_data.items():
                symbol_result = self._validate_single_adjustment(data, tolerance)
                batch_validation_result["symbol_results"][symbol] = symbol_result

                if symbol_result["valid"]:
                    batch_validation_result["valid_symbols"] += 1
                else:
                    batch_validation_result["invalid_symbols"] += 1
                    batch_validation_result["overall_valid"] = False

            logger.info(f"批量复权数据验证完成: 有效 {batch_validation_result['valid_symbols']} 只, "
                       f"无效 {batch_validation_result['invalid_symbols']} 只")

            return batch_validation_result

        except Exception as e:
            logger.error(f"批量复权数据验证异常: {e}")
            batch_validation_result["overall_valid"] = False
            return batch_validation_result

    def get_adjustment_stats(self) -> Dict[str, Any]:
        """获取复权处理统计信息"""
        return {
            **self.adjustment_stats,
            **self._performance_stats
        }


def create_adjustment_pipeline(enable_parallel: bool = True) -> AdjustmentDataPipeline:
    """
    创建复权数据处理管道的工厂函数

    Args:
        enable_parallel: 是否启用并行处理

    Returns:
        AdjustmentDataPipeline: 复权数据处理管道实例
    """
    return AdjustmentDataPipeline(enable_parallel=enable_parallel)
