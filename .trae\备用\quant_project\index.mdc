---
description: 
globs: "*"
alwaysApply: true
---
# 量化交易项目规范索引

本文档为量化交易项目规范的总索引，包含所有规范文件的链接和简要说明。开发者和AI助手应遵循这些规范，以确保项目的一致性、可维护性和高质量。

## 规范文件索引

### 1. [项目结构规范](mdc:project_structure_standards.mdc)
- 定义项目的目录结构、文件组织、命名规范、模块化和开发实践。
- 包含标准目录结构、模块化组织、文件命名与分类、数据存储管理（包括量化项目专属数据规范）、Git规范及实践示例等。

### 2. [Python编码规范](mdc:python_coding_standards.md)
- 定义了Python代码的编写规范和最佳实践
- 包含命名规则、代码结构、错误处理和性能优化规范
- 规定了模块导入与路径处理的标准方法
- 提供了时间序列数据处理的标准格式

### 3. [量化交易系统规范](mdc:quant_system_standards.md)
- 定义了量化交易系统专有的规范
- 包含系统架构、实时交易、回测系统和策略开发规范
- 规定了性能优化、资源管理和监控告警要求
- 提供了数据安全和交易接口使用规范

### 4. [Windows PowerShell规则](mdc:windows_powerShell_rules.md)
- 规定了在Windows环境下使用PowerShell执行命令的规范
- 包含PowerShell与Unix命令的对照表
- 提供了常见问题的解决方案和最佳实践
- 指导AI助手正确执行Windows命令

### 5. [特定工具使用规范](mdc:tool_specific_rules.md)
- 定义了项目中各种特定工具的使用规范
- 包含AI助手命令调用规则和决策指南
- 提供了数据处理、版本控制和测试工具的使用规范
- 规定了文档生成工具的使用标准

### 6. [AI交互规范](mdc:ai_interaction_rules.md)
- 规定了与AI助手交互的规范和沟通流程
- 包含需求理解、确认流程和执行反馈标准
- 提供了AI助手操作和文件处理的规范
- 指导权限管理、沟通风格和安全实践

## 使用指南

1. **新成员入职**：新加入的开发人员应首先阅读全部规范文件，了解项目的标准和要求。

2. **日常开发**：在日常开发过程中，应当随时参考相关规范，确保代码和项目结构符合标准。

3. **代码审查**：在进行代码审查时，应将这些规范作为评审标准之一。

4. **AI助手使用**：与AI助手交互时，应引导AI遵循这些规范，特别是AI交互规范和命令调用规则。

5. **规范更新**：如发现规范需要调整或补充，应提出建议并经团队讨论后更新。

## 规范维护

本规范由项目团队共同维护，定期审查和更新。所有规范的变更都应记录在各文档的更新日志中，并通知所有团队成员。 