#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
回测引擎模块，提供股票和期货策略的回测功能
"""

import logging
import os
import sys
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd

# 添加项目根目录到系统路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import (
    BACKTEST_DEFAULT_COMMISSION_RATE,
    BACKTEST_DEFAULT_INITIAL_CAPITAL,
    BACKTEST_DEFAULT_PERIOD,
    BACKTEST_DEFAULT_SLIPPAGE,
)
from data.data_source_manager import DataSourceManager
from utils.logger import get_unified_logger


class BacktestEngine:
    """
    回测引擎类，用于执行策略回测
    支持多品种回测、自定义手续费和滑点设置、绘制回测结果
    """
    
    def __init__(self, **kwargs):
        """
        初始化回测引擎
        
        Args:
            **kwargs: 回测参数，包括：
                - strategy: 策略对象或策略类
                - strategy_params: 策略参数字典
                - start_date: 回测开始日期，格式为YYYYMMDD
                - end_date: 回测结束日期，格式为YYYYMMDD
                - period: 回测周期，如1d, 1h, 30m, 1m等
                - symbols: 回测品种列表
                - initial_capital: 初始资金
                - commission_rate: 手续费率
                - slippage: 滑点设置
                - data_source: 数据源管理器
                - enable_live_plot: 是否启用实时绘图
                - output_dir: 输出目录
        """
        # 日志记录器
        self.logger = kwargs.get("logger", get_unified_logger("backtest_engine"))
        
        # 策略相关
        self.strategy_class = kwargs.get("strategy")
        self.strategy_params = kwargs.get("strategy_params", {})
        self.strategy_instance = None
        
        # 回测周期
        self.start_date = kwargs.get("start_date")
        self.end_date = kwargs.get("end_date", datetime.now().strftime("%Y%m%d"))
        self.period = kwargs.get("period", BACKTEST_DEFAULT_PERIOD)
        
        # 回测品种
        self.symbols = kwargs.get("symbols", [])
        
        # 资金和费用设置
        self.initial_capital = kwargs.get(
            "initial_capital", BACKTEST_DEFAULT_INITIAL_CAPITAL
        )
        self.commission_rate = kwargs.get(
            "commission_rate", BACKTEST_DEFAULT_COMMISSION_RATE
        )
        self.slippage = kwargs.get("slippage", BACKTEST_DEFAULT_SLIPPAGE)
        
        # 数据源
        self.data_source = kwargs.get("data_source")
        if self.data_source is None:
            self.data_source = DataSourceManager()
        
        # 回测结果
        self.results = {
            "summary": {},
            "trades": [],
            "positions": [],
            "equity_curve": pd.DataFrame()
        }
        
        # 回测状态
        self.current_date = None
        self.account = {
            "initial_capital": self.initial_capital,
            "cash": self.initial_capital,
            "equity": self.initial_capital,
            "positions": {},
            "trades": []
        }
        
        # 输出设置
        self.enable_live_plot = kwargs.get("enable_live_plot", False)
        self.output_dir = kwargs.get("output_dir", "./output")
        
        # 确保输出目录存在
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
        
        # 初始化消息
        self._log_init_message()
    
    def _log_init_message(self):
        """记录初始化消息"""
        self.logger.info("回测引擎初始化")
        self.logger.info(f"回测周期: {self.start_date} - {self.end_date}, {self.period}")
        self.logger.info(f"回测品种: {', '.join(self.symbols)}")
        self.logger.info(f"初始资金: {self.initial_capital}")
        self.logger.info(f"手续费率: {self.commission_rate}")
        self.logger.info(f"滑点设置: {self.slippage}")
    
    def prepare_data(self) -> Dict[str, pd.DataFrame]:
        """
        准备回测所需的历史数据
        
        Returns:
            历史数据字典，键为品种代码，值为DataFrame
        """
        self.logger.info("准备回测数据...")
        
        # 检查是否有品种
        if not self.symbols:
            self.logger.error("未指定回测品种，回测无法进行")
            return {}
        
        # 下载历史数据
        self.logger.info(f"下载历史数据: {self.symbols}, {self.period}, "
                         f"{self.start_date} - {self.end_date}")
        
        self.data_source.download_history_data(
            stock_list=self.symbols,
            period=self.period,
            start_time=self.start_date,
            end_time=self.end_date,
            incremental=True
        )
        
        # 获取历史数据
        data = self.data_source.get_local_data(
            stock_list=self.symbols,
            period=self.period,
            start_time=self.start_date,
            end_time=self.end_date,
            field_list=["open", "high", "low", "close", "volume"]
        )
        
        # 检查数据是否为空
        if not data:
            self.logger.error("获取历史数据失败，回测无法进行")
            return {}
        
        # 检查每个品种的数据
        for symbol in self.symbols:
            if symbol not in data:
                self.logger.warning(f"未找到 {symbol} 的历史数据")
                continue
                
            df = data[symbol]
            if df.empty:
                self.logger.warning(f"{symbol} 的历史数据为空")
                continue
                
            self.logger.info(f"{symbol} 数据获取成功，共 {len(df)} 条记录")
        
        return data
    
    def initialize_strategy(self):
        """初始化策略"""
        if self.strategy_class is None:
            self.logger.error("未指定策略，回测无法进行")
            return False
        
        self.logger.info(f"初始化策略：{self.strategy_class.__name__ if hasattr(self.strategy_class, '__name__') else self.strategy_class}")
        
        # 如果strategy_class是类而不是实例，则实例化
        if isinstance(self.strategy_class, type):
            self.strategy_instance = self.strategy_class(**self.strategy_params)
        else:
            # 如果已经是实例，直接使用
            self.strategy_instance = self.strategy_class
        
        if self.strategy_instance is None:
            self.logger.error("策略初始化失败")
            return False
            
        self.logger.info("策略初始化成功")
        return True
    
    def run_backtest(self) -> Dict:
        """
        运行回测
        
        Returns:
            回测结果字典
        """
        self.logger.info("开始回测...")
        start_time = time.time()
        
        # 准备数据
        data = self.prepare_data()
        if not data:
            self.logger.error("数据准备失败，回测中止")
            return self.results
        
        # 初始化策略
        if not self.initialize_strategy():
            self.logger.error("策略初始化失败，回测中止")
            return self.results
        
        # 查找所有数据集中的所有日期
        all_dates = self._extract_all_dates(data)
        if not all_dates:
            self.logger.error("无法提取回测日期，回测中止")
            return self.results
        
        # 按日期顺序回放数据
        self.logger.info(f"开始数据回放，共 {len(all_dates)} 个交易日")
        
        # 重置账户状态
        self._reset_account()
        
        # 创建股票权益曲线DataFrame
        equity_curve = pd.DataFrame(index=all_dates, columns=[
            "cash", "equity", "drawdown", "drawdown_pct", "returns", "returns_pct"
        ])
        
        # 初始化指标
        prev_equity = self.initial_capital
        max_equity = self.initial_capital
        
        # 按日期回放数据
        for i, date in enumerate(all_dates):
            self.current_date = date
            
            # 更新账户状态
            self._update_account_status(date, data)
            
            # 记录当前日期的账户状态
            equity_curve.loc[date, "cash"] = self.account["cash"]
            equity_curve.loc[date, "equity"] = self.account["equity"]
            
            # 计算回撤
            max_equity = max(max_equity, self.account["equity"])
            drawdown = max_equity - self.account["equity"]
            drawdown_pct = drawdown / max_equity * 100 if max_equity > 0 else 0
            
            equity_curve.loc[date, "drawdown"] = drawdown
            equity_curve.loc[date, "drawdown_pct"] = drawdown_pct
            
            # 计算收益率
            returns = self.account["equity"] - prev_equity
            returns_pct = returns / prev_equity * 100 if prev_equity > 0 else 0
            
            equity_curve.loc[date, "returns"] = returns
            equity_curve.loc[date, "returns_pct"] = returns_pct
            
            prev_equity = self.account["equity"]
            
            # 构建当日每个品种的上下文
            for symbol in self.symbols:
                if symbol not in data:
                    continue
                
                # 获取历史数据截止到当前日期
                symbol_data = data[symbol]
                current_data = symbol_data[symbol_data.index <= date]
                
                if current_data.empty:
                    continue
                
                # 获取当前持仓
                position = self.account["positions"].get(symbol, 0)
                
                # 构建上下文
                context = {
                    "date": date,
                    "symbol": symbol,
                    "data": current_data,
                    "position": position,
                    "account": self.account,
                    "bars": len(current_data)
                }
                
                # 调用策略的on_bar方法
                try:
                    orders = self.strategy_instance.on_bar(context)
                    if orders:
                        self._process_orders(date, orders, data[symbol])
                except Exception as e:
                    self.logger.error(f"策略执行异常: {str(e)}")
            
            # 显示进度
            if i % 20 == 0 or i == len(all_dates) - 1:
                progress = (i + 1) / len(all_dates) * 100
                self.logger.info(f"回测进度: {progress:.2f}%, "
                                 f"当前日期: {date}, "
                                 f"账户净值: {self.account['equity']:.2f}")
                
        # 计算回测性能指标
        self.results["equity_curve"] = equity_curve
        self.results["summary"] = self._calculate_performance(equity_curve)
        self.results["trades"] = self.account["trades"]
        self.results["positions"] = list(self.account["positions"].items())
        
        # 回测完成
        elapsed_time = time.time() - start_time
        self.logger.info(f"回测完成，耗时 {elapsed_time:.2f} 秒")
        self.logger.info(f"初始资金: {self.initial_capital}")
        self.logger.info(f"最终净值: {self.account['equity']:.2f}")
        self.logger.info(f"总收益率: {self.results['summary']['total_return']:.2f}%")
        self.logger.info(f"年化收益率: {self.results['summary']['annual_return']:.2f}%")
        self.logger.info(f"最大回撤: {self.results['summary']['max_drawdown']:.2f}%")
        self.logger.info(f"夏普比率: {self.results['summary']['sharpe_ratio']:.2f}")
        self.logger.info(f"交易总次数: {self.results['summary']['total_trades']}")
        
        # 调用策略的回测完成回调
        if hasattr(self.strategy_instance, "on_backtest_finished"):
            try:
                self.strategy_instance.on_backtest_finished()
            except Exception as e:
                self.logger.error(f"策略回测完成回调异常: {str(e)}")
        
        return self.results
    
    def _extract_all_dates(self, data: Dict[str, pd.DataFrame]) -> List:
        """
        从所有数据集中提取唯一日期列表
        
        Args:
            data: 所有品种的历史数据字典
            
        Returns:
            排序后的唯一日期列表
        """
        all_dates = set()
        
        for symbol, df in data.items():
            if df.empty:
                continue
                
            # 获取日期
            dates = df.index.tolist()
            all_dates.update(dates)
        
        # 排序日期
        sorted_dates = sorted(all_dates)
        
        return sorted_dates
    
    def _reset_account(self):
        """重置账户状态"""
        self.account = {
            "initial_capital": self.initial_capital,
            "cash": self.initial_capital,
            "equity": self.initial_capital,
            "positions": {},
            "trades": []
        }
    
    def _update_account_status(self, date, data: Dict[str, pd.DataFrame]):
        """
        更新账户状态
        
        Args:
            date: 当前日期
            data: 所有品种的历史数据字典
        """
        # 计算持仓市值
        total_value = self.account["cash"]
        
        for symbol, position in self.account["positions"].items():
            if position == 0:
                continue
                
            if symbol not in data:
                self.logger.warning(f"无法找到 {symbol} 的价格数据，无法更新账户状态")
                continue
                
            df = data[symbol]
            
            # 查找当前日期的价格
            price_data = df[df.index <= date]
            if price_data.empty:
                continue
                
            latest_price = price_data["close"].iloc[-1]
            
            # 计算持仓市值
            position_value = position * latest_price
            total_value += position_value
        
        # 更新账户净值
        self.account["equity"] = total_value
    
    def _process_orders(self, date, orders, symbol_data: pd.DataFrame):
        """
        处理订单
        
        Args:
            date: 当前日期
            orders: 订单指令
            symbol_data: 交易品种的历史数据
        """
        if not orders:
            return
            
        # 如果orders不是列表，转换为列表
        if not isinstance(orders, list):
            orders = [orders]
            
        for order in orders:
            symbol = order.get("symbol", "")
            action = order.get("action", "")
            quantity = order.get("quantity", 0)
            price_type = order.get("price", "close")  # 价格类型，如"close", "open"或具体价格
            
            if not symbol or not action or quantity <= 0:
                self.logger.warning(f"订单参数不完整: {order}")
                continue
                
            # 获取价格
            price = 0
            if isinstance(price_type, (int, float)):
                price = float(price_type)
            else:
                # 获取当日数据
                day_data = symbol_data[symbol_data.index <= date]
                if day_data.empty:
                    self.logger.warning(f"无法找到 {date} 的 {symbol} 价格数据")
                    continue
                    
                latest_data = day_data.iloc[-1]
                
                if price_type == "open":
                    price = latest_data["open"]
                elif price_type == "high":
                    price = latest_data["high"]
                elif price_type == "low":
                    price = latest_data["low"]
                else:  # 默认使用收盘价
                    price = latest_data["close"]
            
            # 考虑滑点
            if action in ["买入", "buy", "buy_to_cover"]:
                price *= (1 + self.slippage)
            elif action in ["卖出", "sell", "sell_short"]:
                price *= (1 - self.slippage)
            
            # 处理买入订单
            if action in ["买入", "buy"]:
                self._process_buy_order(date, symbol, quantity, price, order)
                
            # 处理卖出订单
            elif action in ["卖出", "sell"]:
                self._process_sell_order(date, symbol, quantity, price, order)
    
    def _process_buy_order(self, date, symbol, quantity, price, order):
        """
        处理买入订单
        
        Args:
            date: 交易日期
            symbol: 交易品种
            quantity: 交易数量
            price: 交易价格
            order: 原始订单
        """
        # 计算买入成本
        cost = quantity * price
        commission = cost * self.commission_rate
        total_cost = cost + commission
        
        # 检查资金是否足够
        if total_cost > self.account["cash"]:
            affordable_quantity = int(self.account["cash"] / price / (1 + self.commission_rate))
            
            if affordable_quantity <= 0:
                self.logger.warning(f"资金不足，无法买入 {symbol}")
                return
                
            self.logger.warning(f"资金不足，调整买入数量 {quantity} -> {affordable_quantity}")
            quantity = affordable_quantity
            cost = quantity * price
            commission = cost * self.commission_rate
            total_cost = cost + commission
        
        # 更新账户状态
        self.account["cash"] -= total_cost
        self.account["positions"][symbol] = self.account["positions"].get(symbol, 0) + quantity
        
        # 记录交易
        trade = {
            "date": date,
            "symbol": symbol,
            "action": "买入",
            "quantity": quantity,
            "price": price,
            "cost": cost,
            "commission": commission,
            "total_cost": total_cost,
            "reason": order.get("reason", "")
        }
        
        self.account["trades"].append(trade)
        self.logger.info(f"买入: {date}, {symbol}, {quantity}股, 价格: {price}, 总成本: {total_cost}")
        
        # 通知策略
        if hasattr(self.strategy_instance, "on_trade"):
            self.strategy_instance.on_trade(trade)
    
    def _process_sell_order(self, date, symbol, quantity, price, order):
        """
        处理卖出订单
        
        Args:
            date: 交易日期
            symbol: 交易品种
            quantity: 交易数量
            price: 交易价格
            order: 原始订单
        """
        # 检查持仓是否足够
        current_position = self.account["positions"].get(symbol, 0)
        
        if quantity > current_position:
            if current_position <= 0:
                self.logger.warning(f"无 {symbol} 持仓，无法卖出")
                return
                
            self.logger.warning(f"持仓不足，调整卖出数量 {quantity} -> {current_position}")
            quantity = current_position
        
        # 计算卖出收入
        proceeds = quantity * price
        commission = proceeds * self.commission_rate
        net_proceeds = proceeds - commission
        
        # 更新账户状态
        self.account["cash"] += net_proceeds
        self.account["positions"][symbol] = current_position - quantity
        
        # 如果持仓变为0，删除该持仓记录
        if self.account["positions"][symbol] == 0:
            del self.account["positions"][symbol]
        
        # 记录交易
        trade = {
            "date": date,
            "symbol": symbol,
            "action": "卖出",
            "quantity": quantity,
            "price": price,
            "proceeds": proceeds,
            "commission": commission,
            "net_proceeds": net_proceeds,
            "reason": order.get("reason", "")
        }
        
        self.account["trades"].append(trade)
        self.logger.info(f"卖出: {date}, {symbol}, {quantity}股, 价格: {price}, 净收入: {net_proceeds}")
        
        # 通知策略
        if hasattr(self.strategy_instance, "on_trade"):
            self.strategy_instance.on_trade(trade)
    
    def _calculate_performance(self, equity_curve: pd.DataFrame) -> Dict:
        """
        计算回测性能指标
        
        Args:
            equity_curve: 权益曲线DataFrame
            
        Returns:
            性能指标字典
        """
        if equity_curve.empty:
            return {}
            
        # 计算基本指标
        initial_equity = equity_curve["equity"].iloc[0]
        final_equity = equity_curve["equity"].iloc[-1]
        
        # 总收益率
        total_return = (final_equity / initial_equity - 1) * 100
        
        # 计算年化收益率
        days = (equity_curve.index[-1] - equity_curve.index[0]).days
        years = max(days / 365, 0.01)  # 避免除零错误
        annual_return = ((final_equity / initial_equity) ** (1 / years) - 1) * 100
        
        # 最大回撤
        max_drawdown = equity_curve["drawdown_pct"].max()
        
        # 计算夏普比率
        daily_returns = equity_curve["returns_pct"].dropna() / 100
        risk_free_rate = 0.02 / 252  # 假设无风险年化收益率为2%
        excess_returns = daily_returns - risk_free_rate
        
        sharpe_ratio = 0
        if len(excess_returns) > 1 and excess_returns.std() > 0:
            sharpe_ratio = (excess_returns.mean() / excess_returns.std()) * np.sqrt(252)
        
        # 胜率和盈亏比
        trades = self.account["trades"]
        total_trades = len(trades)
        winning_trades = sum(1 for t in trades if t.get("action") == "卖出" and t.get("net_proceeds", 0) > t.get("total_cost", 0))
        
        win_rate = winning_trades / total_trades * 100 if total_trades > 0 else 0
        
        # 年化波动率
        annual_volatility = daily_returns.std() * np.sqrt(252) * 100
        
        # 卡玛比率
        calmar_ratio = annual_return / max_drawdown if max_drawdown > 0 else 0
        
        return {
            "initial_equity": initial_equity,
            "final_equity": final_equity,
            "total_return": total_return,
            "annual_return": annual_return,
            "max_drawdown": max_drawdown,
            "sharpe_ratio": sharpe_ratio,
            "calmar_ratio": calmar_ratio,
            "annual_volatility": annual_volatility,
            "win_rate": win_rate,
            "total_trades": total_trades,
            "winning_trades": winning_trades
        }
    
    def save_results(self, filename=None):
        """
        保存回测结果
        
        Args:
            filename: 文件名，如果为None则自动生成
        """
        if not filename:
            now = datetime.now().strftime("%Y%m%d_%H%M%S")
            strategy_name = self.strategy_instance.__class__.__name__
            filename = f"{strategy_name}_{now}"
        
        # 保存路径
        result_dir = os.path.join(self.output_dir, filename)
        if not os.path.exists(result_dir):
            os.makedirs(result_dir)
        
        # 保存权益曲线
        equity_curve_path = os.path.join(result_dir, "equity_curve.csv")
        self.results["equity_curve"].to_csv(equity_curve_path)
        
        # 保存交易记录
        trades_path = os.path.join(result_dir, "trades.csv")
        trades_df = pd.DataFrame(self.results["trades"])
        if not trades_df.empty:
            trades_df.to_csv(trades_path, index=False)
        
        # 保存性能指标
        summary_path = os.path.join(result_dir, "summary.csv")
        summary_df = pd.DataFrame([self.results["summary"]])
        summary_df.to_csv(summary_path, index=False)
        
        self.logger.info(f"回测结果已保存到: {result_dir}")
        
        return result_dir


if __name__ == "__main__":
    # 这里是测试代码
    from config.settings import setup_system
    from strategy.ma_cross_strategy import MACrossStrategy

    # 初始化系统
    setup_system()
    
    # 创建回测引擎
    backtest = BacktestEngine(
        strategy=MACrossStrategy,
        strategy_params={"short_period": 5, "long_period": 20},
        start_date="20230101",
        end_date="20231231",
        period="1d",
        symbols=["000001.SZ", "600000.SH"],
        initial_capital=100000,
        commission_rate=0.0003,
        slippage=0.0001,
        enable_live_plot=False,
        output_dir="./output"
    )
    
    # 运行回测
    results = backtest.run_backtest()
    
    # 保存结果
    backtest.save_results() 