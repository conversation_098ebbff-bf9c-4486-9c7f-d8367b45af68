#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据处理辅助工具模块

提供通用的数据处理辅助函数，避免重复代码
"""

import os
from typing import List, Optional, Tuple, Dict, Any
from datetime import datetime
from dataclasses import dataclass

from utils.logger import get_unified_logger, LogTarget
from utils.time_formatter.parsing import parse_multi_format_date
from utils.time_formatter.validation import get_default_period_dates
from utils.text_parser import parse_text_items

logger = get_unified_logger(__name__, enhanced=True)


@dataclass
class DisplayOptions:
    """显示选项数据类"""
    mode: str = "both"  # "head", "tail", "both", "all"
    head_lines: int = 5
    tail_lines: int = 5


@dataclass
class DateRange:
    """日期范围数据类"""
    start_date: Optional[str] = None
    end_date: Optional[str] = None


class DataHelpers:
    """数据处理辅助工具类"""
    
    @staticmethod
    def parse_date(date_str: str) -> datetime:
        """
        解析日期字符串，支持多种格式
        
        Args:
            date_str: 日期字符串
            
        Returns:
            解析后的日期时间对象
        """
        return parse_multi_format_date(date_str)
    
    @staticmethod
    def process_date_params(period: str, start_date: Optional[str], end_date: Optional[str]) -> DateRange:
        """
        处理日期参数，提供默认值
        
        Args:
            period: 数据周期
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            处理后的日期范围
        """
        if not start_date or not end_date:
            default_start, default_end = get_default_period_dates(period)
            start_date = start_date if start_date is not None else default_start
            end_date = end_date if end_date is not None else default_end
        
        return DateRange(start_date=start_date, end_date=end_date)
    
    @staticmethod
    def standardize_stock_codes(stock_codes: List[str]) -> List[str]:
        """
        标准化股票代码，确保所有代码都有市场后缀
        
        Args:
            stock_codes: 股票代码列表
            
        Returns:
            标准化后的股票代码列表
        """
        standardized_codes = []
        
        for code in stock_codes:
            code = code.strip().upper()
            
            # 已经有市场后缀
            if "." in code:
                standardized_codes.append(code)
                continue
            
            # 根据代码前缀判断市场
            if code.startswith(("6", "5", "900")):
                standardized_codes.append(f"{code}.SH")
            elif code.startswith(("0", "1", "2", "3", "200", "201", "238")):
                standardized_codes.append(f"{code}.SZ")
            elif code.startswith(("4", "8", "43", "83", "87")):
                standardized_codes.append(f"{code}.BJ")
            elif (code.startswith("6") and len(code) <= 6) or (
                code.startswith("0") and len(code) <= 6
            ):
                standardized_codes.append(f"{code}.SH")
            else:
                standardized_codes.append(code)
                logger.warning(f"无法确定股票 {code} 的市场，保持原样")
        
        logger.info(LogTarget.FILE, f"标准化股票代码: {standardized_codes}")
        return standardized_codes
    
    @staticmethod
    def parse_display_options(display_rows: int = 5, 
                            display_head_rows: Optional[int] = None,
                            display_tail_rows: Optional[int] = None) -> DisplayOptions:
        """
        解析显示选项
        
        Args:
            display_rows: 默认显示行数
            display_head_rows: 头部显示行数
            display_tail_rows: 尾部显示行数
            
        Returns:
            显示选项对象
        """
        head_lines = display_head_rows if display_head_rows is not None else display_rows
        tail_lines = display_tail_rows if display_tail_rows is not None else display_rows
        
        # 根据行数确定显示模式
        if head_lines > 0 and tail_lines > 0:
            mode = "both"
        elif head_lines > 0:
            mode = "head"
        elif tail_lines > 0:
            mode = "tail"
        else:
            mode = "head"
            head_lines = display_rows
        
        return DisplayOptions(mode=mode, head_lines=head_lines, tail_lines=tail_lines)
    
    @staticmethod
    def parse_fields_input(fields_input: str) -> Optional[List[str]]:
        """
        解析字段输入字符串
        
        Args:
            fields_input: 字段输入字符串
            
        Returns:
            字段列表，如果为空则返回None
        """
        if not fields_input.strip():
            return None
        return parse_text_items(fields_input)
    
    @staticmethod
    def format_stock_list_display(stock_list: List[str], max_display: int = 5) -> str:
        """
        格式化股票列表显示
        
        Args:
            stock_list: 股票列表
            max_display: 最大显示数量
            
        Returns:
            格式化的显示字符串
        """
        if len(stock_list) <= max_display:
            return ', '.join(stock_list)
        else:
            return f"{', '.join(stock_list[:max_display])}... (共{len(stock_list)}个)"
    
    @staticmethod
    def validate_stock_codes(stock_codes: List[str]) -> Tuple[List[str], List[str]]:
        """
        验证股票代码格式
        
        Args:
            stock_codes: 股票代码列表
            
        Returns:
            (有效代码列表, 无效代码列表)
        """
        valid_codes = []
        invalid_codes = []
        
        for code in stock_codes:
            code = code.strip()
            if not code:
                continue
                
            # 简单验证：应该包含数字和可能的市场后缀
            if "." in code:
                parts = code.split(".")
                if len(parts) == 2 and parts[0].isdigit() and parts[1].upper() in ["SH", "SZ", "BJ"]:
                    valid_codes.append(code.upper())
                else:
                    invalid_codes.append(code)
            elif code.isdigit() and len(code) == 6:
                valid_codes.append(code)
            else:
                invalid_codes.append(code)
        
        return valid_codes, invalid_codes
    
    @staticmethod
    def get_log_file_path(logger_name: str) -> str:
        """
        获取日志文件路径
        
        Args:
            logger_name: 日志器名称
            
        Returns:
            日志文件路径
        """
        return f"logs/{logger_name}.log"


def get_data_helpers() -> DataHelpers:
    """获取数据辅助工具实例"""
    return DataHelpers()