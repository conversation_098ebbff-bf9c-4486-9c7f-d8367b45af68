#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
tick数据时间过滤模块

专门用于tick数据存储前的非交易时间过滤，集成双重边界处理逻辑。
过滤策略：
1. 保留所有交易时间数据
2. 保留有效的边界数据（应用双重边界处理逻辑）
3. 过滤掉纯休盘时间数据
"""

import pandas as pd
import numpy as np
from datetime import datetime
from typing import Optional, Tuple

from utils.logger import get_unified_logger
from utils.time_formatter.trading_time import detect_symbol_type, detect_futures_category
from utils.time_formatter.vectorized_time_judge import is_trading_time_batch, is_auction_time_batch
from utils.time_utils import simple_string_to_datetime_list
import time as time_module
import functools

def function_performance_monitor(func):
    """
    函数性能监控装饰器

    监控函数执行时间和调用次数
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time_module.time()

        # 执行函数
        result = func(*args, **kwargs)

        # 记录执行时间
        end_time = time_module.time()
        execution_time = end_time - start_time

        # 记录性能日志
        if len(args) > 0 and hasattr(args[0], '__len__'):
            batch_size = len(args[0])
            logger.debug(f"函数 {func.__name__} 执行完成: 数据量={batch_size}, 耗时={execution_time:.4f}秒")
        else:
            logger.debug(f"函数 {func.__name__} 执行完成: 耗时={execution_time:.4f}秒")

        return result

    return wrapper

logger = get_unified_logger(__name__)


@function_performance_monitor
def filter_tick_data_for_storage(df: pd.DataFrame, symbol: str = "") -> pd.DataFrame:
    """
    tick数据存储前的时间过滤函数
    
    集成双重边界处理逻辑，过滤非交易时间数据，保留有效的边界数据。
    
    过滤策略：
    1. 保留所有连续竞价时间数据
    2. 保留所有集合竞价时间数据（如08:55-09:00）🆕
    3. 保留边界延时数据（如15:00:10 → 保留）
    4. 保留跨分钟边界数据（如15:01:00 → 保留）
    5. 过滤纯休盘时间数据
    
    Args:
        df: tick数据DataFrame，索引应为时间字符串格式(YYYYMMDDHHMMSS)
        symbol: 股票代码，用于判断品种类型和交易时间
        
    Returns:
        pd.DataFrame: 过滤后的tick数据
    """
    if df is None or df.empty:
        logger.warning("输入tick数据为空，无法进行时间过滤")
        return df

    # 验证数据格式
    if not isinstance(df.index, (pd.Index, pd.RangeIndex)):
        logger.error("数据索引格式不正确，无法进行时间过滤")
        return df

    if not symbol:
        logger.warning("未提供股票代码，使用默认品种类型进行过滤")
        symbol = "000001.SZ"  # 默认A股
        
    logger.info(f"开始tick数据时间过滤，输入数据行数: {len(df)}")
    logger.info(f"使用双重边界处理逻辑进行时间过滤")
    
    try:
        # 检测品种类型
        symbol_type = detect_symbol_type(symbol)
        futures_category = detect_futures_category(symbol) if symbol_type == 'futures' else 'most'
        logger.info(f"检测到品种类型: {symbol_type}, 期货分类: {futures_category}")
        
        # 步骤1: 安全的时间索引转换
        try:
            time_index = simple_string_to_datetime_list(df.index, '%Y%m%d%H%M%S')
            logger.debug(f"时间索引转换完成: {len(time_index)} 个时间点")
        except Exception as e:
            logger.error(f"时间索引转换失败: {e}")
            logger.warning("时间索引转换失败，返回原始数据")
            return df

        # 验证时间索引长度一致性
        if len(time_index) != len(df):
            logger.error(f"时间索引长度不一致: time_index={len(time_index)}, df={len(df)}")
            logger.warning("时间索引长度不一致，返回原始数据")
            return df

        # 步骤2: 向量化时间判断（交易时间 + 集合竞价时间）
        try:
            is_trading_mask = is_trading_time_batch(time_index, symbol)
            is_auction_mask = is_auction_time_batch(time_index, symbol)
            logger.debug(f"时间判断完成: 生成 {len(is_trading_mask)} 个交易时间判断, {len(is_auction_mask)} 个集合竞价判断")
        except Exception as e:
            logger.error(f"时间判断失败: {e}")
            logger.warning("时间判断失败，返回原始数据")
            return df

        trading_count = is_trading_mask.sum()
        auction_count = is_auction_mask.sum()
        non_trading_auction_count = len(time_index) - trading_count - auction_count

        logger.debug(f"时间判断完成: {trading_count} 个连续竞价时间, {auction_count} 个集合竞价时间, {non_trading_auction_count} 个非交易时间")
        
        # 步骤3: 应用双重边界处理逻辑识别有效边界数据
        try:
            # 传入交易时间和集合竞价时间的组合掩码
            valid_time_mask = is_trading_mask | is_auction_mask
            boundary_mask = _identify_boundary_data_for_filter(time_index, valid_time_mask, symbol)
            boundary_count = boundary_mask.sum()
            logger.debug(f"边界数据识别完成: {boundary_count} 个有效边界数据")
        except Exception as e:
            logger.error(f"边界数据识别失败: {e}")
            logger.warning("边界数据识别失败，仅使用交易时间和集合竞价时间过滤")
            boundary_mask = np.zeros(len(time_index), dtype=bool)
            boundary_count = 0
        
        # 步骤4: 组合过滤条件（交易时间 + 集合竞价时间 + 边界数据）- 确保所有掩码都是numpy数组
        # 将所有掩码转换为numpy数组以确保一致性
        if hasattr(is_trading_mask, 'values'):
            is_trading_mask = is_trading_mask.values
        if hasattr(is_auction_mask, 'values'):
            is_auction_mask = is_auction_mask.values
        if hasattr(boundary_mask, 'values'):
            boundary_mask = boundary_mask.values

        keep_mask = is_trading_mask | is_auction_mask | boundary_mask
        keep_count = keep_mask.sum()
        filter_count = len(df) - keep_count

        # 验证掩码长度一致性
        if len(keep_mask) != len(df):
            logger.error(f"掩码长度不匹配: keep_mask={len(keep_mask)}, df={len(df)}")
            logger.warning("掩码长度不匹配，返回原始数据")
            return df

        logger.debug(f"掩码类型验证: is_trading_mask={type(is_trading_mask)}, boundary_mask={type(boundary_mask)}, keep_mask={type(keep_mask)}")

        # 详细统计日志
        logger.info(f"tick数据时间过滤统计:")
        logger.info(f"- 原始数据: {len(df)} 行")
        logger.info(f"- 连续竞价时间数据: {trading_count} 行")
        logger.info(f"- 集合竞价时间数据: {auction_count} 行")
        logger.info(f"- 有效边界数据: {boundary_count} 行")
        logger.info(f"- 保留数据: {keep_count} 行")
        logger.info(f"- 过滤数据: {filter_count} 行")
        logger.info(f"- 过滤比例: {filter_count/len(df)*100:.1f}%")

        # 如果有边界数据，输出具体的时间点
        if boundary_count > 0:
            try:
                boundary_times = df.index[boundary_mask].tolist()
                logger.debug(f"需要处理的边界时间点: {boundary_times[:10]}{'...' if len(boundary_times) > 10 else ''}")
            except Exception as e:
                logger.warning(f"获取边界时间点失败: {e}")

        # 步骤5: 安全的过滤操作
        try:
            filtered_df = df[keep_mask].copy()
            logger.debug(f"数据过滤操作完成: {len(filtered_df)} 行")
        except Exception as e:
            logger.error(f"数据过滤操作失败: {e}")
            logger.warning("数据过滤失败，返回原始数据")
            return df
        
        logger.info(f"tick数据时间过滤完成，输出数据行数: {len(filtered_df)}")
        
        return filtered_df
        
    except Exception as e:
        logger.error(f"tick数据时间过滤失败: {e}")
        logger.warning("过滤失败，返回原始数据")
        return df


@function_performance_monitor
def filter_kline_data_for_storage(df: pd.DataFrame, symbol: str = "") -> pd.DataFrame:
    """
    下载的1m数据存储前的时间过滤函数

    专门用于下载的1分钟K线数据的时间过滤，只保留交易时间数据。
    注意：不使用双重边界处理逻辑，不需要合并边界数据。

    过滤策略：
    1. 保留所有交易时间数据
    2. 过滤所有非交易时间数据
    3. 不保留边界数据（与tick数据过滤的区别）

    Args:
        df: 1m K线数据DataFrame，索引应为时间字符串格式(YYYYMMDDHHMMSS)
        symbol: 股票代码，用于判断品种类型和交易时间

    Returns:
        pd.DataFrame: 过滤后的1m K线数据
    """
    if df is None or df.empty:
        logger.warning("输入1m数据为空，无法进行时间过滤")
        return df

    # 验证数据格式
    if not isinstance(df.index, (pd.Index, pd.RangeIndex)):
        logger.error("数据索引格式不正确，无法进行时间过滤")
        return df

    if not symbol:
        logger.warning("未提供股票代码，使用默认品种类型进行过滤")
        symbol = "000001.SZ"  # 默认A股

    logger.info(f"开始1m数据时间过滤，输入数据行数: {len(df)}")
    logger.info(f"使用简单交易时间过滤（不使用边界处理逻辑）")

    try:
        # 检测品种类型
        symbol_type = detect_symbol_type(symbol)
        futures_category = detect_futures_category(symbol) if symbol_type == 'futures' else 'most'
        logger.info(f"检测到品种类型: {symbol_type}, 期货分类: {futures_category}")

        # 步骤1: 安全的时间索引转换
        try:
            time_index = simple_string_to_datetime_list(df.index, '%Y%m%d%H%M%S')
            logger.debug(f"时间索引转换完成: {len(time_index)} 个时间点")
        except Exception as e:
            logger.error(f"时间索引转换失败: {e}")
            logger.warning("时间索引转换失败，返回原始数据")
            return df

        # 验证时间索引长度一致性
        if len(time_index) != len(df):
            logger.error(f"时间索引长度不一致: time_index={len(time_index)}, df={len(df)}")
            logger.warning("时间索引长度不一致，返回原始数据")
            return df

        # 步骤2: 向量化交易时间判断（只使用交易时间判断）
        try:
            is_trading_mask = is_trading_time_batch(time_index, symbol)
            logger.debug(f"交易时间判断完成: 生成 {len(is_trading_mask)} 个判断结果")
        except Exception as e:
            logger.error(f"交易时间判断失败: {e}")
            logger.warning("交易时间判断失败，返回原始数据")
            return df
        trading_count = is_trading_mask.sum()
        non_trading_count = (~is_trading_mask).sum()

        logger.debug(f"交易时间判断完成: {trading_count} 个交易时间, {non_trading_count} 个非交易时间")

        # 步骤3: 应用简单过滤条件（只保留交易时间数据）
        # 确保is_trading_mask是numpy数组，避免索引问题
        if hasattr(is_trading_mask, 'values'):
            is_trading_mask = is_trading_mask.values

        keep_mask = is_trading_mask  # 不使用边界处理逻辑
        keep_count = keep_mask.sum()
        filter_count = len(df) - keep_count

        # 验证掩码长度一致性
        if len(keep_mask) != len(df):
            logger.error(f"掩码长度不匹配: keep_mask={len(keep_mask)}, df={len(df)}")
            logger.warning("掩码长度不匹配，返回原始数据")
            return df

        logger.debug(f"掩码类型验证: is_trading_mask={type(is_trading_mask)}, keep_mask={type(keep_mask)}")

        # 详细统计日志
        logger.info(f"1m数据时间过滤统计:")
        logger.info(f"- 原始数据: {len(df)} 行")
        logger.info(f"- 交易时间数据: {trading_count} 行")
        logger.info(f"- 保留数据: {keep_count} 行")
        logger.info(f"- 过滤数据: {filter_count} 行")
        logger.info(f"- 过滤比例: {filter_count/len(df)*100:.1f}%")

        # 步骤4: 安全的过滤操作
        try:
            filtered_df = df[keep_mask].copy()
            logger.debug(f"数据过滤操作完成: {len(filtered_df)} 行")
        except Exception as e:
            logger.error(f"数据过滤操作失败: {e}")
            logger.warning("数据过滤失败，返回原始数据")
            return df

        logger.info(f"1m数据时间过滤完成，输出数据行数: {len(filtered_df)}")

        return filtered_df

    except Exception as e:
        logger.error(f"1m数据时间过滤失败: {e}")
        logger.warning("过滤失败，返回原始数据")
        return df


def _identify_boundary_data_for_filter(time_index: pd.DatetimeIndex,
                                      is_trading_mask: np.ndarray,
                                      symbol: str) -> np.ndarray:
    """
    识别需要保留的边界数据（应用双重边界处理逻辑）

    Args:
        time_index: 时间索引
        is_trading_mask: 交易时间掩码
        symbol: 股票代码

    Returns:
        np.ndarray: 边界数据掩码
    """
    try:
        # 验证输入参数
        if len(time_index) != len(is_trading_mask):
            logger.error(f"输入参数长度不一致: time_index={len(time_index)}, is_trading_mask={len(is_trading_mask)}")
            return np.zeros(len(time_index), dtype=bool)

        logger.debug(f"开始边界数据识别: 处理 {len(time_index)} 个时间点")
        # 计算时间差（秒）- 避免索引依赖
        try:
            # 使用pandas Series但不保留索引，直接转换为numpy数组
            time_series = pd.Series(time_index)
            time_diff_seconds = time_series.diff().dt.total_seconds().values
            time_diff_minutes = time_diff_seconds / 60
            logger.debug(f"时间差计算完成: {len(time_diff_seconds)} 个时间差值")
        except Exception as e:
            logger.error(f"时间差计算失败: {e}")
            return np.zeros(len(time_index), dtype=bool)

        # 双重边界判断条件（与K线合成逻辑一致）- 确保结果为numpy数组
        try:
            # 条件1: 休盘边界处理 - 跨分钟边界数据（如15:01 -> 15:00）
            condition1 = (time_diff_minutes == 1)
            # 条件2: 延时处理 - 同分钟内的录制延时数据（如15:00:10 -> 15:00:00）
            condition2 = (time_diff_seconds > 0) & (time_diff_seconds <= 60)

            # 边界数据必须是非交易时间，且满足双重条件之一
            # 重要：将结果转换为numpy数组，避免索引不匹配问题
            boundary_mask = ((condition1 | condition2) & (~is_trading_mask))
            if hasattr(boundary_mask, 'values'):
                boundary_mask = boundary_mask.values
            logger.debug(f"边界判断条件计算完成: boundary_mask长度={len(boundary_mask)}, 类型={type(boundary_mask)}")
        except Exception as e:
            logger.error(f"边界判断条件计算失败: {e}")
            return np.zeros(len(time_index), dtype=bool)
        
        # 详细统计
        condition1_count = (condition1 & (~is_trading_mask)).sum()
        condition2_count = (condition2 & (~is_trading_mask)).sum()
        overlap_count = (condition1 & condition2 & (~is_trading_mask)).sum()
        
        logger.debug(f"边界数据识别统计:")
        logger.debug(f"- 跨分钟边界数据: {condition1_count} 个")
        logger.debug(f"- 延时边界数据: {condition2_count} 个")
        logger.debug(f"- 重叠数据: {overlap_count} 个")
        logger.debug(f"- 总边界数据: {boundary_mask.sum()} 个")
        
        return boundary_mask
        
    except Exception as e:
        logger.error(f"边界数据识别失败: {e}")
        # 返回空掩码，只保留交易时间数据
        return np.zeros(len(time_index), dtype=bool)


def get_filter_statistics(original_count: int, filtered_count: int) -> dict:
    """
    获取过滤统计信息
    
    Args:
        original_count: 原始数据行数
        filtered_count: 过滤后数据行数
        
    Returns:
        dict: 统计信息字典
    """
    filter_count = original_count - filtered_count
    filter_ratio = filter_count / original_count if original_count > 0 else 0
    
    return {
        'original_count': original_count,
        'filtered_count': filtered_count,
        'removed_count': filter_count,
        'filter_ratio': filter_ratio,
        'storage_reduction': f"{filter_ratio*100:.1f}%"
    }


def is_tick_data_period(period: str) -> bool:
    """
    判断是否为tick数据周期

    Args:
        period: 数据周期字符串

    Returns:
        bool: 是否为tick数据
    """
    return period.lower() == 'tick'


def is_kline_data_period(period: str) -> bool:
    """
    判断是否为需要过滤的K线数据周期（下载的1m数据）

    Args:
        period: 数据周期字符串

    Returns:
        bool: 是否为需要过滤的K线数据
    """
    return period.lower() == '1m'


def should_apply_time_filter(period: str) -> bool:
    """
    判断是否应该应用时间过滤

    Args:
        period: 数据周期字符串

    Returns:
        bool: 是否应该过滤
    """
    # 硬编码：对tick数据和1m数据启用时间过滤
    return is_tick_data_period(period) or is_kline_data_period(period)


# 导出的主要函数
__all__ = [
    'filter_tick_data_for_storage',
    'filter_kline_data_for_storage',
    'get_filter_statistics',
    'is_tick_data_period',
    'is_kline_data_period',
    'should_apply_time_filter'
]
