#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
下载处理器模块

专门负责历史数据下载逻辑，从原data_source_manager.py中提取
"""

import os
import pandas as pd
from datetime import datetime
from typing import Dict, List, Optional, Any

from config.settings import DATA_ROOT, STORAGE_MAX_WORKERS
from utils.logger import get_unified_logger, LogTarget
from utils.time_formatter.validation import validate_date_format, adjust_to_trading_day
from utils.data_processor import merge_dataframes, validate_merged_data
from data.storage.parquet_storage import ParquetStorage
from data.storage.parquet_reader import read_latest_data_timestamp
from data.storage.path_manager import get_latest_partition_file
from .data_processor import DataProcessor

logger = get_unified_logger(__name__, enhanced=True)


class DownloadHandler:
    """历史数据下载处理器"""
    
    def __init__(self, data_root: str = None, fetcher_instance=None):
        """
        初始化下载处理器
        
        Args:
            data_root: 数据根目录
            fetcher_instance: 数据获取器实例
        """
        self.data_root = data_root or DATA_ROOT
        self.fetcher = fetcher_instance
        self.data_processor = DataProcessor()
        
    def download_history_data(self,
                            stock_list: List[str],
                            period: str,
                            start_time: str,
                            end_time: str,
                            incremental: bool = False,
                            overlap_days: int = 1,
                            force_update: bool = False,
                            validate_data: bool = True,
                            display_head_rows: int = 5,
                            display_tail_rows: int = 5,
                            show_data: bool = True,
                            real_time_save: bool = True,
                            result_file: Optional[str] = None,
                            **kwargs) -> Dict[str, Any]:
        """
        下载历史数据的主要方法
        
        Args:
            stock_list: 股票代码列表
            period: 数据周期
            start_time: 开始时间
            end_time: 结束时间
            incremental: 是否增量更新
            overlap_days: 重叠天数
            force_update: 是否强制更新
            validate_data: 是否验证数据
            display_head_rows: 显示头部行数
            display_tail_rows: 显示尾部行数
            show_data: 是否显示数据
            real_time_save: 是否实时保存
            result_file: 结果文件路径
            **kwargs: 其他参数
            
        Returns:
            包含下载结果的字典
        """
        logger.info(f"开始下载 {len(stock_list)} 个股票的 {period} 数据")
        
        # 验证参数
        if not self._validate_parameters(start_time, end_time, period):
            return {"success": False, "message": "参数验证失败"}
            
        # 调整交易日
        start_time, end_time = self._adjust_trading_dates(start_time, end_time, period)
        
        # 初始化结果
        result = self._initialize_result()
        total_stocks = len(stock_list)
        
        # 检查数据源
        if not self.fetcher:
            logger.error("数据获取器未初始化")
            return {"success": False, "message": "数据获取器未初始化"}
            
        # 处理每个股票
        for i, symbol in enumerate(stock_list):
            logger.info(f"[{i+1}/{total_stocks}] 处理: {symbol}")
            
            try:
                # 检查是否需要下载
                needs_download, local_data = self._check_download_needed(
                    symbol, period, start_time, end_time, incremental, force_update
                )
                
                if not needs_download:
                    self._handle_no_download_needed(symbol, local_data, result,
                                                   display_head_rows, display_tail_rows, **kwargs)
                    continue
                    
                # 处理增量更新
                actual_start_time = self._handle_incremental_update(
                    symbol, period, start_time, incremental, overlap_days
                ) if incremental else start_time
                
                # 下载数据
                success = self._download_single_stock(
                    symbol, period, actual_start_time, end_time,
                    local_data, incremental, force_update, validate_data,
                    display_head_rows, display_tail_rows, result, **kwargs
                )
                
                if success:
                    logger.info(f"{symbol} 下载成功")
                else:
                    logger.warning(f"{symbol} 下载失败")
                    
            except Exception as e:
                logger.error(f"处理 {symbol} 时出错: {e}")
                self._handle_download_error(symbol, str(e), result)
                
        # 汇总结果
        return self._finalize_result(result, total_stocks)
    
    def _validate_parameters(self, start_time: str, end_time: str, period: str) -> bool:
        """验证输入参数"""
        if start_time and not validate_date_format(start_time):
            logger.error(f"开始日期格式错误: {start_time}")
            return False
            
        if end_time and not validate_date_format(end_time):
            logger.error(f"结束日期格式错误: {end_time}")
            return False
            
        if start_time and end_time and start_time > end_time:
            logger.error(f"开始日期 {start_time} 晚于结束日期 {end_time}")
            return False
            
        return True
    
    def _adjust_trading_dates(self, start_time: str, end_time: str, period: str) -> tuple:
        """调整到交易日"""
        need_adjustment = period in ['1d', '1w', '1mon'] or (period.endswith('m'))
        
        if not need_adjustment:
            return start_time, end_time
            
        adjusted_start = start_time
        adjusted_end = end_time
        
        # 调整开始日期
        if start_time and len(start_time) >= 8:
            start_date = start_time[:8]
            if start_date:
                adjusted_start_date = adjust_to_trading_day(start_date, 'forward')
                if adjusted_start_date != start_date:
                    logger.info(f"开始日期调整: {start_date} -> {adjusted_start_date}")
                    adjusted_start = adjusted_start_date + (start_time[8:] if len(start_time) > 8 else "")
                    
        # 调整结束日期
        if end_time and len(end_time) >= 8:
            end_date = end_time[:8]
            if end_date:
                adjusted_end_date = adjust_to_trading_day(end_date, 'backward')
                if adjusted_end_date != end_date:
                    logger.info(f"结束日期调整: {end_date} -> {adjusted_end_date}")
                    adjusted_end = adjusted_end_date + (end_time[8:] if len(end_time) > 8 else "")
                    
        return adjusted_start, adjusted_end
    
    def _initialize_result(self) -> Dict[str, Any]:
        """初始化结果字典"""
        return {
            "success": False,
            "successful_symbols": [],
            "failed_symbols": [],
            "data": {},
            "save_paths": {},
            "failed_reasons": {},
            "no_download_needed": []
        }
    
    def _check_download_needed(self, symbol: str, period: str, 
                              start_time: str, end_time: str,
                              incremental: bool, force_update: bool) -> tuple:
        """检查是否需要下载数据"""
        try:
            # 获取最新本地数据
            latest_file = get_latest_partition_file(self.data_root, symbol, period)
            
            if not latest_file or not os.path.exists(latest_file):
                logger.info(f"未找到 {symbol} 本地数据，需要下载")
                return True, None
                
            # 读取本地数据
            local_data = pd.read_parquet(latest_file)
            if local_data.empty:
                logger.info(f"{symbol} 本地数据为空，需要下载")
                return True, None
                
            # 获取本地数据时间范围
            local_start, local_end = self.data_processor.get_data_time_range(local_data)
            
            if local_end is None:
                logger.warning(f"无法获取 {symbol} 本地数据时间范围，需要下载")
                return True, local_data
                
            # 检查时间覆盖
            local_end_str = local_end.strftime("%Y%m%d")
            user_end_str = end_time[:8] if end_time else datetime.now().strftime("%Y%m%d")
            
            # 检查数据源更新
            if not force_update:
                has_update = self._check_data_source_updated(symbol, period, local_end_str)
                if not has_update:
                    logger.info(f"{symbol} 数据源无更新，无需下载")
                    return False, local_data
                    
            # 检查时间覆盖范围
            if local_end_str >= user_end_str:
                logger.info(f"{symbol} 本地数据已覆盖请求范围，无需下载")
                return False, local_data
                
            logger.info(f"{symbol} 需要更新数据")
            return True, local_data
            
        except Exception as e:
            logger.error(f"检查 {symbol} 下载需求时出错: {e}")
            return True, None
    
    def _check_data_source_updated(self, symbol: str, period: str, local_end_date: str) -> bool:
        """检查数据源是否有更新"""
        try:
            # 这里可以实现具体的数据源更新检查逻辑
            # 目前简化为与当前日期比较
            current_date = datetime.now().strftime("%Y%m%d")
            return current_date > local_end_date
        except Exception as e:
            logger.error(f"检查数据源更新失败: {e}")
            return True  # 出错时默认需要更新
    
    def _handle_no_download_needed(self, symbol: str, local_data: pd.DataFrame,
                                  result: Dict[str, Any],
                                  display_head_rows: int = 5, display_tail_rows: int = 5,
                                  **kwargs) -> None:
        """处理无需下载的情况"""
        # 无需下载的股票只添加到no_download_needed列表，不添加到successful_symbols
        result["no_download_needed"].append(symbol)
        result["data"][symbol] = local_data

        logger.info(f"{symbol} 无需下载，使用本地数据")

        # 显示数据（如果启用）
        show_data = kwargs.get('show_data', True)
        if show_data and local_data is not None and not local_data.empty:
            self._display_downloaded_data(symbol, local_data, display_head_rows, display_tail_rows)
    
    def _handle_incremental_update(self, symbol: str, period: str, 
                                  start_time: str, incremental: bool, 
                                  overlap_days: int) -> str:
        """处理增量更新逻辑"""
        if not incremental:
            return start_time
            
        try:
            latest_file = get_latest_partition_file(self.data_root, symbol, period)
            if latest_file and os.path.exists(latest_file):
                latest_timestamp = read_latest_data_timestamp(latest_file)
                if latest_timestamp:
                    # 计算增量开始时间
                    from utils.data_processor.data_merger import calculate_incremental_start_time
                    actual_start_time, _ = calculate_incremental_start_time(
                        local_end_str=latest_timestamp,
                        requested_start=start_time,
                        overlap_days=overlap_days
                    )
                    
                    logger.info(f"{symbol} 增量更新从 {actual_start_time} 开始")
                    return actual_start_time
                    
        except Exception as e:
            logger.error(f"{symbol} 增量更新准备失败: {e}")
            
        return start_time
    
    def _download_single_stock(self, symbol: str, period: str,
                              start_time: str, end_time: str,
                              local_data: Optional[pd.DataFrame],
                              incremental: bool, force_update: bool,
                              validate_data: bool,
                              display_head_rows: int, display_tail_rows: int,
                              result: Dict[str, Any], **kwargs) -> bool:
        """下载单个股票数据"""
        try:
            # 调用数据获取器下载
            # 只传递底层API支持的参数
            allowed_params = {'dividend_type'}  # 底层API支持的额外参数
            filtered_kwargs = {k: v for k, v in kwargs.items() if k in allowed_params}
            
            data = self.fetcher.download_history_data(
                symbols=[symbol],
                period=period,
                start_time=start_time,
                end_time=end_time,
                **filtered_kwargs
            )
            
            if symbol not in data or data[symbol] is None:
                self._handle_download_error(symbol, "数据获取器未返回数据", result)
                return False
                
            # 处理下载的数据
            processed_df = self.data_processor.process_downloaded_data(symbol, data[symbol])
            
            if processed_df is None or processed_df.empty:
                self._handle_download_error(symbol, "处理后数据为空", result)
                return False
                
            # 处理增量合并
            if incremental and local_data is not None and not local_data.empty:
                processed_df = self._merge_incremental_data(
                    local_data, processed_df, force_update, validate_data
                )

            # tick数据时间过滤（存储前过滤非交易时间数据）
            processed_df = self._apply_tick_time_filter(symbol, period, processed_df)

            # 保存数据
            save_success = self._save_processed_data(symbol, period, processed_df)

            if save_success:
                result["successful_symbols"].append(symbol)
                result["data"][symbol] = processed_df

                # 显示数据（如果启用）
                show_data = kwargs.get('show_data', True)
                if show_data:
                    self._display_downloaded_data(symbol, processed_df, display_head_rows, display_tail_rows)

                return True
            else:
                self._handle_download_error(symbol, "数据保存失败", result)
                return False
                
        except Exception as e:
            self._handle_download_error(symbol, f"下载异常: {str(e)}", result)
            return False

    def _display_downloaded_data(self, symbol: str, df: pd.DataFrame,
                                display_head_rows: int, display_tail_rows: int):
        """显示下载的数据"""
        try:
            if df is None or df.empty:
                logger.info(LogTarget.FILE, f"{symbol}: 无数据可显示")
                return

            # 使用数据处理器格式化显示数据
            formatted_df = self.data_processor.format_display_data(
                df,
                data_mode="both",
                head_rows=display_head_rows,
                tail_rows=display_tail_rows
            )

            # 显示数据信息
            logger.info(LogTarget.FILE, f"\n📊 {symbol} 数据预览 (共 {len(df)} 行):")
            logger.info(LogTarget.FILE, f"时间范围: {df.index[0]} ~ {df.index[-1]}")
            logger.info(LogTarget.FILE, f"字段: {list(df.columns)}")

            # 显示格式化的数据
            if not formatted_df.empty:
                logger.info(LogTarget.FILE, f"\n{formatted_df.to_string()}")
            else:
                logger.info(LogTarget.FILE, "数据为空")

        except Exception as e:
            logger.error(LogTarget.FILE, f"显示 {symbol} 数据时出错: {e}")

    def _merge_incremental_data(self, local_data: pd.DataFrame,
                               new_data: pd.DataFrame,
                               force_update: bool, validate_data: bool) -> pd.DataFrame:
        """合并增量数据"""
        try:
            logger.info(f"合并本地数据({len(local_data)}行) 和新数据({len(new_data)}行)")
            
            # 优化：只使用需要合并的部分本地数据
            if len(local_data) > 10000:  # 大数据集优化
                from utils.data_processor.data_merger import filter_data_by_time_range
                from datetime import timedelta
                
                new_start, _ = self.data_processor.get_data_time_range(new_data)
                if new_start:
                    merge_start = (new_start - timedelta(days=30)).strftime('%Y%m%d')
                    local_data = filter_data_by_time_range(local_data, start_time=merge_start)
                    logger.info(f"优化合并：本地数据筛选后 {len(local_data)} 行")
                    
            # 执行合并
            merged_df = merge_dataframes(local_data, new_data, force_update)
            
            # 验证合并结果
            if validate_data and not validate_merged_data(merged_df):
                logger.warning("合并数据验证失败，使用新数据")
                return new_data
                
            logger.info(f"数据合并成功，合并后 {len(merged_df)} 行")
            return merged_df
            
        except Exception as e:
            logger.error(f"数据合并失败: {e}")
            return new_data
    
    def _save_processed_data(self, symbol: str, period: str, 
                           processed_df: pd.DataFrame) -> bool:
        """保存处理后的数据"""
        try:
            logger.info(f"保存 {symbol} ({period}) 数据，{len(processed_df)} 行")
            
            storage = ParquetStorage(base_dir=self.data_root)
            success = storage.save_data_by_partition_parallel(
                dataframe=processed_df,
                symbol=symbol,
                period=period,
                num_workers=STORAGE_MAX_WORKERS
            )
            
            if success:
                logger.info(f"{symbol} 数据保存成功")
                return True
            else:
                logger.error(f"{symbol} 数据保存失败")
                return False
                
        except Exception as e:
            logger.error(f"保存 {symbol} 数据时出错: {e}")
            return False

    def _apply_tick_time_filter(self, symbol: str, period: str, df: pd.DataFrame) -> pd.DataFrame:
        """
        应用tick数据时间过滤

        Args:
            symbol: 股票代码
            period: 数据周期
            df: 处理后的数据

        Returns:
            pd.DataFrame: 过滤后的数据
        """
        try:
            # 导入时间过滤模块
            from utils.data_processor.tick_time_filter import (
                should_apply_time_filter,
                filter_tick_data_for_storage,
                filter_kline_data_for_storage,
                is_tick_data_period,
                is_kline_data_period
            )

            # 判断是否需要应用时间过滤
            if not should_apply_time_filter(period):
                logger.debug(f"{symbol} ({period}) 不需要时间过滤，直接返回")
                return df

            # 根据数据类型选择不同的过滤函数
            if is_tick_data_period(period):
                logger.info(f"对 {symbol} ({period}) 应用tick数据时间过滤（双重边界处理）")
                filtered_df = filter_tick_data_for_storage(df, symbol)
            elif is_kline_data_period(period):
                logger.info(f"对 {symbol} ({period}) 应用1m数据时间过滤（简单交易时间过滤）")
                filtered_df = filter_kline_data_for_storage(df, symbol)
            else:
                logger.warning(f"{symbol} ({period}) 数据类型未识别，跳过过滤")
                return df

            # 记录过滤效果
            original_count = len(df)
            filtered_count = len(filtered_df)
            removed_count = original_count - filtered_count

            if removed_count > 0:
                logger.info(f"{symbol} 时间过滤完成: 原始 {original_count} 行 → 过滤后 {filtered_count} 行 (移除 {removed_count} 行, {removed_count/original_count*100:.1f}%)")
            else:
                logger.info(f"{symbol} 时间过滤完成: 无数据被过滤")

            return filtered_df

        except Exception as e:
            logger.error(f"{symbol} 时间过滤失败: {e}")
            logger.warning(f"{symbol} 时间过滤失败，返回原始数据")
            return df
    
    def _handle_download_error(self, symbol: str, reason: str, 
                              result: Dict[str, Any]) -> None:
        """处理下载错误"""
        if symbol not in result["failed_symbols"]:
            result["failed_symbols"].append(symbol)
        result["failed_reasons"][symbol] = reason
        logger.error(f"{symbol} 下载失败: {reason}")
    
    def _finalize_result(self, result: Dict[str, Any], total_stocks: int) -> Dict[str, Any]:
        """完成结果汇总"""
        success_count = len(result["successful_symbols"])
        no_download_count = len(result["no_download_needed"])
        
        # 设置成功标志
        result["success"] = (success_count > 0) or (no_download_count > 0)
        
        # 记录汇总信息
        if success_count > 0:
            success_rate = success_count / total_stocks * 100
            logger.info(f"下载完成: 成功 {success_count}/{total_stocks} ({success_rate:.1f}%)")
        elif no_download_count > 0:
            logger.info(f"下载完成: 本地数据已是最新 {no_download_count}/{total_stocks}")
        else:
            logger.error("所有股票下载失败")
            
        return result