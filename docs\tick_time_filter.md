# 数据时间过滤功能说明

## 功能概述

数据时间过滤功能是专门为不同类型数据存储前的非交易时间数据过滤而设计的功能模块。该功能针对不同数据类型采用不同的过滤策略：

- **tick数据**：使用双重边界处理逻辑，保留有价值的边界数据
- **1m数据**：使用简单时间过滤，只保留交易时间数据 🆕

## 背景问题

### 存储效率问题
- **问题**：从数据源下载的tick数据包含大量非交易时间数据
- **影响**：浪费存储空间，影响后续数据处理效率
- **示例**：休盘时间（12:00-13:00、15:00后）的tick数据占用大量存储

### 数据质量问题
- **问题**：需要区分有效的边界数据和无效的休盘数据
- **挑战**：如15:00:10的延时数据是有效的，但16:00:00的数据是无效的
- **要求**：保留有价值的边界数据，过滤纯休盘数据

## 技术实现

### 过滤策略

**tick数据过滤逻辑**：
```python
def filter_tick_data_for_storage(df, symbol):
    # 1. 连续竞价时间判断
    is_trading_mask = is_trading_time_batch(time_index, symbol)

    # 2. 集合竞价时间判断 🆕
    is_auction_mask = is_auction_time_batch(time_index, symbol)

    # 3. 边界数据识别（应用双重边界处理逻辑）
    valid_time_mask = is_trading_mask | is_auction_mask
    boundary_mask = identify_boundary_data(time_index, valid_time_mask)

    # 4. 组合过滤条件（连续竞价 + 集合竞价 + 边界数据）
    keep_mask = is_trading_mask | is_auction_mask | boundary_mask

    return df[keep_mask]
```

**1m数据过滤逻辑** 🆕：
```python
def filter_kline_data_for_storage(df, symbol):
    # 1. 交易时间判断
    is_trading_mask = is_trading_time_batch(time_index, symbol)

    # 2. 简单过滤条件（不使用边界处理逻辑）
    keep_mask = is_trading_mask  # 只保留交易时间数据

    return df[keep_mask]
```

### 双重边界处理逻辑

**条件1：跨分钟边界数据**
- 识别：`time_diff_minutes == 1`
- 示例：15:01:00 → 保留（休盘边界数据）

**条件2：延时边界数据**
- 识别：`0 < time_diff_seconds <= 60`
- 示例：15:00:10 → 保留（录制延时数据）

## 应用场景

### 自动集成到数据下载流程

```
下载tick数据 → 基本处理 → 增量合并 → 时间过滤 → 保存到分区
                                    ↑
                              新增过滤步骤
```

### 过滤效果示例

| 时间点 | 数据类型 | 处理结果 | 说明 |
|--------|----------|----------|------|
| 08:55:00 | 集合竞价 | ✅ 保留 | 期货集合竞价数据 🆕 |
| 08:59:00 | 集合竞价 | ✅ 保留 | 期货集合竞价数据 🆕 |
| 09:30:00 | 连续竞价 | ✅ 保留 | 正常连续竞价数据 |
| 11:30:00 | 边界时间 | ✅ 保留 | 交易结束时间 |
| 11:30:10 | 延时数据 | ✅ 保留 | 录制延时，有效边界数据 |
| 11:31:00 | 边界数据 | ✅ 保留 | 跨分钟边界数据 |
| 12:00:00 | 休盘时间 | ❌ 过滤 | 纯休盘数据 |
| 16:00:00 | 休盘时间 | ❌ 过滤 | 收盘后数据 |

## 使用方法

### 自动应用（推荐）

tick数据时间过滤功能已经自动集成到数据下载流程中，用户无需额外操作：

```python
from data.core.operations import download_data

# 下载tick数据时自动应用时间过滤
result = download_data(
    stocks=['000001.SZ'],
    period='tick',
    start_date='20250715',
    end_date='20250717'
)
# 存储的数据已经过滤了非交易时间数据
```

### 手动调用

如果需要手动对tick数据进行时间过滤：

```python
from utils.data_processor.tick_time_filter import filter_tick_data_for_storage

# 手动过滤tick数据
filtered_df = filter_tick_data_for_storage(tick_df, symbol="000001.SZ")
```

### 获取过滤统计

```python
from utils.data_processor.tick_time_filter import get_filter_statistics

# 获取过滤统计信息
stats = get_filter_statistics(original_count=10000, filtered_count=6000)
print(f"存储节省: {stats['storage_reduction']}")
```

## 性能特点

### 高效处理
- **向量化操作**：使用pandas向量化操作，处理速度快
- **内存优化**：避免不必要的数据复制
- **批量处理**：一次性处理整个DataFrame

### 详细监控
- **过滤统计**：记录过滤前后数据量变化
- **性能监控**：记录过滤耗时
- **存储节省**：计算存储空间节省比例

### 典型性能表现

| 数据量 | 过滤耗时 | 存储节省 | 说明 |
|--------|----------|----------|------|
| 10万条 | <0.1秒 | 60-80% | 典型交易日数据 |
| 50万条 | <0.5秒 | 50-70% | 高频交易数据 |
| 100万条 | <1.0秒 | 55-75% | 全天tick数据 |

## 配置说明

### 硬编码配置（简化设计）

为保持使用简单化，所有配置都采用硬编码方式：

- **启用条件**：只对period='tick'的数据启用过滤
- **边界时间窗口**：固定为60秒
- **过滤策略**：固定为双重边界处理逻辑

### 无需用户配置

- ✅ **自动识别**：自动识别tick数据并应用过滤
- ✅ **默认启用**：tick数据下载时默认启用过滤
- ✅ **透明处理**：用户无感知，自动在后台处理

## 日志输出

### 过滤统计日志

```
tick数据时间过滤统计:
- 原始数据: 100000 行
- 交易时间数据: 35000 行
- 有效边界数据: 5000 行
- 保留数据: 40000 行
- 过滤数据: 60000 行
- 过滤比例: 60.0%
```

### 性能监控日志

```
函数 filter_tick_data_for_storage 执行完成: 数据量=100000, 耗时=0.234秒
```

## 测试验证

### 测试用例

项目提供了完整的测试用例：

- `tests/test_tick_time_filter.py`：完整的功能测试

### 运行测试

```bash
# 运行tick数据时间过滤测试
python tests/test_tick_time_filter.py
```

### 测试覆盖

- ✅ 交易时间数据保留测试
- ✅ 非交易时间数据过滤测试
- ✅ 边界延时数据保留测试
- ✅ 跨分钟边界数据保留测试
- ✅ 不同品种的过滤测试
- ✅ 过滤统计功能测试

## 注意事项

### 数据格式要求

1. **索引格式**：DataFrame索引必须为时间字符串格式(YYYYMMDDHHMMSS)
2. **品种识别**：需要提供正确的symbol参数以识别品种类型
3. **数据完整性**：过滤后的数据保持原始列结构不变

### 错误处理和安全性 🆕

**最新修复 (2025-07-19)**：
- ✅ **长度验证**：添加时间索引与DataFrame长度一致性验证
- ✅ **异常处理**：完善时间转换和过滤操作的异常处理
- ✅ **安全过滤**：确保掩码长度与数据长度匹配，避免索引越界
- ✅ **索引对齐**：修复布尔掩码索引不匹配问题 🆕
- ✅ **numpy数组转换**：确保所有布尔运算结果为纯numpy数组 🆕
- ✅ **错误恢复**：过滤失败时自动返回原始数据，确保系统稳定性
- ✅ **详细日志**：提供详细的错误信息和调试日志

**修复的问题**：
1. **索引越界错误**：修复了`indices are out-of-bounds`错误
2. **长度不匹配**：解决了Boolean Series重新索引警告
3. **索引类型不匹配**：修复了DatetimeIndex与字符串索引的不匹配问题 🆕
4. **布尔掩码索引**：确保所有布尔掩码为纯numpy数组，避免pandas索引重新匹配 🆕
5. **异常处理不足**：增强了各个步骤的异常处理机制

**技术细节**：
- 使用`pd.Series(time_index).diff().dt.total_seconds().values`替代`time_index.to_series().diff()`
- 将所有布尔运算结果转换为numpy数组：`boundary_mask.values`
- 确保过滤操作使用位置索引而非标签索引

### 兼容性保证

1. **向后兼容**：不影响现有的数据处理流程
2. **选择性应用**：只对tick数据启用，其他周期数据不受影响
3. **错误处理**：过滤失败时返回原始数据，确保系统稳定性

## 技术优势

### 存储效率提升

- **空间节省**：减少50-80%的无效数据存储
- **I/O优化**：减少磁盘读写操作
- **查询加速**：减少数据量，提高查询速度

### 数据质量保证

- **精确过滤**：准确识别和保留有效边界数据
- **完整性保证**：不丢失有价值的交易相关数据
- **一致性维护**：与K线合成功能保持逻辑一致

### 系统集成优势

- **无缝集成**：自动集成到现有数据处理流程
- **透明处理**：用户无感知，自动在后台工作
- **性能优化**：不影响数据下载和处理速度

## 后续扩展

### 可能的优化方向

1. **并行处理**：对超大数据集启用并行过滤
2. **缓存优化**：缓存时间判断结果，提高重复处理效率
3. **统计增强**：提供更详细的过滤效果分析
4. **监控告警**：异常过滤比例的监控和告警

### 功能扩展可能

1. **自定义过滤规则**：支持用户自定义过滤条件
2. **多周期支持**：扩展到其他高频数据的过滤
3. **实时过滤**：支持实时数据流的在线过滤
4. **压缩优化**：结合数据压缩技术进一步节省存储
