---
description: 
globs: "*"
alwaysApply: true
---
# Python模块导入规范

本文档规定了项目中Python模块导入的标准方式和路径管理规范，旨在解决常见的导入问题并确保代码的可维护性。

## 强制规范：使用项目根目录添加到sys.path的方式导入本地模块

为解决Python模块导入问题，**所有**导入本地模块的Python文件必须在文件顶部添加以下代码：

```python
import os
import sys

# 将项目根目录添加到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 然后使用绝对导入方式导入模块
from data.fetcher.module import Class
# 而不是使用相对导入
# from ..data.fetcher.module import Class
```

## 问题背景

Python模块导入存在以下常见问题：

1. **相对导入错误**：
   - `ImportError: attempted relative import with no known parent package`
   - `ImportError: attempted relative import beyond top-level package`

2. **模块未找到错误**：
   - `ModuleNotFoundError: No module named 'data'`

3. **包结构依赖问题**：
   - 运行方式不同，导入方式可能失效
   - 目录结构变化会导致导入失败

## 解决方案解析

```python
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
```

这行代码的工作原理：

1. `__file__` - 当前文件的路径
2. `os.path.abspath(__file__)` - 转换为绝对路径
3. `os.path.dirname(...)` - 获取文件所在目录
4. `os.path.dirname(os.path.dirname(...))` - 获取上一级目录(项目根目录)
5. `sys.path.insert(0, ...)` - 将项目根目录添加到Python模块搜索路径的最前面

## 导入顺序与模块别名规范

为解决Python模块导入问题并保持代码清晰性，**所有**Python文件必须遵循以下导入顺序、`sys.path`修改及模块别名规范。

### 导入顺序与 `sys.path` 修改

导入语句应按以下严格顺序排列：

1. **标准库导入** (例如：`import os`, `import sys`)
   - 每组之间用一个空行分隔。

2. **第三方库导入** (例如：`import pandas as pd`, `from xtquant import xtdata as xt_data`)
   - 每组之间用一个空行分隔。

3. **添加项目根目录到 `sys.path`**
   - 此操作紧随标准库和第三方库导入之后，且必须在所有本地模块导入之前。
   - 代码示例：
     ```python
     import os
     import sys

     # 将项目根目录添加到Python路径
     # 需根据文件实际位置调整dirname调用次数以指向项目根目录
     project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
     sys.path.insert(0, project_root)
     ```

4. **本地模块导入** (例如：`from utils.logger import get_logger`, `from data.fetcher import BaseFetcher`)
   - 本地模块导入与 `sys.path` 修改块之间可以用空行或注释分隔。

### 模块别名规范

1. **一致性**：
   - 同一个模块在整个项目中应**始终使用相同的别名**。
   - 例如，如果 `pandas` 统一用 `pd` 作为别名 (`import pandas as pd`)，则项目中所有文件都应遵循此约定，避免在其他文件使用 `import pandas as pds` 或 `import pandas`。

2. **清晰性与意义**：
   - 模块别名应**清晰、简洁且具有描述性**，能够直观反映模块的功能或来源。
   - 优先使用社区广泛接受的通用别名 (如 `pd` for `pandas`, `np` for `numpy`, `plt` for `matplotlib.pyplot`)。
   - 对于不常用的库或自定义模块，选择的别名应避免模糊不清或过于简短导致意义不明。
   - 例如，对于 `xtquant.xtdata`，推荐使用 `xt_data` 或 `xtdata` 而非过于简短的 `xt`。

### Linter注意事项

此导入顺序和 `sys.path` 修改方法是功能上正确且推荐的，能确保本地模块正确导入。
然而，一些严格的Linter工具 (如Pylint, Flake8) 可能会针对本地模块导入报告 "E402: module level import not at top of file" 或类似错误。

**处理方式:**

- **首选**: 在报告错误的本地导入行后添加 `# noqa: E402` 来抑制该警告。
  ```python
  # ... sys.path 修改代码 ...

  from utils.logger import get_logger  # noqa: E402
  from data.data_commands import download_data  # noqa: E402
  ```

## 特殊情况处理

### 在项目根目录的文件中

如果Python文件位于项目根目录，使用单次`dirname`即可：

```python
# 当前文件在项目根目录时
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
```

### 在深层嵌套目录中

对于位于多层嵌套目录的文件，使用多次`dirname`操作：

```python
# 对于深度为3的目录结构，如 project/dir1/dir2/dir3/file.py
# 获取项目根目录
project_root = os.path.abspath(__file__)
for _ in range(3):  # 向上3级
    project_root = os.path.dirname(project_root)
sys.path.insert(0, project_root)
```

## 项目数据缓存管理

- 项目内不允许存放脚本运行生成的数据文件，请放到D盘的缓存文件中
- 交互界面除了主菜单以外，所有子菜单都需要设置默认值

## 优势与好处

使用此规范的优势：

1. **环境无关**：无论在何处运行代码，导入都能正常工作
2. **运行方式无关**：可以直接运行脚本或作为模块运行
3. **结构变更兼容**：即使项目结构调整，只需修改dirname次数
4. **IDE友好**：便于IDE识别和提供代码补全
5. **可移植性**：代码可在不同系统间迁移而不出现路径问题

## 注意事项

1. 此代码必须在所有本地模块导入之前添加
2. 确保使用正确的dirname层级以匹配项目结构
3. 禁止使用硬编码的绝对路径
4. 仅对本项目内部模块使用此方法，标准库和第三方库正常导入

## 实现示例

### 正确的模块导入方式

```python
#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""模块功能描述"""

import os
import sys
import logging
from typing import Dict, List, Optional

# 添加项目根目录到Python路径(必须放在所有本地模块导入之前)
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 使用绝对导入本地模块
from data.fetcher.xtquant_fetcher import XtQuantFetcher  # noqa: E402
from utils.logger import setup_logger  # noqa: E402
from config.settings import DEFAULTS  # noqa: E402

# 后续代码...
```

### 错误的导入方式(禁止使用)

```python
# 错误方式1: 直接使用相对导入
from ..data.fetcher.xtquant_fetcher import XtQuantFetcher  # 禁止使用

# 错误方式2: 不添加项目根目录到路径就使用绝对导入
from data.fetcher.xtquant_fetcher import XtQuantFetcher  # 可能失败

# 错误方式3: 使用具有路径依赖的导入
import sys
sys.path.append("D:/quant")  # 使用硬编码路径，禁止使用
``` 