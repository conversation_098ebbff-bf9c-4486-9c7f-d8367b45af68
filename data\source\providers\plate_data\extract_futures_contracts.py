#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
提取期货连续合约信息，并生成两类合约代码列表：
1. 主力连续合约列表
2. 当前主力合约列表 (需要xtquant)

并构建期货品种与交易所的映射关系。
"""

import json
import logging
import os
import re
import sys
from datetime import datetime
from typing import Dict, List, Tuple
import pandas as pd
import numpy as np

# 尝试导入xtquant，并使用标准别名
try:
    from xtquant import xtdata as xt_data

    xt_data.enable_hello = False
    HAS_XTQUANT = True
except ImportError:
    print("警告: 无法导入xtquant库，将无法获取当前主力合约")
    HAS_XTQUANT = False

# 将项目根目录添加到Python路径
root_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
sys.path.insert(0, root_path)

# 导入日志模块
from utils.logger import get_unified_logger

# 设置日志记录器
logger = get_unified_logger(__name__)

# 脚本所在目录
script_dir = os.path.dirname(os.path.abspath(__file__))


def parse_continuous_contracts(file_path: str) -> List[Dict]:
    """
    解析连续合约列表文件，提取所有合约信息

    Args:
        file_path: 连续合约列表文件路径

    Returns:
        合约信息列表
    """
    contracts = []

    try:
        with open(file_path, "r", encoding="utf-8") as f:
            lines = f.readlines()

        # 跳过注释行和表头
        data_start = False
        for line in lines:
            line = line.strip()

            # 跳过空行
            if not line:
                continue

            # 跳过注释行
            if line.startswith("#"):
                continue

            # 检查是否为表头行
            if line.startswith("代码") and "名称" in line and "交易所" in line:
                data_start = True
                continue

            # 跳过分隔线
            if line.startswith("-") and "-" in line:
                continue

            # 解析数据行
            if data_start:
                # 使用正则表达式匹配，捕获合约代码之后的所有内容作为名称
                match = re.match(r"^([A-Za-z0-9]+\.[A-Z]+)\s+(.+)", line)
                if match:
                    code = match.group(1)
                    name = match.group(2).strip()

                    # 提取交易所信息
                    exchange_match = re.search(r"\.([A-Z]+)", code)
                    exchange = exchange_match.group(1) if exchange_match else ""

                    contracts.append({"code": code, "name": name, "exchange": exchange})
                else:
                    logger.warning(f"无法解析行: {line}")  # 增加解析失败的日志

    except Exception as e:
        logger.error(f"解析连续合约列表文件失败: {e}", exc_info=True)  # 增加异常信息
        return []

    return contracts


def filter_standard_continuous_contracts(contracts: List[Dict]) -> List[Dict]:
    """
    筛选标准主力连续合约代码 (格式为[字母]00.[字母])

    Args:
        contracts: 合约信息列表

    Returns:
        标准主力连续合约列表
    """
    standard_contracts = []

    for contract in contracts:
        code = contract["code"]
        # 匹配格式为xx00.YY的合约代码
        if re.match(r"^[A-Za-z]+00\.[A-Z]+$", code):
            standard_contracts.append(contract)

    return standard_contracts


def build_variety_exchange_map(standard_contracts: List[Dict]) -> Dict[str, str]:
    """
    构建期货品种与交易所的映射关系

    Args:
        standard_contracts: 标准主力连续合约列表

    Returns:
        期货品种与交易所的映射字典
    """
    variety_exchange_map = {}

    for contract in standard_contracts:
        code = contract["code"]
        exchange = contract["exchange"]

        # 提取品种代码
        variety_match = re.match(r"^([A-Za-z]+)00\.", code)
        if variety_match:
            variety = variety_match.group(1)
            variety_exchange_map[variety] = exchange
        else:
            logger.warning(f"无法从标准连续合约代码 {code} 中提取品种")  # 增加日志

    return variety_exchange_map


def get_current_main_contracts(
    standard_continuous_codes: List[str],
) -> List[Tuple[str, str]]:
    """
    获取当前主力合约列表

    Args:
        standard_continuous_codes: 标准主力连续合约代码列表

    Returns:
        当前主力合约列表，每项为(连续合约代码, 当前主力合约代码)元组
    """
    if not HAS_XTQUANT:
        logger.warning("未安装xtquant库，无法获取当前主力合约")
        return []

    current_main_contracts = []

    logger.info(
        f"准备获取 {len(standard_continuous_codes)} 个主力连续合约的当前主力合约..."
    )
    for continuous_code in standard_continuous_codes:
        try:
            # 使用 xt_data 调用行情接口
            main_contract = xt_data.get_main_contract(continuous_code)
            if main_contract:
                current_main_contracts.append((continuous_code, main_contract))
                # logger.debug(f"成功获取 {continuous_code} -> {main_contract}") # DEBUG级别信息
            else:
                logger.warning(
                    f"获取 {continuous_code} 的当前主力合约失败，返回为空或无效"
                )
        except Exception as e:
            logger.error(
                f"获取 {continuous_code} 的当前主力合约时出错: {e}", exc_info=True
            )  # 增加异常信息

    logger.info(f"实际获取到 {len(current_main_contracts)} 个当前主力合约映射")
    return current_main_contracts


def main():
    """主函数"""
    # 连续合约列表文件路径
    continuous_contracts_file = os.path.join(script_dir, "连续合约成分列表.txt")

    # 解析连续合约列表
    logger.info("开始解析连续合约列表文件...")
    contracts = parse_continuous_contracts(continuous_contracts_file)
    if not contracts:
        logger.error("连续合约列表解析失败或为空，脚本终止")
        return
    logger.info(f"解析完成，共找到 {len(contracts)} 个合约")

    # 筛选标准主力连续合约
    standard_contracts = filter_standard_continuous_contracts(contracts)
    if not standard_contracts:
        logger.error("未能筛选出任何标准主力连续合约，脚本终止")
        return
    logger.info(f"筛选出 {len(standard_contracts)} 个标准主力连续合约")

    # 构建期货品种与交易所的映射关系
    variety_exchange_map = build_variety_exchange_map(standard_contracts)
    if not variety_exchange_map:
        logger.error("未能构建期货品种与交易所的映射关系，脚本终止")
        return
    logger.info(f"构建了 {len(variety_exchange_map)} 个期货品种与交易所的映射关系")

    # 生成合约代码列表

    # 1. 主力连续合约列表
    main_continuous_codes = [contract["code"] for contract in standard_contracts]

    # 2. 当前主力合约列表 (如果xtquant可用)
    current_main_contracts = []
    if HAS_XTQUANT:
        logger.info("开始获取当前主力合约...")
        current_main_contracts = get_current_main_contracts(main_continuous_codes)
        logger.info(f"获取了 {len(current_main_contracts)} 个当前主力合约")

    # 输出到结果文件
    output_file = os.path.join(script_dir, "期货合约代码汇总.txt")

    try:
        with open(output_file, "w", encoding="utf-8") as f:
            # 写入文件头
            f.write("# 期货合约代码汇总\n")
            f.write(f"# 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("#\n")

            # 写入期货品种与交易所的映射关系
            f.write("\n## 期货品种与交易所映射关系\n")
            f.write("变量名: FUTURES_VARIETY_EXCHANGE_MAP\n")
            f.write("```python\n")
            f.write("FUTURES_VARIETY_EXCHANGE_MAP = {\n")
            # 添加查找名称的辅助函数，避免重复代码

            def get_name(variety_code, contracts_list):
                return next(
                    (
                        c["name"]
                        for c in contracts_list
                        if c["code"].startswith(f"{variety_code}00")
                    ),
                    "",
                )

            for variety, exchange in sorted(variety_exchange_map.items()):
                name = get_name(variety, standard_contracts)
                f.write(f"    '{variety}': '{exchange}',  # {name}\n")
            f.write("}\n")
            f.write("```\n")

            # 写入主力连续合约列表
            f.write("\n## 主力连续合约列表\n")
            f.write("变量名: MAIN_CONTINUOUS_CODES\n")
            f.write("```python\n")
            f.write("MAIN_CONTINUOUS_CODES = [\n")
            for code in sorted(main_continuous_codes):
                # 提取品种代码以查找名称
                variety_match = re.match(r"^([A-Za-z]+)00\.", code)
                name = ""
                if variety_match:
                    variety = variety_match.group(1)
                    name = get_name(variety, standard_contracts)
                f.write(f"    '{code}',  # {name}\n")
            f.write("]\n")
            f.write("```\n")

            # 写入当前主力合约列表
            if current_main_contracts:
                f.write("\n## 当前主力合约列表 (可能随时间变化)\n")
                f.write(f"# 获取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("```\n")
                f.write("主力连续合约代码 -> 当前主力合约代码\n")  # 修改表头
                f.write("-----------------------------------\n")  # 修改分隔线
                for continuous_code, main_code in sorted(current_main_contracts):
                    # 提取品种代码以查找名称
                    variety_match = re.match(r"^([A-Za-z]+)00\.", continuous_code)
                    name = ""
                    if variety_match:
                        variety = variety_match.group(1)
                        name = get_name(variety, standard_contracts)
                    f.write(f"{continuous_code} -> {main_code}  # {name}\n")
                f.write("```\n")
        logger.info(f"结果已保存到文件: {output_file}")
    except IOError as e:
        logger.error(f"写入结果文件 {output_file} 失败: {e}", exc_info=True)
        return  # 写入失败则不继续写JSON

    # 另外保存为JSON格式，方便程序使用
    json_output = {
        "variety_exchange_map": variety_exchange_map,
        "main_continuous_codes": main_continuous_codes,
        # "weighted_continuous_codes": [], # 移除加权合约
        "current_main_contracts": (
            dict(current_main_contracts) if current_main_contracts else {}
        ),
    }

    json_file = os.path.join(script_dir, "期货合约代码汇总.json")
    try:
        with open(json_file, "w", encoding="utf-8") as f:
            json.dump(json_output, f, ensure_ascii=False, indent=2)
        logger.info(f"JSON格式结果已保存到文件: {json_file}")
    except IOError as e:
        logger.error(f"写入JSON结果文件 {json_file} 失败: {e}", exc_info=True)


if __name__ == "__main__":
    main()
