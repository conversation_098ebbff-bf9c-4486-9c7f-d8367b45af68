#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
文本处理功能模块

提供文本宽度计算、格式化、截断和装饰等功能
"""

import os
import sys
import time
import unicodedata
from typing import Any, List, Optional

# 将项目根目录添加到Python路径
root_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, root_path)


def get_display_width(text: Any) -> int:
    """
    计算字符串在等宽字体终端中的显示宽度
    
    中文等全角字符占用两个单位宽度，英文字母、数字等半角字符占用一个单位宽度
    
    Args:
        text: 要计算宽度的文本或任何可以转换为字符串的对象
        
    Returns:
        文本在等宽字体终端中的显示宽度
    """
    if not isinstance(text, str):
        text = str(text)
    
    width = 0
    for char in text:
        # 获取字符的东亚宽度属性
        # 'W'表示全宽字符（占用两个单位宽度），'Na'表示窄字符（占用一个单位宽度）
        # 'F'表示全形字符（占用两个单位宽度），'H'表示半形字符（占用一个单位宽度）
        # 'A'表示不明确的字符，默认按照一个单位宽度处理
        width_category = unicodedata.east_asian_width(char)
        
        # 全宽字符（如中文）占用两个单位宽度
        if width_category in ('W', 'F'):
            width += 2
        # 其他字符占用一个单位宽度
        else:
            width += 1
    
    return width


def format_column(text: Any, width: int, is_last_column: bool = False, is_time_col: bool = False, align: str = 'left') -> str:
    """
    根据显示宽度格式化列文本
    
    Args:
        text: 列文本
        width: 列宽度
        is_last_column: 是否为最后一列(影响右侧填充空格)
        is_time_col: 是否为时间列(影响截断方式)
        align: 对齐方式，可选值为：
              - 'left': 左对齐（默认）
              - 'right': 右对齐
              - 'center': 居中对齐
        
    Returns:
        格式化后的列文本
    """
    text_str = str(text)
    display_width = get_display_width(text_str)
    
    # 如果文本显示宽度小于列宽，添加空格填充
    if display_width < width:
        padding = ' ' * (width - display_width)
        if align == 'left':
            # 左对齐：空格添加到右侧
            return text_str + padding
        elif align == 'right':
            # 右对齐：空格添加到左侧
            return padding + text_str
        else:  # center
            # 居中对齐：空格平均添加到两侧
            left_padding = ' ' * ((width - display_width) // 2)
            right_padding = ' ' * (width - display_width - len(left_padding))
            return left_padding + text_str + right_padding
    # 如果文本刚好等于列宽或超出列宽
    elif display_width > width and width > 4:
        # 截断长文本并添加省略号
        visible_width = width - 3  # 为"..."预留空间
        truncated = ""
        curr_width = 0
        
        for char in text_str:
            char_width = 2 if unicodedata.east_asian_width(char) in ('W', 'F') else 1
            if curr_width + char_width <= visible_width:
                truncated += char
                curr_width += char_width
            else:
                break
        
        return truncated + "..." + (' ' if not is_last_column else '')
    # 正好等于列宽
    else:
        if is_last_column:
            return text_str
        else:
            # 确保非最后列有足够空间分隔
            return text_str + ' '


def truncate_text(text: str, max_length: int, suffix: str = '...') -> str:
    """
    截断文本，确保不超过指定长度
    
    Args:
        text: 要截断的文本
        max_length: 最大长度(包括后缀)
        suffix: 截断后添加的后缀
        
    Returns:
        截断后的文本
    """
    if get_display_width(text) <= max_length:
        return text
    
    suffix_width = get_display_width(suffix)
    target_width = max_length - suffix_width
    
    result = ''
    current_width = 0
    
    for char in text:
        char_width = get_display_width(char)
        if current_width + char_width > target_width:
            break
        
        result += char
        current_width += char_width
    
    return result + suffix


def wrap_text(text: str, width: int) -> List[str]:
    """
    按照显示宽度换行文本
    
    Args:
        text: 要换行的文本
        width: 每行最大显示宽度
        
    Returns:
        换行后的文本行列表
    """
    if get_display_width(text) <= width:
        return [text]
    
    lines = []
    current_line = ''
    current_width = 0
    
    for word in text.split():
        word_width = get_display_width(word)
        
        # 如果单词本身就超过宽度限制
        if word_width > width:
            # 如果当前行不为空，先添加到结果中
            if current_line:
                lines.append(current_line)
                current_line = ''
                current_width = 0
            
            # 逐字符处理超长单词
            temp_word = ''
            temp_width = 0
            for char in word:
                char_width = get_display_width(char)
                if temp_width + char_width > width:
                    lines.append(temp_word)
                    temp_word = char
                    temp_width = char_width
                else:
                    temp_word += char
                    temp_width += char_width
            
            if temp_word:
                current_line = temp_word
                current_width = temp_width
        # 常规单词处理
        elif current_width + word_width + (1 if current_line else 0) <= width:
            # 可以添加到当前行
            if current_line:
                current_line += ' ' + word
                current_width += word_width + 1
            else:
                current_line = word
                current_width = word_width
        else:
            # 需要换行
            lines.append(current_line)
            current_line = word
            current_width = word_width
    
    # 添加最后一行
    if current_line:
        lines.append(current_line)
    
    return lines


# 注意：为了避免循环导入，不直接从formatting模块导入print_boxed_text函数
# 将在需要时使用延迟导入


def print_boxed_text(text: str, width: int = 60, padding: int = 1, title: Optional[str] = None) -> None:
    """
    打印带边框的文本框（向后兼容函数）
    
    注意：这是一个简单的实现，用于避免循环导入。
    实际使用时会延迟导入formatting模块中的完整实现。
    
    Args:
        text: 要显示的文本
        width: 文本框宽度
        padding: 内边距
        title: 文本框标题
    """
    # 延迟导入，避免循环导入问题
    from utils.data_display.formatting import print_boxed_text as real_func
    real_func(text, width=width, padding=padding, title=title)


def create_text_box(
    text: str,
    width: Optional[int] = None,
    padding: int = 1,
    border_char: str = '#'
) -> str:
    """
    创建一个文本框
    
    Args:
        text: 要显示的文本
        width: 文本框宽度，如果为None则根据文本长度自动计算
        padding: 内边距大小
        border_char: 边框字符
        
    Returns:
        文本框字符串
    """
    lines = text.split('\n')
    
    # 计算内容宽度
    content_width = max(get_display_width(line) for line in lines)
    
    # 如果指定了宽度，确保满足最小宽度要求
    if width is not None:
        box_width = max(width, content_width + padding * 2)
    else:
        box_width = content_width + padding * 2
    
    # 创建文本框
    result = []
    
    # 顶部边框
    result.append(border_char * (box_width + 2))
    
    # 空行（上内边距）
    for _ in range(padding):
        result.append(f"{border_char}{' ' * box_width}{border_char}")
    
    # 内容行
    for line in lines:
        line_width = get_display_width(line)
        padding_right = box_width - line_width - padding
        line_with_padding = f"{border_char}{' ' * padding}{line}{' ' * padding_right}"
        result.append(f"{line_with_padding}{border_char}")
    
    # 空行（下内边距）
    for _ in range(padding):
        result.append(f"{border_char}{' ' * box_width}{border_char}")
    
    # 底部边框
    result.append(border_char * (box_width + 2))
    
    return '\n'.join(result)


def animate_text(text: str, delay: float = 0.05) -> None:
    """
    动画方式打印文本
    
    Args:
        text: 要打印的文本
        delay: 每个字符之间的延迟（秒）
    """
    for char in text:
        print(char, end='', flush=True)
        time.sleep(delay)
    print()


def highlight_text(text: str, color: str = 'red') -> str:
    """
    添加ANSI颜色代码使文本在终端中高亮显示
    
    Args:
        text: 要高亮的文本
        color: 颜色名称，支持'red', 'green', 'yellow', 'blue', 'magenta', 'cyan', 'white'
        
    Returns:
        添加了ANSI颜色代码的文本
    """
    color_codes = {
        'red': '31',
        'green': '32',
        'yellow': '33',
        'blue': '34',
        'magenta': '35',
        'cyan': '36',
        'white': '37',
    }
    
    code = color_codes.get(color.lower(), '37')  # 默认为白色
    return f"\033[{code}m{text}\033[0m"