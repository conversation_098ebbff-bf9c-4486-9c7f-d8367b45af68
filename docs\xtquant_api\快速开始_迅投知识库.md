快速开始 | 迅投知识库


[![迅投知识库](/images/logo.png)迅投知识库](/)

[首页](/)

APIAPI

* [投研新手教程](/freshman/ty_rookie.html)
* [QMT新手教程](/freshman/rookie.html)
* [内置Python](/innerApi/start_now.html)
* [XtQuant文档](/nativeApi/start_now.html)
* [VBA](/VBA/start_now.html)

数据字典数据字典

* [快速开始](/dictionary/)
* [股票数据](/dictionary/stock.html)
* [行业概念数据](/dictionary/industry.html)
* [指数数据](/dictionary/indexes.html)
* [期货数据](/dictionary/future.html)
* [期权数据](/dictionary/option.html)
* [场内基金数据](/dictionary/floorfunds.html)
* [债券数据](/dictionary/bond.html)
* [常见问题](/dictionary/question_answer.html)
* [场景化示例](/dictionary/scenario_based_example.html)
* [迅投因子](/dictionary/xuntou_factor.html)

策略迁移策略迁移

* [聚宽策略](/strategy/JoinQuant2QMT.html)
* #### 

  + [客服QQ: 810315303](/dictionary/)
  + [联系方式: 18309226715](/dictionary/)

视频教程视频教程

* [投研端教程](/videos/touyan/ty_register_install.html)

投研平台 在新窗口打开

迅投社区 在新窗口打开

[迅投官网 在新窗口打开](http://www.thinktrader.net)

注册

登录

![微信扫码联系客服](/assets/wechat-d90fd08f.png "点击联系客服")

![分享链接](data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAHqADAAQAAAABAAAAHgAAAADKQTcFAAADjElEQVRIDcVXW0hVQRRdM/fce/OVpfRA8dENDckkMILsYRG9PnqQQUkg9NFfBCFEJJSFRj8R+BP4URREGEVGRNSXWEiE1odoDx+lhkoWpTe1+zrT7KPnes59ddQbDujM7D17rbNn9uzZl8FCqxaC36l1l4iAekgIFDOwDEDIP2psUEAMMoY2ZuONFVUpLdWMqVO66P9ZdBWw/ZZY9GXAfZqpolKCL4+1VtfJj/omOLuWm5VS13SC/dHloX1UYtcld5lA4Lr0MCvUyMpc7sAAg+1M78WUh5HW81ChEIKtqh6rVUXgwVxJCZNsCYOwCDOUxySgBa7LY/dkfzR04XzmjLGG3guLy2UvdByTx3J7a+JNSkSESdg6KfVBj+lMaWuMyniPObMd0c9c85iilwIpHkSZqQyudNNGBmGJg7hIoK2gKzOfQKJt27xawc41dtytSELesijEMuCISyOm5ED3lCazbXaJv6fAjvrjyShcaUPlDidy0mzoHI6eP4hL43TVjG1R/erL2ZAm2IF9ax0oW+9EWiLH0w4PSl02bMhW4PYIFF0diwnHFb5VoTQYc5VBmZrAcLDIgf2FTiQ7p+LyxQcvijO5RkpLO4cDBovIQ+JU5NkWR1bPSFekMByW3u0tcMChBC8Cmrq8yF0iU2ue3ILpZolYckoYliHzsG5n6rOWchwrdqJUAttkDjS2ll4fkuwCB9Y5jWJLHhOnMvPKmOy1yfndichNt4Up2vp9mPAEcGqbdjNM+o6hf281cUaO+2mo2ucTaB/ym4DbB/34/MMfkdQXEOgeiR7RQSAGIYnZYFAQMvj6S8XZR+Ooa5rAuFfg/bAfrX1eVO0K95RMuySpzwIvBBtS6BGXNvkhnKbps04fmrt92CivS315ImSyN+n1iZXAorXEyaly0A1j9eNeYJNLgcIjk5KtVWKJ0CrzNm+MRWjUvekP4KPcztHJyLfAMrHCH3OqkahcMRLEGguZ3uuaPWh466XnzrTUCjFxESenwoxqJBNClEnPSAA3Xk3i5msPzj2ZRPntcfR8n7o+Az9VmS6jGBrExEWc2oHRU9XXP/ppLi+UQ17zkyVOjPxWcf+dz0ARPqQ6LCc7NZ+KwGCkLEghQN9GlQEDvxL+nfGRELZefRBi0GOayGBZmGKPqkCtGoyj55qnIRVmmMck0Bud+f8s6E1brZPq/YL8hNHJqacaKd4/2v4CgdaZJ2zGqYAAAAAASUVORK5CYII= "分享链接")

[首页](/)

APIAPI

* [投研新手教程](/freshman/ty_rookie.html)
* [QMT新手教程](/freshman/rookie.html)
* [内置Python](/innerApi/start_now.html)
* [XtQuant文档](/nativeApi/start_now.html)
* [VBA](/VBA/start_now.html)

数据字典数据字典

* [快速开始](/dictionary/)
* [股票数据](/dictionary/stock.html)
* [行业概念数据](/dictionary/industry.html)
* [指数数据](/dictionary/indexes.html)
* [期货数据](/dictionary/future.html)
* [期权数据](/dictionary/option.html)
* [场内基金数据](/dictionary/floorfunds.html)
* [债券数据](/dictionary/bond.html)
* [常见问题](/dictionary/question_answer.html)
* [场景化示例](/dictionary/scenario_based_example.html)
* [迅投因子](/dictionary/xuntou_factor.html)

策略迁移策略迁移

* [聚宽策略](/strategy/JoinQuant2QMT.html)
* #### 

  + [客服QQ: 810315303](/dictionary/)
  + [联系方式: 18309226715](/dictionary/)

视频教程视频教程

* [投研端教程](/videos/touyan/ty_register_install.html)

投研平台 在新窗口打开

迅投社区 在新窗口打开

[迅投官网 在新窗口打开](http://www.thinktrader.net)

* 数据字典

  + [快速开始](/dictionary/) 
    - [概述](/dictionary/#概述)
    - [VIP 行情用户优势对比](/dictionary/#vip-行情用户优势对比) 
      * [通用功能对比](/dictionary/#通用功能对比)
      * [行情站点对比](/dictionary/#行情站点对比) 
        + [VIP行情站点](/dictionary/#vip行情站点)
        + [普通行情站点](/dictionary/#普通行情站点)
    - [如何成为 VIP 行情用户](/dictionary/#如何成为-vip-行情用户) 
      * [购买流程](/dictionary/#购买流程) 
        + [步骤一：注册登录](/dictionary/#步骤一-注册登录)
        + [步骤二：购买权限](/dictionary/#步骤二-购买权限)
        + [步骤三：查看权限](/dictionary/#步骤三-查看权限)
      * [使用流程](/dictionary/#使用流程) 
        + [1.如何在券商 QMT 中使用](/dictionary/#_1-如何在券商-qmt-中使用)
        + [2.如何使用 Token](/dictionary/#_2-如何使用-token) 
          - [下载指定 xtquant 包](/dictionary/#下载指定-xtquant-包)
          - [基础用法 - 获取数据](/dictionary/#基础用法-获取数据)
          - [进阶用法 - 数据服务](/dictionary/#进阶用法-数据服务)
        + [3.如何在投研端中使用](/dictionary/#_3-如何在投研端中使用)
    - [更新日志](/dictionary/#更新日志) 
      * [2023.11](/dictionary/#_2023-11) 
        + [2023.11.01](/dictionary/#_2023-11-01)
        + [2023.11.21](/dictionary/#_2023-11-21)
        + [2023.11.22](/dictionary/#_2023-11-22)
      * [2023.12](/dictionary/#_2023-12) 
        + [2023.12.07](/dictionary/#_2023-12-07)
        + [2023.12.08](/dictionary/#_2023-12-08)
        + [2023.12.14](/dictionary/#_2023-12-14)
        + [2023.12.20](/dictionary/#_2023-12-20)
        + [2023.12.25](/dictionary/#_2023-12-25)
        + [2024.01.05](/dictionary/#_2024-01-05)
  + [股票数据](/dictionary/stock.html)
  + [行业概念数据](/dictionary/industry.html)
  + [指数数据](/dictionary/indexes.html)
  + [期货数据](/dictionary/future.html)
  + [期权数据](/dictionary/option.html)
  + [场内基金](/dictionary/floorfunds.html)
  + [债券数据](/dictionary/bond.html)
  + [常见问题](/dictionary/question_answer.html)
  + [场景化示例](/dictionary/scenario_based_example.html)
  + [迅投因子](/dictionary/xuntou_factor.html)

[#](#概述) 概述
-----------

欢迎使用迅投数据服务！本数据字典为您在使用过程中提供相关指导，您可以通过搜索相关数据，找到对应的`描述`、`用法`、`参数`、`返回`、`示例`。

如果您是 QMT 基础行情用户，遇到相关问题的时候，请查阅[VIP 行情用户优势对比](#vip-%E8%A1%8C%E6%83%85%E7%94%A8%E6%88%B7%E4%BC%98%E5%8A%BF%E5%AF%B9%E6%AF%94)，找到对应问题描述，如没有对应内容，可联系客服反馈。

如果您想成为 VIP 行情用户，请查阅[VIP 行情用户-购买流程](#%E8%B4%AD%E4%B9%B0%E6%B5%81%E7%A8%8B)，进行购买。

如果您已经是 VIP 行情用户，请查阅[VIP 行情用户-使用流程](#%E4%BD%BF%E7%94%A8%E6%B5%81%E7%A8%8B)，学习使用。

其他问题，欢迎您联系客服反馈。

[#](#vip-行情用户优势对比) VIP 行情用户优势对比
-------------------------------

### [#](#通用功能对比) 通用功能对比

| 数据类型 | 券商版权限 | 基础版权限 | 投研版权限 |
| --- | --- | --- | --- |
| 仿真交易权限 | 不支持 | 支持所有品种：股票、期货、期权 | [支持所有品种：股票、期货、期权](/dictionary/#%E9%80%9A%E7%94%A8%E5%8A%9F%E8%83%BD%E5%AF%B9%E6%AF%94) |
| Python交易权限 | 支持 | 支持 | [支持](/dictionary/#%E9%80%9A%E7%94%A8%E5%8A%9F%E8%83%BD%E5%AF%B9%E6%AF%94) |
| 图表交易权限 | 不支持 | 不支持 | [支持](/dictionary/#%E9%80%9A%E7%94%A8%E5%8A%9F%E8%83%BD%E5%AF%B9%E6%AF%94) |
| 直连期货交易 | 不支持 | 不支持 | [支持](/dictionary/#%E9%80%9A%E7%94%A8%E5%8A%9F%E8%83%BD%E5%AF%B9%E6%AF%94) |
| 高级 VBA、Python 函数 | 不支持 | 不支持 | [支持](/dictionary/#%E9%80%9A%E7%94%A8%E5%8A%9F%E8%83%BD%E5%AF%B9%E6%AF%94) |
| 专属微信组工程师指导 | 不支持 | 不支持 | [支持](/dictionary/#%E9%80%9A%E7%94%A8%E5%8A%9F%E8%83%BD%E5%AF%B9%E6%AF%94) |
|  |  |  | **VIP行情权限** |
| 行情数量 | 100 个限制 | 100 个限制 | [300个限制](/dictionary/#%E9%80%9A%E7%94%A8%E5%8A%9F%E8%83%BD%E5%AF%B9%E6%AF%94) |
| 盘口档位 | 最高 3 档 | 仅最新价 | [最高 5 档](/dictionary/#%E9%80%9A%E7%94%A8%E5%8A%9F%E8%83%BD%E5%AF%B9%E6%AF%94) |
| 品种 | 只支持股票 | 只支持股票 | [支持所有品种：股票、期货、期权](/dictionary/#%E9%80%9A%E7%94%A8%E5%8A%9F%E8%83%BD%E5%AF%B9%E6%AF%94) |
| 下载数据-历史范围 | 5m-1年 1m-1年 tick-1个月 | 5m-1年 1m-1年 tick-1个月 | [5m-3年 1m-3年 tick-1年](/dictionary/#%E9%80%9A%E7%94%A8%E5%8A%9F%E8%83%BD%E5%AF%B9%E6%AF%94) |
| 下载数据-流速 | 限制 | 限制 | [无限制](/dictionary/#%E9%80%9A%E7%94%A8%E5%8A%9F%E8%83%BD%E5%AF%B9%E6%AF%94) |
| 因子数据 | 不支持 | 不支持 | [需购买开通权限](/dictionary/#%E9%80%9A%E7%94%A8%E5%8A%9F%E8%83%BD%E5%AF%B9%E6%AF%94) |
| [北向、资金流、沪港通数据在新窗口打开](http://dict.thinktrader.net/dictionary/stock.html#%E8%8E%B7%E5%8F%96%E8%82%A1%E7%A5%A8%E8%B5%84%E9%87%91%E6%B5%81%E5%90%91%E6%95%B0%E6%8D%AE) | 不支持 | 不支持 | [支持](/dictionary/#%E9%80%9A%E7%94%A8%E5%8A%9F%E8%83%BD%E5%AF%B9%E6%AF%94) |
| [行业、商品指数行情数据在新窗口打开](http://dict.thinktrader.net/dictionary/indexes.html#%E8%8E%B7%E5%8F%96%E6%8C%87%E6%95%B0%E8%A1%8C%E6%83%85%E6%95%B0%E6%8D%AE) | 不支持 | 不支持 | [支持](/dictionary/#%E9%80%9A%E7%94%A8%E5%8A%9F%E8%83%BD%E5%AF%B9%E6%AF%94) |
| [现货、仓单、席位数据在新窗口打开](http://dict.thinktrader.net/dictionary/future.html#%E6%9C%9F%E8%B4%A7%E4%BB%93%E5%8D%95) | 不支持 | 不支持 | [支持](/dictionary/#%E9%80%9A%E7%94%A8%E5%8A%9F%E8%83%BD%E5%AF%B9%E6%AF%94) |
| [可转债数据在新窗口打开](http://dict.thinktrader.net/dictionary/bond.html#%E5%8F%AF%E8%BD%AC%E5%80%BA%E6%95%B0%E6%8D%AE) | 不支持 | 不支持 | [支持](/dictionary/#%E9%80%9A%E7%94%A8%E5%8A%9F%E8%83%BD%E5%AF%B9%E6%AF%94) |
| [ETF申赎清单数据在新窗口打开](http://dict.thinktrader.net/dictionary/floorfunds.html?id=7zqjlm#etf%E7%94%B3%E8%B5%8E%E6%B8%85%E5%8D%95) | 不支持 | 不支持 | [支持](/dictionary/#%E9%80%9A%E7%94%A8%E5%8A%9F%E8%83%BD%E5%AF%B9%E6%AF%94) |

### [#](#行情站点对比) 行情站点对比

普通行情使用的行情站点与VIP行情也有区别，更换VIP行情站点，能够带来更好的行情体验，添加方式如下： ![迅投数据服务_配置行情站点](/assets/迅投数据服务_配置行情站点-2ff7ee2a.png)

#### [#](#vip行情站点) VIP行情站点

| 地点 | 网址 | 端口 |
| --- | --- | --- |
| VIP迅投绍兴电信 | `vipsxmd1.thinktrader.net` | `55310` |
| VIP迅投绍兴电信 | `vipsxmd2.thinktrader.net` | `55310` |
| VIP迅投郑州联通 | `ltzzmd2.thinktrader.net` | `55300` |
| VIP迅投郑州联通 | `ltzzmd1.thinktrader.net` | `55300` |
| VIP迅投郑州电信 | `dxzzmd1.thinktrader.net` | `55300` |
| VIP迅投郑州电信 | `dxzzmd2.thinktrader.net` | `55300` |

  

| 地点 | IP地址 | 端口 |
| --- | --- | --- |
| VIP迅投绍兴电信 | `**************` | `55310` |
| VIP迅投绍兴电信 | `**************` | `55310` |
| VIP迅投郑州联通 | `*************` | `55300` |
| VIP迅投郑州联通 | `*************` | `55300` |
| VIP迅投郑州电信 | `***********` | `55300` |
| VIP迅投郑州电信 | `***********` | `55300` |

#### [#](#普通行情站点) 普通行情站点

| 地点 | 网址 | 端口 |
| --- | --- | --- |
| 迅投浦东电信 | `shmd1.thinktrader.net` | `55300` |
| 迅投浦东电信 | `shmd2.thinktrader.net` | `55300` |
| 迅投东莞电信 | `szmd1.thinktrader.net` | `55300` |
| 迅投东莞电信 | `szmd2.thinktrader.net` | `55300` |

  

| 地点 | IP地址 | 端口 |
| --- | --- | --- |
| 迅投浦东电信 | `*************` | `55300` |
| 迅投浦东电信 | `*************` | `55300` |
| 迅投东莞电信 | `**************` | `55300` |
| 迅投东莞电信 | `**************` | `55300` |

[#](#如何成为-vip-行情用户) 如何成为 VIP 行情用户
---------------------------------

### [#](#购买流程) 购买流程

![](/assets/迅投数据服务_购买流程-a48f3b53.png)

#### [#](#步骤一-注册登录) 步骤一：注册登录

![](/assets/迅投数据服务_用户注册-640215b7.png) 在[迅投研官网在新窗口打开](https://xuntou.net/#/signup)使用手机号注册你的投研账号。

提示

记录好你的密码，后续会很重要

#### [#](#步骤二-购买权限) 步骤二：购买权限

![](/assets/迅投数据服务_购买行情用户VIP-23b6c7d1.png)

登录你的投研账号，访问[投研服务页面在新窗口打开](https://xuntou.net/#/productvip)，选择`行情用户 VIP`，并支付。

![](/assets/迅投数据服务_支付选项-3a5872db.jpg)

支付方式支持`微信支付`、`支付宝`以及`对公转账`，其中`对公转账`信息如下：

* 公司名：`成都睿智融科科技有限公司`
* 账户号：`4402235009000153959`
* 开户行：`中国工商银行成都高新城南支行`

#### [#](#步骤三-查看权限) 步骤三：查看权限

![](/assets/迅投数据服务_权限时长查看-ffbb1b1e.png) 支付成功后，你就可以在[个人中心在新窗口打开](https://xuntou.net/#/userInfo)看到您的服务已经开启相应时长的使用权限

### [#](#使用流程) 使用流程

#### [#](#_1-如何在券商-qmt-中使用) 1.如何在券商 QMT 中使用

![](/assets/迅投数据服务_券商QMT配置行情用户VIP-9739b2c8.png)

1. 登录你的券商 QMT 后，点击行情，进入行情面板
2. 找到迅投行情主站（包括北京、上海、东莞等），点击`修改`
3. 在弹窗中将用户名和密码修改为自己的投研账号密码，点击确认
4. 点击`链接`，即可在券商 QMT 中使用行情用户 VIP 权限
5. 执行以上同样的操作，找到迅投资管行情，点击`修改`
6. 最后，点击右上角`全推行情`，在下拉框中选择`五档全推`

提示

1. 第五步，若不修改迅投资管行情的账号密码，不设置将无法收到五档全推
2. 第六步，若不修改全推行情，也无法收到五档全推

**操作演示**![](/assets/迅投数据服务_券商QMT_VIP行情用户配置演示-c4ab571c.gif)

#### [#](#_2-如何使用-token) 2.如何使用 Token

![](/assets/迅投数据服务_接口Token获取-fd6f236e.png)

1. 在你的迅投研官网的[个人中心在新窗口打开](https://xuntou.net/#/userInfo)，`迅投投研服务平台 - 用户中心 - 个人设置 - 接口 TOKEN` ，找到你的接口 TOKEN
2. 接口 TOKEN 一次生成一个，刷新后前一个 TOKEN 失效（刷新有间隔限制，请勿频繁刷新）
3. 接口 TOKEN 具体用法如下：

##### [#](#下载指定-xtquant-包) 下载指定 xtquant 包

提示

请提前下载指定 xtquant 包，

Windows:[下载链接在新窗口打开](http://dict.thinktrader.net/nativeApi/download_xtquant.html) 或在cmd窗口中运行指令

```
pip install xtquant -i https://pypi.tuna.tsinghua.edu.cn/simple

```

##### [#](#基础用法-获取数据) 基础用法 - 获取数据

python返回值

```
# 导入 xtdatacenter 模块
from xtquant import xtdatacenter as xtdc  
  
'''  
设置用于登录行情服务的token，此接口应该先于 init_quote 调用

token可以从投研用户中心获取
https://xuntou.net/#/userInfo
'''  
xtdc.set_token('这里输入token')
  
'''  
设置数据存储根目录，此接口应该先于 init_quote 调用  
datacenter 启动后，会在 data_home_dir 目录下建立若干目录存储数据  
此接口不是必须调用，如果不设置，会使用默认路径
'''  
# xtdc.set_data_home_dir('data') 

'''
函数用法可通过以下方式查看：
'''
# print(help(xtdc.set_data_home_dir))  
  
'''  
初始化行情模块  
'''  
xtdc.init()

'''
初始化需要一定时间，完成后即可按照数据字典的对应引导使用
'''

# 导入 xtdata
from xtquant import xtdata  

# 获取交易日期
tdl = xtdata.get_trading_dates('SH')  
print(tdl[-10:])  

# 获取板块列表
sl = xtdata.get_stock_list_in_sector('沪深A股')  
print(sl[::100])  

# 输出平安银行的相关信息 
data = xtdata.get_instrument_detail("000001.SZ")  
print(data)

# 其他数据获取的方法请参考数据字典：http://dict.thinktrader.net/dictionary/stock.html  

```

```
[1697558400000, 1697644800000, 1697731200000, 1697990400000, 1698076800000, 1698163200000, 1698249600000, 1698336000000, 1698595200000, 1698681600000]

['000001.SZ', '000507.SZ', '000652.SZ', '000809.SZ', '000961.SZ', '001336.SZ', '002087.SZ', '002191.SZ', '002294.SZ', '002395.SZ', '002505.SZ', '002608.SZ', '002715.SZ', '002826.SZ', '002935.SZ', '003043.SZ', '300107.SZ', '300211.SZ', '300316.SZ', '300425.SZ', '300528.SZ', '300630.SZ', '300735.SZ', '300840.SZ', '300945.SZ', '301050.SZ', '301168.SZ', '301291.SZ', '301487.SZ', '600101.SH', '600222.SH', '600351.SH', '600496.SH', '600611.SH', '600732.SH', '600846.SH', '600995.SH', '601360.SH', '601919.SH', '603088.SH', '603220.SH', '603380.SH', '603638.SH', '603826.SH', '605003.SH', '605499.SH', '688101.SH', '688215.SH', '688330.SH', '688500.SH', '688629.SH']

{'ExchangeID': 'SZ', 'InstrumentID': '000001', 'InstrumentName': '平安银行', 'ProductID': '', 'ProductName': '', 'ExchangeCode': '000001', 'UniCode': '000001', 'CreateDate': '0', 'OpenDate': '19910403', 'ExpireDate': 99999999, 'PreClose': 10.450000000000001, 'SettlementPrice': 10.450000000000001, 'UpStopPrice': 11.5, 'DownStopPrice': 9.41, 'FloatVolume': 19405546950.0, 'TotalVolume': 19405918198.0, 'LongMarginRatio': 1.7976931348623157e+308, 'ShortMarginRatio': 1.7976931348623157e+308, 'PriceTick': 0.01, 'VolumeMultiple': 1, 'MainContract': 2147483647, 'LastVolume': 2147483647, 'InstrumentStatus': 0, 'IsTrading': False, 'IsRecent': False, 'ProductTradeQuota': -1582372688, 'ContractTradeQuota': -476598553, 'ProductOpenInterestQuota': -1662614912, 'ContractOpenInterestQuota': -1582504276}


```

##### [#](#进阶用法-数据服务) 进阶用法 - 数据服务

当您已经实现基础用法，成功获取数据后，随即可能会有新的需求：

**如果我有多个策略，在不同进程中运行，都要获取数据，而 Token 只支持单点访问，该怎么办？**

我们同样提供数据服务，您可以在一个进程中启动数据服务，其他进程连接该数据服务，实现您想要的效果，具体演示如下：

**进程 1**

python返回值

```
    ### 进程1 启动xtdatacenter监听

    from xtquant import xtdatacenter as xtdc

    xtdc.set_token('这里输入token')

    print('xtdc.init')
    xtdc.init() # 初始化行情模块，加载合约数据，会需要大约十几秒的时间
    print('done')

    # 为其他进程的xtdata提供服务时启动server，单进程使用不需要
    print('xtdc.listen')
    listen_addr = xtdc.listen(port = 58610)
    print(f'done, listen_addr:{listen_addr}')

    from xtquant import xtdata
    print('running')
    xtdata.run() #循环，维持程序运行


```

```
xtdc.init

done
xtdc.listen
done, listen_addr:('0.0.0.0', 58610)
running


```

**进程 2**

python返回值

```
from xtquant import xtdata
'''
连接数据服务指定的端口
'''
xtdata.connect(port=58610)


# 以下即可正常执行获取数据的操作
tdl = xtdata.get_trading_dates('SH')
print(tdl[-10:])

sl = xtdata.get_stock_list_in_sector('沪深A股')
print(sl[::100])

# 结合数据字典：http://dict.thinktrader.net/dictionary/stock.html

# 输出平安银行信息的中文名称
data = xtdata.get_instrument_detail("000001.SZ")
print(data)


```

```
[1698249600000, 1698336000000, 1698595200000, 1698681600000, 1698768000000, 1698854400000, 1698940800000, 1699200000000, 1699286400000, 1699372800000]
['000001.SZ', '000507.SZ', '000652.SZ', '000809.SZ', '000961.SZ', '001333.SZ', '002086.SZ', '002190.SZ', '002293.SZ', '002394.SZ', '002502.SZ', '002607.SZ', '002714.SZ', '002825.SZ', '002933.SZ', '003042.SZ', '300106.SZ', '300210.SZ', '300315.SZ', '300424.SZ', '300527.SZ', '300629.SZ', '300733.SZ', '300839.SZ', '300943.SZ', '301049.SZ', '301167.SZ', '301290.SZ', '301486.SZ', '600100.SH', '600221.SH', '600350.SH', '600495.SH', '600610.SH', '600731.SH', '600845.SH', '600993.SH', '601339.SH', '601918.SH', '603086.SH', '603217.SH', '603377.SH', '603633.SH', '603822.SH', '603998.SH', '605398.SH', '688098.SH', '688211.SH', '688327.SH', '688496.SH', '688626.SH']
{'ExchangeID': 'SZ', 'InstrumentID': '000001', 'InstrumentName': '平安银行', 'ProductID': '', 'ProductName': '', 'ExchangeCode': '000001', 'UniCode': '000001', 'CreateDate': '0', 'OpenDate': '19910403', 'ExpireDate': 99999999, 'PreClose': 10.6, 'SettlementPrice': 10.6, 'UpStopPrice': 11.66, 'DownStopPrice': 9.540000000000001, 'FloatVolume': 19405546950.0, 'TotalVolume': 19405918198.0, 'LongMarginRatio': 1.7976931348623157e+308, 'ShortMarginRatio': 1.7976931348623157e+308, 'PriceTick': 0.01, 'VolumeMultiple': 1, 'MainContract': 2147483647, 'LastVolume': 2147483647, 'InstrumentStatus': 0, 'IsTrading': False, 'IsRecent': False, 'ProductTradeQuota': 0, 'ContractTradeQuota': 0, 'ProductOpenInterestQuota': 6, 'ContractOpenInterestQuota': 0}

```

提示

更进一步，您还可以在一台服务器启动数据服务，其他服务器连接过去请求数据。注意：一个数据服务最多支持 5 个接入，如需更多，请联系客服购买。

遇到问题，请参考常见问题[Token 使用相关](/dictionary/question_answer.html#token-%E4%BD%BF%E7%94%A8%E7%9B%B8%E5%85%B3)

#### [#](#_3-如何在投研端中使用) 3.如何在投研端中使用

1. 购买投研端的用户默认拥有行情用户 VIP 权限，且已经自动配置好
2. 投研端的用户可以在券商 QMT 中使用，具体参考[如何在券商 QMT 中使用](#_1-%E5%A6%82%E4%BD%95%E5%9C%A8%E5%88%B8%E5%95%86-qmt-%E4%B8%AD%E4%BD%BF%E7%94%A8)
3. 投研端的用户同样可以在后台找到接口 TOKEN，具体参考[如何使用 Token](#_2-%E5%A6%82%E4%BD%95%E4%BD%BF%E7%94%A8-token)

[#](#更新日志) 更新日志
---------------

### [#](#_2023-11) 2023.11

#### [#](#_2023-11-01) 2023.11.01

* 更新 快速开始
* 补充可转债数据字段
* 优化部分描述

#### [#](#_2023-11-21) 2023.11.21

* 更新K线全推示例
* 更新界面操作-独立python进程
* 新添加get\_trade\_detail\_data - `POSITION_STATISTICS`结构

#### [#](#_2023-11-22) 2023.11.22

* 增加常见pandas问题及处理方案

### [#](#_2023-12) 2023.12

#### [#](#_2023-12-07) 2023.12.07

* 新增历史涨跌停价数据
* 新增历史ST数据下载方式
* 新增获取历史期权合约方法
* 更新财务数据获取方式
* 修正 get\_etf\_info 示例

#### [#](#_2023-12-08) 2023.12.08

* 优化文档显示内容

#### [#](#_2023-12-14) 2023.12.14

* 新增 TOP10HOLDER/TOP10FLOWHOLDER - 十大股东/十大流通股东
* 新增 SHAREHOLDER - 股东数

#### [#](#_2023-12-20) 2023.12.20

* 修复文档描述错误

#### [#](#_2023-12-25) 2023.12.25

* 增加get\_etf\_info字段描述
* 优化VIP行情对比

#### [#](#_2024-01-05) 2024.01.05

* 增加回测复权方式说明
* 增加openInt变化状态说明
* 优化文档显示内容

上次更新: 2025/4/22 14:38:17

邀请注册送VIP优惠券

分享下方的内容给好友、QQ群、微信群,好友注册您即可获得VIP优惠券

玩转qmt,上迅投qmt知识库

登录后获取

[股票数据](/dictionary/stock.html)