---
description: 
globs: "*"
alwaysApply: true
---
# 项目结构规范

本文档定义了项目的目录结构、文件组织和命名规范，旨在保持代码库的一致性和可维护性。所有开发人员和AI助手都应严格遵循这些规范。

## 1. 项目目录结构规范

### 1.1 标准目录结构

project_root/
│
├── .gitignore                # Git忽略文件配置
├── .gitmessage               # 提交信息模板
├── .pre-commit-config.yaml   # 预提交钩子配置
├── requirements.txt          # 项目依赖列表
├── setup.cfg                 # 项目配置文件
├── pyproject.toml            # Python项目配置
├── main.py                   # 项目主入口点
│
├── config/                   # 配置目录
│   ├── __init__.py
│   ├── settings.py           # 全局设置
│   └── logging_config.py     # 日志配置
│
├── data/                     # 数据层
│   ├── __init__.py
│   ├── fetcher/              # 数据获取模块
│   ├── processor/            # 数据处理模块
│   ├── storage/              # 数据存储管理模块
│   └── [其他数据相关子模块]
│
├── [功能模块1]/              # 各功能模块采用一致的子目录结构
│   ├── __init__.py
│   ├── core/                 # 核心功能代码
│   ├── utils/                # 模块专用工具
│   └── tests/                # 模块测试代码
│
├── [功能模块2]/
│   └── ...
│
├── utils/                    # 通用工具模块
│   ├── __init__.py
│   └── [各类工具函数]
│
├── tests/                    # 测试目录
│   ├── __init__.py
│   └── [各测试文件]
│
├── examples/                 # 示例目录
│   └── [示例文件]
│
└── docs/                     # 文档目录
    └── [各种文档]
```
## 2. 模块化组织规范

### 2.1 模块定义

- **独立性**：每个功能模块应当设计为独立的组件，有明确的职责边界
- **内聚性**：相关的功能应当在同一模块内，减少模块间的耦合
- **标准接口**：模块应提供清晰的API，隐藏实现细节

### 2.2 模块内部结构

每个功能模块推荐遵循以下内部组织结构：

```
<module_name>/
├── __init__.py            # 将目录标记为Python包，可控制导出
├── __main__.py            # 模块的命令行接口入口点 (若适用)
├── <module_main>.py       # 模块的功能主入口脚本
├── core/                  # 存放核心功能代码、主要类定义等
├── utils/                 # 存放该模块专用的辅助函数、工具类
├── tests/                 # 存放针对该模块的单元测试、集成测试
├── docs/                  # 存放该模块的详细设计文档、API说明（若复杂）
└── README.md              # (推荐) 模块专属的说明文档
```

### 2.3 目录管理规则

- **严格的模块化**：每个主要功能集应作为独立的模块存放在其专属目录下
- **文件归属**：模块相关的所有文件必须存放在对应模块的目录内
- **根目录内容限制**：项目根目录只允许存放：
  - 项目级文档（如LICENSE文件等其他非主README的文档）。
  - 项目配置文件（如pyproject.toml、setup.cfg）
  - 项目入口点（如main.py）
  - 各模块的根目录
- **临时文件管理**：所有临时文件必须放在模块的临时目录中，并在使用后删除

## 3. 文件命名与分类规范

### 3.1 命名规则

本项目采用统一的文件命名规范以提高一致性，适用于除特别排除外的所有文件类型（包括 `.py`, `.md`, 脚本, 报告等）。

- **项目级文件**:
    - 位于项目根目录下的文件。
    - 命名格式：`项目名_文件名` (使用小写下划线 `snake_case`)。
    - **示例**: `quant_main.py`, `quant_run_backtest.py`
    - **排除**: 标准配置文件（如 `.gitignore`, `.pre-commit-config.yaml`, `setup.cfg`, `pyproject.toml`, `requirements.txt` 等）和 `.cursor/rules/general_doc/` 目录下的文件不遵循此规则。

- **模块级文件**:
    - 位于各功能模块目录下的文件。
    - 命名格式：`模块名_文件名` (使用小写下划线 `snake_case`)。
    - **示例**:
        - 在 `data` 模块下: `data_fetcher.py`, `data_storage_manager.py`
        - 在 `utils` 模块下: `utils_logger.py`, `utils_formatter.py`
    - **注意**: 此规则应用于模块内的所有文件，包括Python模块文件（`.py`），这可能与Python社区常规做法（直接使用`文件名.py`）不同。

- **测试文件**:
    - 遵循模块级或项目级命名规则，并添加 `_test` 后缀。
    - 命名格式：`模块名_被测试文件名_test.py` 或 `项目名_被测试文件名_test.py`。
    - **示例**: `data_fetcher_test.py`, `utils_formatter_test.py`, `quant_main_test.py`

- **临时文件**:
    - 遵循模块级或项目级命名规则，并添加 `_temp` 或 `_test` 前缀/后缀（根据用途）。
    - 命名格式：`模块名_temp_描述.txt` 或 `项目名_test_脚本.py`。
    - **示例**: `data_temp_download_ids.txt`, `quant_test_parallel_run.py`

- **Python类**: （此规则不变，仅影响类定义，非文件命名）
    - 使用驼峰命名法（CamelCase）。
    - **示例**: `class DataProcessor`, `class RiskManager`

### 3.2 文件分类

- **核心功能文件**：放在模块的根目录或core/子目录
- **工具函数文件**：放在模块的utils/子目录
- **测试文件**：放在模块的tests/子目录或项目根目录下的tests/目录
- **示例文件**：放在examples/目录
- **文档文件**：放在docs/目录或模块内的docs/子目录

## 4. 工具和命令行开发规范

### 4.1 命令行工具开发

- **提供`__main__.py`**：每个可独立运行的模块应提供`__main__.py`作为命令行入口
- **标准参数解析**：使用`argparse`或类似库实现命令行参数解析
- **子命令结构**：支持子命令格式 `python -m module_name command [options]`
- **帮助文档**：所有命令和选项必须提供帮助文本（通过`--help`参数访问）
- **非交互支持**：命令行工具必须支持非交互式模式，避免依赖用户输入
- **标准退出码**：成功执行返回0，失败返回非0值

### 4.2 交互式与非交互式设计

- **默认值支持**：所有交互式提示必须提供合理的默认值
- **非交互选项**：提供`--non-interactive`或类似选项绕过所有交互提示
- **批处理支持**：设计支持在自动化脚本中无人值守运行

## 5. 文档规范

### 5.1 项目文档

- **README.md**：项目主说明文档应位于项目特定的规则文件夹内 (例如 .cursor/rules/{项目文件夹}/README.mdc)，描述项目概述、安装方法和基本使用
- **模块文档**：每个主要模块目录应包含自己的README.md，详细描述模块功能和接口
- **API文档**：复杂模块应提供详细的API文档
- **示例文档**：在examples/目录提供使用示例

### 5.2 代码文档

- **函数文档字符串**：所有公共函数和方法必须包含文档字符串，说明功能、参数和返回值
- **模块文档字符串**：每个Python模块文件开头应包含模块功能说明
- **复杂逻辑注释**：复杂的代码逻辑必须添加注释说明

示例函数文档字符串：

```python
def process_data(data: pd.DataFrame, threshold: float = 0.5) -> pd.DataFrame:
    """
    处理输入的数据框。

    Args:
        data: 输入的数据框
        threshold: 过滤阈值，默认0.5

    Returns:
        处理后的数据框
    """
    # 函数实现
    pass
```

## 6. Git相关规范

### 6.1 分支管理

- **主分支**：master/main分支保持稳定，只合并经过测试的代码
- **开发分支**：feature分支用于新功能开发
- **修复分支**：bugfix分支用于修复缺陷
- **发布分支**：release分支用于版本发布准备

### 6.2 提交规范

- **原子提交**：每个提交应专注于单一变更
- **模块化提交**：提交应以模块为单位，避免混合提交
- **提交信息**：使用规范的提交信息格式，包含修改的模块名和变更内容
- **预提交检查**：使用pre-commit钩子进行代码格式和质量检查

## 7. 模块化开发规范

### 7.1 统一项目入口点

#### 7.1.1 交互式入口点
- **创建统一入口点**：在根目录创建交互式Python入口点(main.py)，用于管理多个模块的功能
- **交互式界面**：通过菜单方式选择和使用各模块功能
- **避免模块直接快捷方式**：不在根目录为每个模块单独创建快捷方式或执行文件
- **用户友好设计**：提供简单直观的使用界面，减少命令行参数复杂度

#### 7.1.2 入口点实现规范
- **模块注册机制**：入口点应支持模块的动态注册
- **帮助系统**：提供清晰的模块功能说明
- **错误处理**：提供友好的错误提示和使用建议
- **版本控制**：支持显示各模块版本信息
- **日志记录**：记录命令执行情况和错误信息

### 7.2 模块间依赖与通信

#### 7.2.1 依赖管理
- **显式依赖声明**：在`__init__.py`中声明模块的依赖关系
- **避免循环依赖**：设计时预防循环导入问题
- **最小化跨模块依赖**：减少模块间耦合，提高内聚性
- **使用相对导入**：在模块内部使用相对导入来引用同一模块中的其他文件

#### 7.2.2 模块通信
- **标准化接口**：定义清晰的模块间通信接口
- **事件机制**：使用事件系统进行模块间松耦合通信
- **数据共享规范**：规范化模块间数据传递方式
- **异常处理**：统一的跨模块异常处理机制

### 7.3 脚本执行模式规范

#### 7.3.1 避免交互式菜单
- **禁止使用交互式菜单**：当需要执行脚本时，避免直接进入交互式菜单模式
- **优先查找命令行参数**：执行任何自定义脚本前，先查看脚本是否支持命令行参数
- **查找帮助信息**：通过 `python script.py --help` 或 `python -m module_name --help` 获取参数信息

#### 7.3.2 命令行参数规范
- **直接参数执行**：使用 `python -m module_name specific-command` 的方式直接执行特定功能
- **参数优先级**：命令行参数 > 配置文件 > 环境变量 > 交互式输入
- **避免管道输入**：不要使用 `echo "input" | python script.py` 这类方式模拟交互输入

#### 7.3.3 常见命令行模式
- **帮助查询**：`python -m module_name --help` 获取所有可用命令
- **子命令执行**：`python -m module_name subcommand [options]` 执行特定子命令
- **参数传递**：`python -m module_name --param1 value1 --param2 value2` 传递参数
- **自动化选项**：优先查找诸如 `--non-interactive`, `--batch`, `--quiet` 等非交互选项

### 7.4 防止文件结构混乱的措施

- **定期结构审查**：每周检查项目结构，确保文件位置符合规范
- **清理临时文件**：定期清理不再需要的临时文件
- **结构重构**：识别并修正结构不合理之处
- **代码审查检查点**：在代码审查时将文件位置作为重要检查点

## 8. 实践示例

### 8.1 创建新模块
创建新模块时应遵循以下步骤：

1. 在根目录下创建模块目录：`mkdir new_module`
2. 创建标准目录结构：
   ```
   mkdir new_module\core new_module\utils new_module\tests new_module\docs new_module\examples new_module\temp
   ```
3. 创建模块初始化文件：`touch new_module\__init__.py`
4. 创建README.md说明模块用途
5. 实现模块功能代码
6. 为模块创建主入口点脚本，如`new_module/module_main.py`
7. 在统一入口点(main.py)中添加对新模块的支持

### 8.2 模块使用示例
正确的模块使用方式：
```
# 使用统一交互式入口点
python main.py
# 然后在菜单中选择对应模块和功能

# 直接使用模块
python new_module/module_main.py --interactive
```

## 9. 维护与更新

本规范文档应定期更新以反映项目发展需求。所有更新都应经过团队审查并通知所有项目成员。 