#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
获取 xtquant 板块成分股和交易所合约列表的功能

本脚本支持通过命令行参数获取板块列表、板块成分股、合约基础信息和行情数据。
命令行使用说明：

1. 获取所有板块列表:
   python fetch_xtquant_sector_data.py all_sectors

2. 只获取主流板块列表:
   python fetch_xtquant_sector_data.py mainstream_sectors

3. 获取指定板块的成分列表:
   python fetch_xtquant_sector_data.py sector_stocks --sector "沪深300"
   python fetch_xtquant_sector_data.py sector_stocks -s "沪深300,中证500,中证1000"

4. 获取合约基础信息:
   python fetch_xtquant_sector_data.py contract_info --codes "000001.SZ,600000.SH"
   python fetch_xtquant_sector_data.py contract_info -c "000001.SZ,600000.SH"

5. 获取指定合约列表的实时行情数据:
   python fetch_xtquant_sector_data.py quote_data --codes "000001.SZ,600000.SH"
   python fetch_xtquant_sector_data.py quote_data -c "000001.SZ,600000.SH"

6. 获取指定合约的历史行情数据:
   python fetch_xtquant_sector_data.py market_data --codes "000001.SZ" --start 20230101 --end 20230131 --period 1d
   python fetch_xtquant_sector_data.py market_data -c "000001.SZ,600000.SH" --start 20230101 --end 20230131 --period 5m

获取帮助:
   python fetch_xtquant_sector_data.py -h
   python fetch_xtquant_sector_data.py all_sectors -h
"""

import concurrent.futures
import os
# import re # Removed: unused
import sys
import time
import argparse  # 新增: 命令行参数解析
# import unicodedata # Removed: unused
from datetime import date, datetime, timedelta
from typing import Any, Dict, List, Optional

import pandas as pd

# 标准化导入xtquant
from xtquant import xtdata as xt_data

xt_data.enable_hello = False

# 添加项目根目录到Python路径
current_path = os.path.abspath(__file__)
plate_data_dir = os.path.dirname(current_path)
providers_dir = os.path.dirname(plate_data_dir)
source_dir = os.path.dirname(providers_dir)
data_dir = os.path.dirname(source_dir)
project_root = os.path.dirname(data_dir)
sys.path.insert(0, project_root)

# 导入utils模块中的功能，替换自定义函数
from utils.data_display.text import get_display_width, format_column  # noqa: E402
from utils.text_parser import parse_instrument_input, parse_text_items  # noqa: E402
from utils.logger import get_unified_logger  # noqa: E402
from utils.smart_time_converter import smart_to_datetime

# 命令行输出格式化
def print_cmd_header(title):
    """打印命令行标题"""
    width = 80
    print("\n" + "=" * width)
    print(f"{title:^{width}}")
    print("=" * width + "\n")

def print_cmd_section(title):
    """打印命令行子标题"""
    width = 80
    print("\n" + "-" * width)
    print(f"== {title} ==")
    print("-" * width + "\n")

def print_cmd_info(message):
    """打印命令行信息"""
    print(f"INFO: {message}")

def print_cmd_success(message):
    """打印命令行成功信息"""
    print(f"SUCCESS: {message}")

def print_cmd_warning(message):
    """打印命令行警告信息"""
    print(f"WARNING: {message}")

def print_cmd_error(message):
    """打印命令行错误信息"""
    print(f"ERROR: {message}")

# 定义替代函数 - 使用已有的parse_text_items来实现parse_sector_input
def parse_sector_input(text: str) -> List[str]:
    """
    解析板块名称输入
    
    这是一个替代函数，使用现有的parse_text_items实现
    """
    # 使用通用文本解析，不需要特殊格式化
    return parse_text_items(text)

# 确保项目根目录下的logs文件夹存在
logs_dir = os.path.join(project_root, "logs")
if not os.path.exists(logs_dir):
    os.makedirs(logs_dir)

# 配置日志 (改为使用项目根目录下的logs文件夹)
script_dir = os.path.dirname(os.path.abspath(__file__))  # 保留原始 script_dir 定义 
log_file_name = os.path.join(logs_dir, "fetch_xtquant_sector_data.log")

# 确保plate_data文件夹存在 - 现在脚本已经在plate_data目录中
plate_data_dir = script_dir  # 直接使用脚本所在目录作为数据存储目录

# 使用 utils.logger 中的 get_unified_logger
logger = get_unified_logger(__name__)

logger.info("xt_data (xtquant) 库导入成功, enable_hello已设置为False")
logger.info("通用格式化和解析函数已从 utils 模块导入")
logger.info(f"日志文件将保存在项目根目录的logs文件夹中: {log_file_name}")


def fetch_and_save_sector_list(only_mainstream=False):
    """
    获取所有板块列表并保存到文件
    
    Args:
        only_mainstream: 是否只获取主流板块，默认为False获取全部板块
    """
    logger.info("\n===== 获取板块列表并保存到文件 =====")
    logger.info(f"过滤模式: {'只获取主流板块' if only_mainstream else '获取全部板块'}")
    
    try:
        # 先下载板块列表和成分股信息
        logger.info("开始下载板块列表和成分股信息...")
        xt_data.download_sector_data()
        logger.info("板块列表和成分股信息下载完成。")

        # 获取所有板块名称
        logger.info("开始获取所有板块名称...")
        all_sectors = xt_data.get_sector_list()
        
        if all_sectors:
            logger.info(f"成功获取到 {len(all_sectors)} 个板块名称")
            
            # 如果需要过滤只保留主流板块
            if only_mainstream:
                # 主流板块的过滤条件
                mainstream_keywords = [
                    # 主要交易所
                    "上证", "深证", "沪深", "上交所", "深交所", "北交所", "港股", 
                    "上期所", "中金所", "大商所", "郑商所",
                    # 主要股票分类
                    "A股", "B股", "创业板", "科创板", "主板", "中小板",
                    # 主要指数
                    "上证50", "沪深300", "中证500", "中证800", "中证1000", "创业板指", "科创50", 
                    # 主要ETF
                    "ETF50", "ETF300", "ETF500",
                    # 主要行业分类
                    "SW1", "SW2", "SW3",  # 申万行业
                    "中信一级", "中信二级", "中信三级",
                    "证监会", "国证", "Wind",
                    # 重要板块
                    "白马股", "蓝筹股", "ST股", "绩优股", "主力", "成分股",
                    # 重要概念
                    "大数据", "云计算", "人工智能", "新能源", "军工", "医药", "消费", "金融",
                    "半导体", "新基建", "物联网", "区块链", "元宇宙", "ChatGPT"
                ]
                
                # 排除项
                exclude_keywords = [
                    "TGN",  # 排除TGN开头的冷门概念板块
                ]
                
                # 过滤板块
                filtered_sectors = []
                for sector in all_sectors:
                    # 排除包含排除关键词的板块
                    if any(exclude_kw in sector for exclude_kw in exclude_keywords):
                        continue
                    
                    # 包含主流关键词的板块
                    if any(mainstream_kw in sector for mainstream_kw in mainstream_keywords):
                        filtered_sectors.append(sector)
                
                # 附加手动添加的重要板块
                manual_important_sectors = [
                    "沪深A股", "上证A股", "深证A股",
                    "主力板块",  # 主力期货合约
                ]
                
                for sector in manual_important_sectors:
                    if sector in all_sectors and sector not in filtered_sectors:
                        filtered_sectors.append(sector)
                
                # 更新为过滤后的板块列表
                logger.info(
                    f"应用过滤条件后，保留了 {len(filtered_sectors)} 个主流板块 "
                    f"(占比 {len(filtered_sectors)/len(all_sectors)*100:.1f}%)"
                )
                all_sectors = filtered_sectors
            
            # 分类展示样本
            logger.info("部分板块示例:")
            stock_sectors = [
                s for s in all_sectors if "股" in s or "板" in s or "证" in s
            ][:5]
            industry_sectors = [s for s in all_sectors if "行业" in s or "概念" in s][
                :5
            ]
            exchange_sectors = [
                s for s in all_sectors if "所" in s and "行业" not in s
            ][:5]
            other_exclude_keys = ["股", "板", "证", "行业", "概念", "所"]
            other_sectors = [
                s for s in all_sectors if not any(k in s for k in other_exclude_keys)
            ][:5]

            print(f"  股票相关板块示例: {stock_sectors}")
            print(f"  行业/概念板块示例: {industry_sectors}")
            print(f"  可能的交易所板块示例: {exchange_sectors}")
            print(f"  其他板块示例: {other_sectors}")

            # 保存到文件 - 根据过滤模式指定不同的文件名
            file_prefix = "主流板块" if only_mainstream else "板块"
            output_file_path = os.path.join(plate_data_dir, f"{file_prefix}成分列表.txt")
            try:
                with open(output_file_path, "w", encoding="utf-8") as f:
                    for sector_name in all_sectors:
                        f.write(f"{sector_name}\n")
                logger.info(f"{file_prefix}列表已成功保存到: {output_file_path}")
            except IOError as e:
                logger.error(
                    f"保存{file_prefix}列表到文件 {output_file_path} 时出错: {e}", exc_info=True
                )

        else:
            logger.warning("未能获取到板块列表，返回为空。")
    except Exception as e:
        logger.error(f"获取板块列表失败: {e}", exc_info=True)


def get_stock_list_in_sector(sector_name: str):
    """获取指定板块的成分股/合约列表并保存到文件"""
    logger.info(f"\n===== 获取板块 '{sector_name}' 的成分列表并保存 =====")
    try:
        start_time = time.time()
        stock_list = xt_data.get_stock_list_in_sector(sector_name)
        duration = time.time() - start_time
        if stock_list:
            logger.info(
                f"成功获取板块 '{sector_name}' 的 {len(stock_list)} 个标的 "
                f"(耗时: {duration:.2f}秒)"
            )
            logger.info(f"前 10 个示例: {stock_list[:10]}")

            # 将板块名称中的特殊字符替换为下划线
            safe_sector_name = sector_name.replace("/", "_").replace("\\", "_")
            safe_sector_name = safe_sector_name.replace(":", "_")
            output_file_path = os.path.join(
                plate_data_dir, f"{safe_sector_name}成分列表.txt"
            )
            
            logger.info(f"准备以覆盖模式('w')打开并写入文件: {output_file_path}")
            try:
                # 首先收集所有数据到内存，然后一次性写入文件
                all_data = []
                
                # 定义获取单个股票详情的函数
                def get_instrument_detail_safe(code):
                    try:
                        detail = xt_data.get_instrument_detail(code)
                        if detail and isinstance(detail, dict):
                            # 日期格式化处理
                            open_date = detail.get("OpenDate", "")
                            if (
                                open_date
                                and open_date != "0"
                                and open_date != "99999999"
                            ):
                                try:
                                    open_date = (
                                        f"{open_date[:4]}-"
                                        f"{open_date[4:6]}-"
                                        f"{open_date[6:8]}"
                                    )
                                except Exception as e:
                                    # 出错时保持原样
                                    logger.debug(f"日期解析错误: {e}")

                            expire_date = detail.get("ExpireDate", "")
                            if expire_date == "99999999":
                                expire_date = "长期有效"
                            elif expire_date and expire_date != "0":
                                try:
                                    expire_date = (
                                        f"{expire_date[:4]}-"
                                        f"{expire_date[4:6]}-"
                                        f"{expire_date[6:8]}"
                                    )
                                except Exception as e:
                                    # 出错时保持原样
                                    logger.debug(f"日期解析错误: {e}")
                            
                            # 将状态码转换为更可读的形式
                            status = detail.get("InstrumentStatus", "")
                            status_desc = "未知"
                            if status == "0":
                                status_desc = "正常交易"
                            elif status == "1":
                                status_desc = "停牌"
                            elif status == "2":
                                status_desc = "退市"
                            elif status == "3":
                                status_desc = "暂停上市"
                            
                            # 处理产品类型
                            product_class = detail.get("ProductClass", "--")
                            product_desc = product_class
                            if product_class == "1":
                                product_desc = "期货"
                            elif product_class == "2":
                                product_desc = "期权"
                            elif product_class == "3":
                                product_desc = "股票"
                            elif product_class == "4":
                                product_desc = "债券"
                            elif product_class == "5":
                                product_desc = "基金"

                            # 提取更多有价值的字段
                            price_tick_str = detail.get("PriceTick", "--")
                            price_tick: Any = price_tick_str
                            if price_tick_str and price_tick_str != "0":
                                try:
                                    price_tick = float(price_tick_str)
                                except ValueError:
                                    pass

                            volume_multiple_str = detail.get("VolumeMultiple", "--")
                            volume_multiple: Any = volume_multiple_str
                            if volume_multiple_str and volume_multiple_str != "0":
                                try:
                                    volume_multiple = int(float(volume_multiple_str))
                                except ValueError:
                                    pass

                            # 构建更完整的返回结构
                            result = {
                                "代码": code,
                                "名称": detail.get("InstrumentName", "未知"),
                                "交易所": detail.get("ExchangeID", "--"),
                                "类型": product_desc,
                                "价格单位": price_tick,
                                "合约乘数": volume_multiple,
                                "上市日期": open_date if open_date else "--",
                                "到期日期": expire_date if expire_date else "--",
                                "状态": status_desc,
                            }

                            # 添加其他有用字段
                            max_limit_vol_str = detail.get("MaxLimitOrderVolume")
                            if max_limit_vol_str:
                                result["最大委托量"] = max_limit_vol_str

                            # 处理交割年月 (修正行过长)
                            deliv_year = detail.get("DeliveryYear")
                            deliv_month = detail.get("DeliveryMonth")
                            if deliv_year and deliv_month:
                                if deliv_year != "0" and deliv_month != "0":
                                    result["交割年月"] = f"{deliv_year}-{deliv_month}"

                            if detail.get("IsTrading") == "1":
                                result["交易状态"] = "可交易"
                            elif detail.get("IsTrading") == "0":
                                result["交易状态"] = "不可交易"

                            return result
                        else:
                            logger.debug(f"获取 {code} 的详细信息失败，detail为空或非字典: {detail}")
                            return None
                    except Exception as e:
                        logger.debug(f"获取 {code} 的信息时出错: {e}")
                        return None
                
                # 使用线程池并行获取数据
                logger.info(f"开始并行获取 {len(stock_list)} 个合约的详细信息...")
                max_workers = min(20, len(stock_list))  # 最多20个线程
                with concurrent.futures.ThreadPoolExecutor(
                    max_workers=max_workers
                ) as executor:
                    # 使用tqdm显示进度
                    results = list(executor.map(get_instrument_detail_safe, stock_list))
                
                all_data = results
                logger.info(f"成功获取 {len(all_data)} 个合约的详细信息")
                
                # 打印少量获取到的原始数据样本以供对比
                if all_data and len(all_data) > 3:
                    logger.info(
                        "从xt_data.get_instrument_detail获取到的原始数据样本前3条:"
                    )
                    for i in range(min(3, len(all_data))):
                        # Linter 修复：检查 all_data[i] 是否为 None
                        item_data = all_data[i]
                        if item_data:
                            # 将字典推导式移入 if 块内
                            # item_data is guaranteed not None
                            sample_detail = {
                                k: item_data.get(k)
                                for k in [
                                    "代码",
                                    "名称",
                                    "交易所",
                                    "类型",
                                    "价格单位",
                                    "合约乘数",
                                    "上市日期",
                                    "到期日期",
                                    "状态",
                                ]
                                # item_data is guaranteed not None
                                if k in item_data
                            }
                            logger.info(f"  样本 {i + 1}: {sample_detail}")
                        else:
                            # Linter 修复：修正缩进
                            logger.info(f"  样本 {i + 1}: 获取失败 (None)")
                elif all_data:
                    logger.info("从xt_data.get_instrument_detail获取到的原始数据样本:")
                    for i, detail_sample in enumerate(all_data):
                        # Linter 修复：检查 detail_sample 是否为 None
                        if detail_sample:
                            # 将字典推导式移入 if 块内
                            # detail_sample is guaranteed not None
                            sample_detail = {
                                k: detail_sample.get(k)
                                for k in [
                                    "代码",
                                    "名称",
                                    "交易所",
                                    "类型",
                                    "价格单位",
                                    "合约乘数",
                                    "上市日期",
                                    "到期日期",
                                    "状态",
                                ]
                                # detail_sample is guaranteed not None
                                if k in detail_sample
                            }
                            logger.info(f"  样本 {i + 1}: {sample_detail}")
                        else:
                            # Linter 修复：修正缩进
                            logger.info(f"  样本 {i + 1}: 获取失败 (None)")

                # 创建表格格式化输出
                valid_data = [d for d in all_data if d is not None]
                if not valid_data:
                    logger.warning(f"未能获取板块 '{sector_name}' 中任何合约的有效详细信息。")
                    with open(output_file_path, "w", encoding="utf-8-sig") as f:
                        f.write(f"# 未能获取板块 '{sector_name}' 中任何合约的有效详细信息。\n")
                    logger.info(f"已写入提示信息到: {output_file_path}")
                    return

                df = pd.DataFrame(valid_data)
                
                # 计算每列所需的最大显示宽度（考虑中文和英文的宽度差异）
                col_widths = {}
                for col in df.columns:
                    # 计算列标题的显示宽度
                    header_width = get_display_width(col)
                    
                    # 计算该列所有数据的最大显示宽度
                    if not df.empty and col in df:  # Ensure column exists
                        # MODIFIED: Consistent NA handling
                        data_values = df[col].apply(
                            lambda x: str(x) if pd.notna(x) else "--"
                        )
                        data_width = data_values.map(get_display_width).max()
                        if pd.isna(data_width):  # Handle case: column all NaNs
                            data_width = get_display_width("--") 
                        max_width = max(header_width, data_width)
                    else:
                        max_width = header_width
                    
                    col_widths[col] = int(max_width) + 3  # 增加列宽，确保列之间有足够间隔
                
                # 添加表头注释
                header_comment = (
                    "# 文件说明: {} 板块成分列表\n"
                    "# 获取时间: {}\n"
                    "# 数据总数: {} 条记录\n"
                    "#\n"
                    "# 字段说明:\n"
                    "# 代码: 合约代码\n"
                    "# 名称: 合约名称\n"
                    "# 交易所: 所属交易所代码\n"
                    "# 类型: 合约类型(如股票、期货等)\n"
                    "# 价格单位: 最小变动价位\n"
                    "# 合约乘数: 一手对应的基础数量\n"
                    "# 上市日期: 合约上市交易的日期\n"
                    "# 到期日期: 合约到期的日期\n"
                    "# 状态: 合约当前交易状态\n"
                    "# 交易状态: 是否当前可交易\n"
                    "# 交割年月: 期货合约的交割年月\n"
                    "# 最大委托量: 单笔最大委托数量\n"
                    "#\n"
                ).format(
                    sector_name, datetime.now().strftime("%Y-%m-%d %H:%M:%S"), len(df)
                )
                
                # 一次性写入文件
                with open(output_file_path, "w", encoding="utf-8-sig") as f:
                    # 写入文件头
                    f.write(header_comment)
                    
                    cols = list(df.columns)  # Used for separator and rows

                    # 写入表头
                    header_line = ""
                    if cols:  # Only write header if there are columns
                        for col_name in cols:
                            header_line += format_column(col_name, col_widths[col_name])
                        f.write(header_line + "\n")
                    
                    # MODIFIED: 写入分隔线 (based on total display width)
                    if cols:
                        separator_display_width = sum(col_widths[c] for c in cols)
                        f.write("-" * separator_display_width + "\n")
                    elif header_line:  # Case: No data cols, header_comment written
                        f.write("\n")  # Add newline if only comments exist

                    # 写入数据行
                    if cols:  # Only write data if there are columns
                        for _, row in df.iterrows():
                            line = ""
                            for col_name in cols:
                                val_raw = row[col_name]
                                val = str(val_raw) if pd.notna(val_raw) else "--"
                                line += format_column(val, col_widths[col_name])
                            f.write(line + "\n")
                
                logger.info(f"文件已成功写入并关闭: {output_file_path}")
                total_time = time.time() - start_time
                log_msg = (
                    f"板块 '{sector_name}' 的成分列表已成功保存到: "
                    f"{output_file_path}"
                )
                logger.info(log_msg)
                logger.info(
                    f"总耗时: {total_time:.2f}秒，"
                    f"平均每个合约 {total_time / len(stock_list):.4f}秒"
                )
            except IOError as e:
                logger.error(
                    f"保存板块 '{sector_name}' 成分列表到文件出错: {e}", exc_info=True
                )
        else:
            logger.warning(
                f"未能获取板块 '{sector_name}' 的标的列表，返回为空。"
                f" 可能板块名称无效或无数据。"
            )
    except Exception as e:
        logger.error(
            f"调用 get_stock_list_in_sector('{sector_name}') 时出错: {e}", exc_info=True
        )


def format_instrument_detail_for_file(detail, code):
    """
    格式化合约详细信息，用于写入文件。
    尽可能提取所有可用的合约信息字段。

    Args:
        detail (dict): 从xt_data.get_instrument_detail获取的原始详细信息
        code (str): 合约代码，用于fallback

    Returns:
        dict: 格式化后的详细信息字典
    """
    if not detail or not isinstance(detail, dict):
        return {
            "代码": code,
            "名称": "获取失败",
            "交易所": "--",
            "类型": "--",
            "价格单位": "--",
            "合约乘数": "--",
            "上市日期": "--",
            "到期日期": "--",
            "状态": "--",
            "行情信息": "无法获取",
        }

    # 处理日期格式
    open_date = detail.get("OpenDate", "")
    if open_date and open_date != "0" and open_date != "99999999":
        try:
            open_date = f"{open_date[:4]}-{open_date[4:6]}-{open_date[6:8]}"
        except Exception:
            # 出错时保持原样
            pass

    expire_date = detail.get("ExpireDate", "")
    if expire_date == "99999999":
        expire_date = "长期有效"
    elif expire_date and expire_date != "0":
        try:
            expire_date = f"{expire_date[:4]}-{expire_date[4:6]}-{expire_date[6:8]}"
        except Exception:
            # 出错时保持原样
            pass

    # 处理状态码
    status = detail.get("InstrumentStatus", "")
    status_desc = "未知"
    if status == "0":
        status_desc = "正常交易"
    elif status == "1":
        status_desc = "停牌"
    elif status == "2":
        status_desc = "退市"
    elif status == "3":
        status_desc = "暂停上市"

    # 处理产品类型
    product_class = detail.get("ProductClass", "--")
    product_desc = product_class
    if product_class == "1":
        product_desc = "期货"
    elif product_class == "2":
        product_desc = "期权"
    elif product_class == "3":
        product_desc = "股票"
    elif product_class == "4":
        product_desc = "债券"
    elif product_class == "5":
        product_desc = "基金"

    # 数值类型格式化
    price_tick_str = detail.get("PriceTick", "--")
    price_tick: Any = price_tick_str
    if price_tick_str and price_tick_str != "0":
        try:
            price_tick = float(price_tick_str)
        except ValueError:
            pass

    volume_multiple_str = detail.get("VolumeMultiple", "--")
    volume_multiple: Any = volume_multiple_str
    if volume_multiple_str and volume_multiple_str != "0":
        try:
            volume_multiple = int(float(volume_multiple_str))
        except ValueError:
            pass

    # 构建基础信息
    formatted = {
        "代码": code,
        "名称": detail.get("InstrumentName", "未知"),
        "交易所": detail.get("ExchangeID", "--"),
        "类型": product_desc,
        "价格单位": price_tick,
        "合约乘数": volume_multiple,
        "上市日期": open_date if open_date else "--",
        "到期日期": expire_date if expire_date else "--",
        "状态": status_desc,
    }

    # 尝试获取行情相关字段
    market_fields = [
        ("昨收", "PreClose"),
        ("今开", "Open"),
        ("最高", "High"),
        ("最低", "Low"),
        ("最新价", "LastPrice"),
        ("成交量", "Volume"),
        ("持仓量", "OpenInterest"),
        ("涨停价", "UpperLimitPrice"),
        ("跌停价", "LowerLimitPrice"),
        ("成交额", "Turnover"),
        ("涨跌", "Change"),
        ("涨跌幅", "ChangeRatio"),
    ]

    market_data = {}
    for field_name, field_key in market_fields:
        value = detail.get(field_key, None)
        if value is not None and value != "" and value != "0":
            # 尝试转换数值类型
            try:
                if field_key in ["Volume", "OpenInterest"]:
                    value = int(float(value))
                elif field_key == "ChangeRatio":
                    value = f"{float(value):.2f}%"
                elif field_key in [
                    "Change",
                    "PreClose",
                    "Open",
                    "High",
                    "Low",
                    "LastPrice",
                    "UpperLimitPrice",
                    "LowerLimitPrice",
                    "Turnover",
                ]:
                    value = float(value)
            except ValueError:
                pass
            market_data[field_name] = value

    # 如果有行情信息，添加到结果中
    if market_data:
        market_info = ", ".join([f"{k}: {v}" for k, v in market_data.items()])
        formatted["行情信息"] = market_info
    else:
        formatted["行情信息"] = "无最新行情"

    # 添加交易相关信息
    trading_info = {}
    trading_fields = [
        ("交易单位", "TradeUnit"),
        ("最大委托量", "MaxLimitOrderVolume"),
        ("最小委托量", "MinLimitOrderVolume"),
        ("合约乘数", "VolumeMultiple"),
        ("交割月", "DeliveryMonth"),
        ("交割年份", "DeliveryYear"),
        ("创建日期", "CreateDate"),
        ("开始交割日", "StartDelivDate"),
        ("结束交割日", "EndDelivDate"),
        ("到期天数", "ExpireDays"),
        ("交割类型", "DeliveryType"),
    ]

    for field_name, field_key in trading_fields:
        value = detail.get(field_key, None)
        if value is not None and value != "" and value != "0":
            # 特殊处理某些字段 (修正行过长)
            is_delivery_field = field_key in ["DeliveryYear", "DeliveryMonth"]
            # 修正行过长：将 and field_name not in formatted 移到下一行
            if is_delivery_field and field_name not in formatted:
                deliv_year = detail.get("DeliveryYear")
                deliv_month = detail.get("DeliveryMonth")
                if (
                    deliv_year
                    and deliv_month
                    and deliv_year != "0"
                    and deliv_month != "0"
                ):
                    trading_info["交割年月"] = f"{deliv_year}-{deliv_month}"
                continue

            trading_info[field_name] = value

    # 处理交易状态
    if detail.get("IsTrading") == "1":
        trading_info["交易状态"] = "可交易"
    elif detail.get("IsTrading") == "0":
        trading_info["交易状态"] = "不可交易"

    # 处理期权特有字段
    if product_desc == "期权":
        option_fields = [
            ("标的代码", "UnderlyingInstrID"),
            ("执行价", "StrikePrice"),
            ("期权类型", "OptionsType"),
            ("合约单位", "ContractUnit"),
        ]

        for field_name, field_key in option_fields:
            value = detail.get(field_key, None)
            if value is not None and value != "" and value != "0":
                if field_key == "OptionsType":
                    if value == "1":
                        value = "看涨期权"
                    elif value == "2":
                        value = "看跌期权"
                trading_info[field_name] = value

    # 处理股票特有字段
    if product_desc == "股票":
        stock_fields = [
            ("限制卖出日", "BanlanceDay"),
            ("类型分类", "ProdType"),
            ("行业分类", "Industry"),
        ]

        for field_name, field_key in stock_fields:
            value = detail.get(field_key, None)
            if value is not None and value != "" and value != "0":
                trading_info[field_name] = value

    # 将交易信息添加到结果中
    if trading_info:
        # 将所有交易信息作为单独字段添加
        for k, v in trading_info.items():
            if k not in formatted:  # 避免重复添加
                formatted[k] = v

    # 添加其他可能有用的字段
    extra_fields = [
        ("是否主力", "IsMainContract"),
        ("委托方向", "PositionType"),
        ("流通股本", "Circulation"),
        ("总股本", "TotalShare"),
        ("是否ETF", "IsETF"),
        ("是否ST", "IsST"),
    ]

    for field_name, field_key in extra_fields:
        value = detail.get(field_key, None)
        if value is not None and value != "" and value != "0":
            if field_key in ["IsMainContract", "IsETF", "IsST"]:
                if value == "1":
                    value = "是"
                elif value == "0":
                    value = "否"
            formatted[field_name] = value

    # 将所有剩余的有意义数据字段添加为"其他信息"
    other_info = {}
    for key, value in detail.items():
        # 跳过已处理的字段或空值
        processed_keys = [fk for _, fk in market_fields + trading_fields + extra_fields]
        base_keys = [
            "InstrumentID",
            "InstrumentName",
            "ExchangeID",
            "ProductClass",
            "InstrumentStatus",
            "PriceTick",
            "VolumeMultiple",
            "OpenDate",
            "ExpireDate",
        ]
        if key in processed_keys or key in base_keys:
            continue

        if value is not None and value != "" and value != "0":
            # 尝试以用户友好的方式命名字段
            field_name = key
            # 可以在这里添加更多字段名映射
            field_mappings = {
                "MarketID": "市场ID",
                "CreateDate": "创建日期",
                "OpenDate2": "上市日期2",
                # 添加更多映射...
            }
            if key in field_mappings:
                field_name = field_mappings[key]

            other_info[field_name] = value

    # 如果有其他信息，添加到结果中
    if other_info:
        other_info_str = ", ".join([f"{k}: {v}" for k, v in other_info.items()])
        formatted["其他信息"] = other_info_str

    return formatted


def get_instrument_detail_basic():
    """获取用户指定合约的基础信息"""
    logger.info("\n===== 获取合约基础信息 =====")

    # 获取用户输入的合约代码
    default_codes = "000001.SZ,600000.SH"
    print("请输入要查询的合约代码 (支持多种输入格式):")
    print('- 多行输入 (如 "000001.SZ\\n600000.SH")')
    print('- 逗号分隔 (如 "000001.SZ,600000.SH")')
    print('- 空格分隔 (如 "000001.SZ 600000.SH")')
    print("- 可直接粘贴带序号的文本或表格数据")
    print(f"默认值: {default_codes}")
    print("输入完成后按回车键确认 (输入空行完成多行输入):")

    # 收集多行输入
    lines = []
    while True:
        line = input()
        if not line.strip() and lines:  # 空行且已有输入内容，结束输入
            break
        if not line.strip() and not lines:  # 空行且无输入内容，使用默认值
            lines = [default_codes]
            break
        lines.append(line)

    # 解析输入获取合约代码列表
    instrument_input = "\n".join(lines)
    instrument_codes = parse_instrument_input(instrument_input)

    if not instrument_codes:
        instrument_codes = parse_instrument_input(default_codes)
        logger.info(f"未检测到有效合约代码，使用默认值: {default_codes}")

    log_msg = (
        f"将获取以下 {len(instrument_codes)} 个合约的详细信息: " f"{instrument_codes}"
    )
    logger.info(log_msg)

    try:
        # 准备保存到文件
        output_file_path = os.path.join(plate_data_dir, "合约详细信息汇总.txt")

        # 添加文件头注释和表头
        header_comment = (
            "# 文件说明: 合约详细信息汇总\n"
            f"# 获取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"# 数据总数: {len(instrument_codes)} 条记录\n"
            "#\n"
            "# 字段说明:\n"
            "# 代码: 合约代码\n"
            "# 名称: 合约名称\n"
            "# 交易所: 所属交易所代码\n"
            "# 类型: 合约类型(如股票、期货等)\n"
            "# 价格单位: 最小变动价位\n"
            "# 合约乘数: 一手对应的基础数量\n"
            "# 上市日期: 合约上市交易的日期\n"
            "# 到期日期: 合约到期的日期\n"
            "# 状态: 合约当前交易状态\n"
            "# 行情信息: 包含昨收、今开、最高、最低、最新价等数据\n"
            "#\n"
        )

        # 创建一个DataFrame来存储所有合约信息
        all_details: list = []

        # 获取每个合约的详细信息
        for code in instrument_codes:
            start_time = time.time()
            detail = None
            try:
                detail = xt_data.get_instrument_detail(code)
            except Exception as e:
                logger.error(f"获取 {code} 详细信息时发生xt_data API错误: {e}")
                # detail 保持为 None

            duration = time.time() - start_time

            if detail:
                logger.info(f"成功获取 '{code}' 的详细信息 (耗时: {duration:.2f}秒)")
                # 格式化详细信息
                formatted_detail = format_instrument_detail_for_file(detail, code)
                all_details.append(formatted_detail)
            else:
                logger.warning(
                    f"未能获取 '{code}' 的详细信息，返回值为空或API调用失败。"
                )
                # 添加失败记录
                all_details.append(None)

            time.sleep(0.1)  # 避免频繁请求

        # 转换为DataFrame
        if all_details:
            df = pd.DataFrame(all_details)

            # 计算每列所需的最大显示宽度（考虑中文和英文的宽度差异）
            col_widths = {}
            for col in df.columns:
                # 计算列标题的显示宽度
                header_width = get_display_width(col)

                # 计算该列所有数据的最大显示宽度
                if not df.empty and col in df:
                    data_values = df[col].apply(
                        lambda x: str(x) if pd.notna(x) else "--"
                    )
                    data_width = data_values.map(get_display_width).max()
                    if pd.isna(data_width):
                        data_width = get_display_width("--")
                    max_width = max(header_width, data_width)
                else:
                    max_width = header_width

                col_widths[col] = int(max_width)

            # 写入文件
            with open(output_file_path, "w", encoding="utf-8-sig") as f:
                # 写入文件头
                f.write(header_comment)

                cols = list(df.columns)

                # 写入表头
                header_line = ""
                if cols:
                    for i, col_name in enumerate(cols):
                        is_last = i == len(cols) - 1
                        header_line += format_column(
                            col_name, col_widths[col_name], is_last
                        )
                    f.write(header_line + "\n")

                # 写入分隔线
                if cols:
                    separator_display_width = sum(col_widths[c] for c in cols) + max(
                        0, len(cols) - 1
                    )
                    f.write("-" * separator_display_width + "\n")
                elif header_line:
                    f.write("\n")

                # 写入数据行
                if cols:
                    for _, row in df.iterrows():
                        line = ""
                        for i, col_name in enumerate(cols):
                            is_last = i == len(cols) - 1
                            val_raw = row[col_name]
                            val = str(val_raw) if pd.notna(val_raw) else "--"
                            line += format_column(val, col_widths[col_name], is_last)
                        f.write(line + "\n")

            logger.info(f"合约详细信息已成功保存到: {output_file_path}")
        else:
            logger.warning("未能获取任何有效的合约详细信息。")
    except Exception as e:
        logger.error(f"获取合约详细信息时出错: {e}", exc_info=True)


def get_contract_details(stock_code: str) -> Optional[Dict[str, Any]]:
    """
    获取合约的详细信息

    Args:
        stock_code (str): 合约代码

    Returns:
        Optional[Dict[str, Any]]: 合约的详细信息，如果获取失败则返回None
    """
    try:
        detail = xt_data.get_instrument_detail(stock_code)
        if detail:
            return format_instrument_detail_for_file(detail, stock_code)
        else:
            logger.warning(f"未能获取合约 {stock_code} 的详细信息，返回值为空。")
            return None
    except Exception as e:
        logger.error(f"获取合约 {stock_code} 的详细信息时出错: {e}")
        return None


def fetch_market_data_for_contract_list(
    contract_list: List[str],
    start_time_str: str,
    end_time_str: str,
    period_str: str,
):
    """
    获取指定合约列表的市场数据并保存到文件。

    Args:
        contract_list (List[str]): 要获取数据的合约代码列表。
        start_time_str (str): 开始日期字符串 (YYYYMMDD)。
        end_time_str (str): 结束日期字符串 (YYYYMMDD)。
        period_str (str):周期字符串 (例如 '1d', '1m')。
    """
    # 使用第一个合约代码确定基础日志标签
    base_label = contract_list[0] if contract_list else "未知合约"
    logger.info(
        f"\n===== 获取 {base_label} 等 {len(contract_list)}个合约的市场数据 ====="
    )
    logger.info(f"日期范围: {start_time_str} - {end_time_str}, 周期: {period_str}")

    if not contract_list:
        logger.info("合约列表为空，跳过获取。")  # 调整日志信息
        return

    # 文件名基于第一个合约代码、日期和周期，移除label前缀
    first_contract_safe = "no_contracts"
    if contract_list:
        first_contract_safe = contract_list[0].replace(".", "_")
    output_file_name = (
        f"{first_contract_safe}_"
        f"{start_time_str}_to_{end_time_str}_{period_str}_市场数据.txt"
    )
    output_file_path = os.path.join(plate_data_dir, output_file_name)

    try:
        with open(output_file_path, "w", encoding="utf-8") as f:
            market_data_header = (
                "市场数据\n"  # 简化文件头, 已移除f前缀
                f"合约列表 (部分): {contract_list[:5]}"
                f"{'...' if len(contract_list) > 5 else ''}\n"
                f"合约总数: {len(contract_list)}\n"
                f"日期范围: {start_time_str} - {end_time_str}\n"
                f"周期: {period_str}\n"
                f"获取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            )
            f.write(market_data_header)

            for contract_code in contract_list:
                f.write(f"--- 合约: {contract_code} ---\n")

                target_contract_for_data = contract_code

                if not target_contract_for_data:
                    logger.warning(f"合约代码 {contract_code} 为空，跳过。")
                    f.write("合约代码为空，跳过\n\n")
                    continue

                try:
                    xt_data.download_history_data(
                        target_contract_for_data,
                        period=period_str,
                        start_time=start_time_str,
                        end_time=end_time_str,
                    )

                    market_data = xt_data.get_market_data_ex(
                        stock_list=[target_contract_for_data],
                        period=period_str,
                        start_time=start_time_str,
                        end_time=end_time_str,  # 使用用户指定的结束时间
                        dividend_type="none",
                    )

                    if market_data and target_contract_for_data in market_data:
                        df = market_data[target_contract_for_data]
                        if not df.empty:
                            df_to_display = df.copy()
                            if "time" in df_to_display.columns:
                                try:
                                    # 转换时间戳 (假设是毫秒) 为 UTC Datetime 对象
                                    utc_times = smart_to_datetime(
                                        df_to_display["time"],
                                        unit="ms",
                                        errors="coerce",
                                    )

                                    # 检查是否有转换失败的值 (NaT)
                                    if utc_times.isna().any():
                                        logger.warning(
                                            f"合约 {contract_code}: "
                                            f"存在无法解析的时间戳，将被忽略。"
                                        )
                                        # 移除无效行
                                        df_to_display = df_to_display.loc[
                                            utc_times.notna()
                                        ].copy()
                                        utc_times = utc_times.dropna()

                                    if not utc_times.empty:
                                        # 直接使用原始时间戳
                                        df_to_display["time"] = df["time"]
                                except Exception as time_fmt_e:
                                    logger.error(
                                        f"合约 {contract_code}: 时间格式化失败: {time_fmt_e}",
                                        exc_info=True,
                                    )

                            f.write(
                                f"获取到 {len(df_to_display)} 条数据:\n"
                                f"{df_to_display.to_string()}\n\n"
                            )
                        else:
                            logger.info(f"合约 {target_contract_for_data} 的数据为空。")
                            f.write("数据为空\n\n")
                    else:
                        logger.warning(
                            f"未能获取到合约 {target_contract_for_data} 的市场数据。 "
                        )
                        f.write("未获取到市场数据\n\n")
                except Exception as e:
                    log_msg_process_err = (
                        f"处理合约 {target_contract_for_data} 时发生错误: {e}"
                    )
                    logger.error(log_msg_process_err, exc_info=False)
                    f.write(f"处理出错: {str(e)[:100]}\n\n")
            
        logger.info(f"市场数据已成功保存到: {output_file_path}")  # 调整日志信息
    except Exception as e:
        logger.error(f"写入市场数据文件时出错: {e}", exc_info=True)  # 调整日志信息


def get_main_contracts_via_sector():
    """
    通过查询 "主力板块" 直接获取当前所有主力期货合约列表。
    """
    logger.info("\n===== 获取 '主力板块' 的期货合约列表 =====")
    sector_name = "主力板块"
    output_file_path = os.path.join(plate_data_dir, f"{sector_name}合约列表.txt")
    try:
        with open(output_file_path, "w", encoding="utf-8") as f:
            start_time = time.time()
            stock_list = xt_data.get_stock_list_in_sector(sector_name)
            duration = time.time() - start_time
            if stock_list:
                log_msg_sector_success = (
                    f"成功获取板块 '{sector_name}' 的 {len(stock_list)} 个标的 "
                    f"(耗时: {duration:.2f}秒)"
                )
                logger.info(log_msg_sector_success)
                logger.info(f"前 10 个示例: {stock_list[:10]}")
                # 写入文件
                for item in stock_list:
                    f.write(f"{item}\n")
                logger.info(f"'{sector_name}'合约列表已成功保存到: {output_file_path}")
            else:
                warn_msg_sector_fail = (
                    f"未能获取板块 '{sector_name}' 的标的列表，返回为空。"
                    "可能板块名称无效或无数据。"
                )
                logger.warning(warn_msg_sector_fail)
                f.write(f"未能获取板块 '{sector_name}' 的标的列表，返回为空。\n")
    except Exception as e:
        logger.error(f"获取 '{sector_name}' 合约列表时出错: {e}", exc_info=True)
        with open(output_file_path, "w", encoding="utf-8") as f_err:
            f_err.write(f"获取 '{sector_name}' 合约列表时出错: {str(e)}\n")


def get_contract_basic_info(contract_codes_input: str, silent_mode=False):
    """获取合约基础信息

    Args:
        contract_codes_input: 合约代码，多个代码用逗号或空格分隔
        silent_mode: 是否静默模式，默认为False表示打印详细信息
    """
    logger.info("\n===== 获取合约基础信息 =====")
    
    # 使用通用parse_instrument_input来解析合约代码输入
    contract_codes = parse_instrument_input(contract_codes_input)
    
    if not contract_codes:
        logger.warning("输入为空，无法查询合约信息。")
        return
    
    logger.info(f"需查询的合约数量: {len(contract_codes)}")
    logger.info(f"合约列表: {contract_codes}")
    
    try:
        # 收集数据
        data = []
        for code in contract_codes:
            try:
                detail = xt_data.get_instrument_detail(code)
                if detail and isinstance(detail, dict):
                    # 构建基础信息字典
                    info = {
                        "代码": code,
                        "名称": detail.get("InstrumentName", "未知"),
                        "交易所": detail.get("ExchangeID", "--"),
                        "合约乘数": detail.get("VolumeMultiple", "--"),
                        "价格单位": detail.get("PriceTick", "--"),
                        "上市日期": detail.get("OpenDate", "--"),
                        "到期日期": detail.get("ExpireDate", "--"),
                        "产品类型": detail.get("ProductClass", "--"),
                        "状态": detail.get("InstrumentStatus", "--"),
                    }
                    data.append(info)
                else:
                    logger.warning(f"未能获取到 {code} 的有效基础信息。")
            except Exception as e:
                logger.error(f"获取 {code} 的基础信息时出错: {e}")
        
        if data:
            logger.info(f"成功获取到 {len(data)} 个合约的基础信息:")
            
            # 使用 pandas DataFrame 格式化输出 (优化表格显示)
            df = pd.DataFrame(data)
            
            # 重新排列和过滤列顺序
            columns = [
                "代码", "名称", "交易所", "产品类型", 
                "价格单位", "合约乘数", "上市日期", "到期日期", "状态"
            ]
            df = df[columns]
            
            if not silent_mode:
                # 打印合约信息表格
                print_cmd_section("合约基础信息")
                # 计算每列所需的最大显示宽度
                col_widths = {}
                for col in df.columns:
                    # 计算列标题的显示宽度
                    header_width = get_display_width(col)
                    
                    # 计算该列所有数据的最大显示宽度
                    data_values = df[col].apply(lambda x: str(x) if pd.notna(x) else "--")
                    data_width = data_values.map(get_display_width).max()
                    if pd.isna(data_width):  # 处理全为NaN的列的情况
                        data_width = get_display_width("--")
                    max_width = max(header_width, data_width)
                    
                    col_widths[col] = int(max_width) + 3  # 增加列宽，确保列之间有足够间隔
                
                # 打印表头
                header_line = ""
                for col_name in columns:
                    header_line += format_column(col_name, col_widths[col_name])
                print(header_line)
                
                # 打印分隔线 (更清晰的表格结构)
                separator_display_width = sum(col_widths[c] for c in columns)
                print("-" * separator_display_width)
                
                # 打印数据行
                for _, row in df.iterrows():
                    line = ""
                    for col_name in columns:
                        val_raw = row[col_name]
                        val = str(val_raw) if pd.notna(val_raw) else "--"
                        line += format_column(val, col_widths[col_name])
                    print(line)
        else:
            logger.warning("未能获取任何合约的基础信息。")
    except Exception as e:
        logger.error(f"获取合约基础信息时发生错误: {e}", exc_info=True)


def get_quote_data(contract_codes_input: str, silent_mode=False):
    """获取合约的行情数据
    
    Args:
        contract_codes_input: 合约代码输入，支持多个代码用逗号分隔
        silent_mode: 是否静默模式，默认为False表示打印详细信息
    """
    logger.info("\n===== 获取合约行情数据 =====")
    
    # 使用通用parse_instrument_input来解析合约代码输入
    contract_codes = parse_instrument_input(contract_codes_input)
    
    if not contract_codes:
        logger.warning("输入为空，无法查询行情数据。")
        return
    
    logger.info(f"需查询的合约数量: {len(contract_codes)}")
    logger.info(f"合约列表: {contract_codes}")
    
    try:
        # 同时查询多个合约的快照行情
        quotes = xt_data.get_full_tick(contract_codes)
        
        if quotes:
            logger.info(f"成功获取 {len(quotes)} 个合约的行情数据:")
            
            # 处理和显示行情数据
            data = []
            for code, quote in quotes.items():
                # 提取关键行情信息
                info = {
                    "代码": code,
                    "时间": quote.get("Time", "--"),
                    "最新价": quote.get("LastPrice", "--"),
                    "买价": quote.get("BidPrice1", "--"),
                    "买量": quote.get("BidVolume1", "--"),
                    "卖价": quote.get("AskPrice1", "--"),
                    "卖量": quote.get("AskVolume1", "--"),
                    "成交量": quote.get("Volume", "--"),
                    "成交额": quote.get("Turnover", "--"),
                    "涨跌幅": quote.get("LastPriceRiseFallRate", "--")
                }
                data.append(info)
            
            # 使用 pandas DataFrame 格式化输出
            df = pd.DataFrame(data)
            
            # 重新排列列顺序
            columns = [
                "代码", "时间", "最新价", "涨跌幅", 
                "买价", "买量", "卖价", "卖量", "成交量", "成交额"
            ]
            df = df[columns]
            
            if not silent_mode:
                # 计算每列所需的最大显示宽度
                print_cmd_section("行情数据")
                col_widths = {}
                for col in df.columns:
                    # 计算列标题的显示宽度
                    header_width = get_display_width(col)
                    
                    # 计算该列所有数据的最大显示宽度
                    data_values = df[col].apply(lambda x: str(x) if pd.notna(x) else "--")
                    data_width = data_values.map(get_display_width).max()
                    if pd.isna(data_width):  # 处理全为NaN的列
                        data_width = get_display_width("--")
                    max_width = max(header_width, data_width)
                    
                    col_widths[col] = int(max_width) + 3  # 确保列间距
                
                # 打印表头
                header_line = ""
                for col_name in columns:
                    header_line += format_column(col_name, col_widths[col_name])
                print(header_line)
                
                # 打印分隔线
                separator_display_width = sum(col_widths[c] for c in columns)
                print("-" * separator_display_width)
                
                # 打印数据行
                for _, row in df.iterrows():
                    line = ""
                    for col_name in columns:
                        val_raw = row[col_name]
                        val = str(val_raw) if pd.notna(val_raw) else "--"
                        line += format_column(val, col_widths[col_name])
                    print(line)
        else:
            logger.warning("未能获取任何合约的行情数据，返回为空。")
    except Exception as e:
        logger.error(f"获取行情数据时发生错误: {e}", exc_info=True)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="xtquant板块成分股和交易所合约数据获取工具")
    
    # 版本信息
    parser.add_argument('--version', '-v', action='version', version='%(prog)s 1.0.0')
    
    # 全局选项
    parser.add_argument('--silent', '-q', action='store_true', help='静默模式，减少输出信息')
    parser.add_argument('--output', '-o', help='指定输出文件路径，默认为当前目录下的板块名称.txt')
    
    # 创建子命令分组
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 1. 获取所有板块列表
    all_sectors = subparsers.add_parser('all_sectors', help='获取所有板块列表')
    
    # 2. 只获取主流板块列表
    mainstream_sectors = subparsers.add_parser('mainstream_sectors', help='只获取主流板块列表')
    
    # 3. 获取指定板块的成分列表
    sector_stocks = subparsers.add_parser('sector_stocks', help='获取指定板块的成分列表')
    sector_stocks.add_argument('--sector', '-s', required=True, help='板块名称，可以提供多个，用逗号分隔', type=str)
    
    # 4. 获取合约基础信息
    contract_info = subparsers.add_parser('contract_info', help='获取合约基础信息')
    contract_info.add_argument('--codes', '-c', required=True, help='合约代码，可以提供多个，用逗号分隔', type=str)
    
    # 5. 获取指定合约列表的行情数据
    quote_data = subparsers.add_parser('quote_data', help='获取指定合约列表的行情数据')
    quote_data.add_argument('--codes', '-c', required=True, help='合约代码，可以提供多个，用逗号分隔', type=str)
    
    # 6. 获取市场历史数据
    market_data = subparsers.add_parser('market_data', help='获取市场历史数据')
    market_data.add_argument('--codes', '-c', required=True, help='合约代码，可以提供多个，用逗号分隔', type=str)
    market_data.add_argument('--start', required=True, help='开始日期，格式YYYYMMDD', type=str)
    market_data.add_argument('--end', required=True, help='结束日期，格式YYYYMMDD', type=str)
    market_data.add_argument('--period', default='1d', help='周期，如1d, 1m, 5m等，默认1d', type=str)
    
    # 解析参数
    args = parser.parse_args()
    
    # 如果没有提供任何命令，显示帮助信息
    if not args.command:
        parser.print_help()
        sys.exit(1)
        
    return args


def command_line_main():
    """命令行主函数，替代交互式主函数"""
    args = parse_args()
    
    try:
        # 根据是否静默模式显示程序头
        if not getattr(args, 'silent', False):
            print_cmd_header("xtquant板块成分股和交易所合约数据获取工具")
            
        if args.command == 'all_sectors':
            # 获取全部板块列表
            fetch_and_save_sector_list(only_mainstream=False)
            logger.info("已完成获取全部板块列表")
            if not getattr(args, 'silent', False):
                print_cmd_success("已完成获取全部板块列表")
            
        elif args.command == 'mainstream_sectors':
            # 获取主流板块列表
            fetch_and_save_sector_list(only_mainstream=True)
            logger.info("已完成获取主流板块列表")
            if not getattr(args, 'silent', False):
                print_cmd_success("已完成获取主流板块列表")
            
        elif args.command == 'sector_stocks':
            # 获取指定板块的成分列表
            sectors_to_fetch = parse_sector_input(args.sector)
            
            if not sectors_to_fetch:
                error_msg = f"未能解析出有效的板块名称: {args.sector}"
                logger.error(error_msg)
                if not getattr(args, 'silent', False):
                    print_cmd_error(error_msg)
                sys.exit(1)
            
            if not getattr(args, 'silent', False):
                print_cmd_info(f"将获取以下 {len(sectors_to_fetch)} 个板块的成分列表: {sectors_to_fetch}")
            
            # 逐个获取板块成分列表
            for sector_name in sectors_to_fetch:
                get_stock_list_in_sector(sector_name)
                time.sleep(0.1)  # 避免频繁请求
            
            success_msg = f"已完成获取 {len(sectors_to_fetch)} 个板块的成分列表"
            logger.info(success_msg)
            if not getattr(args, 'silent', False):
                print_cmd_success(success_msg)
            
        elif args.command == 'contract_info':
            # 获取合约基础信息
            contract_codes = parse_instrument_input(args.codes)
            
            if not contract_codes:
                error_msg = f"未能解析出有效的合约代码: {args.codes}"
                logger.error(error_msg)
                if not getattr(args, 'silent', False):
                    print_cmd_error(error_msg)
                sys.exit(1)
            
            get_contract_basic_info("\n".join(contract_codes), silent_mode=getattr(args, 'silent', False))
            
            success_msg = f"已完成获取 {len(contract_codes)} 个合约的基础信息"
            logger.info(success_msg)
            if not getattr(args, 'silent', False):
                print_cmd_success(success_msg)
            
        elif args.command == 'quote_data':
            # 获取指定合约列表的行情数据
            contract_codes = parse_instrument_input(args.codes)
            
            if not contract_codes:
                error_msg = f"未能解析出有效的合约代码: {args.codes}"
                logger.error(error_msg)
                if not getattr(args, 'silent', False):
                    print_cmd_error(error_msg)
                sys.exit(1)
            
            get_quote_data("\n".join(contract_codes), silent_mode=getattr(args, 'silent', False))
            
            success_msg = f"已完成获取 {len(contract_codes)} 个合约的行情数据"
            logger.info(success_msg)
            if not getattr(args, 'silent', False):
                print_cmd_success(success_msg)
            
        elif args.command == 'market_data':
            # 获取市场历史数据
            contract_codes = parse_instrument_input(args.codes)
            
            if not contract_codes:
                error_msg = f"未能解析出有效的合约代码: {args.codes}"
                logger.error(error_msg)
                if not getattr(args, 'silent', False):
                    print_cmd_error(error_msg)
                sys.exit(1)
            
            # 验证日期格式
            try:
                datetime.strptime(args.start, "%Y%m%d")
                datetime.strptime(args.end, "%Y%m%d")
            except ValueError:
                error_msg = "日期格式错误，请使用YYYYMMDD格式"
                logger.error(error_msg)
                if not getattr(args, 'silent', False):
                    print_cmd_error(error_msg)
                sys.exit(1)
                
            fetch_market_data_for_contract_list(
                contract_list=contract_codes,
                start_time_str=args.start,
                end_time_str=args.end,
                period_str=args.period
            )
            
            success_msg = f"已完成获取 {len(contract_codes)} 个合约的市场历史数据"
            logger.info(success_msg)
            if not getattr(args, 'silent', False):
                print_cmd_success(success_msg)
            
    except Exception as e:
        error_msg = f"执行命令时出错: {e}"
        logger.error(error_msg, exc_info=True)
        if not getattr(args, 'silent', False):
            print_cmd_error(error_msg)
        sys.exit(1)


if __name__ == "__main__":
    try:
        command_line_main()
        logger.info("\n===== 脚本执行完毕 =====") 
    except KeyboardInterrupt:
        logger.info("\n程序被用户中断，退出...")
    except Exception as e:
        logger.error(f"程序执行过程中出现未处理的异常: {e}", exc_info=True) 
