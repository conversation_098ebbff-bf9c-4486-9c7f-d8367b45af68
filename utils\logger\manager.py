#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
日志管理模块

提供统一的日志管理功能，包括日志记录器的创建、配置和管理
"""

import os
import sys
import glob
import random
import string
import logging
from typing import List, Optional
from datetime import datetime, timedelta

# 将项目根目录添加到Python路径
# Get the directory of the current file (manager.py)
current_dir = os.path.dirname(os.path.abspath(__file__))  # utils/logger
# Get the parent directory (utils)
parent_dir = os.path.dirname(current_dir)  # utils
# Get the grandparent directory (project root)
root_path = os.path.dirname(parent_dir)  # quant
sys.path.insert(0, root_path)

# 导入配置
from utils.logger.config import (  # noqa: E402
    LOG_LEVELS,
    LogTarget,
    get_all_loggers,
    get_appropriate_encoding
)
from utils.logger.filters import TargetFilter, LevelFilter  # noqa: E402
from utils.logger.formatters import (  # noqa: E402
    EnhancedFormatter,
    TableMarkdownFormatter
)
from config.settings import (  # noqa: E402
    LOG_DIR, LOG_LEVEL, LOG_FORMAT, DATE_FORMAT,
    DEBUG_LOG_ENABLED, DEBUG_LOG_LEVEL, DEBUG_LOG_MAX_SIZE,
    DEBUG_LOG_BACKUP_COUNT, DEBUG_LOG_DAYS_TO_KEEP
)

# 全局变量
_initialized = False  # 是否已初始化
_task_id = None  # 当前任务ID
_unified_log_file = None  # 统一日志文件路径
_managed_loggers = set()  # 被管理的日志记录器集合


def setup_unified_logging(
    logger_names: Optional[List[str]] = None,
    log_level: str = "info",
    console_format: Optional[str] = None,
    file_format: Optional[str] = None,
    task_id: Optional[str] = None,
    log_dir: Optional[str] = None,
    clean_old_logs: bool = True,
    days_to_keep: int = 7,
    default_target: int = LogTarget.FILE,
    use_table_formatter: bool = False,    # 控制是否使用表格式Markdown格式化器
    use_colors: bool = True,              # 控制是否使用颜色
    use_icons: bool = True,               # 控制是否使用图标
    module_width: int = 30,  # 模块名显示的固定宽度
    print_init_message: bool = False,  # 控制是否打印初始化消息
    # 新增参数 - debug日志相关
    enable_debug_log: bool = True,  # 是否启用debug日志文件
    debug_log_level: str = "debug",  # debug日志文件的日志级别
    debug_log_max_size: int = 10 * 1024 * 1024,  # debug日志文件大小限制（10MB）
    debug_log_backup_count: int = 5,  # debug日志文件备份数量
    debug_log_days_to_keep: int = 30,  # debug日志文件保留天数
    debug_log_format: Optional[str] = None  # debug日志文件的格式
) -> bool:
    """
    设置统一的日志记录系统
    
    此函数配置一个统一的日志记录系统，所有日志将写入到同一个文件中，
    同时可以选择性地输出到控制台。
    
    新增功能：支持将所有级别的日志（包括DEBUG级别）写入到专门的debug日志文件中。
    
    Args:
        logger_names: 要配置的日志记录器名称列表，默认为None表示配置所有现有的日志记录器
        log_level: 日志级别，默认为"info"
        console_format: 控制台日志格式，默认为None表示使用DEFAULT_LOG_FORMAT
        file_format: 文件日志格式，默认为None表示使用DEFAULT_LOG_FORMAT
        task_id: 任务ID，默认为None表示自动生成
        log_dir: 日志目录路径，默认为None表示使用项目根目录下的logs目录
        clean_old_logs: 是否清理旧日志文件，默认为True
        days_to_keep: 保留日志文件的天数，默认为7天
        default_target: 默认日志输出目标，默认为LogTarget.FILE
        use_table_formatter: 是否使用表格式Markdown格式化器，默认为False
        use_colors: 是否使用颜色，默认为True
        use_icons: 是否使用图标，默认为True
        module_width: 模块名显示的固定宽度，默认为30
        print_init_message: 控制是否打印初始化消息，默认为False
        enable_debug_log: 是否启用debug日志文件，默认为True
        debug_log_level: debug日志文件的日志级别，默认为"debug"
        debug_log_max_size: debug日志文件大小限制，默认为10MB
        debug_log_backup_count: debug日志文件备份数量，默认为5
        debug_log_days_to_keep: debug日志文件保留天数，默认为30
        debug_log_format: debug日志文件的格式，默认为None（使用详细格式）
        
    Returns:
        bool: 配置是否成功
    """
    # 确保日志目录存在
    if log_dir is None:
        log_dir = os.path.join(root_path, "logs")
    
    if not os.path.exists(log_dir):
        os.makedirs(log_dir, exist_ok=True)
    
    # 清理旧日志文件
    if clean_old_logs:
        clean_old_log_files(log_dir, days_to_keep)
    
    # 生成或使用提供的任务ID
    global _task_id
    if task_id is None:
        _task_id = ''.join(random.choice(string.ascii_uppercase + string.digits)
                           for _ in range(6))
    else:
        _task_id = task_id

    # 创建统一的日志文件路径
    date_str = datetime.now().strftime("%Y%m%d")
    _unified_log_file = os.path.join(log_dir, f"quant_{date_str}.log")
    
    # 创建debug日志文件路径
    debug_log_file = os.path.join(log_dir, f"quant_debug_{date_str}.log")

    # 设置根日志记录器 - 负责控制台输出
    root_logger = logging.getLogger()
    # 清除根记录器所有现有处理器
    for handler in list(root_logger.handlers):
        root_logger.removeHandler(handler)

    # 设置根日志记录器级别为DEBUG，确保能捕获所有级别的日志
    root_logger.setLevel(logging.DEBUG)

    # 添加唯一的控制台处理器到根日志器
    console_handler = logging.StreamHandler(sys.stdout)
    
    # 选择格式化器
    if use_table_formatter:
        # 使用表格式Markdown格式化器
        console_formatter = TableMarkdownFormatter(
            task_id=_task_id,
            use_colors=use_colors,
            module_width=module_width
        )
    else:
        # 使用增强型格式化器
        console_formatter = EnhancedFormatter(
            task_id=_task_id,
            use_colors=use_colors,
            use_icons=use_icons
        )
    
    console_handler.setFormatter(console_formatter)
    
    # 设置控制台处理器的日志级别
    if isinstance(log_level, int):
        log_level_val = log_level
    else:
        log_level_val = LOG_LEVELS.get(log_level.lower(), logging.INFO)
    console_handler.setLevel(log_level_val)
    
    # 添加控制台目标过滤器
    console_filter = TargetFilter(LogTarget.CONSOLE, default_target)
    console_handler.addFilter(console_filter)
    
    # 添加级别过滤器，确保只有指定级别及以上的日志才会输出到控制台
    console_level_filter = LevelFilter(min_level=log_level_val)
    console_handler.addFilter(console_level_filter)
    
    root_logger.addHandler(console_handler)

    # 创建统一的文件处理器 - 记录INFO及以上级别的日志
    unified_file_handler = logging.FileHandler(
        _unified_log_file, encoding=get_appropriate_encoding()
    )
    
    # 选择文件格式化器
    if use_table_formatter:
        # 使用表格式Markdown格式化器
        unified_file_formatter = TableMarkdownFormatter(
            task_id=_task_id,
            use_colors=False,  # 文件中不使用颜色
            module_width=module_width
        )
    else:
        # 使用增强型格式化器
        unified_file_formatter = EnhancedFormatter(
            task_id=_task_id,
            use_colors=False,  # 文件中不使用颜色
            use_icons=False    # 文件中不使用图标，避免乱码
        )
    
    unified_file_handler.setFormatter(unified_file_formatter)
    
    # 设置文件处理器的日志级别
    unified_file_handler.setLevel(logging.DEBUG)  # 设置为DEBUG，让过滤器来控制级别
    
    # 添加文件目标过滤器，确保只有目标为FILE或BOTH的日志才会写入到文件中
    unified_file_filter = TargetFilter(LogTarget.FILE, default_target)
    unified_file_handler.addFilter(unified_file_filter)
    
    # 添加级别过滤器，确保只有INFO及以上级别的日志才会写入到普通日志文件中
    unified_level_filter = LevelFilter(min_level=log_level_val)
    unified_file_handler.addFilter(unified_level_filter)
    
    # 将统一的文件处理器添加到根日志记录器
    root_logger.addHandler(unified_file_handler)
    
    # 全局变量，用于存储debug日志文件路径
    global _debug_log_file
    _debug_log_file = None
    
    # 如果启用debug日志文件，添加debug日志处理器
    if enable_debug_log:
        # 创建debug日志处理器，使用轮转和压缩功能
        from utils.logger.handlers import RotatingFileHandlerWithCompression
        debug_handler = RotatingFileHandlerWithCompression(
            debug_log_file,
            maxBytes=debug_log_max_size,
            backupCount=debug_log_backup_count,
            encoding=get_appropriate_encoding(),
            compress=True
        )
        
        # 使用详细的日志格式
        if debug_log_format:
            debug_fmt = debug_log_format
        else:
            from utils.logger.config import DETAILED_LOG_FORMAT
            debug_fmt = DETAILED_LOG_FORMAT
            
        # 选择格式化器
        if use_table_formatter:
            # 使用表格式Markdown格式化器
            debug_formatter = TableMarkdownFormatter(
                task_id=_task_id,
                use_colors=False,  # 文件中不使用颜色
                module_width=module_width
            )
        else:
            # 使用增强型格式化器
            debug_formatter = EnhancedFormatter(
                fmt=debug_fmt,
                task_id=_task_id,
                use_colors=False,  # 文件中不使用颜色
                use_icons=False    # 文件中不使用图标
            )
            
        debug_handler.setFormatter(debug_formatter)
        
        # 设置日志级别为DEBUG，确保能捕获所有级别的日志
        debug_handler.setLevel(logging.DEBUG)
        
        # 添加目标过滤器，确保只有目标为FILE或BOTH的日志才会写入到debug日志文件中
        debug_filter = TargetFilter(LogTarget.FILE, default_target)
        debug_handler.addFilter(debug_filter)
        
        # 添加到根日志记录器
        root_logger.addHandler(debug_handler)
        
        # 保存debug日志文件路径到全局变量，方便其他函数使用
        _debug_log_file = debug_log_file
        
        # 如果启用了清理旧日志功能，也清理旧的debug日志文件
        if clean_old_logs:
            debug_log_pattern = os.path.join(log_dir, "quant_debug_*.log*")
            # 使用不同的保留天数清理debug日志
            clean_old_log_files(
                log_dir, 
                debug_log_days_to_keep, 
                pattern=debug_log_pattern
            )

    # 获取所有已创建的日志记录器
    all_loggers = get_all_loggers()
    
    # 配置所有已创建的日志记录器
    for logger_name, logger in all_loggers.items():
        # 清除特定记录器所有现有处理器
        for handler in list(logger.handlers):
            logger.removeHandler(handler)

        # 设置日志级别为DEBUG，确保能捕获所有级别的日志
        logger.setLevel(logging.DEBUG)
        logger.propagate = True       # 确保传播到根记录器
        _managed_loggers.add(logger_name)  # 添加到管理的日志记录器集合

    # 创建一个专门的manager日志记录器，用于记录初始化消息
    manager_logger = logging.getLogger("manager")
    # 设置日志级别为DEBUG，确保能捕获所有级别的日志
    manager_logger.setLevel(logging.DEBUG)
    manager_logger.propagate = True  # 确保传播到根记录器
    _managed_loggers.add("manager")  # 添加到管理的日志记录器集合
    
    # 只有当print_init_message为True时，才打印初始化消息
    if print_init_message:
        # 记录一条消息表示配置完成
        init_message = (
            "\n【增强型日志系统初始化完成】" + 
            f"已配置 {len(all_loggers)} 个日志记录器，所有日志将统一写入: {_unified_log_file}"
        )
        if enable_debug_log:
            init_message += f"\n【Debug日志已启用】所有级别的日志将写入: {debug_log_file}"
        
        manager_logger.info(init_message, extra={"target": LogTarget.BOTH})
    
    # 标记为已初始化
    global _initialized
    _initialized = True
    
    return True


def get_unified_logger(
    name: str = None,  # 修改为可选参数
    level: str = "debug",  # 默认级别改为debug
    enhanced: bool = True,
    **kwargs
) -> logging.Logger:
    """
    获取统一配置的日志记录器
    
    此函数返回一个已经配置好的日志记录器，支持目标选择功能
    获取的日志记录器已经增强，可以使用target参数控制日志输出目标，
    以及使用前置内容参数添加自定义的前缀内容（如分隔线、换行符等）
    
    如果未提供name参数，函数会自动使用调用者的模块名作为记录器名称。
    
    Args:
        name: 日志记录器名称（可选，默认使用调用者的模块名）
        level: 日志级别，默认为debug
        enhanced: 是否使用增强功能，默认为True，强烈建议保持为True
        **kwargs: 传递给logger的其他参数
        
    Returns:
        配置好的日志记录器
        
    Examples:
        >>> # 使用当前模块名作为记录器名称（推荐）
        >>> logger = get_unified_logger()
        >>> # 或者显式指定记录器名称
        >>> logger = get_unified_logger("my_module")
        >>> logger.info(LogTarget.BOTH, "这条日志同时输出到文件和控制台")
        >>> logger.debug(LogTarget.FILE, "这条日志只输出到文件")
        >>> # 使用前置内容
        >>> logger.info(LogTarget.FILE, "=========", "这条日志前面会添加分隔线")
        >>> logger.info(LogTarget.FILE, "\\n\\n\\n", "这条日志前面会添加多个换行符")
        >>> logger.info(LogTarget.FILE, "=======数据下载=======", "这条日志前面会添加带标题的分隔线")
    """
    # 将日志级别转换为小写
    level = level.lower() if isinstance(level, str) else level
    
    # 如果未提供name参数，自动检测调用者的模块名
    if name is None:
        import inspect
        frame = inspect.currentframe().f_back
        module = inspect.getmodule(frame)
        name = module.__name__ if module else "__main__"
    
    # 检查日志系统是否已初始化
    global _initialized
    if not _initialized:
        setup_unified_logging(
            default_target=LogTarget.FILE,
            use_table_formatter=True,  # 使用表格式Markdown格式化器
            module_width=30,  # 设置模块名显示宽度为30
            print_init_message=False  # 不打印初始化消息
        )
    
    # 获取日志记录器
    logger = logging.getLogger(name)
    
    # 设置日志级别
    if isinstance(level, int):
        log_level_val = level
    else:
        log_level_val = LOG_LEVELS.get(level, logging.DEBUG)  # 默认为DEBUG
    logger.setLevel(log_level_val)
    
    # 将记录器添加到托管集合
    global _managed_loggers
    _managed_loggers.add(name)
    
    # 如果要使用增强功能
    if enhanced:
        # 保存原始方法引用
        original_debug = logger.debug
        original_info = logger.info
        original_warning = logger.warning
        original_error = logger.error
        original_critical = logger.critical
        
        # 为日志方法添加支持目标选择的装饰器
        def enhanced_debug(target_or_msg=None, *args, **kwargs):
            """增强的debug方法，支持目标选择和前置内容"""
            # 解析参数
            target = None
            prefix = None
            msg = target_or_msg
            
            # 检查是否使用新的参数格式（关键字参数）
            title_target = kwargs.pop('title_target', None)
            title = kwargs.pop('title', None)
            content_target = kwargs.pop('content_target', None)
            content = kwargs.pop('content', None)
            
            # 如果使用了新的参数格式
            new_format = (title_target is not None or content_target is not None or 
                          title is not None or content is not None)
            if new_format:
                # 添加title_target和content_target属性
                extra = kwargs.get('extra', {})
                if title_target is not None:
                    extra['title_target'] = title_target
                if content_target is not None:
                    extra['content_target'] = content_target
                
                # 添加title和content属性
                if title is not None:
                    extra['title'] = title
                if content is not None:
                    extra['content'] = content
                
                # 设置消息内容（兼容现有格式）
                if content is not None:
                    msg = content
                elif title is not None:
                    msg = title
                else:
                    msg = ""  # 设置默认空消息，避免None值错误
                
                kwargs['extra'] = extra
                return original_debug(msg, *args, **kwargs)
            
            # 如果target_or_msg为None，表示未提供位置参数，使用默认目标
            if target_or_msg is None:
                extra = kwargs.get('extra', {})
                extra['target'] = None  # 使用默认目标
                kwargs['extra'] = extra
                if len(args) > 0:
                    msg = args[0]
                    args = args[1:]
                else:
                    msg = ""  # 设置默认空消息，避免None值错误
                return original_debug(msg, *args, **kwargs)
            
            # 解析旧格式的参数
            if len(args) >= 2 and isinstance(target_or_msg, (int, str)) and (
                target_or_msg in [1, 2, 3, 'file', 'console', 'both'] or 
                target_or_msg in [LogTarget.FILE, LogTarget.CONSOLE, LogTarget.BOTH]
            ):
                # 格式：logger.debug(LogTarget.FILE, prefix, "message")
                target = target_or_msg
                prefix = args[0]
                msg = args[1]
                args = args[2:]
            elif len(args) >= 1 and isinstance(target_or_msg, (int, str)) and (
                target_or_msg in [1, 2, 3, 'file', 'console', 'both'] or 
                target_or_msg in [LogTarget.FILE, LogTarget.CONSOLE, LogTarget.BOTH]
            ):
                # 格式：logger.debug(LogTarget.FILE, "message")
                target = target_or_msg
                msg = args[0]
                args = args[1:]
            
            # 添加target和prefix属性
            extra = kwargs.get('extra', {})
            extra['target'] = target
            if prefix is not None:
                extra['prefix'] = prefix
            kwargs['extra'] = extra
            
            return original_debug(msg, *args, **kwargs)
        
        def enhanced_info(target_or_msg=None, *args, **kwargs):
            """增强的info方法，支持目标选择和前置内容"""
            # 解析参数
            target = None
            prefix = None
            msg = target_or_msg
            
            # 检查是否使用新的参数格式（关键字参数）
            title_target = kwargs.pop('title_target', None)
            title = kwargs.pop('title', None)
            content_target = kwargs.pop('content_target', None)
            content = kwargs.pop('content', None)
            
            # 如果使用了新的参数格式
            new_format = (title_target is not None or content_target is not None or 
                          title is not None or content is not None)
            if new_format:
                # 添加title_target和content_target属性
                extra = kwargs.get('extra', {})
                if title_target is not None:
                    extra['title_target'] = title_target
                if content_target is not None:
                    extra['content_target'] = content_target
                
                # 添加title和content属性
                if title is not None:
                    extra['title'] = title
                if content is not None:
                    extra['content'] = content
                
                # 设置消息内容（兼容现有格式）
                if content is not None:
                    msg = content
                elif title is not None:
                    msg = title
                else:
                    msg = ""  # 设置默认空消息，避免None值错误
                
                kwargs['extra'] = extra
                return original_info(msg, *args, **kwargs)
            
            # 如果target_or_msg为None，表示未提供位置参数，使用默认目标
            if target_or_msg is None:
                extra = kwargs.get('extra', {})
                extra['target'] = None  # 使用默认目标
                kwargs['extra'] = extra
                if len(args) > 0:
                    msg = args[0]
                    args = args[1:]
                else:
                    msg = ""  # 设置默认空消息，避免None值错误
                return original_info(msg, *args, **kwargs)
            
            # 解析旧格式的参数
            if len(args) >= 2 and isinstance(target_or_msg, (int, str)) and (
                target_or_msg in [1, 2, 3, 'file', 'console', 'both'] or 
                target_or_msg in [LogTarget.FILE, LogTarget.CONSOLE, LogTarget.BOTH]
            ):
                # 格式：logger.info(LogTarget.FILE, prefix, "message")
                target = target_or_msg
                prefix = args[0]
                msg = args[1]
                args = args[2:]
            elif len(args) >= 1 and isinstance(target_or_msg, (int, str)) and (
                target_or_msg in [1, 2, 3, 'file', 'console', 'both'] or 
                target_or_msg in [LogTarget.FILE, LogTarget.CONSOLE, LogTarget.BOTH]
            ):
                # 格式：logger.info(LogTarget.FILE, "message")
                target = target_or_msg
                msg = args[0]
                args = args[1:]
            
            # 添加target和prefix属性
            extra = kwargs.get('extra', {})
            extra['target'] = target
            if prefix is not None:
                extra['prefix'] = prefix
            kwargs['extra'] = extra
            
            return original_info(msg, *args, **kwargs)
        
        def enhanced_warning(target_or_msg=None, *args, **kwargs):
            """增强的warning方法，支持目标选择和前置内容"""
            # 解析参数
            target = None
            prefix = None
            msg = target_or_msg
            
            # 检查是否使用新的参数格式（关键字参数）
            title_target = kwargs.pop('title_target', None)
            title = kwargs.pop('title', None)
            content_target = kwargs.pop('content_target', None)
            content = kwargs.pop('content', None)
            
            # 如果使用了新的参数格式
            new_format = (title_target is not None or content_target is not None or 
                          title is not None or content is not None)
            if new_format:
                # 添加title_target和content_target属性
                extra = kwargs.get('extra', {})
                if title_target is not None:
                    extra['title_target'] = title_target
                if content_target is not None:
                    extra['content_target'] = content_target
                
                # 添加title和content属性
                if title is not None:
                    extra['title'] = title
                if content is not None:
                    extra['content'] = content
                
                # 设置消息内容（兼容现有格式）
                if content is not None:
                    msg = content
                elif title is not None:
                    msg = title
                else:
                    msg = ""  # 设置默认空消息，避免None值错误
                
                kwargs['extra'] = extra
                return original_warning(msg, *args, **kwargs)
            
            # 如果target_or_msg为None，表示未提供位置参数，使用默认目标
            if target_or_msg is None:
                extra = kwargs.get('extra', {})
                extra['target'] = None  # 使用默认目标
                kwargs['extra'] = extra
                if len(args) > 0:
                    msg = args[0]
                    args = args[1:]
                else:
                    msg = ""  # 设置默认空消息，避免None值错误
                return original_warning(msg, *args, **kwargs)
            
            # 解析旧格式的参数
            if len(args) >= 2 and isinstance(target_or_msg, (int, str)) and (
                target_or_msg in [1, 2, 3, 'file', 'console', 'both'] or 
                target_or_msg in [LogTarget.FILE, LogTarget.CONSOLE, LogTarget.BOTH]
            ):
                # 格式：logger.warning(LogTarget.FILE, prefix, "message")
                target = target_or_msg
                prefix = args[0]
                msg = args[1]
                args = args[2:]
            elif len(args) >= 1 and isinstance(target_or_msg, (int, str)) and (
                target_or_msg in [1, 2, 3, 'file', 'console', 'both'] or 
                target_or_msg in [LogTarget.FILE, LogTarget.CONSOLE, LogTarget.BOTH]
            ):
                # 格式：logger.warning(LogTarget.FILE, "message")
                target = target_or_msg
                msg = args[0]
                args = args[1:]
            
            # 添加target和prefix属性
            extra = kwargs.get('extra', {})
            extra['target'] = target
            if prefix is not None:
                extra['prefix'] = prefix
            kwargs['extra'] = extra
            
            return original_warning(msg, *args, **kwargs)
        
        def enhanced_error(target_or_msg=None, *args, **kwargs):
            """增强的error方法，支持目标选择和前置内容"""
            # 解析参数
            target = None
            prefix = None
            msg = target_or_msg
            
            # 检查是否使用新的参数格式（关键字参数）
            title_target = kwargs.pop('title_target', None)
            title = kwargs.pop('title', None)
            content_target = kwargs.pop('content_target', None)
            content = kwargs.pop('content', None)
            
            # 如果使用了新的参数格式
            new_format = (title_target is not None or content_target is not None or 
                          title is not None or content is not None)
            if new_format:
                # 添加title_target和content_target属性
                extra = kwargs.get('extra', {})
                if title_target is not None:
                    extra['title_target'] = title_target
                if content_target is not None:
                    extra['content_target'] = content_target
                
                # 添加title和content属性
                if title is not None:
                    extra['title'] = title
                if content is not None:
                    extra['content'] = content
                
                # 设置消息内容（兼容现有格式）
                if content is not None:
                    msg = content
                elif title is not None:
                    msg = title
                else:
                    msg = ""  # 设置默认空消息，避免None值错误
                
                kwargs['extra'] = extra
                return original_error(msg, *args, **kwargs)
            
            # 如果target_or_msg为None，表示未提供位置参数，使用默认目标
            if target_or_msg is None:
                extra = kwargs.get('extra', {})
                extra['target'] = None  # 使用默认目标
                kwargs['extra'] = extra
                if len(args) > 0:
                    msg = args[0]
                    args = args[1:]
                else:
                    msg = ""  # 设置默认空消息，避免None值错误
                return original_error(msg, *args, **kwargs)
            
            # 解析旧格式的参数
            if len(args) >= 2 and isinstance(target_or_msg, (int, str)) and (
                target_or_msg in [1, 2, 3, 'file', 'console', 'both'] or 
                target_or_msg in [LogTarget.FILE, LogTarget.CONSOLE, LogTarget.BOTH]
            ):
                # 格式：logger.error(LogTarget.FILE, prefix, "message")
                target = target_or_msg
                prefix = args[0]
                msg = args[1]
                args = args[2:]
            elif len(args) >= 1 and isinstance(target_or_msg, (int, str)) and (
                target_or_msg in [1, 2, 3, 'file', 'console', 'both'] or 
                target_or_msg in [LogTarget.FILE, LogTarget.CONSOLE, LogTarget.BOTH]
            ):
                # 格式：logger.error(LogTarget.FILE, "message")
                target = target_or_msg
                msg = args[0]
                args = args[1:]
            
            # 添加target和prefix属性
            extra = kwargs.get('extra', {})
            extra['target'] = target
            if prefix is not None:
                extra['prefix'] = prefix
            kwargs['extra'] = extra
            
            return original_error(msg, *args, **kwargs)
        
        def enhanced_critical(target_or_msg=None, *args, **kwargs):
            """增强的critical方法，支持目标选择和前置内容"""
            # 解析参数
            target = None
            prefix = None
            msg = target_or_msg
            
            # 检查是否使用新的参数格式（关键字参数）
            title_target = kwargs.pop('title_target', None)
            title = kwargs.pop('title', None)
            content_target = kwargs.pop('content_target', None)
            content = kwargs.pop('content', None)
            
            # 如果使用了新的参数格式
            new_format = (title_target is not None or content_target is not None or 
                          title is not None or content is not None)
            if new_format:
                # 添加title_target和content_target属性
                extra = kwargs.get('extra', {})
                if title_target is not None:
                    extra['title_target'] = title_target
                if content_target is not None:
                    extra['content_target'] = content_target
                
                # 添加title和content属性
                if title is not None:
                    extra['title'] = title
                if content is not None:
                    extra['content'] = content
                
                # 设置消息内容（兼容现有格式）
                if content is not None:
                    msg = content
                elif title is not None:
                    msg = title
                else:
                    msg = ""  # 设置默认空消息，避免None值错误
                
                kwargs['extra'] = extra
                return original_critical(msg, *args, **kwargs)
            
            # 如果target_or_msg为None，表示未提供位置参数，使用默认目标
            if target_or_msg is None:
                extra = kwargs.get('extra', {})
                extra['target'] = None  # 使用默认目标
                kwargs['extra'] = extra
                if len(args) > 0:
                    msg = args[0]
                    args = args[1:]
                else:
                    msg = ""  # 设置默认空消息，避免None值错误
                return original_critical(msg, *args, **kwargs)
            
            # 解析旧格式的参数
            if len(args) >= 2 and isinstance(target_or_msg, (int, str)) and (
                target_or_msg in [1, 2, 3, 'file', 'console', 'both'] or 
                target_or_msg in [LogTarget.FILE, LogTarget.CONSOLE, LogTarget.BOTH]
            ):
                # 格式：logger.critical(LogTarget.FILE, prefix, "message")
                target = target_or_msg
                prefix = args[0]
                msg = args[1]
                args = args[2:]
            elif len(args) >= 1 and isinstance(target_or_msg, (int, str)) and (
                target_or_msg in [1, 2, 3, 'file', 'console', 'both'] or 
                target_or_msg in [LogTarget.FILE, LogTarget.CONSOLE, LogTarget.BOTH]
            ):
                # 格式：logger.critical(LogTarget.FILE, "message")
                target = target_or_msg
                msg = args[0]
                args = args[1:]
            
            # 添加target和prefix属性
            extra = kwargs.get('extra', {})
            extra['target'] = target
            if prefix is not None:
                extra['prefix'] = prefix
            kwargs['extra'] = extra
            
            return original_critical(msg, *args, **kwargs)
        
        # 替换原始的日志方法
        logger.debug = enhanced_debug
        logger.info = enhanced_info
        logger.warning = enhanced_warning
        logger.error = enhanced_error
        logger.critical = enhanced_critical
        
    # 返回配置好的日志记录器
    return logger


def clean_old_log_files(
    log_dir: Optional[str] = None, 
    days_to_keep: int = 7,
    pattern: str = "*.log*"
) -> int:
    """
    清理指定天数之前的日志文件
    
    Args:
        log_dir: 日志目录，如果为None则使用默认目录
        days_to_keep: 保留的天数，默认为7天
        pattern: 文件匹配模式，默认为"*.log*"
        
    Returns:
        int: 删除的文件数量
    """
    if log_dir is None:
        log_dir = os.path.join(root_path, "logs")
    
    if not os.path.exists(log_dir):
        return 0
    
    # 计算截止日期
    cutoff_date = datetime.now() - timedelta(days=days_to_keep)
    
    # 获取所有日志文件
    log_files = glob.glob(os.path.join(log_dir, pattern))
    
    # 记录删除的文件数量
    deleted_count = 0
    
    # 遍历日志文件
    for log_file in log_files:
        try:
            # 获取文件修改时间
            file_mtime = datetime.fromtimestamp(os.path.getmtime(log_file))
            
            # 如果文件修改时间早于截止日期，则删除
            if file_mtime < cutoff_date:
                os.remove(log_file)
                deleted_count += 1
        except Exception:
            # 忽略删除失败的错误
            pass
    
    return deleted_count 