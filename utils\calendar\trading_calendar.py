"""
多层次交易日历模块
提供健壮的交易日历数据获取，支持四层数据源回退机制
"""

import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
import os
import json

# 尝试导入各种数据源
try:
    import xtquant.xtdata as xtdata
    XTQUANT_AVAILABLE = True
except ImportError:
    XTQUANT_AVAILABLE = False
    logging.warning("xtquant不可用，将使用备用数据源")

try:
    import pandas_market_calendars as mcal
    PANDAS_MARKET_CALENDARS_AVAILABLE = True
except ImportError:
    PANDAS_MARKET_CALENDARS_AVAILABLE = False

try:
    import akshare as ak
    AKSHARE_AVAILABLE = True
except ImportError:
    AKSHARE_AVAILABLE = False

logger = logging.getLogger(__name__)

class TradingCalendarManager:
    """交易日历管理器 - 多层次数据源架构"""
    
    def __init__(self):
        self.cache = {}  # 缓存交易日历数据
        self.offline_data_path = os.path.join(os.path.dirname(__file__), 'offline_holidays.json')
        
    def get_trading_calendar_robust(self, market: str, start_date: str, end_date: str) -> List[str]:
        """
        健壮的交易日历获取函数 - 四层数据源回退机制
        
        Args:
            market: 市场代码 (SH/SZ/SF/DF/IF/HK等)
            start_date: 开始日期 (YYYYMMDD)
            end_date: 结束日期 (YYYYMMDD)
            
        Returns:
            交易日列表 (YYYYMMDD格式)
        """
        cache_key = f"{market}_{start_date}_{end_date}"
        
        # 检查缓存
        if cache_key in self.cache:
            logger.debug(f"使用缓存的交易日历数据: {cache_key}")
            return self.cache[cache_key]
        
        # 第一层：尝试xtquant API
        try:
            trading_dates = self._get_trading_calendar_from_xtquant(market, start_date, end_date)
            if trading_dates:
                logger.info(f"使用xtquant交易日历数据: {market} {start_date}-{end_date}")
                self.cache[cache_key] = trading_dates
                return trading_dates
        except Exception as e:
            logger.warning(f"xtquant交易日历获取失败: {e}")
        
        # 第二层：尝试开源库
        try:
            trading_dates = self._get_trading_calendar_from_opensource(market, start_date, end_date)
            if trading_dates:
                logger.info(f"使用开源库交易日历数据: {market} {start_date}-{end_date}")
                self.cache[cache_key] = trading_dates
                return trading_dates
        except Exception as e:
            logger.warning(f"开源库交易日历获取失败: {e}")
        
        # 第三层：使用离线数据
        try:
            trading_dates = self._get_trading_calendar_from_offline(market, start_date, end_date)
            if trading_dates:
                logger.info(f"使用离线交易日历数据: {market} {start_date}-{end_date}")
                self.cache[cache_key] = trading_dates
                return trading_dates
        except Exception as e:
            logger.warning(f"离线交易日历获取失败: {e}")
        
        # 第四层：保守估算
        logger.warning(f"所有交易日历数据源失败，使用保守估算: {market} {start_date}-{end_date}")
        trading_dates = self._get_trading_calendar_conservative(start_date, end_date)
        self.cache[cache_key] = trading_dates
        return trading_dates
    
    def _get_trading_calendar_from_xtquant(self, market: str, start_date: str, end_date: str) -> Optional[List[str]]:
        """从xtquant获取交易日历"""
        if not XTQUANT_AVAILABLE:
            raise Exception("xtquant不可用")
        
        try:
            # 转换市场代码
            xt_market = self._convert_market_to_xtquant(market)
            
            # 获取交易日历
            trading_dates = xtdata.get_trading_dates(xt_market, start_date, end_date)
            
            if trading_dates:
                # 转换为YYYYMMDD格式
                formatted_dates = []
                for d in trading_dates:
                    try:
                        # 处理不同的时间戳格式
                        if isinstance(d, (int, float)):
                            # 如果是时间戳，转换为日期字符串
                            if d > 99999999:  # 毫秒时间戳
                                dt = datetime.fromtimestamp(d / 1000)
                            else:  # 可能是YYYYMMDD格式的整数
                                dt = datetime.strptime(str(int(d)), '%Y%m%d')
                            formatted_dates.append(dt.strftime('%Y%m%d'))
                        else:
                            # 字符串格式，取前8位
                            date_str = str(d)[:8]
                            # 验证格式
                            datetime.strptime(date_str, '%Y%m%d')
                            formatted_dates.append(date_str)
                    except Exception as format_error:
                        logger.warning(f"交易日期格式转换失败: {d} -> {format_error}")
                        continue

                return formatted_dates
            
            return None
            
        except Exception as e:
            logger.error(f"xtquant交易日历获取异常: {e}")
            raise
    
    def _get_trading_calendar_from_opensource(self, market: str, start_date: str, end_date: str) -> Optional[List[str]]:
        """从开源库获取交易日历"""
        if not PANDAS_MARKET_CALENDARS_AVAILABLE and not AKSHARE_AVAILABLE:
            raise Exception("开源库不可用")
        
        # 优先尝试akshare（对中国市场支持更好）
        if AKSHARE_AVAILABLE and market in ['SH', 'SZ', 'SF', 'DF', 'IF']:
            try:
                return self._get_trading_calendar_from_akshare(start_date, end_date)
            except Exception as e:
                logger.warning(f"akshare获取失败: {e}")
        
        # 尝试pandas_market_calendars
        if PANDAS_MARKET_CALENDARS_AVAILABLE:
            try:
                return self._get_trading_calendar_from_pandas_market(market, start_date, end_date)
            except Exception as e:
                logger.warning(f"pandas_market_calendars获取失败: {e}")
        
        raise Exception("所有开源库获取失败")
    
    def _get_trading_calendar_from_akshare(self, start_date: str, end_date: str) -> List[str]:
        """从akshare获取中国交易日历"""
        import akshare as ak
        
        # 获取交易日历
        start_year = start_date[:4]
        end_year = end_date[:4]
        
        trading_dates = []
        for year in range(int(start_year), int(end_year) + 1):
            try:
                year_calendar = ak.tool_trade_date_hist_sina(year=str(year))
                if year_calendar is not None and not year_calendar.empty:
                    year_dates = year_calendar['trade_date'].dt.strftime('%Y%m%d').tolist()
                    trading_dates.extend(year_dates)
            except Exception as e:
                logger.warning(f"akshare获取{year}年交易日历失败: {e}")
        
        # 过滤日期范围并确保格式正确
        filtered_dates = []
        for d in trading_dates:
            try:
                # 确保是YYYYMMDD格式
                if len(str(d)) == 8 and start_date <= str(d) <= end_date:
                    filtered_dates.append(str(d))
            except Exception as e:
                logger.warning(f"过滤交易日期失败: {d} -> {e}")
                continue

        return sorted(filtered_dates)
    
    def _get_trading_calendar_from_pandas_market(self, market: str, start_date: str, end_date: str) -> List[str]:
        """从pandas_market_calendars获取交易日历"""
        import pandas_market_calendars as mcal
        
        # 转换市场代码
        calendar_name = self._convert_market_to_pandas_market(market)
        
        # 获取交易日历
        calendar = mcal.get_calendar(calendar_name)
        
        start_dt = datetime.strptime(start_date, '%Y%m%d')
        end_dt = datetime.strptime(end_date, '%Y%m%d')
        
        trading_days = calendar.valid_days(start_date=start_dt, end_date=end_dt)

        # 确保返回YYYYMMDD格式的字符串列表
        formatted_days = []
        for d in trading_days:
            try:
                formatted_days.append(d.strftime('%Y%m%d'))
            except Exception as e:
                logger.warning(f"pandas_market_calendars日期格式化失败: {d} -> {e}")
                continue

        return formatted_days
    
    def _convert_market_to_xtquant(self, market: str) -> str:
        """转换市场代码为xtquant格式"""
        market_mapping = {
            'SH': 'SH',
            'SZ': 'SZ', 
            'SF': 'SF',  # 上期所
            'DF': 'DF',  # 大商所
            'IF': 'IF',  # 中金所
            'HK': 'HK'   # 港股
        }
        return market_mapping.get(market, 'SH')  # 默认上海
    
    def _convert_market_to_pandas_market(self, market: str) -> str:
        """转换市场代码为pandas_market_calendars格式"""
        market_mapping = {
            'SH': 'SSE',   # 上海证券交易所
            'SZ': 'SZSE',  # 深圳证券交易所
            'HK': 'HKEX',  # 香港交易所
            'SF': 'SSE',   # 期货使用上海
            'DF': 'SSE',   # 期货使用上海
            'IF': 'SSE'    # 期货使用上海
        }
        return market_mapping.get(market, 'SSE')  # 默认上海
    
    def get_market_from_symbol(self, symbol: str) -> str:
        """从股票代码自动识别市场"""
        if not symbol:
            return 'SH'
        
        symbol = symbol.upper()
        
        # A股
        if symbol.endswith('.SH'):
            return 'SH'
        elif symbol.endswith('.SZ'):
            return 'SZ'
        
        # 期货
        elif symbol.endswith('.SF'):
            return 'SF'
        elif symbol.endswith('.DF'):
            return 'DF'
        elif symbol.endswith('.IF'):
            return 'IF'
        
        # 港股
        elif symbol.endswith('.HK'):
            return 'HK'
        
        # 根据代码前缀判断
        elif symbol.startswith(('60', '68', '11', '12', '13')):
            return 'SH'  # 上海A股、科创板、债券
        elif symbol.startswith(('00', '30', '12', '13')):
            return 'SZ'  # 深圳A股、创业板、债券
        
        # 期货品种代码判断
        elif any(symbol.startswith(prefix) for prefix in ['CU', 'AL', 'ZN', 'PB', 'NI', 'SN', 'AU', 'AG']):
            return 'SF'  # 上期所
        elif any(symbol.startswith(prefix) for prefix in ['A', 'B', 'M', 'Y', 'C', 'CS', 'P', 'FB', 'BB', 'JD']):
            return 'DF'  # 大商所
        elif any(symbol.startswith(prefix) for prefix in ['IF', 'IC', 'IH', 'T', 'TF', 'TS']):
            return 'IF'  # 中金所
        
        # 默认返回上海
        return 'SH'

    def _get_trading_calendar_from_offline(self, market: str, start_date: str, end_date: str) -> Optional[List[str]]:
        """从离线数据获取交易日历"""
        try:
            # 加载离线节假日数据
            holidays = self._load_offline_holidays()

            # 生成日期范围，排除周末和节假日
            trading_dates = []
            current_date = datetime.strptime(start_date, "%Y%m%d")
            end_date_obj = datetime.strptime(end_date, "%Y%m%d")

            while current_date <= end_date_obj:
                date_str = current_date.strftime("%Y%m%d")

                # 排除周末
                if current_date.weekday() < 5:
                    # 排除节假日
                    if not self._is_holiday(date_str, holidays, market):
                        trading_dates.append(date_str)

                current_date += timedelta(days=1)

            return trading_dates

        except Exception as e:
            logger.error(f"离线交易日历计算异常: {e}")
            raise

    def _get_trading_calendar_conservative(self, start_date: str, end_date: str) -> List[str]:
        """保守估算交易日历（最后的回退方案）"""
        try:
            trading_dates = []
            current_date = datetime.strptime(start_date, "%Y%m%d")
            end_date_obj = datetime.strptime(end_date, "%Y%m%d")

            while current_date <= end_date_obj:
                # 只排除周末，不考虑节假日（保守策略）
                if current_date.weekday() < 5:
                    trading_dates.append(current_date.strftime("%Y%m%d"))

                current_date += timedelta(days=1)

            logger.warning(f"使用保守估算，可能包含节假日: {len(trading_dates)}个交易日")
            return trading_dates

        except Exception as e:
            logger.error(f"保守估算交易日历异常: {e}")
            # 如果连保守估算都失败，返回空列表
            return []

    def _load_offline_holidays(self) -> Dict[str, List[str]]:
        """加载离线节假日数据"""
        try:
            if os.path.exists(self.offline_data_path):
                with open(self.offline_data_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                logger.warning(f"离线节假日数据文件不存在: {self.offline_data_path}")
                return {}
        except Exception as e:
            logger.error(f"加载离线节假日数据失败: {e}")
            return {}

    def _is_holiday(self, date_str: str, holidays: Dict[str, List[str]], market: str) -> bool:
        """判断是否为节假日"""
        year = date_str[:4]

        # 获取对应市场的节假日数据
        market_holidays = holidays.get(market, {})
        year_holidays = market_holidays.get(year, [])

        return date_str in year_holidays

    def calculate_trading_day_overlap(self, end_date: str, overlap_trading_days: int, symbol: str) -> str:
        """
        基于交易日历计算重叠起始时间

        Args:
            end_date: 结束日期 (YYYYMMDD)
            overlap_trading_days: 重叠的交易日数
            symbol: 股票代码，用于识别市场

        Returns:
            重叠起始日期 (YYYYMMDD)
        """
        try:
            # 获取市场代码
            market = self.get_market_from_symbol(symbol)

            # 计算查询范围（向前扩展足够的天数）
            end_date_obj = datetime.strptime(end_date, "%Y%m%d")
            # 保守估计：重叠天数的3倍自然日应该足够包含所需的交易日
            search_days = max(overlap_trading_days * 3, 30)  # 至少30天
            start_search_date = end_date_obj - timedelta(days=search_days)
            start_search_str = start_search_date.strftime("%Y%m%d")

            # 获取交易日历
            trading_dates = self.get_trading_calendar_robust(market, start_search_str, end_date)

            if not trading_dates:
                raise Exception("无法获取交易日历数据")

            # 找到end_date在交易日历中的位置
            if end_date not in trading_dates:
                # 如果end_date不是交易日，找到最近的前一个交易日
                valid_dates = [d for d in trading_dates if d <= end_date]
                if not valid_dates:
                    raise Exception(f"在{end_date}之前找不到有效交易日")
                end_date = valid_dates[-1]
                logger.info(f"调整结束日期到最近的交易日: {end_date}")

            end_index = trading_dates.index(end_date)

            # 向前推overlap_trading_days个交易日
            start_index = max(0, end_index - overlap_trading_days)
            overlap_start_date = trading_dates[start_index]

            logger.info(f"交易日重叠计算: {symbol} {market} 从{overlap_start_date}到{end_date} (重叠{overlap_trading_days}个交易日)")

            return overlap_start_date

        except Exception as e:
            logger.error(f"交易日重叠计算失败: {e}")
            raise

    def calculate_overlap_with_fallback(self, end_date: str, overlap_trading_days: int, symbol: str) -> str:
        """
        带回退机制的重叠计算

        Args:
            end_date: 结束日期 (YYYYMMDD)
            overlap_trading_days: 重叠的交易日数
            symbol: 股票代码

        Returns:
            重叠起始日期 (YYYYMMDD)
        """
        try:
            # 尝试基于交易日历的精确计算
            return self.calculate_trading_day_overlap(end_date, overlap_trading_days, symbol)

        except Exception as e:
            logger.warning(f"精确交易日重叠计算失败: {e}")

            # 回退到保守的自然日计算（增加2倍安全系数）
            logger.info("使用保守的自然日重叠计算")
            safe_overlap_days = overlap_trading_days * 2  # 2倍安全系数

            end_date_obj = datetime.strptime(end_date, "%Y%m%d")
            start_date_obj = end_date_obj - timedelta(days=safe_overlap_days)

            result = start_date_obj.strftime("%Y%m%d")
            logger.info(f"保守重叠计算: {symbol} 从{result}到{end_date} (自然日{safe_overlap_days}天)")

            return result

# 全局实例
trading_calendar_manager = TradingCalendarManager()

def get_trading_calendar(market: str, start_date: str, end_date: str) -> List[str]:
    """获取交易日历的便捷函数"""
    return trading_calendar_manager.get_trading_calendar_robust(market, start_date, end_date)

def get_market_from_symbol(symbol: str) -> str:
    """从股票代码获取市场的便捷函数"""
    return trading_calendar_manager.get_market_from_symbol(symbol)

def calculate_trading_day_overlap(end_date: str, overlap_trading_days: int, symbol: str) -> str:
    """基于交易日历计算重叠起始时间的便捷函数"""
    return trading_calendar_manager.calculate_trading_day_overlap(end_date, overlap_trading_days, symbol)

def calculate_overlap_with_fallback(end_date: str, overlap_trading_days: int, symbol: str) -> str:
    """带回退机制的重叠计算便捷函数"""
    return trading_calendar_manager.calculate_overlap_with_fallback(end_date, overlap_trading_days, symbol)

def calculate_overlap_by_period(target_period: str) -> int:
    """
    根据目标周期动态计算重叠交易日数

    Args:
        target_period: 目标周期字符串，如'1m', '5m', '1h', '1d'等

    Returns:
        int: 推荐的重叠交易日数
    """
    from utils.data_processor.period_converter import parse_period_to_minutes

    try:
        target_minutes = parse_period_to_minutes(target_period)

        if target_minutes <= 0:
            logger.warning(f"无法解析目标周期{target_period}，使用默认重叠1个交易日")
            return 1

        # 动态重叠策略
        if target_minutes <= 60:  # 小时内周期（1m-60m）
            overlap_days = 2
            logger.debug(f"小时内周期{target_period}({target_minutes}分钟)，重叠{overlap_days}个交易日")
        elif target_minutes <= 1440:  # 日内周期（1h-24h）
            overlap_days = 3
            logger.debug(f"日内周期{target_period}({target_minutes}分钟)，重叠{overlap_days}个交易日")
        else:  # 日级以上周期（1d+）
            overlap_days = 5
            logger.debug(f"日级以上周期{target_period}({target_minutes}分钟)，重叠{overlap_days}个交易日")

        return overlap_days

    except Exception as e:
        logger.error(f"计算动态重叠交易日数失败: {e}")
        return 1  # 默认重叠1个交易日

def calculate_dynamic_overlap(end_date: str, target_period: str, symbol: str) -> str:
    """
    基于目标周期动态计算重叠起始时间

    Args:
        end_date: 结束日期 (YYYYMMDD)
        target_period: 目标周期字符串
        symbol: 股票代码

    Returns:
        str: 重叠起始日期 (YYYYMMDD)
    """
    # 根据目标周期计算重叠交易日数
    overlap_trading_days = calculate_overlap_by_period(target_period)

    # 使用计算出的重叠交易日数进行重叠计算
    return calculate_overlap_with_fallback(end_date, overlap_trading_days, symbol)

# 全局实例
trading_calendar_manager = TradingCalendarManager()

def get_trading_calendar(market: str, start_date: str, end_date: str) -> List[str]:
    """获取交易日历的便捷函数"""
    return trading_calendar_manager.get_trading_calendar_robust(market, start_date, end_date)

def get_market_from_symbol(symbol: str) -> str:
    """从股票代码获取市场的便捷函数"""
    return trading_calendar_manager.get_market_from_symbol(symbol)

def calculate_trading_day_overlap(end_date: str, overlap_trading_days: int, symbol: str) -> str:
    """基于交易日历计算重叠起始时间的便捷函数"""
    return trading_calendar_manager.calculate_trading_day_overlap(end_date, overlap_trading_days, symbol)

def calculate_overlap_with_fallback(end_date: str, overlap_trading_days: int, symbol: str) -> str:
    """带回退机制的重叠计算便捷函数"""
    return trading_calendar_manager.calculate_overlap_with_fallback(end_date, overlap_trading_days, symbol)

def calculate_overlap_by_period(target_period: str) -> int:
    """
    根据目标周期动态计算重叠交易日数

    Args:
        target_period: 目标周期字符串，如'1m', '5m', '1h', '1d'等

    Returns:
        int: 推荐的重叠交易日数
    """
    from utils.data_processor.period_converter import parse_period_to_minutes

    try:
        target_minutes = parse_period_to_minutes(target_period)

        if target_minutes <= 0:
            logger.warning(f"无法解析目标周期{target_period}，使用默认重叠1个交易日")
            return 1

        # 动态重叠策略
        if target_minutes <= 60:  # 小时内周期（1m-60m）
            overlap_days = 2
            logger.debug(f"小时内周期{target_period}({target_minutes}分钟)，重叠{overlap_days}个交易日")
        elif target_minutes <= 1440:  # 日内周期（1h-24h）
            overlap_days = 3
            logger.debug(f"日内周期{target_period}({target_minutes}分钟)，重叠{overlap_days}个交易日")
        else:  # 日级以上周期（1d+）
            overlap_days = 5
            logger.debug(f"日级以上周期{target_period}({target_minutes}分钟)，重叠{overlap_days}个交易日")

        return overlap_days

    except Exception as e:
        logger.error(f"计算动态重叠交易日数失败: {e}")
        return 1  # 默认重叠1个交易日

def calculate_dynamic_overlap(end_date: str, target_period: str, symbol: str) -> str:
    """
    基于目标周期动态计算重叠起始时间

    Args:
        end_date: 结束日期 (YYYYMMDD)
        target_period: 目标周期字符串
        symbol: 股票代码

    Returns:
        str: 重叠起始日期 (YYYYMMDD)
    """
    # 根据目标周期计算重叠交易日数
    overlap_trading_days = calculate_overlap_by_period(target_period)

    # 使用计算出的重叠交易日数进行重叠计算
    return calculate_overlap_with_fallback(end_date, overlap_trading_days, symbol)
