#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据类型管理器 - 确保数据处理过程中的类型一致性

该模块提供数据类型检查、转换和修复功能，确保数据处理流程中的类型安全。
特别针对时间戳列的类型一致性问题进行优化。

作者: Augment Agent
创建时间: 2025-07-31
版本: 1.0.0
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional, Tuple, Union
import logging
from enum import Enum

from utils.logger import get_unified_logger, LogTarget
from utils.time_utils import ms_to_datetime

class DataTypeIssue(Enum):
    """数据类型问题枚举"""
    TYPE_INCONSISTENCY = "type_inconsistency"  # 类型不一致
    STRING_TIMESTAMP = "string_timestamp"      # 字符串时间戳
    MIXED_TYPES = "mixed_types"               # 混合类型
    INVALID_FORMAT = "invalid_format"         # 无效格式
    PRECISION_LOSS = "precision_loss"         # 精度丢失

class TypeConversionResult:
    """类型转换结果"""
    def __init__(self, success: bool, data: Optional[pd.DataFrame] = None, 
                 issues: List[Dict] = None, warnings: List[str] = None):
        self.success = success
        self.data = data
        self.issues = issues or []
        self.warnings = warnings or []
        
    def add_issue(self, issue_type: DataTypeIssue, column: str, 
                  description: str, severity: str = "warning"):
        """添加类型问题"""
        self.issues.append({
            "type": issue_type.value,
            "column": column,
            "description": description,
            "severity": severity
        })
        
    def add_warning(self, message: str):
        """添加警告信息"""
        self.warnings.append(message)

class DataTypeManager:
    """数据类型管理器"""

    # 支持的时间戳列名
    TIME_COLUMNS = ['time', 'timestamp', 'datetime']

    # 预期的数据类型映射
    EXPECTED_DTYPES = {
        'time': 'int64',
        'timestamp': 'int64',
        'datetime': 'datetime64[ns]',
        'lastPrice': 'float64',
        'open': 'float64',
        'high': 'float64',
        'low': 'float64',
        'close': 'float64',
        'volume': 'int64',
        'amount': 'float64'
    }

    def __init__(self, strict_mode: bool = True, auto_fix: bool = True):
        """
        初始化数据类型管理器

        Args:
            strict_mode: 严格模式，发现问题时是否抛出异常
            auto_fix: 是否自动修复常见的类型问题
        """
        self.logger = get_unified_logger(__name__, enhanced=True)
        self.strict_mode = strict_mode
        self.auto_fix = auto_fix
        self.conversion_history = []  # 转换历史记录
        
    def analyze_dataframe_types(self, df: pd.DataFrame, 
                               context: str = "") -> Dict[str, Any]:
        """
        分析DataFrame的数据类型
        
        Args:
            df: 要分析的DataFrame
            context: 上下文信息，用于日志
            
        Returns:
            Dict: 类型分析结果
        """
        if df is None or df.empty:
            return {"error": "DataFrame为空"}
            
        analysis = {
            "shape": df.shape,
            "columns": list(df.columns),
            "dtypes": {col: str(dtype) for col, dtype in df.dtypes.items()},
            "issues": [],
            "recommendations": []
        }
        
        # 检查时间戳列
        for time_col in self.TIME_COLUMNS:
            if time_col in df.columns:
                issue = self._analyze_time_column(df[time_col], time_col)
                if issue:
                    analysis["issues"].append(issue)
                    
        # 检查数值列的类型
        for col in df.columns:
            if col in self.EXPECTED_DTYPES:
                expected_dtype = self.EXPECTED_DTYPES[col]
                actual_dtype = str(df[col].dtype)
                
                if actual_dtype != expected_dtype:
                    analysis["issues"].append({
                        "column": col,
                        "type": "dtype_mismatch",
                        "expected": expected_dtype,
                        "actual": actual_dtype,
                        "severity": "warning"
                    })
                    
        # 生成修复建议
        if analysis["issues"]:
            analysis["recommendations"] = self._generate_recommendations(analysis["issues"])
            
        # 记录分析结果 - 只在有问题时输出日志，避免日志噪音
        if context and analysis['issues']:
            self.logger.debug(LogTarget.FILE, f"[{context}] 数据类型分析发现问题: {len(analysis['issues'])} 个")
            for issue in analysis['issues']:
                self.logger.debug(LogTarget.FILE, f"  - {issue.get('column', 'unknown')}: {issue.get('type', 'unknown')} ({issue.get('severity', 'unknown')})")
            
        return analysis
        
    def _analyze_time_column(self, time_series: pd.Series, 
                           column_name: str) -> Optional[Dict]:
        """分析时间列的类型问题"""
        dtype = str(time_series.dtype)
        
        # 检查是否为字符串类型
        if dtype == 'object':
            # 检查是否为数字字符串
            sample_values = time_series.dropna().head(10)
            if len(sample_values) > 0:
                first_val = sample_values.iloc[0]
                if isinstance(first_val, str) and first_val.isdigit():
                    return {
                        "column": column_name,
                        "type": DataTypeIssue.STRING_TIMESTAMP.value,
                        "description": f"时间列{column_name}为字符串类型，应为数值类型",
                        "severity": "error",
                        "sample_values": sample_values.head(3).tolist()
                    }
                    
        # 检查是否为浮点数类型（可能导致精度问题）
        elif dtype.startswith('float'):
            return {
                "column": column_name,
                "type": DataTypeIssue.PRECISION_LOSS.value,
                "description": f"时间列{column_name}为浮点类型，可能存在精度问题",
                "severity": "warning"
            }
            
        return None
        
    def _generate_recommendations(self, issues: List[Dict]) -> List[str]:
        """根据问题生成修复建议"""
        recommendations = []
        
        for issue in issues:
            if issue["type"] == DataTypeIssue.STRING_TIMESTAMP.value:
                recommendations.append(
                    f"使用pd.to_numeric()将{issue['column']}列转换为数值类型"
                )
            elif issue["type"] == "dtype_mismatch":
                recommendations.append(
                    f"将{issue['column']}列从{issue['actual']}转换为{issue['expected']}"
                )
                
        return recommendations
        
    def ensure_type_consistency(self, dfs: List[pd.DataFrame], 
                              context: str = "") -> TypeConversionResult:
        """
        确保多个DataFrame之间的类型一致性
        
        Args:
            dfs: DataFrame列表
            context: 上下文信息
            
        Returns:
            TypeConversionResult: 转换结果
        """
        if not dfs:
            return TypeConversionResult(False)
            
        result = TypeConversionResult(True)
        
        # 分析所有DataFrame的类型
        type_analysis = {}
        for i, df in enumerate(dfs):
            if df is not None and not df.empty:
                analysis = self.analyze_dataframe_types(df, f"{context}_df{i}")
                type_analysis[i] = analysis
                
        # 检查类型一致性
        inconsistencies = self._find_type_inconsistencies(type_analysis)
        
        if inconsistencies:
            self.logger.warning(LogTarget.FILE,
                         f"[{context}] 发现 {len(inconsistencies)} 个类型不一致问题")
            
            for inconsistency in inconsistencies:
                result.add_issue(
                    DataTypeIssue.TYPE_INCONSISTENCY,
                    inconsistency["column"],
                    inconsistency["description"],
                    "error"
                )
                
            # 如果启用自动修复，尝试修复
            if self.auto_fix:
                fixed_dfs = self._fix_type_inconsistencies(dfs, inconsistencies)
                result.data = fixed_dfs
                result.add_warning("已自动修复类型不一致问题")
            else:
                result.success = False
                
        else:
            result.data = dfs
            # 移除无用的"检查通过"日志，减少日志噪音
            
        return result
        
    def _find_type_inconsistencies(self, type_analysis: Dict) -> List[Dict]:
        """查找类型不一致问题"""
        inconsistencies = []
        
        if len(type_analysis) < 2:
            return inconsistencies
            
        # 获取所有列名
        all_columns = set()
        for analysis in type_analysis.values():
            all_columns.update(analysis.get("columns", []))
            
        # 检查每列的类型一致性
        for column in all_columns:
            dtypes = []
            for analysis in type_analysis.values():
                if column in analysis.get("dtypes", {}):
                    dtypes.append(analysis["dtypes"][column])
                    
            # 如果类型不一致
            unique_dtypes = set(dtypes)
            if len(unique_dtypes) > 1:
                inconsistencies.append({
                    "column": column,
                    "types": list(unique_dtypes),
                    "description": f"列{column}在不同DataFrame中类型不一致: {unique_dtypes}"
                })
                
        return inconsistencies
        
    def _fix_type_inconsistencies(self, dfs: List[pd.DataFrame], 
                                inconsistencies: List[Dict]) -> List[pd.DataFrame]:
        """修复类型不一致问题"""
        fixed_dfs = []
        
        for df in dfs:
            if df is None or df.empty:
                fixed_dfs.append(df)
                continue
                
            df_copy = df.copy()
            
            for inconsistency in inconsistencies:
                column = inconsistency["column"]
                if column in df_copy.columns:
                    df_copy = self._fix_column_type(df_copy, column)
                    
            fixed_dfs.append(df_copy)
            
        return fixed_dfs
        
    def _fix_column_type(self, df: pd.DataFrame, column: str) -> pd.DataFrame:
        """修复单个列的类型问题"""
        if column not in df.columns:
            return df
            
        current_dtype = str(df[column].dtype)
        
        # 处理时间戳列
        if column in self.TIME_COLUMNS:
            if current_dtype == 'object':
                # 尝试转换字符串时间戳为数值
                try:
                    df[column] = pd.to_numeric(df[column], errors='coerce')
                    self.logger.debug(LogTarget.FILE,
                               f"成功将{column}列从字符串转换为数值类型")
                except Exception as e:
                    self.logger.warning(LogTarget.FILE,
                                 f"转换{column}列类型失败: {e}")
                    
        # 处理其他列类型
        elif column in self.EXPECTED_DTYPES:
            expected_dtype = self.EXPECTED_DTYPES[column]
            try:
                df[column] = df[column].astype(expected_dtype)
                self.logger.debug(LogTarget.FILE,
                           f"成功将{column}列转换为{expected_dtype}类型")
            except Exception as e:
                self.logger.warning(LogTarget.FILE,
                             f"转换{column}列到{expected_dtype}失败: {e}")
                
        return df
        
    def validate_merge_compatibility(self, dfs: List[pd.DataFrame]) -> bool:
        """验证DataFrame是否可以安全合并"""
        if len(dfs) < 2:
            return True
            
        result = self.ensure_type_consistency(dfs, "merge_validation")
        return result.success
        
    def get_conversion_report(self) -> Dict[str, Any]:
        """获取类型转换报告"""
        return {
            "total_conversions": len(self.conversion_history),
            "history": self.conversion_history[-10:],  # 最近10次转换
            "auto_fix_enabled": self.auto_fix,
            "strict_mode": self.strict_mode
        }

    def safe_concat_with_type_check(self, dfs: List[pd.DataFrame],
                                   context: str = "") -> pd.DataFrame:
        """
        安全合并DataFrame，确保类型一致性

        Args:
            dfs: 要合并的DataFrame列表
            context: 上下文信息

        Returns:
            pd.DataFrame: 合并后的DataFrame
        """
        if not dfs:
            return pd.DataFrame()

        # 确保类型一致性
        result = self.ensure_type_consistency(dfs, context)

        if not result.success:
            self.logger.error(LogTarget.FILE, f"[{context}] 类型一致性检查失败")
            # 如果严格模式，抛出异常
            if self.strict_mode:
                raise ValueError("数据类型不一致，无法安全合并")

        # 使用修复后的数据
        fixed_dfs = result.data if result.data else dfs

        # 导入IndexManager进行安全合并
        try:
            from utils.data_processor.index_manager import IndexManager
            merged_df = IndexManager.safe_concat(fixed_dfs)

            if merged_df is None:
                self.logger.warning(LogTarget.FILE, f"[{context}] IndexManager.safe_concat失败，使用备用方案")
                merged_df = pd.concat(fixed_dfs, ignore_index=False)

        except ImportError:
            self.logger.warning(LogTarget.FILE, "IndexManager不可用，使用pd.concat")
            merged_df = pd.concat(fixed_dfs, ignore_index=False)

        # 验证合并结果
        if merged_df is not None:
            final_analysis = self.analyze_dataframe_types(merged_df, f"{context}_merged")
            if final_analysis.get("issues"):
                self.logger.warning(LogTarget.FILE,
                             f"[{context}] 合并后仍存在类型问题: {len(final_analysis['issues'])} 个")

        return merged_df
