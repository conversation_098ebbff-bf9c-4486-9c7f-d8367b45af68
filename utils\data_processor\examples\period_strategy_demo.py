#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
周期策略模式演示

演示如何使用周期支持策略模式进行不同数据源的周期判断
"""

import os
import sys
import pandas as pd
from typing import List

# 添加项目根目录到路径，确保可以导入项目模块
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(
    os.path.dirname(os.path.dirname(current_dir))))
sys.path.insert(0, project_root)

try:
    # 导入周期支持模块
    from utils.data_processor.period_support import (
        PeriodSupportContext,
        period_support_context,
        is_period_supported,
        is_supported_by_xtquant,
        get_supported_periods
    )
    from utils.data_processor.period_converter import DataSourcePeriodStrategy
except ImportError as e:
    print(f"导入模块时出错: {e}")
    print(f"当前路径: {os.getcwd()}")
    print(f"Python路径: {sys.path}")
    sys.exit(1)


def demonstrate_period_strategy():
    """演示周期支持策略模式的用法"""
    print("=" * 80)
    print("周期支持策略模式演示")
    print("=" * 80)

    # 创建一个自定义策略类，用于演示自定义数据源
    class CustomDataSourceStrategy(DataSourcePeriodStrategy):
        """自定义数据源策略示例"""

        def __init__(self):
            # 自定义数据源支持的周期列表
            self.supported_periods = ['1m', '5m', '15m', '30m', '3m', '7m', '1d']

        def is_period_supported(self, period: str) -> bool:
            """判断周期是否被支持"""
            return period in self.supported_periods

        def get_supported_periods(self) -> List[str]:
            """获取支持的周期列表"""
            return self.supported_periods.copy()

    # 1. 使用默认的迅投策略
    print("\n1. 使用默认的迅投策略:")
    print(f"默认数据源支持的周期: {get_supported_periods()}")

    test_periods = ['1m', '3m', '5m', '15m', '1h', '4h', '1d']
    print("\n迅投API周期支持情况:")
    for period in test_periods:
        supported = is_supported_by_xtquant(period)
        print(f"  - 周期 '{period}' {'支持' if supported else '不支持'}")

    # 2. 注册自定义数据源策略
    print("\n2. 注册自定义数据源策略:")
    custom_strategy = CustomDataSourceStrategy()
    period_support_context.register_strategy('custom', custom_strategy)

    print(f"可用的数据源: {period_support_context.get_available_sources()}")
    print(f"自定义数据源支持的周期: {period_support_context.get_supported_periods('custom')}")

    # 3. 使用上下文判断不同数据源对周期的支持情况
    print("\n3. 不同数据源周期支持对比:")
    print("| 周期 | 迅投API | 自定义数据源 |")
    print("|------|---------|------------|")
    for period in test_periods:
        xtquant_support = period_support_context.is_period_supported(
            period, PeriodSupportContext.DATA_SOURCE_XTQUANT)
        custom_support = period_support_context.is_period_supported(period, 'custom')
        print(
            f"| {period} | {'✓' if xtquant_support else '✗'} | {'✓' if custom_support else '✗'} |")

    # 4. 更改默认数据源
    print("\n4. 更改默认数据源:")
    period_support_context.set_default_source('custom')
    print("默认数据源已更改为 'custom'")

    print("\n使用默认数据源(现在是'custom')判断周期支持情况:")
    for period in test_periods:
        supported = is_period_supported(period)  # 使用默认数据源
        print(f"  - 周期 '{period}' {'支持' if supported else '不支持'}")

    # 5. 演示如何在实际代码中使用
    print("\n5. 实际应用场景示例:")

    def download_kline_data(stock_code: str, period: str, start_date: str, end_date: str, data_source: str = None):
        """模拟下载K线数据函数"""
        # 使用上下文判断周期是否支持
        if is_period_supported(period, data_source):
            print(f"直接从{data_source or '默认数据源'}下载{period}周期数据")
            return f"获取到{stock_code}的{period}周期数据"
        else:
            print(f"{data_source or '默认数据源'}不支持{period}周期，使用1分钟数据转换")
            # 假设获取1分钟数据并转换
            return f"获取{stock_code}的1分钟数据并转换为{period}周期"

    # 分别用不同数据源测试
    print("\n使用迅投API:")
    result1 = download_kline_data(
        '000001.SZ', '5m', '20250401', '20250430', PeriodSupportContext.DATA_SOURCE_XTQUANT)
    result2 = download_kline_data(
        '000001.SZ', '3m', '20250401', '20250430', PeriodSupportContext.DATA_SOURCE_XTQUANT)

    print("\n使用自定义数据源:")
    result3 = download_kline_data('000001.SZ', '5m', '20250401', '20250430', 'custom')
    result4 = download_kline_data('000001.SZ', '3m', '20250401', '20250430', 'custom')

    print("\n使用默认数据源(当前是'custom'):")
    result5 = download_kline_data('000001.SZ', '7m', '20250401', '20250430')
    result6 = download_kline_data('000001.SZ', '4h', '20250401', '20250430')

    print("\n演示完成!")


if __name__ == "__main__":
    # 执行演示
    demonstrate_period_strategy()
