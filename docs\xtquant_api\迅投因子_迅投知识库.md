迅投因子 | 迅投知识库


[![迅投知识库](/images/logo.png)迅投知识库](/)

[首页](/)

APIAPI

* [投研新手教程](/freshman/ty_rookie.html)
* [QMT新手教程](/freshman/rookie.html)
* [内置Python](/innerApi/start_now.html)
* [XtQuant文档](/nativeApi/start_now.html)
* [VBA](/VBA/start_now.html)

数据字典数据字典

* [快速开始](/dictionary/)
* [股票数据](/dictionary/stock.html)
* [行业概念数据](/dictionary/industry.html)
* [指数数据](/dictionary/indexes.html)
* [期货数据](/dictionary/future.html)
* [期权数据](/dictionary/option.html)
* [场内基金数据](/dictionary/floorfunds.html)
* [债券数据](/dictionary/bond.html)
* [常见问题](/dictionary/question_answer.html)
* [场景化示例](/dictionary/scenario_based_example.html)
* [迅投因子](/dictionary/xuntou_factor.html)

策略迁移策略迁移

* [聚宽策略](/strategy/JoinQuant2QMT.html)
* #### 

  + [客服QQ: 810315303](/dictionary/xuntou_factor.html)
  + [联系方式: 18309226715](/dictionary/xuntou_factor.html)

视频教程视频教程

* [投研端教程](/videos/touyan/ty_register_install.html)

投研平台 在新窗口打开

迅投社区 在新窗口打开

[迅投官网 在新窗口打开](http://www.thinktrader.net)

注册

登录

![微信扫码联系客服](/assets/wechat-d90fd08f.png "点击联系客服")

![分享链接](data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAHqADAAQAAAABAAAAHgAAAADKQTcFAAADjElEQVRIDcVXW0hVQRRdM/fce/OVpfRA8dENDckkMILsYRG9PnqQQUkg9NFfBCFEJJSFRj8R+BP4URREGEVGRNSXWEiE1odoDx+lhkoWpTe1+zrT7KPnes59ddQbDujM7D17rbNn9uzZl8FCqxaC36l1l4iAekgIFDOwDEDIP2psUEAMMoY2ZuONFVUpLdWMqVO66P9ZdBWw/ZZY9GXAfZqpolKCL4+1VtfJj/omOLuWm5VS13SC/dHloX1UYtcld5lA4Lr0MCvUyMpc7sAAg+1M78WUh5HW81ChEIKtqh6rVUXgwVxJCZNsCYOwCDOUxySgBa7LY/dkfzR04XzmjLGG3guLy2UvdByTx3J7a+JNSkSESdg6KfVBj+lMaWuMyniPObMd0c9c85iilwIpHkSZqQyudNNGBmGJg7hIoK2gKzOfQKJt27xawc41dtytSELesijEMuCISyOm5ED3lCazbXaJv6fAjvrjyShcaUPlDidy0mzoHI6eP4hL43TVjG1R/erL2ZAm2IF9ax0oW+9EWiLH0w4PSl02bMhW4PYIFF0diwnHFb5VoTQYc5VBmZrAcLDIgf2FTiQ7p+LyxQcvijO5RkpLO4cDBovIQ+JU5NkWR1bPSFekMByW3u0tcMChBC8Cmrq8yF0iU2ue3ILpZolYckoYliHzsG5n6rOWchwrdqJUAttkDjS2ll4fkuwCB9Y5jWJLHhOnMvPKmOy1yfndichNt4Up2vp9mPAEcGqbdjNM+o6hf281cUaO+2mo2ucTaB/ym4DbB/34/MMfkdQXEOgeiR7RQSAGIYnZYFAQMvj6S8XZR+Ooa5rAuFfg/bAfrX1eVO0K95RMuySpzwIvBBtS6BGXNvkhnKbps04fmrt92CivS315ImSyN+n1iZXAorXEyaly0A1j9eNeYJNLgcIjk5KtVWKJ0CrzNm+MRWjUvekP4KPcztHJyLfAMrHCH3OqkahcMRLEGguZ3uuaPWh466XnzrTUCjFxESenwoxqJBNClEnPSAA3Xk3i5msPzj2ZRPntcfR8n7o+Az9VmS6jGBrExEWc2oHRU9XXP/ppLi+UQ17zkyVOjPxWcf+dz0ARPqQ6LCc7NZ+KwGCkLEghQN9GlQEDvxL+nfGRELZefRBi0GOayGBZmGKPqkCtGoyj55qnIRVmmMck0Bud+f8s6E1brZPq/YL8hNHJqacaKd4/2v4CgdaZJ2zGqYAAAAAASUVORK5CYII= "分享链接")

[首页](/)

APIAPI

* [投研新手教程](/freshman/ty_rookie.html)
* [QMT新手教程](/freshman/rookie.html)
* [内置Python](/innerApi/start_now.html)
* [XtQuant文档](/nativeApi/start_now.html)
* [VBA](/VBA/start_now.html)

数据字典数据字典

* [快速开始](/dictionary/)
* [股票数据](/dictionary/stock.html)
* [行业概念数据](/dictionary/industry.html)
* [指数数据](/dictionary/indexes.html)
* [期货数据](/dictionary/future.html)
* [期权数据](/dictionary/option.html)
* [场内基金数据](/dictionary/floorfunds.html)
* [债券数据](/dictionary/bond.html)
* [常见问题](/dictionary/question_answer.html)
* [场景化示例](/dictionary/scenario_based_example.html)
* [迅投因子](/dictionary/xuntou_factor.html)

策略迁移策略迁移

* [聚宽策略](/strategy/JoinQuant2QMT.html)
* #### 

  + [客服QQ: 810315303](/dictionary/xuntou_factor.html)
  + [联系方式: 18309226715](/dictionary/xuntou_factor.html)

视频教程视频教程

* [投研端教程](/videos/touyan/ty_register_install.html)

投研平台 在新窗口打开

迅投社区 在新窗口打开

[迅投官网 在新窗口打开](http://www.thinktrader.net)

* 数据字典

  + [快速开始](/dictionary/)
  + [股票数据](/dictionary/stock.html)
  + [行业概念数据](/dictionary/industry.html)
  + [指数数据](/dictionary/indexes.html)
  + [期货数据](/dictionary/future.html)
  + [期权数据](/dictionary/option.html)
  + [场内基金](/dictionary/floorfunds.html)
  + [债券数据](/dictionary/bond.html)
  + [常见问题](/dictionary/question_answer.html)
  + [场景化示例](/dictionary/scenario_based_example.html)
  + [迅投因子](/dictionary/xuntou_factor.html) 
    - [因子数据下载](/dictionary/xuntou_factor.html#因子数据下载)
    - [因子数据获取](/dictionary/xuntou_factor.html#因子数据获取) 
      * [在python中获取](/dictionary/xuntou_factor.html#在python中获取)
      * [在因子公式中获取](/dictionary/xuntou_factor.html#在因子公式中获取)
    - [成长因子](/dictionary/xuntou_factor.html#成长因子)
    - [基础因子](/dictionary/xuntou_factor.html#基础因子)
    - [每股因子](/dictionary/xuntou_factor.html#每股因子)
    - [质量因子](/dictionary/xuntou_factor.html#质量因子)
    - [动量因子](/dictionary/xuntou_factor.html#动量因子)
    - [风险因子](/dictionary/xuntou_factor.html#风险因子)
    - [技术因子](/dictionary/xuntou_factor.html#技术因子)
    - [情绪因子](/dictionary/xuntou_factor.html#情绪因子)

### [#](#因子数据下载) 因子数据下载

```
from xtquant import xtdata

xtdata.download_metatable_data() # 更新因子列表

metainfo = xtdata.get_metatable_list() # 获取因子对照字典，结构是{英文因子表名:中文因子表名}

metainfo_rev = {v:k for k,v in metainfo.items()} # 转置使结构变为{中文因子表名:英文因子表名}

zn_factor_name = "质量类因子" 

xtdata.download_history_data('XXXXXX.XX', metainfo_rev[zn_factor_name], '', '', incrementally=False) # 下载质量类因子数据


```

### [#](#因子数据获取) 因子数据获取

#### [#](#在python中获取) 在python中获取

```
import pandas as pd
import os
path = "{安装目录}\\datadir\\EP"

factor_name = "{factor_name}"

data_file = os.path.join(path,factor_name + "_Xdat2","data.fe")

factor_data = pd.read_feather(data_file)

print(factor_data)


```

#### [#](#在因子公式中获取) 在因子公式中获取

```
factor_type := '{因子分类}';
factor_name := '{因子名称}';

// 获取当前标的的因子值
factor_value :=EXTDATA2(factor_type,factor_name,'');

// 获取当前品种在该因子中的正序排名
factor_rank := EXTDATARANK2(factor_type,factor_name,'',0)

// 获取当前品种在该因子中,指定板块范围的正序排名
factor_rank := EXTDATABLOCKRANK2(factor_type,factor_name,'',0,'SW1农林牧渔')


```

[#](#成长因子) 成长因子
---------------

* **表名: `factor_growth`**

| 参数 | 因子名称 | 因子说明 |
| --- | --- | --- |
| `fin_cash_flow_growth` | 筹资活动产生的现金流量净额增长率 | 反映公司通过筹资活动（如发行股票、债券等）获得的现金流量净额的增长率，体现融资能力的提升。 |
| `net_profit_growth_parent` | 归属母公司股东的净利润增长率 | 表明公司净利润中归属母公司股东部分的增长速度，是衡量公司核心盈利能力的重要指标。 |
| `net_assets_growth` | 净资产增长率 | 显示公司净资产（总资产减去总负债）的增长率，反映公司的资本积累和财务健康状况。 |
| `total_profit_growth` | 利润总额增长率 | 表示公司总利润的增长率，涵盖主营业务利润和非主营业务利润，是公司整体盈利能力的标志。 |

[#](#基础因子) 基础因子
---------------

* **表名: `factor_base_derivative`**

| 参数 | 因子名称 | 因子说明 |
| --- | --- | --- |
| `fin_exp_ttm` | 财务费用TTM | 公司在最近十二个月内（TTM）的财务费用总额，包括利息支出等。 |
| `total_rev_ttm` | 营业总收入TTM | 最近十二个月内公司的营业总收入，包括主营业务收入和其它业务收入。 |
| `admin_exp_ttm` | 管理费用TTM | 公司在最近十二个月内管理活动所产生的费用总额。 |
| `net_profit_attr_shr_ttm` | 归属于母公司股东的净利润TTM | 最近十二个月内归属于母公司股东的净利润总额。 |
| `net_profit_ttm` | 净利润TTM | 公司在最近十二个月内的净利润总额。 |
| `op_rev_ttm` | 营业收入TTM | 公司在最近十二个月内的主营业务收入总额。 |
| `op_cost_ttm` | 营业成本TTM | 公司在最近十二个月内的主营业务成本总额。 |
| `op_profit_ttm` | 营业利润TTM | 公司在最近十二个月内的营业利润，即营业收入减去营业成本和期间费用后的利润。 |
| `non_op_inc_ttm` | 营业外收入TTM | 公司在最近十二个月内与主营业务无关的收入总额。 |
| `non_op_exp_ttm` | 营业外支出TTM | 公司在最近十二个月内与主营业务无关的支出总额。 |
| `non_op_net_inc_ttm` | 营业外收支净额TTM | 公司在最近十二个月内营业外收入减去营业外支出的净额。 |
| `total_profit_ttm` | 利润总额TTM | 公司在最近十二个月内的总利润，包括营业利润和非营业利润。 |
| `net_debt` | 净债务 | 公司的总负债减去其现金和现金等价物的余额。 |
| `ret_earnings` | 留存收益 | 公司在最近十二个月内累积的未分配利润。 |
| `net_wc` | 净运营资本 | 公司的流动资产减去流动负债，反映公司短期偿债能力。 |
| `fin_cash_flow_ttm` | 筹资活动现金流量净额TTM | 最近十二个月内公司通过筹资活动产生的现金流量净额，包括借款、发行股票等活动。 |
| `op_cash_flow_ttm` | 经营活动现金流量净额TTM |  |
| `impair_loss_ttm` | 资产减值损失TTM | 最近十二个月内公司因资产价值下降而确认的损失总额。 |
| `cf_mv_ratio` | 现金流市值比 | 公司的经营现金流与市值的比例，反映公司现金流的充足度以及市场对其价值的评估是否合理。 |
| `circ_market_value` | 流通市值 | 公司已发行在外且可自由交易的股票的总市值。 |

[#](#每股因子) 每股因子
---------------

* **表名: `factor_metrics`**

| 参数 | 因子名称 | 因子说明 |
| --- | --- | --- |
| `total_mv` | 总市值 | 公司的总市值除以其总股本，反映每股的市场价值。 |
| `op_cash_flow_ps` | 每股经营活动产生的现金流量净额 | 公司经营活动产生的现金流量净额除以总股本，反映每股的现金生成能力。 |
| `net_assets_ps` | 每股净资产 | 公司的净资产除以总股本，反映每股拥有的资产净值。 |
| `ret_earnings_ps` | 每股留存收益 | 公司累积的未分配利润除以总股本，反映每股积累的未分配利润。 |
| `eps_ttm` | 每股收益TTM | 公司最近十二个月（TTM）的净利润除以总股本，反映每股的盈利水平。 |
| `undist_profit_ps` | 每股未分配利润 | 公司未分配利润除以总股本，反映每股可供分配的利润。 |
| `cash_equiv_ps` | 每股现金及现金等价物余额 | 公司持有的现金及现金等价物除以总股本，反映每股的现金持有量。 |
| `op_profit_ps_ttm` | 每股营业利润TTM | 公司最近十二个月（TTM）的营业利润除以总股本，反映每股的营业盈利能力。 |
| `op_revenue_ps` | 每股营业收入 | 公司的营业收入除以总股本，反映每股的销售收入。 |
| `op_revenue_ps_ttm` | 每股营业收入TTM | 公司最近十二个月（TTM）的营业收入除以总股本，反映每股的销售收入水平。 |
| `total_op_revenue_ps` | 每股营业总收入 | 公司的营业总收入除以总股本，反映每股的总销售收入。 |
| `pb_ratio` | 市净率 | 公司的股价除以每股净资产，反映市场对公司每股净资产的估值倍数。 |
| `cap_reserve_ps` | 每股资本公积金 | 公司的资本公积金除以总股本，反映每股的资本公积水平，资本公积金通常用于转增股本或弥补亏损。 |

[#](#质量因子) 质量因子
---------------

* **表名: `factor_quality`**

| 参数 | 因子名称 | 因子说明 |
| --- | --- | --- |
| `eq_ratio` | 产权比率 | 公司负债总额与股东权益的比率，反映公司的资本结构和财务杠杆程度。 |
| `non_curr_assets_ratio` | 非流动资产比率 | 非流动资产占总资产的比例，反映公司长期投资的比重。 |
| `sh_eq_ratio` | 股东权益比率 | 股东权益占总资产的比例，反映公司自有资本的比重。 |
| `fin_exp_to_rev_ratio` | 财务费用与营业总收入之比 | 财务费用占营业总收入的比例，反映财务成本对收入的影响。 |
| `eq_to_fixed_assets_ratio` | 股东权益与固定资产比率 | 股东权益与固定资产的比率，反映固定资产的资金来源情况。 |
| `op_cash_flow_to_tot_liab_ratio` | 经营活动产生的现金流量净额与负债合计比率 | 经营活动现金流量净额与负债总额的比率，反映公司偿还债务的能力。 |
| `op_cash_flow_to_rev_ratio` | 经营活动产生的现金流量净额与营业收入之比 | 经营活动现金流量净额与营业收入的比率，反映经营现金的效率。 |
| `op_cash_flow_to_enterprise_value_ratio_ttm` | 经营活动产生的现金流量净额与企业价值之比TTM | 经营活动现金流量净额与企业价值的比率，反映现金流量对整个企业价值的贡献。 |
| `op_cash_flow_growth_rate` | 经营活动产生的现金流量净额增长率 | 经营活动现金流量净额的增长率，反映经营现金流的变动趋势。 |
| `cash_to_liab_ratio` | 现金流动负债比 | 现金及现金等价物与流动负债的比率，反映短期偿债能力。 |
| `cash_flow_to_assets_ROA_diff` | 现金流资产比和资产回报率之差 | 现金流与资产总额的比率减去资产回报率，反映现金流与资产回报之间的差异。 |
| `debt_to_tot_assets_ratio` | 债务总资产比 | 总债务与总资产的比率，反映公司的债务负担程度。 |
| `lt_debt_to_wc_ratio` | 长期负债与营运资金比率 | 长期负债与营运资金（流动资产-流动负债）的比率，反映长期债务对流动性的压力。 |
| `lt_debt_to_tot_assets_ratio` | 长期负债与资产总计之比 | 长期负债与总资产的比率，反映长期债务在总资产中的比重。 |
| `lt_borrowings_to_tot_assets_ratio` | 长期借款与资产总计之比 | 长期借款与总资产的比率，反映长期借款对总资产的影响。 |
| `tot_asset_cash_recovery_ratio` | 总资产现金回收率 | 经营活动现金流入与总资产的比率，反映资产转换成现金的能力。 |
| `op_cash_flow_to_net_debt_ratio` | 经营活动产生现金流量净额与净债务比率 | 经营活动现金流量净额与净债务的比率，反映偿还净债务的能力。 |
| `net_profit_margin_on_sales` | 销售净利率 | 净利润与销售收入的比率，反映销售收入转化为净利润的效率。 |
| `inventory_turnover_ratio` | 存货周转率 | 销售成本与平均存货的比率，反映存货的周转速度。 |
| `days_sales_of_inventory` | 存货周转天数 | 平均存货转化为销售所需的时间，反映存货的流动性。 |
| `net_profit_cash_content` | 净利润现金含量 | 经营活动现金流量净额与净利润的比率，反映净利润的质量。 |
| `days_sales_outstanding` | 应收账款周转天数 | 平均应收账款转化为现金所需的时间，反映应收账款的回收速度。 |
| `AR_turnover_ratio` | 应收账款周转率 | 营业收入与平均应收账款的比率，反映应收账款的周转速度。 |
| `net_profit_after_extra_to_profit_ratio` | 扣除非经常损益后的净利润与利润总额之比 | 扣除非经常性损益后的净利润与利润总额的比率，反映主营业务的盈利质量。 |
| `admin_exp_to_rev_ratio` | 管理费用与营业总收入之比 | 管理费用占营业总收入的比例，反映管理成本的效率。 |
| `cash_ratio` | 现金比率 | 现金及现金等价物与流动负债的比率，反映即时偿债能力。 |
| `curr_assets_turnover_ratio_ttm` | 流动资产周转率TTM | 流动资产与销售收入的比率，反映流动资产的使用效率。 |
| `current_ratio` | 流动比率 | 流动资产与流动负债的比率，反映短期偿债能力。 |
| `tangible_net_worth_to_debt_ratio` | 有形净值债务率 | 负债总额与扣除无形资产后的股东权益的比率，反映有形资产的债务负担。 |
| `gross_profit_margin_growth_rate` | 毛利率增长 | 毛利率的变化率，反映产品盈利能力的变化趋势。 |
| `AR_index` | 应收账款指数 | 应收账款相对于营业收入的变化率，反映应收账款管理的效率。 |
| `sh_eq_turnover_ratio` | 股东权益周转率 | 营业收入与股东权益的比率，反映股东权益的周转速度。 |

[#](#动量因子) 动量因子
---------------

* **表名: `factor_momentum`**

| 参数 | 因子名称 | 因子说明 |
| --- | --- | --- |
| `aroon_up` | Aroon指标上轨，反映价格上升的趋势强度。 |  |
| `aroon_down` | Aroon指标下轨 | 反映价格下降的趋势强度。 |
| `bbic` | BBI动量 | 基于多条均线的综合指标，反映价格动量。 |
| `bear_power` | 空头力道 | 反映市场下跌趋势的力量。 |
| `bias5` | 5日乖离率 | 反映短期价格偏离均线的程度。 |
| `bias10` | 10日乖离率 | 反映中期价格偏离均线的程度。 |
| `bias20` | 20日乖离率 | 反映长期价格偏离均线的程度。 |
| `bias60` | 60日乖离率 | 反映超长期价格偏离均线的程度。 |
| `bull_power` | 多头力道 | 反映市场上升趋势的力量。 |
| `cci10` | 10日顺势指标 | 衡量价格偏离其统计平均值的程度。 |
| `cci15` | 15日顺势指标 | 衡量价格偏离其统计平均值的程度。 |
| `cci20` | 20日顺势指标 | 衡量价格偏离其统计平均值的程度。 |
| `cci88` | 88日顺势指标 | 衡量价格偏离其统计平均值的程度。 |
| `cr20` | CR指标 | 反映多空双方力量对比。 |
| `mass` | 梅斯线 | 反映价格趋势的强度和稳定性。 |
| `plrc6` | 6日收盘价格与日期线性回归系数 | 反映短期价格趋势。 |
| `plrc12` | 12日收盘价格与日期线性回归系数 | 反映中期价格趋势。 |
| `plrc24` | 24日收盘价格与日期线性回归系数 | 反映长期价格趋势。 |
| `price1m` | 当前股价除以过去一个月股价均值再减1 | 反映近期动量。 |
| `price3m` | 当前股价除以过去三个月股价均值再减1 | 反映中期动量。 |
| `price1y` | 当前股价除以过去一年股价均值再减1 | 反映长期动量。 |
| `roc12` | 12日变动速率(Price Rate of Change) | 反映价格变化率。 |
| `roc120` | 120日变动速率(Price Rate of Change) | 反映价格变化率。 |
| `roc6` | 6日变动速率(Price Rate of Change) | 反映价格变化率。 |
| `roc60` | 60日变动速率(Price Rate of Change) | 反映价格变化率。 |
| `single_day_vpt` | 单日价量趋势 | 反映价格和成交量的关系。 |
| `single_day_vpt_12` | 单日价量趋势12均值 | 反映中期价量趋势的平均。 |
| `single_day_vpt_6` | 单日价量趋势6日均值 | 反映短期价量趋势的平均。 |
| `trix10` | 10日终极指标TRIX | 反映价格变化的加速度。 |
| `trix5` | 5日终极指标TRIX | 反映价格变化的加速度。 |

[#](#风险因子) 风险因子
---------------

* **表名: `factor_risk`**

| 参数 | 因子名称 | 因子说明 |
| --- | --- | --- |
| `variance20` | 20日年化收益方差 | 衡量短期内投资回报的波动程度。 |
| `skewness_20` | 个股收益的20日偏度 | 反映收益分布的不对称性。 |
| `kurtosis_20` | 个股收益的20日峰度 | 反映收益分布的尖峭程度或尾部厚度。 |
| `sharpe_ratio_20` | 20日夏普比率 | 衡量每单位风险所获得的超额回报。 |
| `variance60` | 60日年化收益方差 | 衡量中期投资回报的波动程度。 |
| `skewness_60` | 个股收益的60日偏度 | 反映中期收益分布的不对称性。 |
| `kurtosis_60` | 个股收益的60日峰度 | 反映中期收益分布的尖峭程度或尾部厚度。 |
| `sharpe_ratio_60` | 60日夏普比率 | 衡量中期每单位风险所获得的超额回报。 |
| `variance120` | 120日年化收益方差 | 衡量长期投资回报的波动程度。 |
| `skewness_120` | 个股收益的120日偏度 | 反映长期收益分布的不对称性。 |
| `kurtosis_120` | 个股收益的120日峰度 | 反映长期收益分布的尖峭程度或尾部厚度。 |
| `sharpe_ratio_120` | 120日夏普比率 | 衡量长期每单位风险所获得的超额回报。 |

[#](#技术因子) 技术因子
---------------

* **表名: `factor_technical`**

| 参数 | 因子名称 | 因子说明 |
| --- | --- | --- |
| `boll_down` | 下轨线（布林线）指标 | 反映价格的下限支撑位。 |
| `boll_up` | 上轨线（布林线）指标 | 反映价格的上限阻力位。 |
| `emac5` | 5日指数移动均线 | 反映短期价格趋势。 |
| `emac10` | 10日指数移动均线 | 反映中期价格趋势。 |
| `emac12` | 12日指数移动均线 | 用于MACD计算。 |
| `emac20` | 20日指数移动均线 | 反映中长期价格趋势。 |
| `emac26` | 26日指数移动均线 | 用于MACD计算。 |
| `emac120` | 120日指数移动均线 | 反映长期价格趋势。 |
| `mac5` | 5日移动均线 | 反映短期价格趋势。 |
| `mac10` | 10日移动均线 | 反映中期价格趋势。 |
| `mac12` | 12日移动均线 | 用于某些技术分析策略。 |
| `mac20` | 20日移动均线 | 反映中长期价格趋势。 |
| `mac26` | 26日移动均线 | 用于某些技术分析策略。 |
| `mac120` | 120日移动均线 | 反映长期价格趋势。 |
| `macdc` | 平滑异同移动平均线 | 通过比较两条移动均线的差值来预测趋势。 |
| `mfi` | 资金流量指标 | 衡量资金进出市场的强度。 |

[#](#情绪因子) 情绪因子
---------------

* **表名: `factor_sentiment`**

| 参数 | 因子名称 | 因子说明 |
| --- | --- | --- |
| `vroc12` | 12日成交量变动速率 | 反映成交量变化的速度。 |
| `tvma6` | 6日成交量加权移动均线 | 反映短期成交量的平均趋势。 |
| `vema10` | 10日成交量指数移动均线 | 反映中期成交量的平滑趋势。 |
| `vol5` | 5日成交量均值 | 反映短期平均成交量。 |
| `vema12` | 12日成交量指数移动均线 | 反映成交量的中期趋势。 |
| `tvma20` | 20日成交量加权移动均线 | 反映中长期成交量的平均趋势。 |
| `davol5` | 5日成交量偏差 | 反映成交量与均值的偏离程度。 |
| `wvad` | 威廉变异离散量 | 反映价格与成交量的同步变化。 |
| `mawvad` | 移动平均威廉变异离散量 | 反映价格与成交量的平滑同步变化。 |
| `vstd10` | 10日成交量标准差 | 反映成交量的波动性。 |
| `atr14` | 14日平均真实波幅 | 反映价格波动的幅度。 |
| `vol10` | 10日成交量均值 | 反映中期平均成交量。 |
| `davol10` | 10日成交量偏差 | 反映成交量与均值的偏离程度。 |
| `vstd20` | 20日成交量标准差 | 反映成交量的波动性。 |
| `atr6` | 6日平均真实波幅 | 反映短期价格波动的幅度。 |
| `vol20` | 20日成交量均值 | 反映中长期平均成交量。 |
| `davol20` | 20日成交量偏差 | 反映成交量与均值的偏离程度。 |
| `vdiff` | 成交量差值 | 反映成交量的瞬时变化。 |
| `vdea` | 成交量差指数移动平均线 | 反映成交量差值的平滑趋势。 |
| `vmacd` | 成交量MACD | 通过比较不同周期的成交量移动平均线差值来识别买卖信号。 |
| `br` | 买盘力道 | 反映买盘成交量的强度。 |
| `ar` | 卖盘力道 | 反映卖盘成交量的强度。 |
| `vol60` | 60日成交量均值 | 反映长期平均成交量。 |
| `vol120` | 120日成交量均值 | 反映超长期平均成交量。 |
| `vroc6` | 6日成交量变动速率 | 反映成交量变化的速度。 |
| `tvstd20` | 20日成交量加权标准差 | 反映成交量的波动性。 |
| `arbr` | 买卖盘力道比 | 反映买卖盘强度的对比。 |
| `money_flow_20` | 20日资金流向 | 反映资金进出市场的趋势。 |
| `vema5` | 5日成交量指数移动均线 | 反映短期成交量的平滑趋势。 |
| `vol240` | 240日成交量均值 | 反映极长期平均成交量。 |
| `vema26` | 26日成交量指数移动均线 | 反映成交量的长期趋势。 |
| `vosc` | 量能震荡指标 | 反映成交量的波动趋势。 |
| `tvstd6` | 6日成交量加权标准差 | 反映成交量的短期波动性。 |
| `psy` | 心理线 | 反映股票上涨天数占比，用以判断市场情绪的超买或超卖状态。 |

上次更新: 2024/8/23 18:20:34

邀请注册送VIP优惠券

分享下方的内容给好友、QQ群、微信群,好友注册您即可获得VIP优惠券

玩转qmt,上迅投qmt知识库

登录后获取

[场景化示例](/dictionary/scenario_based_example.html)