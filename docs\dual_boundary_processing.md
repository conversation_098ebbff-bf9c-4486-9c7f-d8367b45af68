# 双重边界数据处理功能说明

## 功能概述

双重边界数据处理功能是对`merge_non_trading_data_vectorized`函数的重要增强，用于处理tick数据中的两种不同类型的边界数据问题。

## 背景问题

在实际的tick数据处理中，存在两种不同的边界数据情况：

### 1. 休盘边界数据（原有功能）
- **问题**：交易结束后的下一分钟可能还有数据
- **示例**：15:01:00的数据需要合并到15:00:00
- **原因**：数据重采样或系统延迟导致

### 2. 录制延时数据（新增功能）
- **问题**：录制机制导致的秒级时间戳延迟
- **示例**：15:00:10、15:00:30等数据需要合并到15:00:00
- **原因**：L1 tick数据按固定频率录制，录制时间与交易结束时间不对齐

## 技术实现

### 双重判断条件

```python
# 计算时间差
time_diff_seconds = time_index.to_series().diff().dt.total_seconds()
time_diff_minutes = time_diff_seconds / 60

# 条件1: 休盘边界处理 - 跨分钟边界数据
condition1 = (time_diff_minutes == 1)

# 条件2: 延时处理 - 同分钟内的录制延时数据
condition2 = (time_diff_seconds > 0) & (time_diff_seconds <= 60)

# 合并条件：两种情况都需要处理，且当前时间为休盘时间
boundary_mask = (condition1 | condition2) & (~is_trading_mask)
```

### 处理逻辑

1. **时间差计算**：同时计算秒级和分钟级时间差
2. **双重判断**：分别识别两种类型的边界数据
3. **统一合并**：使用OR逻辑组合两个条件
4. **向量化处理**：保持高性能的向量化操作

## 适用场景

### 所有休盘边界时间点

| 品种类型 | 边界时间点 | 延时数据示例 | 合并目标 |
|----------|------------|--------------|----------|
| **A股** | 11:30:00 | 11:30:10, 11:30:25 | → 11:30:00 |
| **A股** | 15:00:00 | 15:00:15, 15:00:30 | → 15:00:00 |
| **期货日盘** | 10:15:00 | 10:15:20, 10:15:45 | → 10:15:00 |
| **期货日盘** | 11:30:00 | 11:30:10, 11:30:35 | → 11:30:00 |
| **期货日盘** | 15:00:00 | 15:00:25, 15:00:50 | → 15:00:00 |
| **期货夜盘** | 23:00:00 | 23:00:15, 23:00:40 | → 23:00:00 |
| **期货夜盘** | 01:00:00 | 01:00:20, 01:00:55 | → 01:00:00 |
| **期货夜盘** | 02:30:00 | 02:30:10, 02:30:30 | → 02:30:00 |
| **中金所** | 11:30:00 | 11:30:15, 11:30:45 | → 11:30:00 |
| **中金所** | 15:00:00 | 15:00:20, 15:00:35 | → 15:00:00 |
| **中金所** | 15:15:00 | 15:15:25, 15:15:50 | → 15:15:00 |

## 使用方法

### 基本用法

```python
from utils.data_processor.period_converter import merge_non_trading_data

# 处理A股数据
result = merge_non_trading_data(df, symbol="000001.SZ")

# 处理期货数据
result = merge_non_trading_data(df, symbol="rb2501.SH")

# 处理中金所数据
result = merge_non_trading_data(df, symbol="IF2501.CF")
```

### 直接使用向量化版本

```python
from utils.data_processor.period_converter import merge_non_trading_data_vectorized

result = merge_non_trading_data_vectorized(df, symbol="000001.SZ")
```

## 日志输出

函数执行时会输出详细的处理统计信息：

```
双重边界数据处理统计:
- 休盘边界数据(1分钟): 2 个
- 延时数据(0-60秒): 5 个
- 重叠数据: 0 个
- 总计需要处理: 7 个
```

## 性能特点

1. **高性能**：保持向量化操作的高性能特点
2. **内存效率**：避免数据复制，直接在原数据上操作
3. **准确性**：精确识别和处理两种类型的边界数据
4. **兼容性**：完全向后兼容，不影响现有功能

## 测试验证

### 测试用例

项目提供了完整的测试用例：

- `tests/test_dual_boundary_processing.py`：完整的单元测试
- `tests/simple_boundary_test.py`：简单的功能验证测试

### 运行测试

```bash
# 运行完整测试
python tests/test_dual_boundary_processing.py

# 运行简单测试
python tests/simple_boundary_test.py
```

## 注意事项

1. **数据格式**：输入数据的索引必须为时间字符串格式(YYYYMMDDHHMMSS)
2. **品种识别**：需要提供正确的symbol参数以识别品种类型
3. **时间精度**：处理精度为秒级，能够处理1分钟内的所有延时数据
4. **数据完整性**：合并后的数据保持OHLCV的正确性

## 技术优势

1. **问题解决**：彻底解决录制延时数据遗漏问题
2. **数据完整性**：确保收盘价等关键数据的准确性
3. **业界标准**：符合业界主流的边界数据处理做法
4. **代码简洁**：避免复杂的回退机制，保持代码简洁性

## 后续扩展

1. **配置化**：可以考虑将时间窗口配置化
2. **监控增强**：添加更详细的处理监控和统计
3. **性能优化**：进一步优化大数据集的处理性能
4. **错误处理**：增强异常情况的处理能力
