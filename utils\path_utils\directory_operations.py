#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
目录操作功能模块

提供目录的创建、删除、复制、比较等功能
"""

import os
import sys
import shutil
import logging
import time
from typing import Dict, List, Optional, Set, Tuple, Union
from pathlib import Path

# 将项目根目录添加到Python路径
root_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, root_path)

# 导入其他模块
from utils.path_utils.path_management import normalize_path, is_path_inside
from utils.logger import get_unified_logger

# 设置日志记录器
logger = get_unified_logger(__name__)


def create_directory(dir_path: Union[str, Path], mode: int = 0o755, exist_ok: bool = True) -> bool:
    """
    创建目录
    
    Args:
        dir_path: 目录路径
        mode: 目录权限模式（八进制），默认为0o755
        exist_ok: 如果目录已存在，是否视为成功，默认为True
        
    Returns:
        操作是否成功
    """
    # 标准化路径
    norm_path = normalize_path(dir_path)
    
    try:
        os.makedirs(norm_path, mode=mode, exist_ok=exist_ok)
        logger.info(f"创建目录: {norm_path}")
        return True
    except Exception as e:
        logger.error(f"创建目录失败: {norm_path}, 错误: {str(e)}")
        return False


def delete_directory(dir_path: Union[str, Path], recursive: bool = True) -> bool:
    """
    删除目录
    
    Args:
        dir_path: 目录路径
        recursive: 是否递归删除目录内容，默认为True
        
    Returns:
        操作是否成功
    """
    # 标准化路径
    norm_path = normalize_path(dir_path)
    
    # 检查目录是否存在
    if not os.path.exists(norm_path):
        logger.warning(f"目录不存在，无需删除: {norm_path}")
        return True  # 目录不存在视为删除成功
    
    # 检查目录是否为目录
    if not os.path.isdir(norm_path):
        logger.error(f"路径不是目录: {norm_path}")
        return False
    
    try:
        if recursive:
            shutil.rmtree(norm_path)
            logger.info(f"已删除目录及其内容: {norm_path}")
        else:
            os.rmdir(norm_path)
            logger.info(f"已删除空目录: {norm_path}")
        return True
    except Exception as e:
        logger.error(f"删除目录失败: {norm_path}, 错误: {str(e)}")
        return False


def copy_directory(src_dir: Union[str, Path], dst_dir: Union[str, Path],
                 overwrite: bool = False, ignore_patterns: Optional[List[str]] = None) -> bool:
    """
    复制目录
    
    Args:
        src_dir: 源目录路径
        dst_dir: 目标目录路径
        overwrite: 是否覆盖目标目录，默认为False
        ignore_patterns: 要忽略的文件模式列表，默认为None
        
    Returns:
        操作是否成功
    """
    # 标准化路径
    src_path = normalize_path(src_dir)
    dst_path = normalize_path(dst_dir)
    
    # 检查源目录是否存在
    if not os.path.exists(src_path):
        logger.error(f"源目录不存在: {src_path}")
        return False
    
    # 检查源目录是否为目录
    if not os.path.isdir(src_path):
        logger.error(f"源路径不是目录: {src_path}")
        return False
    
    # 检查目标目录是否已存在
    if os.path.exists(dst_path):
        if not overwrite:
            logger.warning(f"目标目录已存在，未启用覆盖: {dst_path}")
            return False
        
        # 如果启用覆盖，则删除现有目录
        if not delete_directory(dst_path):
            logger.error(f"无法删除现有目标目录: {dst_path}")
            return False
    
    # 创建忽略函数
    def ignore_func(dir, files):
        if ignore_patterns is None:
            return []
        
        import fnmatch
        ignored = []
        for pattern in ignore_patterns:
            ignored.extend(fnmatch.filter(files, pattern))
        return set(ignored)
    
    try:
        shutil.copytree(src_path, dst_path, ignore=ignore_func if ignore_patterns else None)
        logger.info(f"已复制目录: {src_path} -> {dst_path}")
        return True
    except Exception as e:
        logger.error(f"复制目录失败: {src_path} -> {dst_path}, 错误: {str(e)}")
        return False


def move_directory(src_dir: Union[str, Path], dst_dir: Union[str, Path],
                 overwrite: bool = False) -> bool:
    """
    移动目录
    
    Args:
        src_dir: 源目录路径
        dst_dir: 目标目录路径
        overwrite: 是否覆盖目标目录，默认为False
        
    Returns:
        操作是否成功
    """
    # 标准化路径
    src_path = normalize_path(src_dir)
    dst_path = normalize_path(dst_dir)
    
    # 检查源目录是否存在
    if not os.path.exists(src_path):
        logger.error(f"源目录不存在: {src_path}")
        return False
    
    # 检查源目录是否为目录
    if not os.path.isdir(src_path):
        logger.error(f"源路径不是目录: {src_path}")
        return False
    
    # 检查目标目录是否已存在
    if os.path.exists(dst_path):
        if not overwrite:
            logger.warning(f"目标目录已存在，未启用覆盖: {dst_path}")
            return False
        
        # 如果启用覆盖，则删除现有目录
        if not delete_directory(dst_path):
            logger.error(f"无法删除现有目标目录: {dst_path}")
            return False
    
    try:
        shutil.move(src_path, dst_path)
        logger.info(f"已移动目录: {src_path} -> {dst_path}")
        return True
    except Exception as e:
        logger.error(f"移动目录失败: {src_path} -> {dst_path}, 错误: {str(e)}")
        return False


def compare_directories(dir1: Union[str, Path], dir2: Union[str, Path],
                      ignore_patterns: Optional[List[str]] = None) -> Dict[str, List[str]]:
    """
    比较两个目录的内容
    
    Args:
        dir1: 第一个目录路径
        dir2: 第二个目录路径
        ignore_patterns: 要忽略的文件模式列表，默认为None
        
    Returns:
        比较结果字典，包含'only_in_dir1'、'only_in_dir2'和'different'三个键
    """
    import filecmp
    import fnmatch
    
    # 标准化路径
    path1 = normalize_path(dir1)
    path2 = normalize_path(dir2)
    
    # 检查目录是否存在
    if not os.path.exists(path1):
        logger.error(f"目录不存在: {path1}")
        return {'only_in_dir1': [], 'only_in_dir2': [], 'different': []}
    
    if not os.path.exists(path2):
        logger.error(f"目录不存在: {path2}")
        return {'only_in_dir1': [], 'only_in_dir2': [], 'different': []}
    
    # 结果字典
    result = {
        'only_in_dir1': [],
        'only_in_dir2': [],
        'different': []
    }
    
    # 创建目录比较对象
    comparison = filecmp.dircmp(path1, path2)
    
    # 获取忽略模式对应的文件
    def get_ignored_files(files, patterns):
        if not patterns:
            return set()
        
        ignored = set()
        for pattern in patterns:
            for filename in files:
                if fnmatch.fnmatch(filename, pattern):
                    ignored.add(filename)
        return ignored
    
    # 忽略的文件
    ignored_left = get_ignored_files(comparison.left_list, ignore_patterns)
    ignored_right = get_ignored_files(comparison.right_list, ignore_patterns)
    
    # 只在dir1中存在的文件（不包括被忽略的）
    result['only_in_dir1'] = [
        os.path.join(path1, f) for f in comparison.left_only 
        if f not in ignored_left
    ]
    
    # 只在dir2中存在的文件（不包括被忽略的）
    result['only_in_dir2'] = [
        os.path.join(path2, f) for f in comparison.right_only 
        if f not in ignored_right
    ]
    
    # 内容不同的文件
    result['different'] = [
        os.path.join(path1, f) for f in comparison.diff_files
        if f not in ignored_left and f not in ignored_right
    ]
    
    # 递归处理子目录
    for subdir in comparison.common_dirs:
        # 忽略子目录
        if ignore_patterns and any(fnmatch.fnmatch(subdir, pattern) for pattern in ignore_patterns):
            continue
        
        # 递归比较子目录
        subresult = compare_directories(
            os.path.join(path1, subdir),
            os.path.join(path2, subdir),
            ignore_patterns
        )
        
        # 合并结果
        result['only_in_dir1'].extend(subresult['only_in_dir1'])
        result['only_in_dir2'].extend(subresult['only_in_dir2'])
        result['different'].extend(subresult['different'])
    
    return result


def directory_stats(dir_path: Union[str, Path], recursive: bool = True,
                  count_hidden: bool = False) -> Dict[str, Union[int, Dict[str, int]]]:
    """
    获取目录统计信息
    
    Args:
        dir_path: 目录路径
        recursive: 是否递归统计子目录，默认为True
        count_hidden: 是否计算隐藏文件，默认为False
        
    Returns:
        统计信息字典，包含文件数、子目录数、总大小等
    """
    # 标准化路径
    norm_path = normalize_path(dir_path)
    
    # 检查目录是否存在
    if not os.path.exists(norm_path):
        logger.error(f"目录不存在: {norm_path}")
        return {
            'total_files': 0,
            'total_dirs': 0,
            'total_size': 0,
            'file_types': {}
        }
    
    # 检查是否为目录
    if not os.path.isdir(norm_path):
        logger.error(f"路径不是目录: {norm_path}")
        return {
            'total_files': 0,
            'total_dirs': 0,
            'total_size': 0,
            'file_types': {}
        }
    
    # 统计信息
    stats = {
        'total_files': 0,
        'total_dirs': 0,
        'total_size': 0,
        'file_types': {}
    }
    
    # 是否为隐藏文件/目录
    def is_hidden(path):
        return os.path.basename(path).startswith('.')
    
    # 遍历目录
    if recursive:
        for root, dirs, files in os.walk(norm_path):
            # 是否跳过隐藏目录
            if not count_hidden:
                dirs[:] = [d for d in dirs if not is_hidden(d)]
            
            # 统计子目录数量
            stats['total_dirs'] += len(dirs)
            
            # 统计文件
            for file in files:
                # 是否跳过隐藏文件
                if not count_hidden and is_hidden(file):
                    continue
                
                file_path = os.path.join(root, file)
                
                try:
                    # 统计文件数量
                    stats['total_files'] += 1
                    
                    # 统计文件大小
                    file_size = os.path.getsize(file_path)
                    stats['total_size'] += file_size
                    
                    # 统计文件类型
                    file_ext = os.path.splitext(file)[1].lower()
                    if file_ext:
                        # 去掉前导点号
                        file_ext = file_ext[1:]
                        stats['file_types'][file_ext] = stats['file_types'].get(file_ext, 0) + 1
                    else:
                        stats['file_types']['无扩展名'] = stats['file_types'].get('无扩展名', 0) + 1
                except Exception as e:
                    logger.debug(f"统计文件时出错: {file_path}, 错误: {str(e)}")
    else:
        # 仅统计当前目录
        try:
            # 获取目录中的所有项
            items = os.listdir(norm_path)
            
            for item in items:
                item_path = os.path.join(norm_path, item)
                
                # 是否跳过隐藏文件/目录
                if not count_hidden and is_hidden(item):
                    continue
                
                if os.path.isdir(item_path):
                    # 统计子目录数量
                    stats['total_dirs'] += 1
                elif os.path.isfile(item_path):
                    # 统计文件数量
                    stats['total_files'] += 1
                    
                    # 统计文件大小
                    try:
                        file_size = os.path.getsize(item_path)
                        stats['total_size'] += file_size
                    except Exception:
                        pass
                    
                    # 统计文件类型
                    file_ext = os.path.splitext(item)[1].lower()
                    if file_ext:
                        # 去掉前导点号
                        file_ext = file_ext[1:]
                        stats['file_types'][file_ext] = stats['file_types'].get(file_ext, 0) + 1
                    else:
                        stats['file_types']['无扩展名'] = stats['file_types'].get('无扩展名', 0) + 1
        except Exception as e:
            logger.error(f"统计目录时出错: {norm_path}, 错误: {str(e)}")
    
    return stats


def get_latest_modified_files(dir_path: Union[str, Path], count: int = 10,
                            recursive: bool = True, file_pattern: Optional[str] = None) -> List[Dict[str, Union[str, float]]]:
    """
    获取最近修改的文件
    
    Args:
        dir_path: 目录路径
        count: 返回的文件数量，默认为10
        recursive: 是否递归查找子目录，默认为True
        file_pattern: 文件名匹配模式（可使用通配符），如果为None则匹配所有文件
        
    Returns:
        文件信息列表，每个文件包含路径和修改时间
    """
    import fnmatch
    
    # 标准化路径
    norm_path = normalize_path(dir_path)
    
    # 检查目录是否存在
    if not os.path.exists(norm_path):
        logger.error(f"目录不存在: {norm_path}")
        return []
    
    # 收集文件信息
    files_info = []
    
    # 是否匹配文件模式
    def matches_pattern(filename):
        if file_pattern is None:
            return True
        return fnmatch.fnmatch(filename, file_pattern)
    
    # 遍历目录
    if recursive:
        for root, _, files in os.walk(norm_path):
            for file in files:
                if matches_pattern(file):
                    file_path = os.path.join(root, file)
                    try:
                        mtime = os.path.getmtime(file_path)
                        files_info.append({
                            'path': file_path,
                            'mtime': mtime
                        })
                    except Exception as e:
                        logger.debug(f"获取文件修改时间时出错: {file_path}, 错误: {str(e)}")
    else:
        # 仅查找当前目录
        for item in os.listdir(norm_path):
            item_path = os.path.join(norm_path, item)
            if os.path.isfile(item_path) and matches_pattern(item):
                try:
                    mtime = os.path.getmtime(item_path)
                    files_info.append({
                        'path': item_path,
                        'mtime': mtime
                    })
                except Exception as e:
                    logger.debug(f"获取文件修改时间时出错: {item_path}, 错误: {str(e)}")
    
    # 按修改时间排序
    files_info.sort(key=lambda x: x['mtime'], reverse=True)
    
    # 返回指定数量的文件
    return files_info[:count]