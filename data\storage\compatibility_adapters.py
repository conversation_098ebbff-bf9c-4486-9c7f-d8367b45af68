#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
兼容性适配器

为统一数据保存器提供向后兼容的API适配器，确保现有代码无需修改即可使用新的统一架构。

设计理念：
- 保持100%向后兼容性
- 内部调用统一数据保存器
- 提供迁移提示和最佳实践建议
- 逐步引导用户迁移到新API

版本: v3.0
作者: Augment AI
日期: 2025-08-05
"""

import pandas as pd
import warnings
from typing import Optional, Dict, Any, Union
from datetime import datetime

from utils.logger import get_unified_logger, LogTarget
from data.storage.unified_data_saver import (
    get_unified_data_saver, 
    SaveConfig, 
    SaveStrategy,
    save_data_unified
)

# 创建统一日志记录器
logger = get_unified_logger(__name__, enhanced=True)


def _show_migration_tip(function_name: str, recommended_approach: str):
    """显示迁移提示"""
    tip_message = (
        f"建议迁移到统一数据保存接口: {recommended_approach}\n"
        f"新接口提供更好的性能和功能，详见文档: docs/数据保存架构重构指南.md"
    )
    logger.info(LogTarget.FILE, f"{function_name} 调用 - {tip_message}")


# 适配器函数
def save_to_partition_adapter(
    df: pd.DataFrame,
    data_root: str,
    symbol: str,
    period: str,
    timestamp: Optional[str] = None,
    engine: str = 'pyarrow',
    compression: str = 'snappy',
    metadata: Optional[Dict[str, Any]] = None,
    data_type: str = "raw",
    adj_type: Optional[str] = None
) -> bool:
    """
    save_to_partition函数的适配器
    
    保持原有API不变，内部调用统一数据保存器。
    """
    _show_migration_tip(
        "save_to_partition", 
        "save_data_unified(df, data_root, symbol, period, strategy=SaveStrategy.SINGLE_PARTITION)"
    )
    
    try:
        # 创建配置
        config = SaveConfig(
            strategy=SaveStrategy.SINGLE_PARTITION,
            parallel=False,
            engine=engine,
            compression=compression,
            data_type=data_type,
            adj_type=adj_type
        )
        
        # 如果指定了timestamp，需要特殊处理
        if timestamp:
            # 对于明确指定timestamp的情况，使用原有逻辑
            from data.storage.parquet_storage import save_to_partition as _original_save_to_partition
            return _original_save_to_partition(
                df, data_root, symbol, period, timestamp, engine, compression, metadata, data_type, adj_type
            )
        
        # 使用统一数据保存器
        saver = get_unified_data_saver()
        result = saver.save(df, data_root, symbol, period, config)
        
        return result.success
        
    except Exception as e:
        logger.error(LogTarget.FILE, f"save_to_partition适配器执行失败: {e}")
        return False


def save_data_by_partition_adapter(
    df: pd.DataFrame,
    data_root: str,
    symbol: str,
    period: str,
    engine: str = 'pyarrow',
    compression: str = 'snappy',
    metadata: Optional[Dict[str, Any]] = None,
    parallel: Optional[bool] = None,
    use_threading: Optional[bool] = None,
    max_workers: Optional[int] = None,
    data_type: str = "raw",
    adj_type: Optional[str] = None
) -> Dict[str, str]:
    """
    save_data_by_partition函数的适配器
    
    保持原有API不变，内部调用统一数据保存器。
    """
    _show_migration_tip(
        "save_data_by_partition", 
        "save_data_unified(df, data_root, symbol, period, strategy=SaveStrategy.AUTO)"
    )
    
    try:
        # 创建配置
        config = SaveConfig(
            strategy=SaveStrategy.AUTO,
            parallel=parallel if parallel is not None else True,
            use_threading=use_threading if use_threading is not None else True,
            max_workers=max_workers if max_workers is not None else 4,
            engine=engine,
            compression=compression,
            data_type=data_type,
            adj_type=adj_type
        )
        
        # 使用统一数据保存器
        saver = get_unified_data_saver()
        result = saver.save(df, data_root, symbol, period, config)
        
        return result.saved_partitions if result.success else {}
        
    except Exception as e:
        logger.error(LogTarget.FILE, f"save_data_by_partition适配器执行失败: {e}")
        return {}


def append_to_partition_adapter(
    df: pd.DataFrame,
    data_root: str,
    symbol: str,
    period: str,
    timestamp: Optional[str] = None,
    engine: str = 'pyarrow',
    compression: str = 'snappy',
    metadata: Optional[Dict[str, Any]] = None
) -> bool:
    """
    append_to_partition函数的适配器
    
    注意：追加功能在新架构中统一为保存操作。
    """
    _show_migration_tip(
        "append_to_partition", 
        "save_data_unified(df, data_root, symbol, period) # 新架构自动处理追加逻辑"
    )
    
    # 追加操作统一为保存操作
    return save_to_partition_adapter(
        df, data_root, symbol, period, timestamp, engine, compression, metadata
    )


class ParquetStorageAdapter:
    """
    ParquetStorage类的适配器
    
    保持原有类接口不变，内部调用统一数据保存器。
    """
    
    def __init__(self, base_dir: str = "data"):
        """初始化适配器"""
        self.base_dir = base_dir
        _show_migration_tip(
            "ParquetStorage.__init__", 
            "直接使用 save_data_unified() 函数，无需创建类实例"
        )
    
    def save_data_by_partition(self, 
                              dataframe: pd.DataFrame, 
                              symbol: str, 
                              period: str, 
                              partition_columns=None, 
                              append=False, 
                              data_type="raw", 
                              adj_type=None) -> bool:
        """类方法版本的适配器"""
        _show_migration_tip(
            "ParquetStorage.save_data_by_partition", 
            "save_data_unified(dataframe, data_root, symbol, period)"
        )
        
        try:
            config = SaveConfig(
                strategy=SaveStrategy.AUTO,
                parallel=True,
                data_type=data_type,
                adj_type=adj_type
            )
            
            saver = get_unified_data_saver()
            result = saver.save(dataframe, self.base_dir, symbol, period, config)
            
            return result.success
            
        except Exception as e:
            logger.error(LogTarget.FILE, f"ParquetStorage.save_data_by_partition适配器执行失败: {e}")
            return False
    
    def save_data_by_partition_parallel(self, 
                                       dataframe: pd.DataFrame, 
                                       symbol: str, 
                                       period: str,
                                       partition_columns=None, 
                                       num_workers=4,
                                       chunk_size=None, 
                                       data_type="raw", 
                                       adj_type=None) -> bool:
        """并行版本类方法的适配器"""
        _show_migration_tip(
            "ParquetStorage.save_data_by_partition_parallel", 
            "save_data_unified(dataframe, data_root, symbol, period, parallel=True)"
        )
        
        try:
            config = SaveConfig(
                strategy=SaveStrategy.PARALLEL,
                parallel=True,
                max_workers=num_workers,
                data_type=data_type,
                adj_type=adj_type
            )
            
            saver = get_unified_data_saver()
            result = saver.save(dataframe, self.base_dir, symbol, period, config)
            
            return result.success
            
        except Exception as e:
            logger.error(LogTarget.FILE, f"ParquetStorage.save_data_by_partition_parallel适配器执行失败: {e}")
            return False


def save_data_to_parquet_adapter(data: Dict, period: str, compression: str = 'snappy', 
                                has_new_data: bool = True) -> Dict[str, str]:
    """
    save_data_to_parquet函数的适配器
    """
    _show_migration_tip(
        "save_data_to_parquet", 
        "使用 save_data_unified() 循环处理多个股票数据"
    )
    
    try:
        from config.settings import DATA_ROOT
        save_paths = {}
        
        for symbol, df in data.items():
            if hasattr(df, 'empty') and not df.empty:
                result = save_data_unified(
                    df=df,
                    data_root=DATA_ROOT,
                    symbol=symbol,
                    period=period,
                    strategy=SaveStrategy.AUTO,
                    parallel=True,
                    compression=compression
                )
                
                if result.success and result.saved_partitions:
                    # 返回第一个分区路径（保持兼容性）
                    save_paths[symbol] = list(result.saved_partitions.values())[0]
        
        return save_paths
        
    except Exception as e:
        logger.error(LogTarget.FILE, f"save_data_to_parquet适配器执行失败: {e}")
        return {}


# 迁移指南函数
def show_migration_guide():
    """显示完整的迁移指南"""
    guide = """
    ========== 数据保存API迁移指南 ==========
    
    旧API -> 新API:
    
    1. save_to_partition() -> save_data_unified(strategy=SaveStrategy.SINGLE_PARTITION)
    2. save_data_by_partition() -> save_data_unified(strategy=SaveStrategy.AUTO)
    3. ParquetStorage.save_data_by_partition_parallel() -> save_data_unified(parallel=True)
    
    新API优势:
    - 统一接口，减少选择困难
    - 智能策略选择，自动优化性能
    - 完善的跨日期数据分组
    - 更好的错误处理和日志记录
    
    详细文档: docs/数据保存架构重构指南.md
    ==========================================
    """
    print(guide)
    logger.info(LogTarget.FILE, "显示数据保存API迁移指南")


# 便捷的迁移检查函数
def check_migration_status(module_path: str = None):
    """检查模块的迁移状态"""
    if module_path:
        logger.info(LogTarget.FILE, f"检查模块 {module_path} 的迁移状态")
        # 这里可以添加代码扫描逻辑，检查是否还在使用旧API
    else:
        logger.info(LogTarget.FILE, "建议运行全项目迁移状态检查")
