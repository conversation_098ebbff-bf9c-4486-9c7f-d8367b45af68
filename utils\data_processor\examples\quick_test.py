#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
快速测试period_converter修改后的功能
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime
import traceback

# 相对导入
try:
    sys.path.append(os.path.join(os.path.dirname(__file__), ".."))
    print(f"添加路径: {os.path.join(os.path.dirname(__file__), '..')}")
    from period_converter import resample_1m_kline
    print("成功导入resample_1m_kline函数")
except Exception as e:
    print(f"导入失败: {e}")
    traceback.print_exc()
    sys.exit(1)

# 创建测试数据


def create_test_data():
    # 创建日期范围
    start_time = datetime.now().replace(hour=9, minute=30, second=0, microsecond=0)
    date_range = pd.date_range(start=start_time, periods=100, freq='1min')

    # 生成随机数据
    base_price = 100.0
    data = np.random.normal(0, 1, 100).cumsum() + base_price

    # 创建DataFrame
    df = pd.DataFrame({
        'open': data,
        'high': data + np.random.rand(100),
        'low': data - np.random.rand(100),
        'close': data + np.random.normal(0, 0.5, 100),
        'volume': np.random.randint(1000, 10000, 100),
    })

    # 添加时间列并设置索引
    df['time'] = date_range.astype(np.int64) // 10**6  # 毫秒时间戳
    df.index = date_range.strftime('%Y%m%d%H%M%S')  # YYYYMMDDHHMMSS格式的字符串

    return df

# 测试函数


def test_resample():
    # 创建测试数据
    df_1m = create_test_data()
    print("原始数据结构:")
    print(f"索引类型: {type(df_1m.index)}")
    print(f"索引示例: {df_1m.index[:3].tolist()}")
    print(f"time列类型: {type(df_1m['time'].iloc[0])}")
    print(f"time列示例: {df_1m['time'].iloc[:3].tolist()}")

    # 调用重采样函数
    df_5m = resample_1m_kline(df_1m, 5)

    # 检查结果
    print("\n重采样后数据结构:")
    print(f"索引类型: {type(df_5m.index)}")
    print(f"索引示例: {df_5m.index[:3].tolist()}")
    print(f"time列类型: {type(df_5m['time'].iloc[0])}")
    print(f"time列示例: {df_5m['time'].iloc[:3].tolist()}")

    # 判断格式是否保持一致
    index_format_ok = all(len(idx) == 14 and idx.isdigit() for idx in df_5m.index)
    time_format_ok = all(isinstance(t, (int, np.int64))
                         and t > 1e12 for t in df_5m['time'])

    print("\n格式检查:")
    print(f"索引格式正确: {index_format_ok}")
    print(f"time列格式正确: {time_format_ok}")


if __name__ == "__main__":
    test_resample()
