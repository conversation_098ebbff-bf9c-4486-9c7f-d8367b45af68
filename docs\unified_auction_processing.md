# 统一集合竞价数据处理技术文档

## 概述

本文档详细说明了统一集合竞价数据处理系统的设计原理、实现方案和使用方法。该系统支持A股、期货、中金所等所有品种的集合竞价数据处理，确保数据的准确性和一致性。

## 背景问题

### 原有问题
1. **重采样阶段**：tick数据重采样时使用标准OHLC计算（first/max/min/last），导致集合竞价时段出现不一致的价格（如0.00价格）
2. **休盘合并阶段**：休盘数据向前合并时没有考虑集合竞价边界时间的特殊性，使用通用合并策略
3. **品种覆盖不全**：只处理A股开盘集合竞价，缺少收盘集合竞价、期货集合竞价、中金所集合竞价的处理

### 集合竞价特殊性
集合竞价是一次性撮合成交，不存在价格变化过程，因此：
- **OHLC一致性**：开盘价、最高价、最低价、收盘价都应该等于最终的集合竞价成交价格
- **边界时间处理**：集合竞价结束时间点的下一分钟数据合并时，应使用集合竞价的特殊处理逻辑

## 系统架构

### 核心组件

#### 1. 统一识别框架 (`utils/time_formatter/trading_time.py`)
- `get_all_auction_periods()`: 获取指定品种的所有集合竞价时间段
- `is_auction_period_unified()`: 统一的集合竞价时段判断
- `get_auction_boundary_times()`: 获取集合竞价边界时间点
- `is_auction_boundary_time()`: 判断是否为集合竞价边界时间
- `is_auction_period_batch()`: 向量化批量集合竞价时段判断
- `is_auction_boundary_time_batch()`: 向量化批量边界时间判断

#### 2. 重采样处理 (`utils/data_processor/period_converter.py`)
- `resample_tick_data()`: 支持集合竞价特殊处理的tick数据重采样
- 集合竞价时段OHLC统一使用最后价格

#### 3. 休盘合并处理 (`utils/data_processor/period_converter.py`)
- `_vectorized_merge_ohlcv()`: 支持集合竞价边界时间特殊处理的OHLCV合并
- 集合竞价边界时间OHLC都使用后一个数据的价格

## 支持的集合竞价时段

### A股集合竞价
- **开盘集合竞价**：9:15-9:30
  - 边界时间：9:30
  - 处理方式：向后合并到9:30，OHLC使用最终成交价格
- **收盘集合竞价**：14:57-15:00
  - 边界时间：15:00
  - 处理方式：15:01数据向前合并到15:00时，OHLC使用15:01的价格

### 期货集合竞价
- **日盘集合竞价**：8:55-9:00（仅day_only品种）
  - 边界时间：9:00
  - 处理方式：9:01数据向前合并到9:00时，OHLC使用9:01的价格
- **夜盘集合竞价**：20:55-21:00（most/metals/precious品种）
  - 边界时间：21:00
  - 处理方式：21:01数据向前合并到21:00时，OHLC使用21:01的价格

### 中金所集合竞价
- **集合竞价**：9:25-9:30
  - 边界时间：9:30
  - 处理方式：9:31数据向前合并到9:30时，OHLC使用9:31的价格

## 实现原理

### 1. 重采样阶段处理

```python
# 检测品种类型和期货分类
symbol_type = detect_symbol_type(symbol)
futures_category = detect_futures_category(symbol) if symbol_type == 'futures' else 'most'

# 向量化判断集合竞价时段
auction_mask = is_auction_period_batch(
    pd.Series(resampled_index), 
    symbol_type=symbol_type, 
    futures_category=futures_category
)

# 应用集合竞价特殊处理：OHLC都使用最后价格
if auction_mask.any():
    auction_indices = auction_mask[auction_mask].index
    for idx in auction_indices:
        if idx in close_prices.index and pd.notna(close_prices[idx]) and close_prices[idx] > 0:
            final_price = close_prices[idx]
            open_prices[idx] = final_price
            high_prices[idx] = final_price
            low_prices[idx] = final_price
```

### 2. 休盘合并阶段处理

```python
# 检测是否为集合竞价边界时间
target_dt = smart_to_datetime(target_time_str, format='%Y%m%d%H%M%S')
symbol_type = detect_symbol_type(symbol)
futures_category = detect_futures_category(symbol) if symbol_type == 'futures' else 'most'

is_auction_boundary = is_auction_boundary_time(
    target_dt, symbol_type=symbol_type, futures_category=futures_category
)

# 根据是否为集合竞价边界时间选择合并策略
if is_auction_boundary:
    # 集合竞价边界时间特殊处理：OHLC都使用后一个数据的价格
    ohlc_columns = ['open', 'high', 'low', 'close']
    for col in ohlc_columns:
        if col in row1 and col in row2 and pd.notna(row2[col]) and row2[col] > 0:
            merged_row[col] = row2[col]
else:
    # 一般休盘时间标准合并策略
    # high取max，low取min，close取后者
```

## 性能优化

### 1. 缓存机制
- 使用`@lru_cache`装饰器缓存集合竞价时段查询结果
- 使用全局缓存字典避免重复计算

### 2. 向量化处理
- 批量判断集合竞价时段，避免逐个时间点判断
- 使用pandas向量化操作提升性能

### 3. 性能监控
- 使用`@function_performance_monitor`装饰器监控关键函数性能
- 记录处理耗时和数据量统计

## 使用示例

### 基本使用
```python
from utils.data_processor.period_converter import resample_tick_data

# tick数据重采样，自动处理集合竞价
result = resample_tick_data(tick_df, '1m', '000001.SZ')
```

### 集合竞价时段查询
```python
from utils.time_formatter.trading_time import get_all_auction_periods, is_auction_period_unified

# 获取A股集合竞价时段
stock_periods = get_all_auction_periods('stock', 'most')
# 结果: [(time(9, 15), time(9, 30)), (time(14, 57), time(15, 0))]

# 判断指定时间是否为集合竞价
is_auction = is_auction_period_unified(datetime(2025, 7, 25, 9, 20), 'stock', 'most')
# 结果: True
```

### 向量化批量处理
```python
from utils.time_formatter.trading_time import is_auction_period_batch

# 批量判断集合竞价时段
time_series = pd.Series([datetime(2025, 7, 25, 9, 20), datetime(2025, 7, 25, 10, 30)])
auction_mask = is_auction_period_batch(time_series, 'stock', 'most')
# 结果: [True, False]
```

## 测试验证

### 测试套件
- `tests/test_unified_auction_processing.py`: 完整的统一集合竞价处理测试
- `tests/test_auction_simple.py`: 简化版基本功能测试

### 测试覆盖
- ✅ 集合竞价时段识别功能
- ✅ 集合竞价边界时间识别功能
- ✅ 向量化批量处理功能
- ✅ OHLCV合并特殊处理功能
- ✅ 所有品种的集合竞价处理

## 注意事项

1. **品种识别**：系统会自动识别品种类型，但需要确保symbol格式正确
2. **时间格式**：时间字符串需要使用YYYYMMDDHHMMSS格式
3. **数据质量**：确保输入数据的时间字段和价格字段完整有效
4. **性能考虑**：大批量数据处理时建议使用向量化批量函数

## 更新日志

- **2025-07-25**: 实现统一集合竞价数据处理系统
- **2025-07-23**: 实现A股开盘集合竞价向后合并功能
- **2025-07-22**: 修复集合竞价OHLC价格不一致问题
