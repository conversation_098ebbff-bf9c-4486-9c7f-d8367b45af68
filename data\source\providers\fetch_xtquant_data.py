#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
迅投量化数据获取器实现

提供对迅投量化平台数据的访问功能
"""

# import asyncio # 未使用
import logging
import os
import sys
from typing import Dict, List, Optional, Union

import numpy as np
import pandas as pd

# 标准化导入xtquant (属于第三方库，放在 sys.path 修改前)
from xtquant import xtdata as xt_data  # noqa: E402

# from tqdm import tqdm # 未使用

xt_data.enable_hello = False

# 将项目根目录添加到Python路径
root_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.insert(0, root_path)

# 本地模块导入
from data.source.base import BaseFetcher  # noqa: E402

# from data.storage.database_manager import DatabaseManager # DatabaseManager 未使用，移除
# from utils.config_loader import ConfigLoader # ConfigLoader 未使用，移除

# 导入日志模块
from utils.logger import get_unified_logger
from utils.smart_time_converter import smart_to_datetime


class XtQuantFetcher(BaseFetcher):
    """迅投量化数据获取器

    提供对迅投量化平台行情数据的访问功能
    """

    def __init__(self, api_wrapper=None):
        """初始化迅投量化数据获取器

        检查SDK是否可用，并初始化日志记录器
        """
        super().__init__()
        self.api_wrapper = api_wrapper
        self.logger = get_unified_logger(__name__)
        # self.db_manager = db_manager # DatabaseManager 未使用，移除
        # 移除XTQUANT_AVAILABLE检查，因为导入失败会在启动时直接报错
        self.logger.info("迅投量化数据获取器初始化完成")
        self.subscribed_symbols = set()

    def download_history_data(
        self,
        symbols: Union[str, List[str]],
        period: str = "1d",
        start_time: Optional[str] = None,
        end_time: Optional[str] = None,
        output_dir: Optional[str] = None
    ) -> Dict[str, bool]:
        """下载历史数据到本地 (仅下载，不读取)

        Args:
            symbols: 股票代码或代码列表
            period: 数据周期，支持的值参见BaseFetcher.PERIOD_*常量
            start_time: 开始时间，格式 YYYYMMDD 或 YYYY-MM-DD
            end_time: 结束时间，格式 YYYYMMDD 或 YYYY-MM-DD
            output_dir: 输出目录，默认为None，表示使用迅投默认目录

        Returns:
            Dict[str, bool]: 每个代码的下载尝试结果 (True表示尝试下载，不保证成功)
        """
        self.logger.info(
            f"开始下载历史数据: {symbols}, 周期: {period}, "
            f"开始时间: {start_time}, 结束时间: {end_time}"
        )

        # 移除XTQUANT_AVAILABLE和xt is None的检查

        stock_list = [symbols] if isinstance(symbols, str) else symbols
        start_time_str = (str(self._format_date_to_int(start_time))
                          if start_time else "")
        end_time_str = (str(self._format_date_to_int(end_time))
                        if end_time else "")

        results = {}
        overall_success = True

        try:
            # 尝试转换周期，这会验证周期是否有效
            xt_period = self._convert_period(period)

            self.logger.info(f"调用 download_history_data2: {stock_list} ...")
            xt_data.download_history_data2(
                stock_list=stock_list,
                period=xt_period,
                start_time=start_time_str,
                end_time=end_time_str,
            )
            results = {s: True for s in stock_list}
            self.logger.info("download_history_data2 调用完成")

        except ValueError as ve:
            # 处理周期无效的异常
            error_msg = str(ve)
            self.logger.error(f"周期参数错误: {error_msg}")
            overall_success = False
            results = {s: False for s in stock_list}

        except Exception as e:
            self.logger.error(f"调用 download_history_data2 失败: {str(e)}", exc_info=True)
            overall_success = False
            results = {s: False for s in stock_list}

        success_count = sum(1 for success in results.values() if success)
        self.logger.info(
            f"下载尝试完成: 尝试 {success_count}/{len(stock_list)} 个标的。"
            f"API 调用状态: {'成功' if overall_success else '失败'}"
        )
        return results

    def get_local_data(
        self,
        symbols: Union[str, List[str]],
        period: str = "1d",
        start_time: Optional[str] = None,
        end_time: Optional[str] = None,
        dividend_type: str = "none",
        fields: Optional[List[str]] = None
    ) -> Optional[Dict[str, pd.DataFrame]]:
        """获取本地已下载的历史数据

        Args:
            symbols: 股票代码或代码列表
            period: 数据周期，支持的值参见BaseFetcher.PERIOD_*常量
            start_time: 开始时间，格式 YYYYMMDD 或 YYYY-MM-DD
            end_time: 结束时间，格式 YYYYMMDD 或 YYYY-MM-DD
            dividend_type: 复权类型，支持的值参见BaseFetcher.DIVIDEND_*常量
            fields: 需要的字段列表，默认为None表示所有字段

        Returns:
            Optional[Dict[str, pd.DataFrame]]: 本地历史数据字典。
                                              键为字段名，值为包含所有请求股票的DataFrame。
                                              若无数据或读取失败，返回None。
        """
        self.logger.info(
            f"尝试读取本地数据: {symbols}, 周期: {period}, "
            f"时间: {start_time}-{end_time}, 复权: {dividend_type}"
        )

        # 移除XTQUANT_AVAILABLE和xt is None的检查

        stock_list = [symbols] if isinstance(symbols, str) else symbols
        start_time_str = (str(self._format_date_to_int(start_time))
                          if start_time else "")
        end_time_str = (str(self._format_date_to_int(end_time))
                        if end_time else "")
        xt_dividend = self._convert_dividend_type(dividend_type)

        field_list_to_fetch = fields if fields is not None else []

        try:
            # 尝试转换周期，这会验证周期是否有效
            xt_period = self._convert_period(period)

            self.logger.info(f"调用 get_local_data: {stock_list} ...")
            local_data = xt_data.get_local_data(
                field_list=field_list_to_fetch,
                stock_list=stock_list,
                period=xt_period,
                start_time=start_time_str,
                end_time=end_time_str,
                dividend_type=xt_dividend,
            )

            if not local_data:
                self.logger.warning("get_local_data 未返回任何数据")
                return None

            filtered_data = {field: df for field, df in local_data.items()
                             if df is not None and not df.empty}

            if not filtered_data:
                self.logger.warning("读取到的本地数据为空或所有字段均为空")
                return None

            self.logger.info(f"成功读取本地数据，包含字段: {list(filtered_data.keys())}")
            # 统一时间格式和时区
            for field, df in filtered_data.items():
                if 'time' in df.columns:
                    # df['datetime'] = smart_to_datetime(df['time'], unit='ms').dt.tz_localize('UTC').dt.tz_convert('Asia/Shanghai') # noqa: E501
                    datetime_index = smart_to_datetime(df['time'], unit='ms')
                    if hasattr(datetime_index, 'tz_localize'):
                        # DatetimeIndex 直接有时区方法
                        df['datetime'] = datetime_index.tz_localize('UTC').tz_convert('Asia/Shanghai')
                    else:
                        # Series 需要使用 .dt 访问器
                        df['datetime'] = datetime_index.dt.tz_localize('UTC').dt.tz_convert('Asia/Shanghai')
                    # logger.debug(f"转换后时间字段类型: {df['datetime'].dtype}")
                else:
                    self.logger.warning(f"读取到的数据中没有时间字段: {field}")
            return filtered_data

        except ValueError as ve:
            # 处理周期无效的异常
            error_msg = str(ve)
            self.logger.error(f"周期参数错误: {error_msg}")
            return None

        except Exception as e:
            self.logger.error(f"读取本地数据失败 (get_local_data): {str(e)}", exc_info=True)
            return None

    def subscribe_quote(self, symbols: Union[str, List[str]]) -> bool:
        """订阅实时行情

        Args:
            symbols: 股票代码或代码列表

        Returns:
            bool: 是否成功订阅
        """
        if isinstance(symbols, str):
            symbols = [symbols]

        self.logger.info(f"订阅行情: {symbols}")

        try:
            for symbol in symbols:
                # 移除对xt和hasattr的检查，因为导入失败会直接报错
                result = xt_data.subscribe_quote(symbol)
                if result:
                    self.subscribed_symbols.add(symbol)
                    self.logger.debug(f"订阅成功: {symbol}")
                else:
                    self.logger.warning(f"订阅失败: {symbol}")
            return True
        except Exception as e:
            self.logger.error(f"订阅行情失败: {str(e)}")
            return False

    def unsubscribe_quote(self, symbols: Union[str, List[str]]) -> bool:
        """取消订阅实时行情

        Args:
            symbols: 股票代码或代码列表

        Returns:
            bool: 是否成功取消订阅
        """
        if isinstance(symbols, str):
            symbols = [symbols]

        self.logger.info(f"取消订阅行情: {symbols}")

        try:
            for symbol in symbols:
                if symbol in self.subscribed_symbols:
                    self.subscribed_symbols.remove(symbol)
                    self.logger.debug(f"取消订阅: {symbol}")
            return True
        except Exception as e:
            self.logger.error(f"取消订阅行情失败: {str(e)}")
            return False

    def get_trading_dates(
        self,
        market: str,
        start_date: str,
        end_date: str
    ) -> List[str]:
        """获取交易日历

        Args:
            market: 市场代码，如'SH'、'SZ'
            start_date: 开始日期，格式为YYYY-MM-DD
            end_date: 结束日期，格式为YYYY-MM-DD

        Returns:
            List[str]: 交易日列表，格式为YYYY-MM-DD
        """
        self.logger.info(f"获取交易日历: 市场={market}, 开始={start_date}, 结束={end_date}")

        # 移除XTQUANT_AVAILABLE和xt is None的检查

        try:
            start_date_str = str(self._format_date_to_int(
                start_date)) if start_date else ""
            end_date_str = str(self._format_date_to_int(end_date)) if end_date else ""
            trading_days_int = xt_data.get_trading_dates(
                market, start_date_str, end_date_str
            )
            result = [self._format_int_to_date(day) for day in trading_days_int]
            self.logger.debug(f"获取到{len(result)}个交易日")
            return result
        except Exception as e:
            self.logger.error(f"获取交易日历失败: {str(e)}")
            return []

    def _convert_period(self, period: str) -> str:
        """转换周期格式为迅投支持的格式

        Args:
            period: 周期，支持的值参见BaseFetcher.PERIOD_*常量

        Returns:
            str: 迅投SDK支持的周期格式

        Raises:
            ValueError: 当提供的周期值不被迅投API支持时
        """
        period_map = {
            self.PERIOD_TICK: "tick",
            self.PERIOD_1M: "1m",
            self.PERIOD_5M: "5m",
            self.PERIOD_15M: "15m",
            self.PERIOD_30M: "30m",
            self.PERIOD_1H: "1h",
            self.PERIOD_1D: "1d",
            self.PERIOD_1W: "1w",
            self.PERIOD_1MON: "1mon"
        }

        # 首先尝试从映射中查找
        converted_period = period_map.get(period)
        if converted_period is not None:
            return converted_period

        # 如果用户直接使用的是迅投支持的周期字符串格式，进行验证
        valid_periods = set(period_map.values())
        valid_periods.update(["3m", "1q", "1hy", "1y"])  # 添加其他迅投支持但我们没有映射的周期

        if period in valid_periods:
            return period

        # 如果周期既不在我们的映射中，也不是迅投直接支持的周期，则抛出异常
        supported_periods = sorted(list(valid_periods))
        raise ValueError(
            f"不支持的数据周期: '{period}'。支持的周期有: {', '.join(supported_periods)}"
        )

    def _convert_dividend_type(self, dividend_type: str) -> str:
        """转换复权类型为迅投支持的格式

        Args:
            dividend_type: 复权类型，支持的值参见BaseFetcher.DIVIDEND_*常量

        Returns:
            str: 迅投SDK支持的复权类型
        """
        dividend_map = {
            self.DIVIDEND_NONE: "none",
            self.DIVIDEND_FRONT: "front",
            self.DIVIDEND_BACK: "back"
        }
        return dividend_map.get(dividend_type, dividend_type)

    def _format_date_to_int(self, date_str: Optional[str]) -> Optional[int]:
        """将日期字符串转换为整数格式 YYYYMMDD

        Args:
            date_str: 日期字符串，格式 YYYY-MM-DD 或 YYYYMMDD, 或 None

        Returns:
            Optional[int]: 整数格式的日期，例如 20220101, 或 None
        """
        if isinstance(date_str, int):
            return date_str

        if date_str:
            try:
                clean_date_str = date_str.replace("-", "")
                clean_date_str = clean_date_str.replace("/", "")
                clean_date_str = clean_date_str.replace(".", "")
                if len(clean_date_str) == 8 and clean_date_str.isdigit():
                    return int(clean_date_str)
                else:
                    self.logger.warning(
                        f"无效的日期格式: {date_str}, 期望 YYYYMMDD 或 YYYY-MM-DD"
                    )
                    return None
            except ValueError:
                self.logger.warning(f"无法将日期 '{date_str}' 转换为整数")
                return None
        return None

    def _format_int_to_date(self, date_int: int) -> str:
        """将整数格式的日期 YYYYMMDD 转换为字符串 YYYY-MM-DD

        Args:
            date_int: 整数格式的日期，例如20220101

        Returns:
            str: 日期字符串，格式为YYYY-MM-DD
        """
        date_str = str(date_int)
        if len(date_str) == 8:
            return f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:]}"
        return str(date_int)

    def _get_default_fields(self, period: str) -> List[str]:
        """根据周期获取默认字段列表

        Args:
            period: 数据周期

        Returns:
            List[str]: 默认字段列表
        """
        if period == self.PERIOD_TICK:
            return [
                "time", "current", "volume", "amount", "position", "trade_type",
                "buy_price", "buy_volume", "sell_price", "sell_volume"
            ]
        else:
            return ["time", "open", "high", "low", "close",
                    "volume", "amount", "settle", "position"]
