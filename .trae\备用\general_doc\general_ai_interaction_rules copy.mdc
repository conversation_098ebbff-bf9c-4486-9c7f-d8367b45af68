---
description: 
globs: "*"
alwaysApply: true
---
# AI交互规范

## 0. 沟通语言规范
- **AI助手必须始终使用中文进行回复。** 所有交互都应以中文进行。

## 1. 强制执行工作流程

### 1.1 统一工作流程框架

本节提供了一个结构化的、不可跳过的工作流程框架，AI助手**必须严格按照以下顺序执行**每个步骤。每个步骤完成后必须在内部标记为已完成，才能进入下一步骤。这种顺序执行模式确保不会遗漏关键步骤。

```
┌─────────────────────────────────┐
│ 强制工作流程框架                │
└─────────────────────────────────┘
         ↓
┌─────────────────────────────────┐
│ 步骤0: 任务状态检查与上下文加载 │ ← 不可跳过
└─────────────────────────────────┘
         ↓
┌─────────────────────────────────┐
│ 步骤1: 需求分析与规则识别       │ ← 不可跳过
└─────────────────────────────────┘
         ↓
┌─────────────────────────────────┐
│ 步骤2: 执行计划与用户确认       │ ← 不可跳过
└─────────────────────────────────┘
         ↓
┌─────────────────────────────────┐
│ 步骤3: 代码实现与变更记录       │ ← 不可跳过
└─────────────────────────────────┘
         ↓
┌─────────────────────────────────┐
│ 步骤4: 文档更新                 │ ← 不可跳过
└─────────────────────────────────┘
         ↓
┌─────────────────────────────────┐
│ 步骤5: 质量验证与完成确认       │ ← 不可跳过
└─────────────────────────────────┘
```

### 1.2 各步骤详细说明

#### 1.2.0 步骤0: 任务状态检查与上下文加载 [必须执行]

**输入**: 用户请求、项目环境
**输出**: 任务状态与上下文报告

**必须执行的操作**:
1. **项目状态自动检查**:
   * **检查任务状态文件**: 检查`项目根目录/utils/tasks`目录下是否存在`TASK_STATUS.md`文件，获取最新的任务状态信息。首先检查utils/tasks目录（推荐位置），如果不存在再检查项目根目录。
   * **检查项目工具**: 检查`utils/tasks`目录下是否存在任务检查点系统相关工具，如`checkpoint_manager.py`、`tool_monitor.py`、`auto_save.py`、`decorators.py`和`report_generator.py`等。
   * **确认项目结构**: 分析项目目录结构和关键文件位置，确保了解项目上下文。
2. **任务管理工具加载**:
   * 如果发现任务检查点系统相关工具，分析其功能和使用方法。
   * 如果存在`utils/tasks/README.md`，查阅其中的工具说明。
   * 如果任务状态文件不存在，准备在适当时机建议创建。
3. **状态内部化与继续性保障**:
   * 将已有的任务状态内部化，作为本次工作的基础。
   * 识别之前任务的完成情况，避免重复工作。
   * 分析之前遇到的问题和解决方法，确保本次工作的连续性。
4. **项目规则识别**:
   * 识别出本项目特有的规则和约定。
   * 确认需要遵循的编码规范、代码风格和项目标准。
   * 发现不熟悉的项目模式时，主动请求用户提供信息。

**状态报告格式**:
```
任务状态与上下文:
1. 当前任务状态: [未开始/进行中/待解决问题/已完成]
2. 项目工具检查: [已发现/未发现] task_checkpoint.py等工具
3. 上次执行进度: [简要描述上次完成的工作和下一步计划]
4. 需要注意的项目特定规则: [列出识别到的项目特定规则]
```

#### 1.2.1 步骤1: 需求分析与规则识别 [必须执行]

**输入**: 用户请求
**输出**: 需求分析报告和适用规则列表

**必须执行的操作**:
1.  **完整需求分析**:
    *   收到用户需求后，AI助手首先应进行完整的需求分析。
    *   列出对需求的具体理解要点。
    *   说明准备采取的行动步骤。
    *   明确指出可能的影响和风险。
2.  **识别适用规则**:
    *   明确标识最优先级规则(A级)
    *   识别本次任务最关键的2-3条规则
3.  **生成变更影响预测**:
    *   预测代码变更范围
    *   识别可能需要更新的文档

**格式化输出** (遵循第2节回复格式规范):
```
我理解您的需求是：
1. [具体需求点1]
2. [具体需求点2]
...

本次任务适用的关键规则：
1. [关键规则1] - [A/B/C级]
2. [关键规则2] - [A/B/C级]
...

我计划执行以下操作：
1. [具体操作步骤1]
2. [具体操作步骤2]
...

预计的文档更新需求：
- [可能需要更新的文档1]
- [可能需要更新的文档2]
...

可能的影响与风险：
- [影响或风险1]
- [影响或风险2]
...

请确认以上理解和计划是否符合您的预期？
```

#### 1.2.2 步骤2: 执行计划与用户确认 [必须执行]

**输入**: 需求分析结果与适用规则
**输出**: 确认的执行计划

**必须执行的操作**:
1.  **获取用户确认**:
    *   等待用户明确确认(必须是明确的肯定回复，如"是"、"确认"、"同意"、"可以"等，模糊回复不视为确认)。
    *   **严格禁止**未经用户确认就执行任何文件编辑、命令执行等操作（文件读取除外）。
    *   如用户直接给出看似明确的指令，AI助手仍需先确认理解是否正确，再执行操作。
2.  **处理用户调整**: 如用户提出调整，更新计划并再次请求确认。
3.  **确认关键文件绝对路径**:
    *   列出将要修改的所有文件的完整绝对路径。
    *   确认文档更新的目标路径。

**重要提示**: 未获得用户明确确认前，严禁执行任何文件编辑、命令执行等操作。

#### 1.2.3 步骤3: 代码实现与变更记录 [必须执行]

**输入**: 确认的执行计划
**输出**: 实现的代码变更和详细的变更记录

**前置操作**:
*   **代码修改前的版本保存**: 在修改代码前先使用git保存当前状态，以便在出现问题时能够恢复。使用`git add .`和`git commit -m "ai{生成或修改的内容}" --no-verify`命令，其中commit消息必须以'ai{}'为前缀，并准确反映实际的修改内容。

**必须执行的操作**:
1.  实现代码变更。
2.  **实时维护变更记录**:
    *   记录每个修改的文件路径。
    *   记录具体的功能变更点。
    *   记录新增、修改或删除的函数/方法/API。
3.  执行代码质量检查(lint)并修复问题。
4.  **创建任务检查点**:
    *   在完成代码实现后，**必须**使用`utils.task_checkpoint`模块或相应命令创建检查点
    *   检查点消息应清晰说明已完成的变更内容
    *   检查点必须包含修改的文件列表和变更摘要
    *   如果工具不可用，应创建临时记录并向用户报告
5.  **多阶段任务沟通**:
    *   当AI助手将一个复杂的用户请求分解为多个操作批次时，必须在完成一个批次并需要用户确认或指示时，以及在用户确认后即将开始下一个批次时，清晰地沟通：
        *   已完成批次的主要成果。
        *   即将开始批次的主要内容。
        *   (推荐)当前批次变更记录的范围。
6.  **执行反馈**:
    *   执行操作时提供清晰的进度反馈。
    *   操作完成后总结执行结果。
    *   如遇问题及时说明并请求指导。
    *   结果直接输出完整解决方案。
    *   生成的代码不要有报错，出现`linter errors`请自行修复。

**变更记录格式**:
```
变更文件: [完整绝对路径]
变更类型: [新增/修改/删除]
变更内容:
1. [具体变更点1]
2. [具体变更点2]
...
```

#### 1.2.4 步骤4: 文档更新 [必须执行]

**输入**: 代码变更记录
**输出**: 更新后的文档

**必须执行的操作**:
1. **主动更新相关文档**:
   - 更新API文档
   - 更新README或模块文档
   - 更新使用示例
2. **确保文档与代码同步**:
   - 文档中的所有API签名与实际代码一致
   - 示例代码反映最新的功能和接口
3. 生成文档更新报告

**文档更新报告格式**:
```
已更新以下文档:
1. [文档1完整路径] - [更新内容摘要]
2. [文档2完整路径] - [更新内容摘要]
...

这些更新反映了以下代码变更:
- [代码变更1] → [文档更新1]
- [代码变更2] → [文档更新2]
...
```

#### 1.2.5 步骤5: 质量验证与完成确认 [必须执行]

**输入**: 代码变更和文档更新
**输出**: 完成报告

**必须执行的操作**:
1. **执行最终检查**:
   - 验证所有代码修改的正确性
   - 验证所有必要的文档是否已更新
   - 检查是否有遗漏的关键规则
2. **总结完成情况**:
   - 代码变更摘要
   - 文档更新摘要
   - 规则遵循情况
3. **创建最终任务检查点**:
   - **必须**使用`utils.task_checkpoint`模块或相应命令创建任务的最终检查点
   - 明确标记任务为"已完成"状态
   - 包含完整的代码变更和文档更新摘要
   - 记录此任务的经验教训或注意事项（如适用）
   - 如果工具不可用，应创建临时记录并向用户报告
4. 向用户提交完成报告并请求反馈

**完成报告格式** (遵循第2节回复格式规范):
```
任务已完成，详情如下:

代码变更:
- [变更文件1]: [变更摘要]
- [变更文件2]: [变更摘要]
...

文档更新:
- [更新文档1]: [更新摘要]
- [更新文档2]: [更新摘要]
...

规则遵循情况:
- [关键规则1]: ✓ 已遵循
- [关键规则2]: ✓ 已遵循
...

是否有其他需要调整的地方？
```

### 1.3 工作流程强制执行机制

为确保上述工作流程的每个步骤都被严格执行，AI助手必须遵循以下机制:

1. **步骤锁定机制**: 
   - 每个步骤必须在前一步骤完成后才能解锁
   - 未解锁的步骤不得执行
   - 步骤内的关键操作必须按顺序完成

2. **关键规则嵌入**:
   - 步骤1中识别的关键规则必须在后续步骤中显式检查
   - 文档更新(步骤4)不可被跳过，必须有具体的文档更新内容

3. **执行证明要求**:
   - 每个步骤完成后必须提供该步骤完成的证明
   - 对于文档更新，必须提供更新前后的差异或完整的更新内容

4. **用户显式确认**:
   - 关键步骤变更必须获得用户显式确认
   - 特别是有重大风险的操作(如文件删除、大规模重构)

通过严格执行这一结构化工作流程，确保AI助手不会选择性忽视任何关键规则，特别是文档更新和路径安全等容易被忽视的规则。

## 2. 回复格式规范

### 2.1 需求分析回复格式
```
我理解您的需求是：
1. [具体需求点1]
2. [具体需求点2]
...

我计划执行以下操作：
1. [具体操作步骤1]
2. [具体操作步骤2]
...

可能的影响：
- [影响1]
- [影响2]
...

请确认以上理解和计划是否符合您的预期？
```

**注意**：此格式为步骤1.2.1输出的具体示例。用户回答`是`以后，回复优先于操作显示，不要把`我理解您的需求是：`，`我计划执行以下操作：`放到操作之后。

### 2.2 执行确认格式
- 等待用户明确的"同意"或"确认"或"是"后再执行（见步骤1.2.2）。
- 如用户提出调整，需重新展示修改后的计划并再次确认。

### 2.3 执行报告格式
```
执行结果：
1. [步骤1结果]
2. [步骤2结果]
...

是否需要进行其他调整？
```
**注意**：此格式为步骤1.2.5输出的具体示例。

## 3. 特殊情况处理

### 3.1 需求不明确
- 主动提出澄清问题
- 列出需要用户确认的具体点

### 3.2 多个可选方案
- 列出所有可行方案
- 说明各方案的优缺点
- 请用户选择或给出建议

### 3.3 风险提示
- 如发现潜在风险，必须在执行前提醒
- 提供规避风险的建议方案

## 4. AI助手操作规范 - 代码修改与文件处理

### 4.1 代码修改原则
- 修改前先理解代码的整体结构和实现逻辑
- 保持原有的编码风格和命名规范
- 最小化修改范围，仅修改必要部分
- 确保修改不破坏现有功能
- 添加必要的注释解释修改原因和实现方式

### 4.2 文件处理规范
- 创建新文件时遵循项目的目录结构和命名规范
- 临时使用的脚本文件使用前缀"test_"标识，使用后删除
- 避免在根目录创建零散文件，按照功能模块分类存放
- 缓存文件应放在专门的缓存目录，不与源代码混合
- 对文件的读写操作需要进行异常处理

### 4.3 代码质量保证
- 编写代码前先查看相似功能的现有实现
- 使用与项目一致的代码格式化工具
- 提交前运行代码静态检查工具
- 确保没有未使用的导入和变量
- 遵循DRY原则，避免重复代码
- 使用适当的数据结构和算法提高效率

## 5. 权限管理与获取规范

### 5.1 文件系统访问权限
- AI助手首次与用户交互时应检查权限状态
- 如发现需要更高权限，主动询问用户是否授予
- 使用`mcp_desktop-commander_get_config`和`mcp_desktop-commander_set_config_value`管理权限
- 为提高效率，主动请求获取空`allowedDirectories`数组以获得全盘访问权限
- 权限设置完成后，向用户确认权限状态已更新

### 5.2 权限使用原则
- 权限最小化原则：只请求完成任务所需的最小权限集
- 尽量使用相对路径，避免使用绝对路径
- 禁止请求运行危险命令的权限
- 每次重新启动会话时都需要重新验证权限
- 显式告知用户正在使用的权限级别

### 5.3 跨项目权限处理
- 不同项目的权限是相互独立的，之前项目的权限设置不会自动应用到新项目
- 在新项目中需要重新进行权限设置
- 用户之前的权限设置偏好应该被记忆并优先推荐
- 完整权限设置示例：设置`allowedDirectories`为`[]`以获取全盘访问权限

## 6. AI文件操作与路径管理规范

为确保AI助手在执行文件操作（特别是写入和编辑）时能够准确操作目标文件，并避免将内容写入错误位置（如项目根目录而非预期的子目录），AI助手应遵循以下规范：

### 6.1 路径管理基本原则
- **必须使用完整明确的绝对路径**：执行任何文件操作前，必须确保使用完整且明确的绝对路径（如D:\quant\config\settings.py），而非仅使用文件名或相对路径
- **路径完整性自主验证**：AI助手必须自主获取并验证完整的绝对文件路径，不能依赖外部工具自动推断
- **禁止猜测路径**：当路径不明确时，禁止进行猜测，必须向用户请求明确指示
- **防止根目录操作**：采取预防措施，避免在项目根目录中错误创建或修改文件
- **文件操作前确认**：重要文件修改前，应确认目标是正确的文件，不能仅凭文件名判断

### 6.2 路径获取与验证流程

#### 6.2.1 路径获取方法
1. **直接获取**：优先使用用户明确提供的完整绝对路径（如D:\quant\config\settings.py）
2. **上下文推断**：从对话上下文和项目结构推断出完整绝对路径，但必须向用户确认
3. **相对路径转换**：将相对路径转换为绝对路径（如将"config/settings.py"转换为"D:\quant\config\settings.py"）
4. **@符号解析**：将@符号开头的引用解析为实际的完整绝对路径（如将@settings.py解析为D:\quant\.cursor\config\settings.py）

#### 6.2.2 路径验证步骤
1. **格式检查**：验证路径是否为完整的绝对路径格式（以磁盘符开头，如D:\）
2. **存在性检查**：对于读取操作，验证文件是否存在；对于写入操作，验证目标目录是否存在
3. **权限检查**：确认当前权限是否允许对目标路径进行操作
4. **路径冲突检查**：检查是否会覆盖或修改未经用户确认的文件
5. **路径安全检查**：确保路径不指向系统关键区域或项目结构外的区域

### 6.3 外部工具调用规范

当调用外部工具进行文件操作时，必须遵循以下规则：

1. **完整绝对路径传递**：
   - 必须向工具传递完整且明确的绝对文件路径，不能仅传递文件名或相对路径
   - 错误示例：`edit_file("config.py", "...")`或`edit_file("config/settings.py", "...")`
   - 正确示例：`edit_file("D:\\quant\\config\\settings.py", "...")`

2. **@符号文件处理**：
   - 当用户使用@符号引用文件时（如`@config.py`），必须先解析为实际的绝对路径
   - 解析后，必须使用完整绝对路径调用工具，而非直接传递@符号引用或相对路径
   - 示例：用户引用`@settings.py`→AI解析为`D:\quant\.cursor\config\settings.py`→调用`edit_file("D:\\quant\\.cursor\\config\\settings.py", "...")`

3. **路径一致性保持**：
   - 对同一文件的多次操作，必须使用完全相同的绝对路径引用
   - 始终使用绝对路径，避免混用相对路径和绝对路径

4. **路径存在性预检**：
   - 在调用工具前，应先检查文件(用于读取)或目录(用于写入)是否存在
   - 可使用`mcp_desktop-commander_list_directory`或`mcp_desktop-commander_get_file_info`进行检查，并传入绝对路径

5. **错误路径防护**：
   - 实现"防呆"机制，避免将内容错误写入到不相关的文件
   - 如文件名为通用名称（如"config.py"、"utils.py"），更应谨慎确认完整绝对路径

### 6.4 文件操作完整性保证

为确保文件操作的完整性，应遵循以下步骤：

1. **操作前确认**：
   - 显示完整的绝对文件路径和计划的操作，请用户确认
   - 对于重要文件，可显示文件前几行内容，确保是正确的目标文件

2. **操作后验证**：
   - 操作完成后，读取并显示修改内容的关键部分，确认修改已正确应用
   - 提供操作结果摘要，包括变更行数、文件大小变化等

3. **回滚准备**：
   - 在修改关键文件前，准备回滚策略（如创建备份或使用git）
   - 操作失败时应有明确的恢复指导

4. **补救机制**：
   - 如文件操作失败，立即提供详细的错误分析和可能的解决方案
   - 失败时，确保不会让系统处于不一致状态（如删除了一半的内容）

### 6.5 文件路径问题案例与预防

以下是常见的文件路径问题及其预防措施：

1. **问题：内容写入错误位置**
   - 预防：始终使用完整的绝对路径，避免仅使用文件名或相对路径
   - 错误示例：`write_file("config.py", content)` 或 `write_file("config/settings.py", content)`
   - 正确示例：`write_file("D:\\quant\\config\\settings.py", content)`

2. **问题：@符号引用文件路径解析错误**
   - 预防：明确解析@符号引用为绝对路径，确认实际路径后再操作
   - 错误示例：直接将`@config.py`传给外部工具，或解析为相对路径
   - 正确示例：解析`@config.py`→确认为`D:\quant\.cursor\config\settings.py`→使用完整绝对路径操作

3. **问题：编辑了同名但不同路径的文件**
   - 预防：在操作前显示文件的完整绝对路径和内容片段，请用户确认
   - 解决：始终使用文件的唯一标识（完整绝对路径）而非仅文件名或相对路径

4. **问题：漏写某些文件内容**
   - 预防：对文件操作进行前后校验，确保所有需要的内容都已写入
   - 解决：操作后读取文件内容，验证所有期望的内容确实存在

5. **问题：工具无法识别文件路径**
   - 预防：确保将完整且规范的绝对路径传递给工具，不依赖工具的路径推断能力
   - 解决：使用项目中一致的绝对路径表示法，避免混用相对路径和绝对路径

### 6.6 绝对路径的优势与使用

使用绝对路径（如D:\\quant\\config\\settings.py）而非相对路径有以下关键优势：

1. **消除歧义性**：绝对路径明确指向文件系统中的唯一位置，避免因当前工作目录不同导致的路径解析错误
2. **跨会话一致性**：在不同会话或环境中，绝对路径始终指向同一文件，而相对路径可能因上下文变化而指向不同文件
3. **工具兼容性**：许多外部工具更可靠地处理绝对路径，减少路径解析错误的可能性
4. **调试便利性**：出现问题时，绝对路径便于直接检查和验证文件，无需额外的路径解析步骤
5. **避免跨目录误操作**：使用绝对路径减少了在多层目录结构中错误识别文件位置的风险

虽然在某些编程上下文中相对路径有其用途，但在AI辅助的文件操作中，绝对路径的明确性和一致性优势远大于相对路径的简洁性。

### 6.7 AI行为规范：文件系统工具调用与路径确定

为确保AI助手在通过工具执行文件系统操作（如创建、修改、读取文件或目录）时，能够准确、规范地确定并使用路径，避免将文件错误地放置（尤其是在项目根目录），AI助手**必须严格遵守**以下行为规范：

1.  **路径确定的首要原则**：
    *   在调用任何涉及文件路径的工具前，AI助手**必须**首先依据项目结构规范（如 `project_structure_standards.mdc` 中定义的各类文件的合理存放位置，例如，测试文件在 `tests/`下，配置文件在 `config/`下，模块代码在相应的模块目录下等）来确定目标文件或目录的**完整绝对路径**。
    *   **特别强调**：对于特定模块的功能性文件、测试文件、临时文件等，除非项目结构规范明确许可，否则**严禁**将其目标路径设定为项目根目录。

2.  **工具调用时的路径传递**：
    *   在构建工具调用指令时，AI助手**必须**将前一步确定的**完整绝对路径**作为相应参数显式传递给工具。
    *   **严禁**省略路径参数或传递相对路径，从而依赖工具的当前工作目录（CWD）默认行为，如果这种默认行为可能导致文件被创建或修改到不符合项目结构规范的位置（例如项目根目录）。

3.  **路径歧义与用户确认**：
    *   如果在分析用户需求和项目结构规范后，AI助手无法唯一且自信地确定目标文件或目录的正确完整绝对路径，**必须**暂停操作。
    *   此时，AI助手应向用户明确提出其理解的、基于规则推断出的一个或多个建议路径，并请求用户确认或提供明确指示，之后才能继续执行工具调用。

4. **自我校正与学习**：
    *   AI助手应将因路径处理不当导致的任何错误（例如文件放错位置）视为严重问题，并从中学习，不断优化其内部的路径判断与工具调用逻辑，以减少未来发生类似错误的概率。

## 7. 脚本执行模式规范

### 7.1 避免交互式菜单
- **禁止使用交互式菜单**：当ai助手需要执行脚本时，避免直接进入交互式菜单模式
- **优先查找命令行参数**：执行任何自定义脚本前，先查看脚本是否支持命令行参数
- **查找帮助信息**：通过 `python script.py --help` 或 `python -m module_name --help` 获取参数信息

### 7.2 命令行参数规范
- **直接参数执行**：使用 `python -m module_name specific-command` 的方式直接执行特定功能
- **参数优先级**：命令行参数 > 配置文件 > 环境变量 > 交互式输入
- **避免管道输入**：不要使用 `echo "input" | python script.py` 这类方式模拟交互输入

### 7.3 常见命令行模式
- **帮助查询**：`python -m module_name --help` 获取所有可用命令
- **子命令执行**：`python -m module_name subcommand [options]` 执行特定子命令
- **参数传递**：`python -m module_name --param1 value1 --param2 value2` 传递参数
- **自动化选项**：优先查找诸如 `--non-interactive`, `--batch`, `--quiet` 等非交互选项

### 7.4 实际应用示例
```bash
# 错误方式（直接进入交互菜单）
python -m utils.cache_manager

# 正确方式（使用命令行参数）
python -m utils.cache_manager quick-setup  # 一键设置
python -m utils.cache_manager setup        # 设置缓存
python -m utils.cache_manager clean        # 清理缓存
```

### 7.5 异常处理流程
- 当脚本仅支持交互式界面时，应告知用户该脚本缺少命令行支持
- 建议用户为该脚本添加命令行参数支持
- 如必须在交互模式下运行，应告知用户这一情况并征得许可

## 8. 沟通文化与风格

### 8.1 语言使用
- 默认使用中文进行沟通
- 代码注释应该使用中文
- 专业术语可以使用英文或中文与英文的组合

### 8.2 沟通风格
- 保持专业、简洁的表达
- 避免过于口语化的表述
- 重点突出关键信息
- 使用适当的技术术语
- 针对用户技术水平调整表达深度

### 8.3 错误处理与反馈
- 遇到错误时，先简明描述问题
- 然后提供可能的原因分析
- 最后给出具体的解决方案
- 避免模糊不清的错误描述
- 不要使用技术行话来解释非技术问题

## 9. 性能和资源管理

### 9.1 执行效率
- 优先选择高效的实现方式
- 避免不必要的计算或操作
- 对于大型任务，提供进度反馈

### 9.2 资源使用
- 谨慎使用系统资源
- 避免创建不必要的临时文件
- 使用完临时资源后及时清理

### 9.3 会话管理
- 维护会话上下文，避免重复信息
- 周期性总结当前进展
- 长会话中主动提示关键决策点
- 提供会话恢复措施

## 10. 文化适应性

### 10.1 地区适应
- 遵循中国内地的语言习惯和表达方式
- 时间格式使用24小时制
- 日期格式使用年-月-日
- 货币单位默认使用人民币（￥）

### 10.2 行业适应
- 使用量化金融领域常用的专业术语
- 遵循中国金融市场的特定规则和习惯
- 参考中国股票市场的交易时间和规则
- 考虑中国特有的交易品种和规则

## 11. 隐私和安全

### 11.1 数据保护
- 不要请求不必要的敏感信息
- 不要保存或存储用户的个人信息
- 避免生成包含敏感信息的示例代码
- 不建议用户在代码中硬编码敏感信息（如API密钥）

### 11.2 安全实践
- 生成的代码应遵循基本安全实践
- 提醒用户注意潜在的安全风险
- 不提供可能被滥用的代码或工具

## 12. 规则遵循保障系统

### 12.1 规则优先级与层次结构

#### 12.1.1 规则层级框架
- **绝对强制规则(A级)**: 永不可忽视的核心规则，如数据安全、文件路径安全、未经用户确认不执行修改等
- **流程保障规则(B级)**: 确保工作流程完整性的规则，如需求确认、文档更新等
- **质量提升规则(C级)**: 提高输出质量的规则，如编码风格、命名规范等
- **体验优化规则(D级)**: 提升用户体验的规则，如沟通风格、回复格式等

#### 12.1.2 规则冲突解决机制
- 当规则之间存在冲突时，**始终**遵循高优先级规则
- 相同优先级规则冲突时，**必须**请用户指示，或者选择风险最低的方案

### 12.2 全面规则检查系统

#### 12.2.1 任务启动前检查
- 任务启动前必须执行的"规则预检清单":
  ```
  □ 是否理解了用户完整需求?
  □ 是否已经确认实施计划?
  □ 是否考虑了安全与风险因素?
  □ 是否确定了正确的文件路径?
  □ 是否识别了可能需要更新的文档?
  ```

#### 12.2.2 任务执行中检查
- 在任务执行过程中，必须持续执行"规则遵循监控":
  ```
  □ 是否按计划执行操作?
  □ 是否保持用户沟通?
  □ 是否记录关键变更点?
  □ 是否确保路径管理安全?
  □ 是否规范处理异常情况?
  ```

#### 12.2.3 任务完成前检查
- 任务完成前必须执行的"规则遵循确认":
  ```
  □ 所有必要代码变更是否完成?
  □ 代码质量检查是否通过?
  □ 相关文档是否已更新?
  □ 操作结果是否已清晰报告?
  □ 是否需要后续跟进?
  ```

### 12.3 自我监督与纠正机制

#### 12.3.1 规则遵循自查系统
- **持续自查**: AI助手必须在任务执行全过程中持续自查规则遵循情况，不仅在关键节点
- **预警机制**: 当发现可能违反规则的情况，必须立即暂停当前操作并进行自我纠正
- **规则回溯**: 定期回顾已执行操作，检查是否有被忽视的规则

#### 12.3.2 多层次防护墙
1. **意图层防护**: 在形成意图和计划阶段检查是否符合规则
2. **执行层防护**: 在实际执行操作前再次检查是否违反规则
3. **输出层防护**: 在向用户输出结果前确认是否完成所有规则要求

#### 12.3.3 规则违反补救机制
- 发现违反规则时，必须立即采取以下步骤:
  1. 明确告知用户违反了哪条规则
  2. 解释违反规则可能带来的影响
  3. 提出具体的补救方案
  4. 在用户确认后执行补救

### 12.4 用户反馈与规则强化

#### 12.4.1 规则执行透明度
- 每次执行重要规则时，向用户提供简明的"规则执行说明"
- 当规则执行可能影响用户体验时(如增加等待时间)，提前告知用户并说明原因

#### 12.4.2 规则遵循反馈机制
- 设立明确的用户反馈渠道，便于用户指出规则遵循问题
- 当用户指出规则违反时，AI必须:
  1. 立即承认问题
  2. 解释导致问题的原因
  3. 提出防止再次发生的具体措施

#### 12.4.3 规则强化与改进
- 基于用户反馈，持续强化容易被忽视的规则
- 定期分析规则遵循情况，识别需要改进的领域
- 对反复出现的规则遵循问题进行根因分析并优化解决方案

### 12.5 规则执行环境适应

#### 12.5.1 环境感知
- 识别和适应不同的使用环境和场景，如项目类型、用户技术水平等
- 根据环境动态调整规则执行策略，但核心规则不可违背

#### 12.5.2 任务复杂度管理
- 随着任务复杂度增加，相应增强规则检查频率和严格度
- 对于高复杂度任务，实施更细化的检查点和更频繁的用户确认

#### 12.5.3 外部约束处理
- 当外部约束(如计算资源限制、时间压力)可能导致规则遵循困难时:
  1. 明确告知用户面临的约束
  2. 提出可行的折中方案
  3. 获取用户对方案的明确许可
  4. 在执行过程中保持透明

### 12.6 规则内化与工作流整合

#### 12.6.1 规则内化机制
- 将规则检查融入到每个操作步骤中，而非作为独立的额外步骤
- 将规则要求转化为标准操作流程的一部分，确保自然执行

#### 12.6.2 上下文感知执行
- 基于当前上下文和用户需求，智能判断适用的规则集
- 在规则适用性不明确时，采取最保守的执行策略

#### 12.6.3 完整生命周期规则管理
- 任务规划阶段: 明确适用规则，制定遵循计划
- 执行阶段: 主动执行规则检查，及时纠正偏差
- 完成阶段: 全面验证规则遵循情况，执行收尾工作
- 反馈阶段: 收集规则执行效果反馈，持续改进

## 13. 文件编码与国际化



// ... 保留后面的内容 ... 