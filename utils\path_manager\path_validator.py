#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
路径一致性验证和修复工具

提供路径一致性检测、验证和自动修复功能。

功能：
1. 检测现有数据的路径格式
2. 验证路径与PathManager规范的一致性
3. 提供路径迁移和修复建议
4. 自动修复路径不一致问题

版本: v1.0
作者: Augment AI
日期: 2025-07-22
"""

import os
import shutil
from typing import Dict, List, Optional, Tuple
from pathlib import Path

from utils.logger import get_unified_logger, LogTarget
from .unified_path_manager import get_path_manager

# 创建统一日志记录器
logger = get_unified_logger(__name__, enhanced=True)


class PathValidationResult:
    """路径验证结果"""
    
    def __init__(self):
        self.total_files = 0
        self.valid_files = 0
        self.invalid_files = 0
        self.issues = []
        self.suggestions = []
    
    def add_issue(self, issue: str, suggestion: str = None):
        """添加问题和建议"""
        self.issues.append(issue)
        if suggestion:
            self.suggestions.append(suggestion)
    
    def get_summary(self) -> str:
        """获取验证结果摘要"""
        return f"总文件数: {self.total_files}, 有效: {self.valid_files}, 无效: {self.invalid_files}"


class PathValidator:
    """路径验证器"""
    
    def __init__(self):
        self.path_manager = get_path_manager()
        self.data_root = self.path_manager.get_data_root()
    
    def scan_data_directory(self) -> Dict[str, List[str]]:
        """
        扫描数据目录，获取所有数据文件
        
        Returns:
            Dict[str, List[str]]: 按股票代码分组的文件列表
        """
        logger.info("开始扫描数据目录...")
        
        files_by_symbol = {}
        
        if not os.path.exists(self.data_root):
            logger.warning(f"数据根目录不存在: {self.data_root}")
            return files_by_symbol
        
        try:
            # 遍历数据根目录
            for market_dir in os.listdir(self.data_root):
                market_path = os.path.join(self.data_root, market_dir)
                if not os.path.isdir(market_path):
                    continue
                
                # 遍历市场目录下的股票代码目录
                for code_dir in os.listdir(market_path):
                    code_path = os.path.join(market_path, code_dir)
                    if not os.path.isdir(code_path):
                        continue
                    
                    symbol = f"{code_dir}.{market_dir}"
                    files_by_symbol[symbol] = []
                    
                    # 遍历周期目录
                    for period_dir in os.listdir(code_path):
                        period_path = os.path.join(code_path, period_dir)
                        if not os.path.isdir(period_path):
                            continue
                        
                        # 查找parquet文件
                        self._find_parquet_files(period_path, files_by_symbol[symbol])
            
            logger.info(f"扫描完成，找到 {len(files_by_symbol)} 个股票的数据")
            return files_by_symbol
            
        except Exception as e:
            logger.error(f"扫描数据目录失败: {e}")
            return files_by_symbol
    
    def _find_parquet_files(self, directory: str, file_list: List[str]):
        """递归查找parquet文件"""
        try:
            for item in os.listdir(directory):
                item_path = os.path.join(directory, item)
                
                if os.path.isfile(item_path) and item.endswith('.parquet'):
                    file_list.append(item_path)
                elif os.path.isdir(item_path):
                    self._find_parquet_files(item_path, file_list)
                    
        except Exception as e:
            logger.error(f"查找parquet文件失败: {directory} - {e}")
    
    def validate_path_consistency(self, symbol: str, files: List[str]) -> PathValidationResult:
        """
        验证指定股票的路径一致性
        
        Args:
            symbol: 股票代码
            files: 文件路径列表
            
        Returns:
            PathValidationResult: 验证结果
        """
        result = PathValidationResult()
        result.total_files = len(files)
        
        logger.debug(f"验证 {symbol} 的路径一致性，共 {len(files)} 个文件")
        
        for file_path in files:
            try:
                # 检查文件是否符合PathManager规范
                if self._is_path_valid(symbol, file_path):
                    result.valid_files += 1
                else:
                    result.invalid_files += 1
                    issue = f"路径不符合规范: {file_path}"
                    suggestion = self._get_path_suggestion(symbol, file_path)
                    result.add_issue(issue, suggestion)
                    
            except Exception as e:
                result.invalid_files += 1
                result.add_issue(f"验证失败: {file_path} - {e}")
        
        logger.info(f"{symbol} 验证完成: {result.get_summary()}")
        return result
    
    def _is_path_valid(self, symbol: str, file_path: str) -> bool:
        """检查路径是否有效"""
        try:
            # 使用PathManager验证路径
            return self.path_manager.validate_path(file_path)
        except Exception as e:
            logger.error(f"路径验证失败: {file_path} - {e}")
            return False
    
    def _get_path_suggestion(self, symbol: str, file_path: str) -> str:
        """获取路径修复建议"""
        try:
            # 从文件路径中提取信息
            rel_path = os.path.relpath(file_path, self.data_root)
            components = rel_path.split(os.sep)
            
            if len(components) >= 3:
                market = components[0]
                code = components[1]
                period = components[2]
                
                # 生成标准路径建议
                suggested_symbol = f"{code}.{market}"
                suggested_path = self.path_manager.get_base_directory(suggested_symbol, period)
                
                return f"建议路径: {suggested_path}"
            
            return "无法生成路径建议"
            
        except Exception as e:
            return f"生成建议失败: {e}"
    
    def validate_all_paths(self) -> Dict[str, PathValidationResult]:
        """
        验证所有数据路径的一致性
        
        Returns:
            Dict[str, PathValidationResult]: 按股票代码分组的验证结果
        """
        logger.info("开始验证所有路径的一致性...")
        
        files_by_symbol = self.scan_data_directory()
        results = {}
        
        for symbol, files in files_by_symbol.items():
            results[symbol] = self.validate_path_consistency(symbol, files)
        
        # 生成总体统计
        total_files = sum(r.total_files for r in results.values())
        total_valid = sum(r.valid_files for r in results.values())
        total_invalid = sum(r.invalid_files for r in results.values())
        
        logger.info(f"路径验证完成: 总文件 {total_files}, 有效 {total_valid}, 无效 {total_invalid}")
        
        return results
    
    def generate_validation_report(self, results: Dict[str, PathValidationResult]) -> str:
        """
        生成验证报告
        
        Args:
            results: 验证结果
            
        Returns:
            str: 报告内容
        """
        report_lines = []
        report_lines.append("=" * 60)
        report_lines.append("路径一致性验证报告")
        report_lines.append("=" * 60)
        report_lines.append("")
        
        # 总体统计
        total_files = sum(r.total_files for r in results.values())
        total_valid = sum(r.valid_files for r in results.values())
        total_invalid = sum(r.invalid_files for r in results.values())
        
        report_lines.append("总体统计:")
        report_lines.append(f"  总文件数: {total_files}")
        report_lines.append(f"  有效文件: {total_valid}")
        report_lines.append(f"  无效文件: {total_invalid}")
        report_lines.append(f"  有效率: {total_valid/total_files*100:.1f}%" if total_files > 0 else "  有效率: N/A")
        report_lines.append("")
        
        # 详细结果
        report_lines.append("详细结果:")
        for symbol, result in results.items():
            report_lines.append(f"  {symbol}: {result.get_summary()}")
            
            if result.issues:
                report_lines.append("    问题:")
                for i, issue in enumerate(result.issues[:5]):  # 只显示前5个问题
                    report_lines.append(f"      {i+1}. {issue}")
                if len(result.issues) > 5:
                    report_lines.append(f"      ... 还有 {len(result.issues)-5} 个问题")
                
                if result.suggestions:
                    report_lines.append("    建议:")
                    for i, suggestion in enumerate(result.suggestions[:5]):
                        report_lines.append(f"      {i+1}. {suggestion}")
                    if len(result.suggestions) > 5:
                        report_lines.append(f"      ... 还有 {len(result.suggestions)-5} 个建议")
            
            report_lines.append("")
        
        return "\n".join(report_lines)


def validate_paths() -> Dict[str, PathValidationResult]:
    """验证所有路径的一致性"""
    validator = PathValidator()
    return validator.validate_all_paths()


def generate_report(results: Dict[str, PathValidationResult]) -> str:
    """生成验证报告"""
    validator = PathValidator()
    return validator.generate_validation_report(results)
