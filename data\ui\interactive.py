#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
交互式界面模块

专门处理交互式用户界面，包括菜单显示、用户输入处理、参数收集等
负责用户体验和界面逻辑，与数据操作模块分离

更新历史：
- 2025-07-11: 修复周期合成功能的用户体验问题
  * 实现动态目标周期提示（根据源周期类型生成相应示例）
  * 实现实际数据时间范围获取（从分区文件中获取真实的开始和结束日期）
"""

import os
import sys
from typing import List, Optional, Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from config.settings import DATA_ROOT
from utils.logger import get_unified_logger, LogTarget
from utils.data_display.text import print_boxed_text
from utils.data_display.console import show_menu
from utils.input_handler import (
    get_multiline_input, parse_code_input, get_int_input,
    get_date_input, get_yes_no_input, get_period_input
)
from utils.text_parser import parse_text_items
from utils.time_formatter import get_default_period_dates
from utils.data_helpers import DataHelpers
from data.core.operations import read_data, synthesize_data

# 初始化
logger = get_unified_logger(__name__, enhanced=True)
helpers = DataHelpers()


def get_stock_input(prompt: str) -> List[str]:
    """
    获取股票代码输入，支持文件路径和直接输入
    
    Args:
        prompt: 输入提示信息
        
    Returns:
        有效的股票代码列表
    """
    full_prompt = (
        f"{prompt} (多个代码用逗号、空格或换行分隔，"
        "默认: ['000001.SZ'])"
    )
    
    user_input = get_multiline_input(full_prompt)
    
    # 处理空输入
    if not user_input.strip():
        return ["000001.SZ"]
    
    # 检查是否为文件路径
    user_input = user_input.strip()
    if user_input.endswith('.txt') and os.path.isfile(user_input):
        logger.info(LogTarget.FILE, f"检测到文件路径: {user_input}，正在读取文件内容...")
        try:
            with open(user_input, 'r', encoding='utf-8') as f:
                file_content = f.read()
            logger.info(LogTarget.FILE, f"成功读取文件内容，正在解析股票代码...")
            return parse_code_input(file_content)
        except Exception as e:
            logger.error(LogTarget.FILE, f"读取文件失败: {e}")
            retry_input = get_multiline_input("请重新输入股票代码")
            if not retry_input.strip():
                return ["000001.SZ"]
            return parse_code_input(retry_input)
    
    return parse_code_input(user_input)


def show_welcome():
    """显示欢迎信息"""
    print_boxed_text("欢迎使用数据管理系统", width=60)


def show_main_menu():
    """显示主菜单"""
    main_menu_options = [
        (1, "查看本地数据"),
        (2, "管理数据源"),
        (3, "管理数据存储"),
        (4, "数据工具"),
        (0, "退出系统")
    ]

    print("\n📋 数据合成功能说明:")
    print("   周期合成功能已优化为批量脚本模式")
    print("   请使用: python data/批量合成历史数据.py")

    return show_menu("主菜单", main_menu_options, default=None, min_value=0, max_value=4)


def get_display_options() -> tuple:
    """
    获取数据显示选项
    
    Returns:
        (display_mode, head_lines, tail_lines)
    """
    head_lines = get_int_input("请输入要显示的头部行数", default=5, min_value=0)
    tail_lines = get_int_input("请输入要显示的尾部行数", default=5, min_value=0)
    
    # 确定显示模式
    if head_lines > 0 and tail_lines > 0:
        display_mode = "both"
    elif head_lines > 0:
        display_mode = "head"
    elif tail_lines > 0:
        display_mode = "tail"
    else:
        display_mode = "head"
    
    return display_mode, head_lines, tail_lines


def get_target_period_prompt(source_period: str) -> str:
    """
    根据源周期生成目标周期输入提示 - 支持分层合成策略

    Args:
        source_period: 源数据周期

    Returns:
        适合的提示字符串
    """
    # 根据源周期类型生成相应的示例，应用分层合成策略
    if source_period.endswith('d') or source_period in ['1d']:
        return "\n请输入目标周期 (例如: 1h, 2h, 30m, 1m): "
    elif source_period.endswith('m') or source_period in ['1m', '5m', '15m', '30m']:
        return "\n请输入目标周期 (例如: 2m, 3m, 5m, 1h, 1d): "
    elif source_period.endswith('h') or source_period in ['1h']:
        return "\n请输入目标周期 (例如: 2h, 4h, 6h, 1d): "
    elif source_period == 'tick':
        # 分层合成策略：tick数据推荐只用于1m合成
        return "\n请输入目标周期 (推荐: 1m，也支持: 30s, 2m, 5m等): "
    else:
        # 默认提示
        return "\n请输入目标周期 (例如: 30s, 1m, 5m, 1h, 1d): "


def get_target_period_error_prompt(source_period: str) -> str:
    """
    根据源周期生成目标周期错误提示 - 支持分层合成策略

    Args:
        source_period: 源数据周期

    Returns:
        适合的错误提示字符串
    """
    # 根据源周期类型生成相应的错误提示，应用分层合成策略
    if source_period.endswith('d') or source_period in ['1d']:
        return "请输入有效的周期格式 (例如: 1h, 2h, 30m, 1m)"
    elif source_period.endswith('m') or source_period in ['1m', '5m', '15m', '30m']:
        return "请输入有效的周期格式 (例如: 2m, 3m, 5m, 1h, 1d)"
    elif source_period.endswith('h') or source_period in ['1h']:
        return "请输入有效的周期格式 (例如: 2h, 4h, 6h, 1d)"
    elif source_period == 'tick':
        # 分层合成策略：tick数据推荐只用于1m合成
        return "请输入有效的周期格式 (推荐: 1m，也支持: 30s, 2m, 5m等)"
    else:
        # 默认提示
        return "请输入有效的周期格式 (例如: 30s, 1m, 5m, 1h, 1d)"


def smart_synthesis_check(symbol: str, source_period: str, target_period: str, data_root: str) -> tuple:
    """
    智能合成检查，确定是否需要合成以及合成范围

    Args:
        symbol: 股票代码，如 "rb00.SF"
        source_period: 源数据周期，如 "tick"
        target_period: 目标周期，如 "1m"
        data_root: 数据根目录

    Returns:
        tuple: (status, start_time, end_time)
        status可能的值：
        - 'no_source': 源数据不存在，无需合成
        - 'no_new_data': 没有新数据需要合成
        - 'full_synthesis': 需要全量合成
        - 'incremental_synthesis': 需要增量合成
    """
    from utils.path_manager import (
        get_latest_partition_file,
        get_earliest_partition_file
    )
    from data.storage.parquet_reader import (
        read_latest_data_timestamp,
        read_first_data_timestamp
    )

    try:
        logger.debug(f"开始智能合成检查: {symbol}, 源周期={source_period}, 目标周期={target_period}")

        # 1. 检查源数据是否存在
        source_latest_file = get_latest_partition_file(symbol, source_period)
        if not source_latest_file:
            logger.info(f"{symbol} 源数据({source_period})不存在，无需合成")
            return 'no_source', None, None

        source_last_timestamp = read_latest_data_timestamp(source_latest_file)
        if not source_last_timestamp:
            logger.warning(f"{symbol} 无法获取源数据({source_period})的最后时间戳，无需合成")
            return 'no_source', None, None

        logger.debug(f"{symbol} 源数据({source_period})最后时间戳: {source_last_timestamp}")

        # 2. 检查目标数据是否存在
        target_latest_file = get_latest_partition_file(symbol, target_period)
        if not target_latest_file:
            # 需要全量合成
            logger.info(f"{symbol} 目标数据({target_period})不存在，需要全量合成")

            # 获取源数据的最早时间戳
            source_earliest_file = get_earliest_partition_file(symbol, source_period)
            if not source_earliest_file:
                logger.error(f"{symbol} 无法获取源数据({source_period})的最早文件")
                return 'no_source', None, None

            source_first_timestamp = read_first_data_timestamp(source_earliest_file)
            if not source_first_timestamp:
                logger.error(f"{symbol} 无法获取源数据({source_period})的最早时间戳")
                return 'no_source', None, None

            logger.info(f"{symbol} 全量合成范围: {source_first_timestamp} 至 {source_last_timestamp}")
            return 'full_synthesis', source_first_timestamp, source_last_timestamp

        # 3. 检查是否有新数据需要合成
        target_last_timestamp = read_latest_data_timestamp(target_latest_file)
        if not target_last_timestamp:
            logger.warning(f"{symbol} 无法获取目标数据({target_period})的最后时间戳")
            # 如果无法获取目标数据时间戳，按全量合成处理
            source_earliest_file = get_earliest_partition_file(symbol, source_period)
            source_first_timestamp = read_first_data_timestamp(source_earliest_file)
            return 'full_synthesis', source_first_timestamp, source_last_timestamp

        logger.debug(f"{symbol} 目标数据({target_period})最后时间戳: {target_last_timestamp}")

        # 比较时间戳（只比较日期部分，忽略时间部分）
        source_date = source_last_timestamp[:8]
        target_date = target_last_timestamp[:8]

        if source_date <= target_date:
            logger.info(f"{symbol} 源数据最后日期({source_date}) <= 目标数据最后日期({target_date})，无新数据需要合成")
            return 'no_new_data', None, None

        # 4. 需要增量合成
        logger.info(f"{symbol} 检测到新数据，需要增量合成: 从 {target_last_timestamp} 到 {source_last_timestamp}")
        return 'incremental_synthesis', target_last_timestamp, source_last_timestamp

    except Exception as e:
        logger.error(f"智能合成检查失败 {symbol}: {e}")
        return 'no_source', None, None


def get_actual_data_time_range(stock_code: str, period: str) -> tuple:
    """
    获取指定股票和周期的实际数据时间范围（优化版）

    使用智能检查，只读取必要的时间戳数据，避免全量数据扫描

    Args:
        stock_code: 股票代码，如 "000001.SZ"
        period: 数据周期，如 "1d", "1m"

    Returns:
        (start_date, end_date) 元组，格式为 "YYYYMMDD"，如果获取失败则返回 ("", "")
    """
    try:
        from utils.path_manager import (
            get_earliest_partition_file,
            get_latest_partition_file
        )
        from data.storage.parquet_reader import (
            read_first_data_timestamp,
            read_latest_data_timestamp
        )

        logger.debug(f"获取 {stock_code} {period} 数据时间范围（使用优化方法）")

        # 获取最早的分区文件
        earliest_file = get_earliest_partition_file(stock_code, period)
        if not earliest_file:
            logger.warning(f"未找到 {stock_code} {period} 的数据文件")
            return "", ""

        # 获取最新的分区文件
        latest_file = get_latest_partition_file(stock_code, period)
        if not latest_file:
            logger.warning(f"未找到 {stock_code} {period} 的数据文件")
            return "", ""

        # 读取第一条数据的时间戳
        start_timestamp = read_first_data_timestamp(earliest_file)
        if not start_timestamp:
            logger.warning(f"无法获取 {stock_code} {period} 的开始时间戳")
            return "", ""

        # 读取最后一条数据的时间戳
        end_timestamp = read_latest_data_timestamp(latest_file)
        if not end_timestamp:
            logger.warning(f"无法获取 {stock_code} {period} 的结束时间戳")
            return "", ""

        # 提取日期部分（YYYYMMDD）
        start_date = start_timestamp[:8]
        end_date = end_timestamp[:8]

        logger.info(f"{stock_code} {period} 数据时间范围: {start_date} 至 {end_date}")
        return start_date, end_date

    except Exception as e:
        logger.error(f"获取 {stock_code} {period} 数据时间范围失败: {e}")
        return "", ""


def get_date_prompt_with_period(period: str, default_date: str, is_start: bool = True) -> str:
    """
    根据周期和默认日期构建日期输入提示

    Args:
        period: 数据周期
        default_date: 默认日期
        is_start: 是否为开始日期

    Returns:
        构建好的提示字符串
    """
    if is_start:
        if default_date == "":
            return f"请输入开始日期 (默认为空，系统将自动选择最早的起始日期) (格式: YYYYMMDD, 默认: )"
        else:
            return f"请输入开始日期 (数据最早日期: {default_date}) (格式: YYYYMMDD, 默认: {default_date})"
    else:
        if default_date == "":
            return f"请输入结束日期 (默认为空，系统将自动选择最新的结束日期) (格式: YYYYMMDD, 默认: )"
        else:
            return f"请输入结束日期 (数据最新日期: {default_date}) (格式: YYYYMMDD, 默认: {default_date})"








def get_view_params() -> Dict[str, Any]:
    """获取查看数据的参数"""
    # 获取数据周期
    period = get_period_input("请选择数据周期", default="1d")
    
    # 获取日期范围（使用空字符串作为默认值）
    default_start_date = ""
    default_end_date = ""
    
    start_date_prompt = get_date_prompt_with_period(period, default_start_date, is_start=True)
    start_date = get_date_input(start_date_prompt, default=default_start_date)
    
    end_date_prompt = get_date_prompt_with_period(period, default_end_date, is_start=False)
    end_date = get_date_input(end_date_prompt, default=default_end_date)
    
    # 获取要显示的字段
    print("\n请输入要显示的字段，多个字段用逗号分隔。")
    print("常用字段: open, high, low, close, volume, amount")
    print("如果不需要筛选字段，直接按回车显示所有字段。")
    fields_input = input("字段列表 (默认: 全部): ").strip()
    
    fields = None
    if fields_input:
        fields = parse_text_items(fields_input)
    
    # 获取显示选项
    display_mode, head_lines, tail_lines = get_display_options()
    
    return {
        "period": period,
        "start_date": start_date,
        "end_date": end_date,
        "fields": fields,
        "display_mode": display_mode,
        "head_lines": head_lines,
        "tail_lines": tail_lines
    }


# get_synthesis_params函数已删除
# 合成功能已迁移到批量脚本 data/批量合成历史数据.py


def confirm_query(stocks: List[str], params: Dict[str, Any]) -> bool:
    """确认查询参数"""
    print("\n即将查询以下数据:")
    print(f"股票代码: {', '.join(stocks)}")
    print(f"数据周期: {params['period']}")
    print(f"开始日期: {params['start_date'] or '最早可用'}")
    print(f"结束日期: {params['end_date'] or '今天'}")
    print(f"字段列表: {', '.join(params['fields']) if params['fields'] else '全部'}")
    
    display_mode = params['display_mode']
    head_lines = params['head_lines']
    tail_lines = params['tail_lines']
    
    if display_mode == "head":
        print(f"显示模式: 头部 {head_lines} 行")
    elif display_mode == "tail":
        print(f"显示模式: 尾部 {tail_lines} 行")
    else:
        print(f"显示模式: 头部 {head_lines} 行 + 尾部 {tail_lines} 行")
    
    confirm = get_yes_no_input("确认查询？", default=True)
    if not confirm:
        logger.info("已取消查询，返回主菜单。")
        return False
    return True


# confirm_synthesis函数已删除
# 合成功能已迁移到批量脚本 data/批量合成历史数据.py








def view_local_data_menu():
    """查看本地数据菜单"""
    show_menu("查看本地数据")
    
    # 获取股票代码
    stocks = get_stock_input("请输入股票代码")
    if not stocks:
        logger.info("未输入有效的股票代码，返回主菜单。")
        return
    
    # 获取参数
    params = get_view_params()
    
    # 确认查询
    if not confirm_query(stocks, params):
        return
    
    # 执行查询
    try:
        data_dict = read_data(
            symbols=stocks,
            period=params["period"],
            start_time=params["start_date"],
            end_time=params["end_date"],
            fields=params["fields"],
            mode=params["display_mode"],
            head_lines=params["head_lines"],
            tail_lines=params["tail_lines"]
        )
        
        if not data_dict:
            logger.warning("没有找到符合条件的数据。")
    except Exception as e:
        logger.error(f"查询数据时出错: {e}")
    
    input("\n按回车键继续...")


def manage_data_sources_menu():
    """管理数据源菜单"""
    show_menu("管理数据源 (功能待实现)")
    print("此功能正在开发中，敬请期待。")
    input("\n按Enter键继续...")


def manage_data_storage_menu():
    """管理数据存储菜单"""
    show_menu("管理数据存储")
    
    # 显示存储信息
    logger.info(f"当前数据根目录: {DATA_ROOT}")
    
    # 显示日志文件路径
    from datetime import datetime
    root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    date_str = datetime.now().strftime("%Y%m%d")
    log_dir = os.path.join(root_path, "logs")
    log_file = os.path.join(log_dir, f"quant_{date_str}.log")
    print(f"日志文件将统一保存到: {log_file}")
    
    if os.path.exists(DATA_ROOT):
        size_bytes = sum(
            os.path.getsize(os.path.join(dirpath, filename))
            for dirpath, _, filenames in os.walk(DATA_ROOT)
            for filename in filenames
        )
        size_mb = size_bytes / (1024 * 1024)
        logger.info(f"目录总大小: {size_mb:.2f} MB")
    else:
        logger.error("数据目录不存在。")
    
    input("\n按Enter键继续...")


def data_tools_menu():
    """数据工具菜单"""
    show_menu("数据工具 (功能待实现)")
    print("此功能正在开发中，敬请期待。")
    input("\n按Enter键继续...")


# 周期合成功能已迁移到批量脚本 data/批量合成历史数据.py
# 请使用批量脚本进行高效的数据合成操作


def interactive_mode():
    """
    交互式模式主循环
    
    Returns:
        bool: 是否正常退出
    """
    try:
        show_welcome()
        
        while True:
            choice = show_main_menu()
            
            if choice == 0:
                logger.info("退出程序")
                break
            elif choice == 1:
                view_local_data_menu()
            elif choice == 2:
                manage_data_sources_menu()
            elif choice == 3:
                manage_data_storage_menu()
            elif choice == 4:
                data_tools_menu()
            else:
                print(f"\n无效选择: {choice}")
                print("提示: 数据合成功能请使用 python data/批量合成历史数据.py")
        
        return True
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        return False
    except Exception as e:
        print(f"\n程序发生错误: {str(e)}")
        return False