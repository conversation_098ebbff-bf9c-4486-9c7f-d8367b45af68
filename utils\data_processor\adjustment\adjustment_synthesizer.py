"""
复权数据合成器

该模块整合所有复权相关功能，提供统一的数据合成接口，包括：
1. 整合复权因子存储管理器
2. 整合前复权计算引擎
3. 整合复权价格缓存系统
4. 提供统一的复权数据合成接口
5. 错误处理和降级策略

这是复权功能的核心模块，隐藏底层复杂性，为用户提供简单易用的接口。
"""

import pandas as pd
from typing import Optional, Dict, Any, List
import logging
from datetime import datetime

from utils.logger import get_unified_logger
from .dividend_factor_storage import dividend_factor_storage
from .forward_adjustment_engine import forward_adjustment_engine
from data.storage.unified_data_accessor import unified_data_accessor

logger = get_unified_logger(__name__, enhanced=True)


class AdjustmentSynthesizer:
    """复权数据合成器

    整合所有复权相关功能，提供统一的数据合成接口。
    负责协调复权因子存储、计算引擎和统一数据访问器的工作。

    注意：已移除缓存系统依赖，使用VectorizedDataReader内置缓存。
    """

    def __init__(self):
        """初始化复权数据合成器"""
        self.factor_storage = dividend_factor_storage
        self.adjustment_engine = forward_adjustment_engine
        self.data_accessor = unified_data_accessor  # 使用统一数据访问器

        logger.debug("复权数据合成器初始化完成（使用统一数据访问器）")
    
    def synthesize_adjusted_data(
        self,
        symbol: str,
        price_data: pd.DataFrame,
        dividend_type: str = "front",
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        method: str = "ratio",
        use_cache: bool = True
    ) -> Optional[pd.DataFrame]:
        """合成复权数据（主要接口）
        
        Args:
            symbol: 股票代码
            price_data: 原始价格数据
            dividend_type: 复权类型，"front"（前复权）、"back"（后复权）
            start_date: 开始日期
            end_date: 结束日期
            method: 计算方法，"ratio"（等比复权）、"standard"（标准复权）
            use_cache: 是否使用缓存
            
        Returns:
            复权后的价格数据，如果失败返回None
        """
        try:
            if price_data is None or price_data.empty:
                logger.warning(f"股票 {symbol} 价格数据为空")
                return None
            
            if dividend_type == "none":
                logger.debug(f"股票 {symbol} 不需要复权处理，返回原始数据")
                return price_data.copy()
            
            logger.info(f"开始合成股票 {symbol} 的复权数据，类型: {dividend_type}, 方法: {method}")
            
            # 注意：缓存功能已集成到统一数据访问器中，无需单独处理
            # 如果需要从存储读取复权数据，可以使用统一数据访问器
            if use_cache:
                logger.debug(f"股票 {symbol} 复权数据将使用VectorizedDataReader内置缓存")
            
            # 获取复权因子数据
            dividend_factors = self._get_dividend_factors(symbol, start_date, end_date)
            if dividend_factors is None or dividend_factors.empty:
                logger.info(f"股票 {symbol} 无复权因子数据，返回原始数据")
                return price_data.copy()
            
            # 计算复权数据
            if dividend_type == "front":
                adjusted_data = self._calculate_forward_adjustment(
                    price_data, dividend_factors, method
                )
            elif dividend_type == "back":
                adjusted_data = self._calculate_backward_adjustment(
                    price_data, dividend_factors, method
                )
            else:
                logger.error(f"不支持的复权类型: {dividend_type}")
                return price_data.copy()
            
            # 验证结果
            if adjusted_data is not None:
                # 索引格式验证：确保复权后数据保持正确的索引格式
                from utils.data_processor.index_manager import IndexManager

                if not IndexManager.validate_index_format(adjusted_data):
                    logger.warning(f"股票 {symbol} 复权后数据索引格式不正确，尝试修复")
                    adjusted_data = IndexManager.ensure_proper_index(adjusted_data, 'time')

                    if IndexManager.validate_index_format(adjusted_data):
                        logger.info(f"股票 {symbol} 复权后数据索引格式修复成功")
                    else:
                        logger.error(f"股票 {symbol} 复权后数据索引格式修复失败")

                # 注意：缓存功能已集成到VectorizedDataReader中，无需手动存储缓存
                # 复权数据应该直接保存到存储系统，由统一数据访问器管理缓存
                if use_cache:
                    logger.debug(f"股票 {symbol} 复权数据将由VectorizedDataReader自动缓存")

                logger.info(f"股票 {symbol} 复权数据合成成功")
                return adjusted_data
            else:
                logger.error(f"股票 {symbol} 复权数据计算失败")
                return price_data.copy()
                
        except Exception as e:
            logger.error(f"股票 {symbol} 复权数据合成失败: {e}")
            return price_data.copy()
    
    def _get_dividend_factors(
        self, 
        symbol: str, 
        start_date: Optional[str] = None, 
        end_date: Optional[str] = None
    ) -> Optional[pd.DataFrame]:
        """获取复权因子数据
        
        Args:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            复权因子数据
        """
        try:
            # 首先尝试从本地存储加载
            dividend_factors = self.factor_storage.query_dividend_factors(
                symbol, start_date, end_date
            )
            
            if dividend_factors is None or dividend_factors.empty:
                logger.info(f"本地无股票 {symbol} 复权因子数据，尝试从xtquant获取")
                
                # 尝试更新复权因子数据
                success = self.factor_storage.update_dividend_factors(
                    symbol, force_update=False, start_time=start_date or '', end_time=end_date or ''
                )
                
                if success:
                    # 重新查询
                    dividend_factors = self.factor_storage.query_dividend_factors(
                        symbol, start_date, end_date
                    )
                else:
                    logger.warning(f"无法获取股票 {symbol} 的复权因子数据")
            
            return dividend_factors
            
        except Exception as e:
            logger.error(f"获取股票 {symbol} 复权因子数据失败: {e}")
            return None
    
    def _calculate_forward_adjustment(
        self,
        price_data: pd.DataFrame,
        dividend_factors: pd.DataFrame,
        method: str
    ) -> Optional[pd.DataFrame]:
        """计算前复权数据

        Args:
            price_data: 原始价格数据
            dividend_factors: 复权因子数据
            method: 计算方法

        Returns:
            前复权后的价格数据
        """
        try:
            # 统一索引类型处理：确保价格数据和复权因子数据索引类型一致
            from utils.smart_time_converter import smart_to_datetime
            from utils.data_processor.index_manager import IndexManager

            # 处理复权因子数据索引
            if not dividend_factors.empty and 'time' in dividend_factors.columns:
                # 将time列转换为datetime并设置为索引
                dividend_factors_indexed = dividend_factors.copy()
                time_index = smart_to_datetime(dividend_factors_indexed['time'])
                dividend_factors_indexed.index = time_index

                logger.debug(f"复权因子数据索引转换完成: {type(time_index)} -> {len(time_index)} 条记录")
            else:
                logger.warning("复权因子数据缺少time列，使用原始索引")
                dividend_factors_indexed = dividend_factors.copy()

            # 统一价格数据和复权因子数据的索引类型
            # 检查价格数据索引类型
            price_data_processed = price_data.copy()

            if pd.api.types.is_string_dtype(price_data_processed.index):
                # 价格数据索引是字符串类型，需要转换复权因子索引为字符串
                if pd.api.types.is_datetime64_any_dtype(dividend_factors_indexed.index):
                    # 将复权因子的datetime索引转换为字符串格式
                    dividend_factors_indexed.index = dividend_factors_indexed.index.strftime('%Y%m%d%H%M%S')
                    logger.debug("复权因子数据索引已转换为字符串格式以匹配价格数据")
            elif pd.api.types.is_datetime64_any_dtype(price_data_processed.index):
                # 价格数据索引是datetime类型，确保复权因子索引也是datetime类型
                if not pd.api.types.is_datetime64_any_dtype(dividend_factors_indexed.index):
                    # 将复权因子的字符串索引转换为datetime格式
                    dividend_factors_indexed.index = smart_to_datetime(dividend_factors_indexed.index)
                    logger.debug("复权因子数据索引已转换为datetime格式以匹配价格数据")
            else:
                # 其他情况，尝试使用IndexManager确保正确的索引格式
                price_data_processed = IndexManager.ensure_proper_index(price_data_processed, 'time')
                logger.debug("使用IndexManager处理价格数据索引格式")

            logger.debug(f"索引类型统一完成 - 价格数据: {type(price_data_processed.index)}, 复权因子: {type(dividend_factors_indexed.index)}")

            return self.adjustment_engine.calculate_forward_adjustment(
                price_data_processed, dividend_factors_indexed, method
            )
        except Exception as e:
            logger.error(f"前复权计算失败: {e}")
            return None
    
    def _calculate_backward_adjustment(
        self, 
        price_data: pd.DataFrame, 
        dividend_factors: pd.DataFrame, 
        method: str
    ) -> Optional[pd.DataFrame]:
        """计算后复权数据
        
        Args:
            price_data: 原始价格数据
            dividend_factors: 复权因子数据
            method: 计算方法
            
        Returns:
            后复权后的价格数据
        """
        try:
            # 后复权计算（暂时使用前复权引擎的逻辑，后续可扩展）
            logger.warning("后复权计算功能暂未实现，返回原始数据")
            return price_data.copy()
        except Exception as e:
            logger.error(f"后复权计算失败: {e}")
            return None
    
    def batch_synthesize_adjusted_data(
        self,
        stock_data: Dict[str, pd.DataFrame],
        dividend_type: str = "front",
        method: str = "ratio",
        use_cache: bool = True,
        max_workers: int = 4
    ) -> Dict[str, pd.DataFrame]:
        """批量合成复权数据
        
        Args:
            stock_data: 股票数据字典 {symbol: price_data}
            dividend_type: 复权类型
            method: 计算方法
            use_cache: 是否使用缓存
            max_workers: 最大并发数
            
        Returns:
            复权后的股票数据字典
        """
        try:
            results = {}
            stock_codes = list(stock_data.keys())
            
            logger.info(f"开始批量合成复权数据，股票数量: {len(stock_codes)}")
            
            for symbol in stock_codes:
                price_data = stock_data[symbol]
                adjusted_data = self.synthesize_adjusted_data(
                    symbol=symbol,
                    price_data=price_data,
                    dividend_type=dividend_type,
                    method=method,
                    use_cache=use_cache
                )
                results[symbol] = adjusted_data if adjusted_data is not None else price_data
            
            logger.info(f"批量复权数据合成完成，处理 {len(results)} 只股票")
            return results
            
        except Exception as e:
            logger.error(f"批量复权数据合成失败: {e}")
            return stock_data
    
    def invalidate_cache_for_symbol(self, symbol: str) -> bool:
        """使指定股票的缓存失效
        
        Args:
            symbol: 股票代码
            
        Returns:
            操作是否成功
        """
        try:
            # 统一缓存管理器暂不支持按symbol失效，记录日志
            logger.info(f"股票 {symbol} 缓存失效请求已记录，统一缓存管理器将基于使用频率自动清理")
            return True
        except Exception as e:
            logger.error(f"使股票 {symbol} 缓存失效失败: {e}")
            return False
    
    def update_dividend_factors_for_symbol(self, symbol: str, force_update: bool = False) -> bool:
        """更新指定股票的复权因子数据
        
        Args:
            symbol: 股票代码
            force_update: 是否强制更新
            
        Returns:
            更新是否成功
        """
        try:
            success = self.factor_storage.update_dividend_factors(symbol, force_update)
            if success:
                # 更新成功后，使相关缓存失效
                self.invalidate_cache_for_symbol(symbol)
                logger.info(f"股票 {symbol} 复权因子数据更新成功")
            return success
        except Exception as e:
            logger.error(f"更新股票 {symbol} 复权因子数据失败: {e}")
            return False
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取复权系统状态信息

        Returns:
            系统状态信息字典
        """
        try:
            return {
                'dividend_factor_storage': self.factor_storage.get_storage_info(),
                'data_accessor': 'VectorizedDataReader内置缓存',
                'system_time': datetime.now().isoformat(),
                'status': 'healthy'
            }
        except Exception as e:
            logger.error(f"获取系统状态失败: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'system_time': datetime.now().isoformat()
            }


# 创建全局实例
adjustment_synthesizer = AdjustmentSynthesizer()
