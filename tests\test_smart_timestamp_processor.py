#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能时间戳处理器测试

测试SmartTimestampProcessor的核心功能，确保时间戳提取和处理的准确性。
"""

import pandas as pd
import numpy as np
from datetime import datetime
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.smart_timestamp_processor import (
    SmartTimestampProcessor, 
    get_smart_timestamp_processor,
    extract_partition_timestamp,
    analyze_data_time_range
)


def test_basic_timestamp_extraction():
    """测试基本时间戳提取功能"""
    print("=== 测试基本时间戳提取功能 ===")
    
    # 创建测试数据
    test_data = pd.DataFrame({
        'time': [1752562262000, 1752562265000, 1752562268000],  # 2025年7月15日的毫秒时间戳
        'price': [13.53, 13.54, 13.55]
    })
    
    processor = get_smart_timestamp_processor()
    
    # 测试时间戳提取
    timestamp = processor.get_partition_timestamp(test_data, "tick")
    print(f"提取的时间戳: {timestamp}")
    
    # 验证结果
    expected = "20250715"  # 应该是2025年7月15日
    if timestamp == expected:
        print("✅ 基本时间戳提取测试通过")
        return True
    else:
        print(f"❌ 基本时间戳提取测试失败，期望: {expected}, 实际: {timestamp}")
        return False


def test_time_range_analysis():
    """测试时间范围分析功能"""
    print("\n=== 测试时间范围分析功能 ===")
    
    # 创建跨时间的测试数据
    test_data = pd.DataFrame({
        'time': [1752562262000, 1752629698000],  # 从7月15日到7月16日
        'price': [13.53, 13.45]
    })
    
    time_range = analyze_data_time_range(test_data)
    
    if time_range:
        print(f"开始时间: {time_range.start_timestamp}")
        print(f"结束时间: {time_range.end_timestamp}")
        print(f"跨越天数: {time_range.span_days}")
        print(f"日期范围: {time_range.date_range}")
        
        # 验证结果
        if time_range.start_timestamp.startswith("20250715") and time_range.end_timestamp.startswith("20250716"):
            print("✅ 时间范围分析测试通过")
            return True
        else:
            print("❌ 时间范围分析测试失败")
            return False
    else:
        print("❌ 时间范围分析返回None")
        return False


def test_fallback_mechanism():
    """测试备选机制"""
    print("\n=== 测试备选机制 ===")
    
    # 创建没有时间列的测试数据
    test_data = pd.DataFrame({
        'price': [13.53, 13.54, 13.55],
        'volume': [100, 200, 300]
    })
    
    fallback_timestamp = "20250805"
    timestamp = extract_partition_timestamp(test_data, "tick", fallback_timestamp)
    
    print(f"备选时间戳: {fallback_timestamp}")
    print(f"提取的时间戳: {timestamp}")
    
    if timestamp == fallback_timestamp:
        print("✅ 备选机制测试通过")
        return True
    else:
        print("❌ 备选机制测试失败")
        return False


def test_cross_date_analysis():
    """测试跨日期数据分析"""
    print("\n=== 测试跨日期数据分析 ===")
    
    processor = get_smart_timestamp_processor()
    
    # 创建跨日期测试数据
    test_data = pd.DataFrame({
        'time': [
            1752562262000,  # 2025-07-15 14:51:02
            1752629698000   # 2025-07-16 09:34:58
        ],
        'price': [13.53, 13.45]
    })
    
    grouped_data = processor.analyze_cross_date_data(test_data)
    
    print(f"分组结果: {list(grouped_data.keys())}")
    
    if len(grouped_data) >= 1:
        print("✅ 跨日期数据分析测试通过")
        return True
    else:
        print("❌ 跨日期数据分析测试失败")
        return False


def test_empty_data_handling():
    """测试空数据处理"""
    print("\n=== 测试空数据处理 ===")
    
    # 测试空DataFrame
    empty_df = pd.DataFrame()
    timestamp = extract_partition_timestamp(empty_df, "tick", "20250805")
    
    print(f"空数据提取的时间戳: {timestamp}")
    
    if timestamp == "20250805":
        print("✅ 空数据处理测试通过")
        return True
    else:
        print("❌ 空数据处理测试失败")
        return False


def run_all_tests():
    """运行所有测试"""
    print("开始智能时间戳处理器测试...")
    
    tests = [
        test_basic_timestamp_extraction,
        test_time_range_analysis,
        test_fallback_mechanism,
        test_cross_date_analysis,
        test_empty_data_handling
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试执行失败: {e}")
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return True
    else:
        print("⚠️ 部分测试失败")
        return False


if __name__ == "__main__":
    run_all_tests()
