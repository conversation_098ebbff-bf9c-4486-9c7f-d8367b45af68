#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
统一路径管理模块

提供系统级别的统一路径管理功能，解决路径不一致问题。

主要功能：
1. 统一的路径构建和验证
2. 智能路径检测和修复
3. 路径缓存和性能优化
4. 配置管理和环境变量支持

使用示例：
```python
from utils.path_manager import get_path_manager, build_partitioned_path

# 获取路径管理器
pm = get_path_manager()

# 构建分区路径
path = build_partitioned_path("rb00.SF", "tick", "20250722")
print(path)  # D:\\data\\SF\\rb00\\tick\\2025\\07\\22.parquet
```

版本: v1.0
作者: Augment AI
日期: 2025-07-22
"""

from .unified_path_manager import (
    PathManagerV2 as PathManager,
    PathManagerError,
    get_path_manager,
    get_data_root,
    parse_symbol,
    build_partitioned_path,
    get_base_directory,
    validate_path,
    normalize_path,
    is_futures_symbol,
    is_stock_symbol,
    get_latest_partition_file,
    get_earliest_partition_file
)

from .path_validator import (
    PathValidator,
    PathValidationResult,
    validate_paths,
    generate_report
)

from .path_fixer import (
    PathFixer,
    PathFixResult,
    fix_paths
)

# 版本信息
__version__ = "1.0.0"
__author__ = "Augment AI"
__email__ = "<EMAIL>"

# 导出的公共接口
__all__ = [
    # 核心类
    'PathManager',
    'PathManagerError',

    # 主要函数
    'get_path_manager',
    'get_data_root',
    'parse_symbol',
    'build_partitioned_path',
    'get_base_directory',
    'validate_path',
    'normalize_path',

    # 新增函数
    'is_futures_symbol',
    'is_stock_symbol',
    'get_latest_partition_file',
    'get_earliest_partition_file',

    # 路径验证和修复
    'PathValidator',
    'PathValidationResult',
    'validate_paths',
    'generate_report',
    'PathFixer',
    'PathFixResult',
    'fix_paths',

    # 版本信息
    '__version__',
    '__author__',
    '__email__'
]

# 模块级别的初始化
def initialize():
    """初始化路径管理模块"""
    try:
        pm = get_path_manager()
        return True
    except Exception as e:
        print(f"路径管理模块初始化失败: {e}")
        return False

# 自动初始化
if not initialize():
    print("警告: 路径管理模块初始化失败，可能影响系统功能")
