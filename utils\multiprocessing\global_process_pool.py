#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
全局进程池管理器

提供全局共享的进程池，避免为每个任务创建新的进程池
支持任务提交、结果收集和资源管理
"""

import os
import time
import atexit
import gc
import multiprocessing
from typing import Any, Callable, Dict, List, Optional, Tuple

# 导入日志模块
from utils.logger import get_unified_logger, LogTarget
logger = get_unified_logger(__name__, enhanced=True)


class GlobalProcessPool:
    """
    全局进程池管理器，实现为单例模式
    
    提供全局共享的进程池，避免为每个任务创建新的进程池
    支持任务提交、结果收集和资源管理
    """
    _instance = None
    _pool = None
    _initialized = False
    _task_count = 0
    _start_time = None
    
    @classmethod
    def get_instance(cls, max_workers: Optional[int] = None) -> 'GlobalProcessPool':
        """
        获取全局进程池实例
        
        Args:
            max_workers: 最大工作进程数，默认使用配置值或CPU核心数
            
        Returns:
            GlobalProcessPool: 全局进程池实例
        """
        if cls._instance is None:
            cls._instance = GlobalProcessPool(max_workers)
        elif not cls._instance.is_active():
            # 如果实例存在但进程池已关闭，重置实例并重新初始化
            logger.info(LogTarget.FILE, "检测到全局进程池已关闭，正在重新初始化...")
            cls._initialized = False
            cls._instance = GlobalProcessPool(max_workers)
        return cls._instance
    
    @classmethod
    def reset(cls):
        """
        重置全局进程池
        
        关闭现有的进程池并允许创建新的实例
        """
        if cls._instance is not None:
            cls._instance.close()
            cls._instance = None
            cls._initialized = False
            logger.info(LogTarget.FILE, "全局进程池已重置")
    
    def is_active(self) -> bool:
        """
        检查进程池是否处于活动状态
        
        Returns:
            bool: 如果进程池已初始化且未关闭，则返回True
        """
        return GlobalProcessPool._initialized and self._pool is not None
    
    def __init__(self, max_workers: Optional[int] = None):
        """
        初始化进程池
        
        Args:
            max_workers: 最大工作进程数，默认使用配置值或CPU核心数
        """
        # 单例模式检查
        if GlobalProcessPool._initialized:
            return
        
        # 记录启动时间
        self._start_time = time.time()
        
        # 确定进程数
        if max_workers is None:
            # 尝试从配置中获取
            try:
                from config.settings import GLOBAL_POOL_MAX_WORKERS
                max_workers = GLOBAL_POOL_MAX_WORKERS
            except (ImportError, AttributeError):
                # 配置不可用，使用CPU核心数
                max_workers = None
        
        # 如果配置值为None，使用CPU核心数
        if max_workers is None:
            max_workers = os.cpu_count()
        
        # 限制最大进程数，避免资源耗尽
        max_workers = min(max_workers, os.cpu_count() * 2)
        
        logger.info(LogTarget.FILE, f"初始化全局进程池，工作进程数: {max_workers}")
        
        # 创建进程池
        if self._pool is None:
            self._pool = multiprocessing.Pool(processes=max_workers)
            
            # 注册退出处理函数
            atexit.register(self.close)
        
        # 标记为已初始化
        GlobalProcessPool._initialized = True
        logger.debug(LogTarget.FILE, "全局进程池初始化完成")
    
    def map(self, func: Callable, iterable: List[Any], 
            chunksize: int = 1) -> List[Any]:
        """
        使用进程池映射函数到可迭代对象的每个元素
        
        Args:
            func: 要应用的函数
            iterable: 可迭代对象
            chunksize: 每个工作进程处理的任务块大小
            
        Returns:
            List[Any]: 结果列表
        """
        if self._pool is None:
            raise RuntimeError("进程池未初始化或已关闭")
        
        # 记录任务数量
        task_count = len(iterable)
        self._task_count += task_count
        
        msg = f"提交 {task_count} 个任务到进程池，总任务数: {self._task_count}"
        logger.debug(LogTarget.FILE, msg)
        
        try:
            # 执行映射
            start_time = time.time()
            results = self._pool.map(func, iterable, chunksize)
            elapsed_time = time.time() - start_time
            
            logger.debug(LogTarget.FILE, f"完成 {task_count} 个任务，耗时: {elapsed_time:.2f}秒")
            return results
            
        except Exception as e:
            logger.error(LogTarget.FILE, f"执行任务时出错: {e}")
            raise
    
    def apply_async(self, func: Callable, args: Tuple = (), 
                    kwargs: Dict = {}, callback: Optional[Callable] = None,
                    error_callback: Optional[Callable] = None) -> Any:
        """
        异步应用函数
        
        Args:
            func: 要应用的函数
            args: 位置参数
            kwargs: 关键字参数
            callback: 成功回调函数
            error_callback: 错误回调函数
            
        Returns:
            Any: AsyncResult对象
        """
        if self._pool is None:
            raise RuntimeError("进程池未初始化或已关闭")
        
        # 记录任务
        self._task_count += 1
        
        logger.debug(LogTarget.FILE, f"提交异步任务到进程池，总任务数: {self._task_count}")
        
        return self._pool.apply_async(
            func, args, kwargs, callback, error_callback
        )
    
    def close(self):
        """
        关闭进程池并释放资源
        """
        if self._pool:
            logger.info(LogTarget.FILE, "关闭全局进程池")
            
            # 记录运行统计信息
            if self._start_time:
                uptime = time.time() - self._start_time
                logger.info(
                    LogTarget.FILE,
                    f"进程池运行时间: {uptime:.2f}秒，处理任务总数: {self._task_count}"
                )
            
            # 关闭进程池
            self._pool.close()
            self._pool.join()
            self._pool = None
            
            # 重置状态
            GlobalProcessPool._initialized = False
            self._task_count = 0
            self._start_time = None
            
            # 强制垃圾回收
            gc.collect()
            
            logger.info(LogTarget.FILE, "全局进程池已关闭并释放资源")
    
    def __del__(self):
        """
        析构函数，确保进程池被关闭
        """
        self.close()
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取进程池统计信息
        
        Returns:
            Dict[str, Any]: 统计信息字典
        """
        stats = {
            "initialized": GlobalProcessPool._initialized,
            "task_count": self._task_count,
        }
        
        if self._start_time:
            stats["uptime"] = time.time() - self._start_time
        
        return stats 