#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据处理工具模块

提供通用的数据处理、格式化和验证功能
"""

import pandas as pd
from typing import Any, Dict, Optional, Tuple
from datetime import datetime

from utils.logger import get_unified_logger, LogTarget
from utils.smart_time_converter import smart_to_datetime

logger = get_unified_logger(__name__, enhanced=True)


class DataProcessor:
    """数据处理工具类"""
    
    @staticmethod
    def process_downloaded_data(symbol: str, data_obj: Any) -> Optional[pd.DataFrame]:
        """
        处理下载的数据，支持DataFrame、Series和字典类型
        
        Args:
            symbol: 股票代码
            data_obj: 下载的数据对象
            
        Returns:
            处理后的DataFrame，失败返回None
        """
        if data_obj is None:
            logger.warning(f"{symbol} 数据源返回None")
            return None
            
        # DataFrame直接返回
        if isinstance(data_obj, pd.DataFrame):
            if data_obj.empty:
                logger.warning(f"{symbol} 返回空DataFrame")
            return data_obj
            
        # Series转DataFrame
        elif isinstance(data_obj, pd.Series):
            if data_obj.empty:
                logger.warning(f"{symbol} 返回空Series")
                return pd.DataFrame()
            
            try:
                col_name = data_obj.name if data_obj.name is not None else symbol
                return pd.DataFrame({col_name: data_obj})
            except Exception as e:
                logger.error(f"转换{symbol}的Series失败: {e}")
                return None
                
        # 字典转DataFrame
        elif isinstance(data_obj, dict):
            if not data_obj:
                logger.warning(f"{symbol} 返回空字典")
                return pd.DataFrame()
                
            try:
                # 收集Series类型数据
                series_dict = {k: v for k, v in data_obj.items() 
                              if isinstance(v, pd.Series)}
                
                if series_dict:
                    return pd.DataFrame(series_dict)
                else:
                    logger.warning(f"{symbol} 字典中无Series数据")
                    return pd.DataFrame()
            except Exception as e:
                logger.error(f"处理{symbol}字典数据失败: {e}")
                return None
                
        else:
            logger.warning(f"{symbol} 不支持的数据类型: {type(data_obj)}")
            return None
    
    @staticmethod
    def format_display_data(df: pd.DataFrame, 
                          data_mode: str = "both",
                          head_rows: int = 5,
                          tail_rows: int = 5) -> pd.DataFrame:
        """
        格式化显示数据
        
        Args:
            df: 要格式化的DataFrame
            data_mode: 显示模式 ("head", "tail", "both", "all")
            head_rows: 头部行数
            tail_rows: 尾部行数
            
        Returns:
            格式化后的DataFrame
        """
        if df is None or df.empty:
            return pd.DataFrame()
            
        if data_mode == "all":
            return df
        elif data_mode == "head":
            return df.head(head_rows)
        elif data_mode == "tail":
            return df.tail(tail_rows)
        else:  # "both"
            if len(df) <= head_rows + tail_rows:
                return df
                
            head_df = df.head(head_rows)
            tail_df = df.tail(tail_rows)
            
            # 创建分隔行
            empty_row = pd.DataFrame(
                [pd.Series(["..."] * len(df.columns), index=df.columns)], 
                index=["..."]
            )
            
            return pd.concat([head_df, empty_row, tail_df])
    
    @staticmethod
    def extract_timestamp_from_data(df: pd.DataFrame,
                                   end_time: Optional[str] = None) -> Optional[str]:
        """
        从DataFrame中提取时间戳（使用统一日期提取模块）

        Args:
            df: 数据DataFrame
            end_time: 备选时间戳

        Returns:
            格式为'YYYYMMDD'的时间戳
        """
        try:
            # 使用统一日期提取模块
            from utils.time_formatter.date_extraction import extract_timestamp_from_data as unified_extract

            result = unified_extract(df, end_time)

            # 确保返回格式为YYYYMMDD（兼容原有接口）
            if result and len(result) >= 8:
                return result[:8]

            return result

        except Exception as e:
            logger.error(f"提取时间戳失败: {e}")
            return end_time[:8] if end_time else None
            
        return end_time[:8] if end_time else None
    
    @staticmethod
    def validate_symbol_format(symbol: str) -> bool:
        """
        验证股票代码格式
        
        Args:
            symbol: 股票代码
            
        Returns:
            是否为有效格式
        """
        if not isinstance(symbol, str) or '.' not in symbol:
            return False
            
        parts = symbol.split('.')
        return len(parts) == 2 and parts[0] and parts[1]
    
    @staticmethod
    def get_data_time_range(df: pd.DataFrame) -> Tuple[Optional[datetime], Optional[datetime]]:
        """
        获取数据的时间范围
        
        Args:
            df: 数据DataFrame
            
        Returns:
            (开始时间, 结束时间) 的元组
        """
        try:
            if df is None or df.empty:
                return None, None
                
            # 优先使用time列
            if 'time' in df.columns:
                time_series = df['time'].dropna()
                if not time_series.empty:
                    min_time = time_series.min()
                    max_time = time_series.max()
                    
                    # 处理毫秒时间戳
                    if isinstance(min_time, (int, float)):
                        min_time = smart_to_datetime(min_time, unit='ms')
                        max_time = smart_to_datetime(max_time, unit='ms')
                    else:
                        min_time = smart_to_datetime(min_time)
                        max_time = smart_to_datetime(max_time)
                        
                    return min_time, max_time
                    
            # 使用索引
            elif isinstance(df.index, pd.DatetimeIndex):
                return df.index.min(), df.index.max()
                
        except Exception as e:
            logger.error(f"获取数据时间范围失败: {e}")
            
        return None, None