#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
文本解析工具模块

提供各类文本解析函数，包括股票代码、期货合约等金融工具代码解析
"""

import re
from typing import List, Dict, Any, Tuple, Optional


def parse_instrument_input(text: str) -> List[str]:
    """
    解析金融工具代码输入
    
    支持多种格式:
    - 单个代码: 000001
    - 带市场的代码: 000001.SZ
    - 代码列表: 000001,600000,300059
    - 带分隔符的代码列表: 000001.SZ,600000.SH,300059.SZ
    - 混合输入: 000001 600000.SH 300059
    - 表格行数据: "601866.SH 中远海发    SH     --   0.01     1        2007-12-12 长期有效   未知"
    
    Args:
        text: 用户输入的代码文本
        
    Returns:
        规范化的代码列表
    """
    # 首先尝试使用增强的股票代码解析函数
    # 该函数能处理更复杂的格式，包括表格行和带注释的代码
    codes = parse_stock_code_input(text)
    
    # 如果能够解析出标准格式的股票代码，直接返回
    if codes:
        return codes
    
    # 旧的解析逻辑作为后备方案
    # 去除首尾空格
    text = text.strip()
    
    if not text:
        return []
    
    # 处理多种分隔符
    # 先将常见分隔符统一为逗号
    text = re.sub(r'[\s;，、|]+', ',', text)
    
    # 按逗号分割
    codes = [code.strip() for code in text.split(',') if code.strip()]
    
    # 规范化代码格式
    normalized_codes = []
    for code in codes:
        # 已经有市场后缀
        if '.' in code:
            # 保持原始大小写格式，不进行转换
            normalized_codes.append(code)
        else:
            # 根据代码前缀判断市场
            code_num = code
            if code_num.startswith(('6', '5', '9')):
                normalized_codes.append(f"{code_num}.SH")
            elif code_num.startswith(('0', '1', '2', '3')):
                normalized_codes.append(f"{code_num}.SZ")
            elif code_num.startswith(('4', '8')):
                normalized_codes.append(f"{code_num}.BJ")
            else:
                # 无法判断市场的，保持原始格式
                normalized_codes.append(code)
    
    return normalized_codes


def parse_key_value_text(text: str, delimiter: str = '=') -> Dict[str, str]:
    """
    解析键值对文本
    
    Args:
        text: 包含键值对的文本
        delimiter: 键值对的分隔符，默认为'='
        
    Returns:
        包含解析结果的字典
    """
    result = {}
    
    if not text:
        return result
    
    # 按行分割
    lines = text.strip().split('\n')
    
    for line in lines:
        line = line.strip()
        
        # 跳过空行和注释行
        if not line or line.startswith('#'):
            continue
        
        # 查找分隔符
        parts = line.split(delimiter, 1)
        if len(parts) == 2:
            key = parts[0].strip()
            value = parts[1].strip()
            
            # 去除值两侧的引号
            if (value.startswith('"') and value.endswith('"')) or \
               (value.startswith("'") and value.endswith("'")):
                value = value[1:-1]
            
            result[key] = value
    
    return result


def parse_number_range(text: str) -> Tuple[Optional[float], Optional[float]]:
    """
    解析数字范围
    
    支持格式:
    - 单个数字: 100
    - 范围格式: 10-100
    - 大于格式: >10 或 >=10
    - 小于格式: <100 或 <=100
    
    Args:
        text: 表示数字范围的文本
        
    Returns:
        (最小值, 最大值)的元组，None表示无限制
    """
    text = text.strip()
    
    if not text:
        return None, None
    
    # 检查是否为范围格式
    range_match = re.match(r'^([-+]?\d*\.?\d+)\s*-\s*([-+]?\d*\.?\d+)$', text)
    if range_match:
        min_val = float(range_match.group(1))
        max_val = float(range_match.group(2))
        return min_val, max_val
    
    # 检查是否为大于格式
    gt_match = re.match(r'^>\s*([-+]?\d*\.?\d+)$', text)
    gte_match = re.match(r'^>=\s*([-+]?\d*\.?\d+)$', text)
    if gt_match:
        min_val = float(gt_match.group(1))
        return min_val, None
    if gte_match:
        min_val = float(gte_match.group(1))
        return min_val, None
    
    # 检查是否为小于格式
    lt_match = re.match(r'^<\s*([-+]?\d*\.?\d+)$', text)
    lte_match = re.match(r'^<=\s*([-+]?\d*\.?\d+)$', text)
    if lt_match:
        max_val = float(lt_match.group(1))
        return None, max_val
    if lte_match:
        max_val = float(lte_match.group(1))
        return None, max_val
    
    # 尝试解析为单个数字
    try:
        val = float(text)
        return val, val
    except ValueError:
        return None, None


def parse_date_range(text: str, date_format: str = '%Y%m%d') -> Tuple[Optional[str], Optional[str]]:
    """
    解析日期范围
    
    支持格式:
    - 单个日期: 20210101
    - 范围格式: 20210101-20210131
    - 大于格式: >20210101 或 >=20210101
    - 小于格式: <20210131 或 <=20210131
    
    Args:
        text: 表示日期范围的文本
        date_format: 日期格式字符串
        
    Returns:
        (开始日期, 结束日期)的元组，格式与输入一致，None表示无限制
    """
    # 实现类似parse_number_range的逻辑，但针对日期字符串
    text = text.strip()
    
    if not text:
        return None, None
    
    # 检查是否为范围格式
    range_match = re.match(r'^(\d{8})\s*-\s*(\d{8})$', text)
    if range_match:
        start_date = range_match.group(1)
        end_date = range_match.group(2)
        return start_date, end_date
    
    # 检查是否为大于格式
    gt_match = re.match(r'^>\s*(\d{8})$', text)
    gte_match = re.match(r'^>=\s*(\d{8})$', text)
    if gt_match:
        start_date = gt_match.group(1)
        return start_date, None
    if gte_match:
        start_date = gte_match.group(1)
        return start_date, None
    
    # 检查是否为小于格式
    lt_match = re.match(r'^<\s*(\d{8})$', text)
    lte_match = re.match(r'^<=\s*(\d{8})$', text)
    if lt_match:
        end_date = lt_match.group(1)
        return None, end_date
    if lte_match:
        end_date = lte_match.group(1)
        return None, end_date
    
    # 尝试解析为单个日期
    date_match = re.match(r'^(\d{8})$', text)
    if date_match:
        date = date_match.group(1)
        return date, date
    
    return None, None


def parse_text_items(text: str, normalize: bool = False) -> List[str]:
    """
    通用文本项目解析函数，支持多种分隔符
    
    比parse_instrument_input更通用，不会尝试添加市场后缀。
    支持多种格式:
    - 空格分隔: "item1 item2 item3"
    - 逗号分隔: "item1,item2,item3"
    - 混合分隔: "item1 item2,item3;item4"
    - 支持中文分隔符: "item1，item2、item3"
    
    Args:
        text: 用户输入的文本
        normalize: 是否将文本标准化为大写（默认False）
        
    Returns:
        解析后的项目列表
    """
    # 去除首尾空格
    text = text.strip()
    
    if not text:
        return []
    
    # 处理多种分隔符
    # 先将常见分隔符统一为空格
    text = re.sub(r'[,;，、|]+', ' ', text)
    
    # 按空格分割并去除空项
    items = [item.strip() for item in re.split(r'\s+', text) if item.strip()]
    
    # 如果需要，将所有项标准化为大写
    if normalize:
        items = [item.upper() for item in items]
    
    return items 


def parse_stock_code_input(text: str) -> List[str]:
    """
    解析股票代码输入，处理带有注释的文本、表格行和其他复杂格式
    
    支持格式：
    - 标准代码格式: "600000.SH", "000001.SZ"
    - 带注释的代码: "'pg00.DF',  # 液化气连续(2506)         DCE    --   1.0      20       0          0          未知"
    - 带引号的代码: "'000001.SZ'" 或 "\"600000.SH\""
    - 混合格式输入: "sh000001, '600000.SH' # 上证指数"
    - 表格行数据: "601866.SH 中远海发    SH     --   0.01     1        2007-12-12 长期有效   未知"
    - 多行输入，每行一个代码
    
    自动处理：
    - 从复杂表格行中提取标准格式的股票代码（如"601866.SH"）
    - 删除Python风格的注释(#后面的内容)
    - 删除引号和括号
    - 处理常见分隔符(逗号、空格等)
    - 严格过滤只保留符合"代码.交易所"格式的股票代码
    
    Args:
        text: 包含股票代码的文本
        
    Returns:
        List[str]: 提取的有效股票代码列表
    """
    # 去除首尾空格
    text = text.strip()
    
    if not text:
        return []
    
    # 首先定义股票代码的正则表达式模式
    # 标准格式: 数字和字母组成的代码，后跟点号和2-3个字母的交易所代码
    stock_pattern = r'([A-Za-z0-9]+)\.([A-Za-z]{2,5})'
    
    # 提取的所有可能的股票代码
    all_codes = []
    
    # 按行分割输入
    lines = text.split('\n')
    
    for line in lines:
        # 去除行首尾空格
        line = line.strip()
        
        # 跳过空行
        if not line:
            continue
        
        # 去除Python风格的注释（#后面的内容）
        comment_pos = line.find('#')
        if comment_pos >= 0:
            line = line[:comment_pos].strip()
        
        # 在每行中查找所有符合股票代码格式的模式
        for match in re.finditer(stock_pattern, line):
            code_part = match.group(1)
            market_part = match.group(2).upper()
            
            # 构造完整的股票代码，保持原始大小写
            full_code = f"{code_part}.{market_part}"
            all_codes.append(full_code)
    
    # 如果没有找到任何股票代码，尝试使用传统的方法解析
    if not all_codes:
        # 处理带引号的项目
        # 先识别出所有引号括起来的代码
        quoted_items = []
        pattern = r'["\']([^"\']+)["\']'
        for match in re.finditer(pattern, text):
            quoted_items.append(match.group(1).strip())
        
        # 从原文本中移除所有引号括起来的内容
        processed_text = re.sub(pattern, ' ', text)
        
        # 处理多种分隔符
        # 先将常见分隔符统一为空格
        processed_text = re.sub(r'[,;，、|]+', ' ', processed_text)
        
        # 按空格分割并去除空项
        non_quoted_items = [item.strip() for item in processed_text.split() if item.strip()]
        
        # 合并引号括起来的项目和非引号项目
        all_items = quoted_items + non_quoted_items
        
        # 移除代码中可能存在的引号、括号等字符
        for item in all_items:
            # 去除可能残留的引号和括号
            item = re.sub(r'["\'\(\)\[\]\{\}]', '', item)
            item = item.strip()
            
            # 检查清理后的项目是否符合股票代码格式
            match = re.match(stock_pattern, item)
            if match:
                code_part = match.group(1)
                market_part = match.group(2)  # 保持原始大小写
                full_code = f"{code_part}.{market_part}"
                all_codes.append(full_code)
    
    # 过滤并保留唯一的有效股票代码
    unique_codes = []
    seen = set()
    
    for code in all_codes:
        # 保持原始大小写格式，不进行转换
        # 但验证时需要考虑大小写不敏感的匹配
        code_upper = code.upper()

        # 再次验证代码格式（使用大写版本验证）
        if re.match(r'^[A-Z0-9]+\.[A-Z]{2,5}$', code_upper) and code_upper not in seen:
            seen.add(code_upper)
            unique_codes.append(code)  # 保存原始格式
    
    return unique_codes 