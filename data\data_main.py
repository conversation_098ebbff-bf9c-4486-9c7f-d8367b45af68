#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据管理主入口模块

负责协调各个子模块，提供统一的程序入口点
包含系统初始化、日志配置、模块调度等功能
"""

import os
import sys

# 将项目根目录添加到系统路径
root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, root_path)

# 导入日志模块
from utils.logger import (
    get_unified_logger, LogTarget,
    setup_unified_logging, clean_old_log_files
)

# 导入各功能模块
from data.ui.cli import create_argument_parser, execute_command_line
from data.ui.interactive import interactive_mode

# 初始化
logger = None  # 将在main函数中初始化


def initialize_system():
    """初始化系统"""
    global logger
    
    # 初始化日志系统
    setup_unified_logging(
        default_target=LogTarget.FILE,
        use_table_formatter=True,
        module_width=30,
        print_init_message=False
    )
    
    # 清理旧的日志文件
    deleted_count = clean_old_log_files()
    if deleted_count > 0:
        print(f"已清理 {deleted_count} 个旧日志文件")
    
    # 初始化日志记录器
    logger = get_unified_logger(__name__, enhanced=True)


def main():
    """主函数"""
    try:
        # 初始化系统
        initialize_system()
        
        # 解析命令行参数
        parser = create_argument_parser()
        args = parser.parse_args()
        
        # 执行命令行命令
        result = execute_command_line(args)
        
        if result is not None:
            # 命令行模式已处理完成
            return result
        elif args.interactive:
            # 显式请求交互模式
            interactive_mode()
            return 0
        else:
            # 默认启动交互模式
            interactive_mode()
            return 0
    
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        return 1
    except Exception as e:
        print(f"\n程序发生错误: {str(e)}")
        if logger:
            logger.error(f"程序异常: {str(e)}", exc_info=True)
        return 1


if __name__ == "__main__":
    # 处理特殊的测试日志参数
    if len(sys.argv) > 1 and sys.argv[1] == "--test-logging":
        from utils.logger.examples import test_logging_system
        test_logging_system()
        sys.exit(0)
    else:
        # 正常运行主函数
        sys.exit(main())