#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
用户输入处理工具模块

提供各类用户输入处理函数，包括命令行参数解析、交互式输入等
"""

import os
import sys
import re
from typing import Any, List, Optional, Callable

# 将项目根目录添加到Python路径
root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, root_path)

# 导入项目模块
from utils.text_parser import parse_instrument_input  # noqa: E402


def get_input_with_default(
    prompt: str,
    default: Any = None,
    validator: Optional[Callable[[str], bool]] = None,
    error_message: str = "输入无效，请重新输入: "
) -> str:
    """
    获取用户输入，支持默认值和验证函数

    Args:
        prompt: 输入提示信息
        default: 默认值，如果用户直接按回车则返回此值
        validator: 验证函数，接收输入字符串并返回布尔值表示是否有效
        error_message: 验证失败时显示的错误消息

    Returns:
        用户输入的字符串或默认值
    """
    # 如果有默认值，在提示信息中显示
    if default is not None:
        prompt = f"{prompt} (默认: {default}): "
    else:
        prompt = f"{prompt}: "

    while True:
        user_input = input(prompt)

        # 如果用户直接按回车且有默认值，则返回默认值
        if not user_input and default is not None:
            return str(default)

        # 如果提供了验证函数，验证输入
        if validator is None or validator(user_input):
            return user_input

        # 输入验证失败，提示错误
        print(error_message)


def get_yes_no_input(prompt: str, default: bool = True) -> bool:
    """
    获取用户是/否输入

    Args:
        prompt: 输入提示信息
        default: 默认值，True表示是，False表示否

    Returns:
        用户选择，True表示是，False表示否
    """
    default_str = "y" if default else "n"
    prompt = f"{prompt} (y/n, 默认: {default_str}): "

    while True:
        user_input = input(prompt).lower()

        # 用户直接按回车，返回默认值
        if not user_input:
            return default

        # 解析用户输入
        if user_input in ["y", "yes", "是", "确认", "1", "true"]:
            return True
        if user_input in ["n", "no", "否", "不", "0", "false"]:
            return False

        # 输入无效，重新提示
        print("请输入 y/yes 或 n/no")


def get_choice_input(
    prompt: str,
    choices: List[str],
    default_index: Optional[int] = 0
) -> str:
    """
    获取用户从列表中的选择

    Args:
        prompt: 输入提示信息
        choices: 可选项列表
        default_index: 默认选项的索引，如果为None则没有默认值

    Returns:
        用户选择的选项

    Raises:
        ValueError: 如果choices为空列表
        IndexError: 如果default_index超出choices范围
    """
    if not choices:
        raise ValueError("选项列表不能为空")

    if default_index is not None and (default_index < 0 or default_index >= len(choices)):
        raise IndexError(f"默认索引{default_index}超出选项列表范围(0-{len(choices)-1})")

    # 构建选项菜单字符串
    menu = "\n".join([f"{i+1}. {choice}" for i, choice in enumerate(choices)])

    # 确定默认选项显示
    default_display = f" (默认: {default_index+1})" if default_index is not None else ""

    # 完整提示
    full_prompt = f"{prompt}\n{menu}\n请选择{default_display}: "

    while True:
        user_input = input(full_prompt)

        # 处理直接回车的情况
        if not user_input and default_index is not None:
            return choices[default_index]

        try:
            # 尝试将输入解析为数字
            choice_index = int(user_input) - 1
            if 0 <= choice_index < len(choices):
                return choices[choice_index]
        except ValueError:
            # 如果输入不是数字，检查是否直接输入了选项
            if user_input in choices:
                return user_input

        # 输入无效，重新提示
        print(f"请输入有效的选项编号(1-{len(choices)})")


def get_multi_choice_input(
    prompt: str,
    choices: List[str],
    default_indices: Optional[List[int]] = None
) -> List[str]:
    """
    获取用户从列表中的多选

    Args:
        prompt: 输入提示信息
        choices: 可选项列表
        default_indices: 默认选项的索引列表，如果为None则没有默认值

    Returns:
        用户选择的选项列表

    Raises:
        ValueError: 如果choices为空列表
        IndexError: 如果default_indices中的任何值超出choices范围
    """
    if not choices:
        raise ValueError("选项列表不能为空")

    if default_indices:
        for idx in default_indices:
            if idx < 0 or idx >= len(choices):
                raise IndexError(f"默认索引{idx}超出选项列表范围(0-{len(choices)-1})")

    # 构建选项菜单字符串
    menu = "\n".join([f"{i+1}. {choice}" for i, choice in enumerate(choices)])

    # 确定默认选项显示
    default_display = ""
    if default_indices:
        default_options = [str(idx + 1) for idx in default_indices]
        default_display = f" (默认: {','.join(default_options)})"

    # 完整提示
    full_prompt = f"{prompt}\n{menu}\n请选择(多个选项用逗号分隔){default_display}: "

    while True:
        user_input = input(full_prompt)

        # 处理直接回车的情况
        if not user_input and default_indices:
            return [choices[idx] for idx in default_indices]

        # 分割用户输入
        selection = []
        try:
            # 去除空格并按逗号分割
            items = [item.strip() for item in user_input.split(',')]

            for item in items:
                if item.isdigit():
                    # 数字输入
                    idx = int(item) - 1
                    if 0 <= idx < len(choices):
                        selection.append(choices[idx])
                elif item in choices:
                    # 直接输入选项
                    selection.append(item)

            # 如果至少有一个有效选择，返回结果
            if selection:
                return selection
        except (ValueError, IndexError) as e:
            print(f"无效的选择: {str(e)}")
            continue

        # 输入无效，重新提示
        print(f"请输入有效的选项编号(1-{len(choices)})，多个选项用逗号分隔")


def get_int_input(
    prompt: str,
    default: Optional[int] = None,
    min_value: Optional[int] = None,
    max_value: Optional[int] = None
) -> int:
    """
    获取整数输入

    Args:
        prompt: 输入提示信息
        default: 默认值，如果用户直接按回车则返回此值
        min_value: 最小允许值
        max_value: 最大允许值

    Returns:
        用户输入的整数
    """
    # 构建范围描述
    range_str = ""
    if min_value is not None and max_value is not None:
        range_str = f" ({min_value}-{max_value})"
    elif min_value is not None:
        range_str = f" (最小: {min_value})"
    elif max_value is not None:
        range_str = f" (最大: {max_value})"

    # 构建完整提示
    default_str = f", 默认: {default}" if default is not None else ""
    full_prompt = f"{prompt}{range_str}{default_str}: "

    while True:
        user_input = input(full_prompt)

        # 处理直接回车的情况
        if not user_input and default is not None:
            return default

        try:
            value = int(user_input)

            # 检查范围
            if min_value is not None and value < min_value:
                print(f"输入必须大于或等于 {min_value}")
                continue

            if max_value is not None and value > max_value:
                print(f"输入必须小于或等于 {max_value}")
                continue

            return value
        except ValueError:
            print("请输入有效的整数")


def get_float_input(
    prompt: str,
    default: Optional[float] = None,
    min_value: Optional[float] = None,
    max_value: Optional[float] = None
) -> float:
    """
    获取浮点数输入

    Args:
        prompt: 输入提示信息
        default: 默认值，如果用户直接按回车则返回此值
        min_value: 最小允许值
        max_value: 最大允许值

    Returns:
        用户输入的浮点数
    """
    # 构建范围描述
    range_str = ""
    if min_value is not None and max_value is not None:
        range_str = f" ({min_value}-{max_value})"
    elif min_value is not None:
        range_str = f" (最小: {min_value})"
    elif max_value is not None:
        range_str = f" (最大: {max_value})"

    # 构建完整提示
    default_str = f", 默认: {default}" if default is not None else ""
    full_prompt = f"{prompt}{range_str}{default_str}: "

    while True:
        user_input = input(full_prompt)

        # 处理直接回车的情况
        if not user_input and default is not None:
            return default

        try:
            value = float(user_input)

            # 检查范围
            if min_value is not None and value < min_value:
                print(f"输入必须大于或等于 {min_value}")
                continue

            if max_value is not None and value > max_value:
                print(f"输入必须小于或等于 {max_value}")
                continue

            return value
        except ValueError:
            print("请输入有效的数字")


def get_date_input(
    prompt: str,
    default: Optional[str] = None,
    format_str: str = "%Y%m%d"
) -> str:
    """
    获取日期输入

    Args:
        prompt: 输入提示信息
        default: 默认值，如果用户直接按回车则返回此值
        format_str: 日期格式字符串

    Returns:
        格式化的日期字符串
    """
    format_hint = {
        "%Y%m%d": "YYYYMMDD",
        "%Y-%m-%d": "YYYY-MM-DD",
        "%Y/%m/%d": "YYYY/MM/DD",
        "%Y%m": "YYYYMM",
        "%Y-%m": "YYYY-MM"
    }.get(format_str, format_str)

    # 检查提示信息中是否已包含格式说明，避免重复显示
    if f"(格式: {format_hint}" in prompt:
        full_prompt = prompt
        # 只添加默认值（如果不在提示中）
        if default is not None and f"默认: {default}" not in prompt:
            full_prompt += f" (默认: {default}): "
        elif not full_prompt.endswith(":"):
            full_prompt += ": "
    else:
        # 构建完整提示
        default_str = f", 默认: {default}" if default is not None else ""
        full_prompt = f"{prompt} (格式: {format_hint}{default_str}): "

    while True:
        user_input = input(full_prompt)

        # 处理直接回车的情况
        if not user_input and default is not None:
            return default

        try:
            # 验证日期格式
            from datetime import datetime
            datetime.strptime(user_input, format_str)
            return user_input
        except ValueError:
            print(f"请输入有效的日期，格式为 {format_hint}")


def get_multiline_input(
    prompt: str,
    end_marker: str = '',
    input_prefix: str = '> '
) -> str:
    """
    获取多行输入，直到用户输入结束标记（默认为空行）

    Args:
        prompt: 初始提示信息
        end_marker: 输入结束的标记，默认为空行
        input_prefix: 每行输入的前缀符号

    Returns:
        合并后的多行输入文本
    """
    print(prompt)
    # 拆分行以修复行长度问题
    end_hint = "空行" if not end_marker else end_marker
    print(f"(输入{end_hint}结束，支持多行粘贴)")

    lines = []
    while True:
        line = input(input_prefix)
        if line == end_marker:
            break
        lines.append(line)

    return '\n'.join(lines)


def get_code_list_input(
    prompt: str,
    default: Optional[List[str]] = None,
    validator: Optional[Callable[[str], bool]] = None,
    error_message: str = "输入无效，请重新输入"
) -> List[str]:
    """
    获取代码列表输入

    支持多种输入格式：
    1. 直接回车: 使用默认值
    2. 空格分隔: "000001.SZ 600000.SH"
    3. 逗号分隔: "000001.SZ,600000.SH"
    4. 多行输入，每行一个代码
    5. 带序号的列表: "1. 000001.SZ"
    6. 支持多行粘贴，包括带引号和注释的代码

    Args:
        prompt: 提示信息
        default: 默认代码列表
        validator: 验证函数，接收单个代码字符串
        error_message: 验证失败时显示的错误信息

    Returns:
        用户输入的代码列表
    """
    # 构建完整提示
    default_str = ""
    if default:
        default_str = f", 默认: {', '.join(default)}"

    # 拆分长行以修复行长度问题
    full_prompt = f"{prompt}"
    full_prompt += f" (多个代码用逗号、空格或换行分隔{default_str})"

    while True:
        # 使用多行输入函数
        user_input = get_multiline_input(full_prompt)

        # 处理直接回车的情况（空输入）
        if not user_input.strip() and default:
            return default

        # 使用text_parser中的函数解析代码输入
        codes = parse_instrument_input(user_input)

        # 如果有验证函数，验证每个代码
        if validator and codes:
            invalid_codes = [code for code in codes if not validator(code)]
            if invalid_codes:
                print(f"以下代码无效: {', '.join(invalid_codes)}")
                continue

        if codes:
            return codes
        else:
            print("请输入至少一个有效的代码")


# 保留原函数以兼容现有代码，但标记为过时
def parse_code_input(text: str) -> List[str]:
    """
    解析代码输入文本 (已废弃)

    请使用text_parser.parse_instrument_input替代此函数

    Args:
        text: 代码输入文本

    Returns:
        解析出的代码列表
    """
    # 调用 text_parser 中的更强大的解析函数
    return parse_instrument_input(text)


def get_period_input(prompt="请选择数据周期", default="tick"):
    """
    获取用户输入的数据周期

    Args:
        prompt: 提示信息
        default: 默认周期，当用户输入为空时返回

    Returns:
        str: 用户输入的数据周期
    """
    from utils.data_processor.period_handler import is_valid_period, get_native_periods

    # 获取原生支持的周期列表
    native_periods = get_native_periods()

    # 构建完整提示
    full_prompt = f"{prompt} ('tick', '1m', '5m', '15m', '30m', '1h', '1d',或输入其他数据周期如'3m'使用1m合成，默认：{default}): "

    while True:
        period = input(full_prompt).strip().lower()

        # 检查是否为空
        if not period:
            return default

        # 检查是否为有效周期（原生支持的或可合成的）
        if period in native_periods or is_valid_period(period):
            return period
        else:
            print(f"错误: '{period}' 不是有效的数据周期，请重新输入。")
            continue


def normalize_period_string(period: str) -> str:
    """
    标准化周期字符串

    Args:
        period: 输入的周期字符串

    Returns:
        标准化后的周期字符串
    """
    # 去除空格并转为小写
    period = period.strip().lower()

    # 处理常见的别名
    aliases = {
        't': 'tick',
        'tick': 'tick',
        'd': '1d',
        'day': '1d',
        'w': '1w',
        'week': '1w',
        'm': '1m',
        'month': '1mon',
        'min': '1m',
        'minute': '1m',
        'h': '1h',
        'hour': '1h',
    }

    # 检查是否是直接的别名
    if period in aliases:
        return aliases[period]

    # 处理带数字的周期，如"5m"、"30min"等
    match = re.match(r'^(\d+)([a-z]+)$', period)
    if match:
        number, unit = match.groups()

        # 标准化单位
        if unit in ['min', 'minute', 'minutes']:
            unit = 'm'
        elif unit in ['hour', 'hours']:
            unit = 'h'
        elif unit in ['day', 'days']:
            unit = 'd'
        elif unit in ['week', 'weeks']:
            unit = 'w'
        elif unit in ['month', 'months']:
            unit = 'mon'

        return f"{number}{unit}"

    # 如果无法标准化，返回原始值
    return period


if __name__ == "__main__":
    # 测试用户输入处理工具
    print("=== 用户输入处理工具测试 ===")

    # 注意：这些测试代码需要在终端环境下运行才能交互
    try:
        # 测试基本输入
        name = get_input_with_default("请输入您的名字", "用户")
        print(f"您输入的名字是: {name}")

        # 测试是/否输入
        confirm = get_yes_no_input("是否继续测试?")
        print(f"您选择了: {'继续' if confirm else '停止'}")

        if confirm:
            # 测试选择输入
            choices = ["选项A", "选项B", "选项C"]
            choice = get_choice_input("请选择一个选项", choices)
            print(f"您选择了: {choice}")

            # 测试多选输入
            multi_choices = get_multi_choice_input("请选择多个选项", choices)
            print(f"您选择了: {', '.join(multi_choices)}")

            # 测试整数输入
            age = get_int_input("请输入年龄", 25, 18, 120)
            print(f"您输入的年龄是: {age}")

            # 测试浮点数输入
            score = get_float_input("请输入分数", 85.5, 0, 100)
            print(f"您输入的分数是: {score}")

            # 测试日期输入
            date = get_date_input("请输入日期", "20220101")
            print(f"您输入的日期是: {date}")

            # 测试代码列表输入
            codes = get_code_list_input("请输入股票代码", ["000001.SZ", "600000.SH"])
            print(f"您输入的代码是: {codes}")

            # 测试周期输入
            period = get_period_input()
            print(f"您输入的周期是: {period}")
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试出错: {e}")
