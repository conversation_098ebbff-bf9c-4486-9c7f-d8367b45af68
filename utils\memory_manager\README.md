# 内存管理模块

内存管理模块提供了一套工具，用于监控和优化程序内存使用，防止内存溢出和程序崩溃。

## 主要功能

1. **内存监控** - 实时监控系统和进程内存使用情况
2. **阈值管理** - 设置内存使用阈值并触发回调
3. **资源优化** - 提供垃圾回收、缓存清理和大型对象检测功能
4. **批处理控制** - 管理数据分批处理，支持内存监控和自适应批次大小
5. **内存仪表板** - 提供内存使用情况的可视化界面

## 模块组件

### 1. MemoryMonitor

监控系统和进程内存使用情况，提供内存使用趋势分析。

```python
from utils.memory_manager import MemoryMonitor

# 创建内存监控器实例
monitor = MemoryMonitor(check_interval=5, history_size=100)

# 启动监控
monitor.start_monitoring()

# 获取内存信息
memory_info = monitor.get_memory_info()

# 获取内存趋势
trend = monitor.get_memory_trend(minutes=5)

# 停止监控
monitor.stop_monitoring()
```

### 2. ThresholdManager

管理内存阈值和触发条件，当内存使用达到阈值时触发回调。

```python
from utils.memory_manager import ThresholdManager, ThresholdType, ThresholdLevel, ThresholdDirection

# 创建阈值管理器实例
threshold_mgr = ThresholdManager()

# 添加默认阈值
threshold_mgr.add_default_thresholds(
    callback=lambda info: print(f"内存阈值触发: {info}")
)

# 创建自定义阈值
threshold_mgr.create_threshold(
    threshold_id="custom_threshold",
    threshold_type=ThresholdType.SYSTEM_PERCENT,
    value=85.0,
    level=ThresholdLevel.WARNING,
    direction=ThresholdDirection.ABOVE,
    callback=lambda info: print("系统内存使用率超过85%"),
    cooldown_seconds=60
)

# 检查阈值
memory_info = monitor.get_memory_info()
triggered = threshold_mgr.check_thresholds(memory_info)
```

### 3. ResourceOptimizer

提供垃圾回收、缓存清理和大型对象检测功能，优化内存使用。

```python
from utils.memory_manager import ResourceOptimizer

# 创建资源优化器实例
optimizer = ResourceOptimizer()

# 触发垃圾回收
gc_result = optimizer.trigger_garbage_collection(full=True)

# 注册缓存清理函数
def clear_my_cache():
    my_cache.clear()

optimizer.register_cache_cleaner("my_cache", clear_my_cache)

# 清理所有缓存
optimizer.clean_all_caches()

# 检测大型对象
large_objects = optimizer.detect_large_objects(threshold_mb=10)
```

### 4. BatchController

管理数据分批处理，支持内存监控和自适应批次大小。

```python
from utils.memory_manager import BatchController
from typing import List, Dict

# 定义批处理函数
def process_batch(batch: List[int]) -> List[Dict]:
    results = []
    for item in batch:
        # 处理每个项目
        results.append({"input": item, "output": item * 2})
    return results

# 创建批处理控制器实例
batch_ctrl = BatchController(
    process_func=process_batch,
    batch_size=100,
    max_workers=4,
    memory_threshold_percent=80.0,
    pause_threshold_percent=90.0
)

# 处理数据
data = list(range(1000))
result = batch_ctrl.process_data(data)

# 获取处理摘要
summary = result.get_summary()
```

### 5. MemoryManager

整合上述组件，提供统一的内存管理接口。

```python
from utils.memory_manager import MemoryManager

# 创建内存管理器实例
memory_mgr = MemoryManager(
    memory_warning_threshold=75.0,
    memory_critical_threshold=85.0,
    memory_pause_threshold=90.0,
    check_interval=5.0,
    auto_optimize=True
)

# 启动内存监控
memory_mgr.start_monitoring()

# 注册回调函数
memory_mgr.register_warning_callback(
    lambda info: print(f"警告: 内存使用率 {info['memory']['percent']}%")
)

# 使用上下文管理器处理批量数据
with memory_mgr:
    result = memory_mgr.process_batch(
        data=data,
        process_func=process_batch,
        batch_size=100
    )

# 获取内存状态
status = memory_mgr.get_memory_status()

# 优化内存
memory_mgr.optimize_memory(deep=True)

# 停止内存监控
memory_mgr.stop_monitoring()
```

### 6. MemoryDashboard

提供内存使用情况的可视化界面，支持实时监控和历史数据查看。

```python
from utils.memory_manager import MemoryManager, MemoryDashboard

# 创建内存管理器实例
memory_mgr = MemoryManager()

# 创建内存仪表板实例
dashboard = MemoryDashboard(
    memory_manager=memory_mgr,
    update_interval=1.0,
    history_size=3600
)

# 启动仪表板
dashboard.start()

# 注册警报回调
dashboard.register_alert_callback(
    lambda msg, info: print(f"内存警报: {msg}")
)

# 获取当前状态
status = dashboard.get_current_status()

# 获取历史数据
history = dashboard.get_history_data(
    start_time=time.time() - 300,  # 最近5分钟
    end_time=None
)

# 生成报告
report = dashboard.generate_report(include_history=True)

# 打印状态
dashboard.print_status()

# 停止仪表板
dashboard.stop()
```

## 使用建议

1. **内存监控** - 在应用程序启动时启动内存监控，以便及时发现内存问题
2. **阈值设置** - 根据应用程序的特点设置合适的内存阈值，避免过早或过晚触发
3. **批处理** - 处理大量数据时使用批处理控制器，避免一次性加载过多数据
4. **定期优化** - 在空闲时间定期执行内存优化，释放不再使用的资源
5. **缓存管理** - 注册缓存清理函数，在内存紧张时清理缓存

## 示例代码

完整的示例代码可以在 `examples/memory_manager_demo.py` 中找到，展示了如何使用内存管理模块的各个组件。

## 依赖项

- psutil - 用于获取系统和进程信息
- 其他依赖项均为Python标准库

## 注意事项

- 内存监控会创建后台线程，请确保在应用程序退出前停止监控
- 垃圾回收可能会暂时影响性能，建议在非关键时刻执行
- 批处理控制器的批次大小会根据内存使用情况自动调整，但初始值应合理设置 