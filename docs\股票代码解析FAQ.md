# 股票代码解析常见问题解答

## ❓ 常见问题

### Q1: 为什么我的股票代码解析出现路径错误？

**问题现象**: 日志显示类似这样的错误信息：
```
标准化股票代码: ['"A00.DF",      # 豆一主力连续合约', "'pg00.DF',     # 液化气连续(2506)"]
```

**原因分析**: 使用了简单的文本解析方法，没有去除注释和引号。

**解决方案**: 使用专业的文本解析工具：
```python
from utils.text_parser import parse_stock_code_input

# 替换简单解析
# stocks.append(line)  # ❌ 错误方式

# 使用专业解析
parsed_codes = parse_stock_code_input(line)  # ✅ 正确方式
stocks.extend(parsed_codes)
```

### Q2: 支持哪些股票代码格式？

**支持的格式**:
- 标准格式: `000001.SZ`, `600000.SH`
- 带注释: `000001.SZ    # 平安银行`
- 带引号: `"A00.DF"`, `'pg00.DF'`
- 带逗号: `"A00.DF",    # 豆一主力连续合约`
- 混合格式: 上述格式的任意组合

**示例文件内容**:
```text
# 股票列表文件示例
000001.SZ    # 平安银行
600000.SH    # 浦发银行

# 期货合约（带引号和逗号）
"A00.DF",    # 豆一主力连续合约
'pg00.DF',   # 液化气连续合约
rb00.DF      # 螺纹钢主力连续合约
```

### Q3: 期货合约代码为什么要保持小写？

**原因**: xtquant期货合约代码区分大小写：
- ✅ 正确: `pp00.DF` (小写品种代码)
- ❌ 错误: `PP00.DF` (大写品种代码，返回空数据)

**解决方案**: 使用 `parse_stock_code_input()` 函数会保持原始大小写格式。

### Q4: 如何在股票列表文件中添加注释？

**推荐格式**:
```text
# 这是注释行，会被忽略

# A股股票
000001.SZ    # 平安银行
600000.SH    # 浦发银行

# 期货合约
"A00.DF",    # 豆一主力连续合约
"pg00.DF",   # 液化气连续合约
```

**注意事项**:
- 以 `#` 开头的行会被完全忽略
- 行尾的注释会被自动去除
- 引号会被自动去除

### Q5: 批量合成功能无法识别股票代码怎么办？

**检查步骤**:
1. 确认股票列表文件格式正确
2. 检查是否使用了正确的解析函数
3. 查看日志输出，确认解析结果

**测试方法**:
```python
# 创建测试脚本
from utils.text_parser import parse_stock_code_input

test_text = '''
"A00.DF",      # 豆一主力连续合约
000001.SZ      # 平安银行
'''

result = parse_stock_code_input(test_text)
print("解析结果:", result)
# 期望输出: ['A00.DF', '000001.SZ']
```

### Q6: 如何确保项目中统一使用正确的解析方法？

**最佳实践**:
1. **统一导入**: 在所有需要解析股票代码的模块中导入专业解析函数
2. **避免重复**: 不要在不同模块中重复实现简单解析逻辑
3. **代码审查**: 定期检查是否有使用简单解析的地方
4. **测试验证**: 使用带注释的测试数据验证解析效果

**查找需要修改的代码**:
```bash
# 搜索可能需要修改的简单解析逻辑
grep -r "line.strip()" --include="*.py" .
grep -r "not line.startswith('#')" --include="*.py" .
```

## 🔧 修复指南

### 修复步骤
1. **识别问题**: 查看日志，确认是否有包含注释的股票代码
2. **定位代码**: 找到使用简单解析逻辑的地方
3. **替换解析**: 使用 `parse_stock_code_input()` 函数
4. **测试验证**: 使用带注释的测试数据验证修复效果

### 修复模板
```python
# 修复前
stocks = []
for line in stock_lines:
    line = line.strip()
    if line and not line.startswith('#') and '.' in line:
        stocks.append(line)

# 修复后
from utils.text_parser import parse_stock_code_input

stocks = []
for line in stock_lines:
    line = line.strip()
    if line and not line.startswith('#'):
        parsed_codes = parse_stock_code_input(line)
        stocks.extend(parsed_codes)
```

## 📞 获取帮助

如果遇到其他股票代码解析问题，请：
1. 检查日志输出，确认具体错误信息
2. 验证股票列表文件格式
3. 使用测试脚本验证解析效果
4. 参考 `utils/text_parser_README.md` 获取详细使用说明
