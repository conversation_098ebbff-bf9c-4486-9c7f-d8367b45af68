---
description: 
globs: "*"
alwaysApply: true
---
# 量化交易系统规范

本文档规定了量化交易系统的专有规范，包括系统架构、性能标准、回测规范以及风控要求等。

## 2. 系统架构规范
### 2.1 模块化设计
- 交易执行模块与策略模块必须解耦
- 数据处理模块必须独立封装
- 风控模块必须可独立配置和运行
- 按功能模块分目录存放代码
- 统一使用相对引用路径
- 配置文件集中管理

## 3. 实时交易规范
### 3.1 性能要求
- API响应时间不得超过10ms
- 订单延迟不超过100ms
- 内存使用需实时监控，不超过预设阈值

### 3.2 风控系统
- 实现多层风控体系（订单前风控、实时持仓风控、资金规模风控）
- 异常交易自动中断机制
- 关键操作必须记录详细日志

### 3.3 数据处理标准
- 实时行情必须进行异常值过滤
- 所有时间必须统一使用UTC时间戳
- 价格和数量必须使用Decimal类型
- 实现并行异步数据处理，提高处理效率
- 采用流式处理机制，边处理数据边释放资源

## 4. 回测系统规范
### 4.1 数据获取与管理
- 回测数据与实盘数据分离存储
- 历史数据必须包含清晰的数据源标注
- 数据预处理流程必须可重现
- 实现数据增量更新机制，避免重复处理
- 数据下载实现模块化管理，支持多个数据源获取数据

### 4.2 回测功能
- 支持多周期回测
- 提供完整的交易成本模型
- 回测结果必须包含标准化指标（夏普比率、最大回撤、年化收益、胜率统计）
- 回测过程必须保证结果准确性，同时优化内存使用

### 4.3 回测性能
- 严格控制回测速度指标
- 实时监控内存使用
- 优化数据加载效率
- 日线回测速度：<1分钟/10年数据
- 分钟线回测速度：<5分钟/1年数据
- tick回测速度：<5分钟/1年数据
- 支持100+并行回测
- 回测策略必须实现边处理数据边释放资源的流式机制

## 5. 策略开发规范
### 5.1 高频策略
- 信号生成延迟<1ms
- 必须考虑市场冲击
- 严格控制订单频率
- 实现队列位置估计

### 5.2 低频策略
- 完整的因子文档
- 因子有效性检验流程
- 严格的止损止盈机制

### 5.3 套利策略
- 价差计算标准化
- 考虑交易成本
- 设置最小套利空间
- 实现多腿订单管理

### 5.4 市场中性策略
- 严格控制Beta暴露
- 实现动态对冲
- 定期再平衡机制

## 6. 性能优化规范
### 6.1 数据加载优化
- 实现数据按需加载（仅加载所需时间段和字段）
- 支持数据分片加载
- 使用压缩存储格式（如Parquet）
- 建立数据索引加速查询
- 实现数据加载与处理的并行化

### 6.2 并行处理
- 实现数据并行加载器
- 设置最优线程池大小
- 因子计算并行处理
- 多品种并行回测
- GPU加速支持（适用于大规模矩阵运算）
- 实现异步IO操作，减少数据读取等待时间

### 6.3 内存管理
- 动态内存控制（设置使用上限、监控机制）
- 数据生命周期管理（及时释放无用数据）
- 实现数据缓存淘汰机制
- 内存峰值使用率<80%，稳定使用率<60%
- 确保无内存泄漏
- 回测过程中实现流式释放不再需要的历史数据

### 6.4 计算优化
- 流式处理机制（边处理边释放资源）
- 支持checkpoint机制
- 向量化运算
- 预计算常用指标
- 缓存中间结果
- 实现计算任务优先级队列，优先处理关键路径

### 6.5 资源管理
- 预分配内存池
- 循环利用对象
- 自动内存碎片整理
- 多级缓存机制
- LRU缓存淘汰策略
- 热点数据优先缓存

## 7. 代码质量规范
### 7.1 注释要求
- 策略逻辑必须有中文注释
- 关键参数必须说明含义
- 复杂算法必须附带文档

### 7.2 测试要求
- 单元测试覆盖率>80%
- 必须包含压力测试
- 模拟实盘测试不少于1个月

### 7.3 性能分析
- 关键函数必须进行性能分析
- 大数据操作必须使用异步处理
- 缓存机制合理使用
- 代码热点分析
- 内存泄漏检测
- IO瓶颈分析

## 8. 监控告警规范
### 8.1 系统监控
- CPU使用率预留部分保持系统运行流程
- 内存使用预留部分保持系统运行流程
- 网络延迟监控
- IO等待时间监控

### 8.2 交易监控
- 异常交易量告警
- 持仓超限告警
- 盈亏异常告警

## 9. 数据安全规范
### 9.1 数据存储
- 敏感数据加密存储
- 关键配置参数加密
- 定期数据备份机制
- 按照标准路径格式存储数据：
  ```
  <数据根目录>/<交易所>/<股票代码>/<周期>.parquet
  
  例如：
  D:\data\SZ\000001\tick.parquet
  D:\data\SH\600000\1d.parquet
  ```

### 9.2 访问控制
- 严格的权限分级制度
- API访问认证机制
- 操作日志完整记录

## 10. 数据加载性能指标
### 10.1 速度目标
- 日线数据：<1秒/年数据
- 分钟线：<5秒/月数据
- tick数据：<10秒/日数据



## 11. 时间序列数据处理规范

对于项目中的所有时间序列数据，当涉及到时间戳或日期时间列时，应遵循以下规范：

### 11.1 数据类型与转换
- **统一数据类型**：时间相关列应首先转换为 `pandas.Timestamp` 或 `pandas.DatetimeIndex` 类型
   * 对于Unix时间戳：`pd.to_datetime(series, unit='ms', errors='coerce')`
   * 对于日期时间字符串：`pd.to_datetime(series, format='%Y-%m-%d %H:%M:%S', errors='coerce')`

- **处理无效值**：使用 `errors='coerce'` 会将转换失败的值转为 `NaT`。应适当处理这些值

### 11.2 时区处理
- **统一时区**：所有时间序列数据应统一转换为**上海时区 (`Asia/Shanghai`)**
   * 对于无时区数据：先使用 `.dt.tz_localize('UTC')` 再 `.dt.tz_convert('Asia/Shanghai')`
   * 对于带时区数据：直接使用 `.dt.tz_convert('Asia/Shanghai')`

### 11.3 标准格式化
- **标准字符串输出格式**：
   * tick级数据：`'%Y-%m-%d %H:%M:%S.%f'` (如：`2023-10-26 10:30:01.123`)
   * 其他周期：`'%Y-%m-%d %H:%M:%S'` (如：`2023-10-26 10:30:02`)
   * 仅日期：`'%Y-%m-%d'`

### 11.4 完整示例
```python
import pandas as pd

# 假设 df 是包含时间戳列的 DataFrame，period_str 是数据周期字符串
if 'event_time' in df.columns:
    # 1. 转换为Datetime对象 (假设是毫秒时间戳)
    df['event_time'] = pd.to_datetime(df['event_time'], unit='ms', errors='coerce')
    
    # 2. 处理无效时间戳行
    df.dropna(subset=['event_time'], inplace=True)
    
    if not df.empty:
        # 3. 时区转换 (假设原始无时区，先本地化为UTC再转上海)
        try:
            df['event_time'] = df['event_time'].dt.tz_localize('UTC').dt.tz_convert('Asia/Shanghai')
        except TypeError: # 如果已经是带时区的
            df['event_time'] = df['event_time'].dt.tz_convert('Asia/Shanghai')
        
        # 4. 转换为标准字符串格式 (例如，用于显示或保存到文本)
        if period_str == 'tick':
            df['display_time'] = df['event_time'].dt.strftime('%Y-%m-%d %H:%M:%S.%f')
        else:
            df['display_time'] = df['event_time'].dt.strftime('%Y-%m-%d %H:%M:%S')
```

**注意**：在进行数据存储时，推荐存储为带时区信息的 `datetime64[ns, Asia/Shanghai]` 类型，而不是字符串，以便后续进行时间计算和时区转换。仅在需要文本表示时才转换为上述字符串格式。 
