# 统一路径管理器使用指南

## 🎯 概述

统一路径管理器（PathManager）是一个单例模式的路径管理系统，解决了项目中路径构建不一致的问题。它提供了统一的路径构建、验证、查找和修复功能。

## 🚀 快速开始

### 基本使用

```python
from utils.path_manager import (
    get_path_manager,
    build_partitioned_path,
    get_base_directory,
    get_latest_partition_file
)

# 构建分区路径
path = build_partitioned_path("rb00.SF", "tick", "20250722")
print(path)  # D:\data\SF\rb00\tick\2025\07\22.parquet

# 获取基础目录
base_dir = get_base_directory("rb00.SF", "tick")
print(base_dir)  # D:\data\SF\rb00\tick

# 查找最新文件
latest_file = get_latest_partition_file("rb00.SF", "tick")
if latest_file:
    print(f"最新文件: {latest_file}")
```

### 高级使用

```python
from utils.path_manager import get_path_manager

# 获取路径管理器实例
pm = get_path_manager()

# 设置数据根目录
pm.set_data_root("D:\\my_data")

# 解析股票代码
code, market = pm.parse_symbol("rb00.SF")
print(f"代码: {code}, 市场: {market}")

# 验证路径
is_valid = pm.validate_path("D:\\data\\SF\\rb00\\tick\\2025.parquet")
print(f"路径有效: {is_valid}")
```

## 📋 核心功能

### 1. 路径构建

#### build_partitioned_path()
构建标准的分区存储路径。

```python
# tick数据路径（按日分区）
path = build_partitioned_path("rb00.SF", "tick", "20250722")
# 结果: D:\data\SF\rb00\tick\2025\07\22.parquet

# 其他周期路径（按年分区）
path = build_partitioned_path("000001.SZ", "1m", "20250722")
# 结果: D:\data\SZ\000001\1m\2025.parquet
```

#### get_base_directory()
获取股票数据的基础目录。

```python
base_dir = get_base_directory("rb00.SF", "tick")
# 结果: D:\data\SF\rb00\tick
```

### 2. 文件查找

#### get_latest_partition_file()
获取最新的分区文件。

```python
latest_file = get_latest_partition_file("rb00.SF", "tick")
# 返回最新的parquet文件路径，如果不存在则返回None
```

#### get_earliest_partition_file()
获取最早的分区文件。

```python
earliest_file = get_earliest_partition_file("rb00.SF", "tick")
# 返回最早的parquet文件路径，如果不存在则返回None
```

#### find_partition_files()
查找所有分区文件。

```python
files = pm.find_partition_files("rb00.SF", "tick")
# 返回所有parquet文件路径的列表
```

### 3. 路径验证

#### validate_path()
验证路径是否符合规范。

```python
is_valid = validate_path("D:\\data\\SF\\rb00\\tick\\2025.parquet")
# 返回True或False
```

#### normalize_path()
标准化路径格式。

```python
normalized = normalize_path("some/path/../normalized/path")
# 返回标准化后的绝对路径
```

### 4. 股票代码解析

#### parse_symbol()
解析股票代码，获取代码和市场。

```python
code, market = parse_symbol("rb00.SF")
# 返回: ("rb00", "SF")

code, market = parse_symbol("000001.SZ")
# 返回: ("000001", "SZ")
```

## 🔧 配置管理

### 数据根目录

PathManager支持多种方式设置数据根目录：

1. **环境变量**（优先级最高）
   ```bash
   set QUANT_DATA_ROOT=D:\my_data
   # 或
   set DATA_ROOT=D:\my_data
   ```

2. **代码设置**
   ```python
   pm = get_path_manager()
   pm.set_data_root("D:\\my_data")
   ```

3. **默认值**
   ```
   D:\data
   ```

### 路径规范配置

```python
pm = get_path_manager()

# 查看当前配置
print(pm._path_rules)

# 配置示例
{
    'case_sensitive': False,      # 是否区分大小写
    'normalize_case': 'preserve', # 大小写处理：preserve/lower/upper
    'separator': os.sep,          # 路径分隔符
    'validate_paths': True,       # 是否验证路径
}
```

## 🛠️ 路径验证和修复

### 路径验证

```python
from utils.path_manager import validate_paths, generate_report

# 验证所有路径
results = validate_paths()

# 生成报告
report = generate_report(results)
print(report)
```

### 路径修复

```python
from utils.path_manager import fix_paths

# 试运行模式（不实际修改文件）
results = fix_paths(dry_run=True)

# 实际修复模式
results = fix_paths(dry_run=False)
```

## 📊 性能优化

PathManager内置了多种性能优化机制：

### 1. 缓存机制
- **解析缓存**：缓存股票代码解析结果
- **路径缓存**：缓存路径构建结果

### 2. 日志优化
- 只在DEBUG级别时输出详细日志
- 减少不必要的日志输出

### 3. 单例模式
- 全局唯一实例，避免重复初始化
- 线程安全的实现

## 🔍 故障排除

### 常见问题

1. **找不到文件**
   ```python
   # 检查数据根目录是否正确
   pm = get_path_manager()
   print(f"数据根目录: {pm.get_data_root()}")
   
   # 检查路径是否存在
   from utils.path_manager import path_exists
   print(f"路径存在: {path_exists('D:\\data\\SF\\rb00\\tick')}")
   ```

2. **路径格式错误**
   ```python
   # 验证路径格式
   from utils.path_manager import validate_path
   is_valid = validate_path("your/path/here")
   if not is_valid:
       print("路径格式不正确")
   ```

3. **股票代码格式错误**
   ```python
   try:
       code, market = parse_symbol("invalid_symbol")
   except PathManagerError as e:
       print(f"股票代码格式错误: {e}")
   ```

### 调试技巧

1. **启用DEBUG日志**
   ```python
   import logging
   logging.getLogger('utils.path_manager').setLevel(logging.DEBUG)
   ```

2. **清空缓存**
   ```python
   pm = get_path_manager()
   pm.clear_cache()
   ```

3. **检查配置**
   ```python
   pm = get_path_manager()
   print(f"数据根目录: {pm.get_data_root()}")
   print(f"路径规则: {pm._path_rules}")
   ```

## 🎯 最佳实践

### 1. 统一使用PathManager
```python
# ✅ 推荐：使用PathManager
from utils.path_manager import build_partitioned_path
path = build_partitioned_path("rb00.SF", "tick", "20250722")

# ❌ 不推荐：手动构建路径
path = os.path.join("D:", "data", "SF", "rb00", "tick", "2025", "07", "22.parquet")
```

### 2. 错误处理
```python
from utils.path_manager import PathManagerError

try:
    code, market = parse_symbol("invalid")
except PathManagerError as e:
    logger.error(f"解析股票代码失败: {e}")
    # 处理错误
```

### 3. 性能考虑
```python
# ✅ 推荐：批量操作时复用PathManager实例
pm = get_path_manager()
for symbol in symbols:
    path = pm.build_partitioned_path(symbol, "tick", date)

# ❌ 不推荐：频繁调用便捷函数
for symbol in symbols:
    path = build_partitioned_path(symbol, "tick", date)
```

### 4. 路径验证
```python
# 在关键操作前验证路径
if validate_path(file_path):
    # 执行文件操作
    process_file(file_path)
else:
    logger.warning(f"路径无效: {file_path}")
```

## 📚 API参考

详细的API文档请参考各函数的docstring，或使用Python的help()函数：

```python
from utils.path_manager import get_path_manager
help(get_path_manager)
```

## 🔄 版本历史

- **v1.0.0** (2025-07-22)
  - 初始版本
  - 统一路径管理功能
  - 路径验证和修复工具
  - 性能优化和缓存机制
