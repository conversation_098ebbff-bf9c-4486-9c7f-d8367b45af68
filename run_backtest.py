#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
回测运行脚本，提供命令行参数解析和主入口
可以通过命令行参数运行回测，支持多种参数设置
"""

import argparse
import datetime
import importlib
import json
import logging
import os
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional, Type, Union

# 将项目根目录添加到系统路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.logger import get_unified_logger, setup_unified_logging, LogTarget

# 导入必要的模块
from config.settings import (
    BACKTEST_CONFIG,
    DATA_ROOT,
    DEFAULT_INITIAL_CAPITAL,
    initialize_system,
)
from data.data_source_manager import data_source_manager

# 初始化日志
logger = get_unified_logger("backtest")


def parse_args():
    """
    解析命令行参数
    
    Returns:
        解析后的参数对象
    """
    parser = argparse.ArgumentParser(description="量化交易回测工具")
    
    # 策略相关参数
    parser.add_argument(
        "-s", "--strategy", 
        type=str, 
        required=True, 
        help="策略类名或策略文件路径，如 'simple_ma_strategy' 或 'strategy/ma/simple_ma_strategy.py'"
    )
    
    parser.add_argument(
        "--strategy-params", 
        type=str, 
        default=None, 
        help="策略参数，JSON格式字符串，如 '{\"fast_period\": 5, \"slow_period\": 20}'"
    )
    
    # 回测相关参数
    parser.add_argument(
        "--start-date", 
        type=str, 
        required=True, 
        help="回测开始日期，格式为 YYYYMMDD，如 '20210101'"
    )
    
    parser.add_argument(
        "--end-date", 
        type=str, 
        default=datetime.datetime.now().strftime("%Y%m%d"),
        help="回测结束日期，格式为 YYYYMMDD，如 '20211231'，默认为当前日期"
    )
    
    parser.add_argument(
        "-p", "--period", 
        type=str, 
        default="1d", 
        help="回测周期，可选值：tick, 1m, 5m, 15m, 30m, 1h, 1d，默认为 1d"
    )
    
    parser.add_argument(
        "--initial-capital", 
        type=float, 
        default=DEFAULT_INITIAL_CAPITAL,
        help=f"初始资金，默认为 {DEFAULT_INITIAL_CAPITAL}"
    )
    
    parser.add_argument(
        "--commission-rate", 
        type=float, 
        default=BACKTEST_CONFIG.get("commission_rate", 0.0003),
        help="手续费率，默认为 0.0003 (万分之三)"
    )
    
    parser.add_argument(
        "--slippage", 
        type=float, 
        default=BACKTEST_CONFIG.get("slippage", 0.0001),
        help="滑点率，默认为 0.0001 (万分之一)"
    )
    
    # 股票池相关参数
    parser.add_argument(
        "--symbols", 
        type=str, 
        nargs="+",
        help="股票代码列表，如 '600000.SH 000001.SZ'"
    )
    
    parser.add_argument(
        "--index", 
        type=str, 
        default=None,
        help="指数代码，用于获取成分股，如 '000300.SH' (沪深300)"
    )
    
    parser.add_argument(
        "--index-weight", 
        action="store_true",
        help="是否使用指数权重，只在指定 --index 时有效"
    )
    
    # 运行模式参数
    parser.add_argument(
        "--plot", 
        action="store_true", 
        help="是否绘制回测结果图表"
    )
    
    parser.add_argument(
        "--output-dir", 
        type=str, 
        default="./backtest/results",
        help="回测结果输出目录，默认为 './backtest/results'"
    )
    
    parser.add_argument(
        "--log-level", 
        type=str, 
        choices=["debug", "info", "warning", "error", "critical"],
        default="info",
        help="日志级别，默认为 'info'"
    )
    
    parser.add_argument(
        "--parallel", 
        action="store_true", 
        help="是否使用多进程运行"
    )
    
    parser.add_argument(
        "--processes", 
        type=int, 
        default=None,
        help="多进程数量，默认为CPU核心数"
    )
    
    return parser.parse_args()


def load_strategy_class(strategy_path: str) -> Type:
    """
    加载策略类
    
    Args:
        strategy_path: 策略类名或文件路径
    
    Returns:
        策略类
    
    Raises:
        ImportError: 无法导入策略类
        AttributeError: 策略类不存在
    """
    try:
        # 尝试从策略目录导入
        if os.path.isfile(strategy_path):
            # 如果是文件路径，则直接加载
            module_path = os.path.splitext(strategy_path.replace(os.path.sep, "."))[0]
            if module_path.startswith("."):
                module_path = module_path[1:]
            
            module = importlib.import_module(module_path)
            # 获取文件名中的类名
            class_name = os.path.splitext(os.path.basename(strategy_path))[0]
            # 尝试将下划线命名转换为驼峰命名
            class_name = "".join(x.capitalize() for x in class_name.split("_"))
            
            # 尝试获取策略类
            if hasattr(module, class_name):
                return getattr(module, class_name)
            
            # 尝试获取 Strategy 后缀的类
            if hasattr(module, f"{class_name}Strategy"):
                return getattr(module, f"{class_name}Strategy")
            
            # 如果没找到，则尝试找到模块中的第一个 Strategy 类
            for name in dir(module):
                if name.endswith("Strategy") and name[0].isupper():
                    return getattr(module, name)
            
            raise AttributeError(f"在模块 {module_path} 中找不到策略类")
        else:
            # 如果不是文件路径，则尝试从策略目录导入
            # 先尝试直接导入模块
            try:
                module = importlib.import_module(f"strategy.{strategy_path}")
                # 尝试将模块名转换为类名
                class_name = "".join(x.capitalize() for x in strategy_path.split("_"))
                
                # 尝试获取策略类
                if hasattr(module, class_name):
                    return getattr(module, class_name)
                
                # 尝试获取 Strategy 后缀的类
                if hasattr(module, f"{class_name}Strategy"):
                    return getattr(module, f"{class_name}Strategy")
                
                # 尝试找到模块中的第一个 Strategy 类
                for name in dir(module):
                    if name.endswith("Strategy") and name[0].isupper():
                        return getattr(module, name)
                
                # 如果还找不到，则尝试直接导入类
                class_module = importlib.import_module(f"strategy.{strategy_path}")
                if hasattr(class_module, strategy_path):
                    return getattr(class_module, strategy_path)
                
                raise AttributeError(f"在模块 strategy.{strategy_path} 中找不到策略类")
            except ImportError:
                # 尝试从全限定路径导入
                module_parts = strategy_path.split(".")
                if len(module_parts) > 1:
                    module_path = ".".join(module_parts[:-1])
                    class_name = module_parts[-1]
                    
                    module = importlib.import_module(module_path)
                    if hasattr(module, class_name):
                        return getattr(module, class_name)
                
                raise ImportError(f"无法导入策略 {strategy_path}")
    except Exception as e:
        logger.error(f"加载策略类失败: {str(e)}")
        raise


def get_symbols_from_index(index_code: str, date: str = None) -> List[str]:
    """
    获取指数成分股
    
    Args:
        index_code: 指数代码
        date: 查询日期，格式为 YYYYMMDD，默认为当前日期
    
    Returns:
        成分股列表
    """
    try:
        # 导入符号映射
        from config.symbols import INDEX_SYMBOLS_MAP

        # 如果指数在配置中存在预定义的成分股，则直接返回
        if index_code in INDEX_SYMBOLS_MAP:
            return INDEX_SYMBOLS_MAP[index_code]
        
        # 否则尝试从数据源获取
        # 这里可以通过数据源管理器获取成分股数据
        logger.warning(f"从配置中未找到指数 {index_code} 的成分股，尝试从数据源获取")
        # TODO: 从数据源获取指数成分股
        
        # 如果无法获取，则返回空列表
        return []
    except Exception as e:
        logger.error(f"获取指数 {index_code} 成分股失败: {str(e)}")
        return []


def prepare_backtest_data(args) -> Dict:
    """
    准备回测数据
    
    Args:
        args: 命令行参数
    
    Returns:
        回测数据配置字典
    """
    # 确定股票池
    symbols = []
    if args.symbols:
        symbols = args.symbols
    elif args.index:
        symbols = get_symbols_from_index(args.index, args.end_date)
    
    if not symbols:
        raise ValueError("未指定有效的股票池，请使用 --symbols 或 --index 参数")
    
    logger.info(f"回测股票池: {len(symbols)} 只股票")
    
    # 检查数据是否存在，如果不存在则下载
    logger.info("正在检查并准备回测数据...")
    
    # 尝试从本地获取部分数据，检查是否存在
    try:
        data = data_source_manager.get_local_data(
            stock_list=symbols[:1],  # 只取第一个股票检查
            period=args.period,
            start_time=args.start_date,
            end_time=args.end_date,
            field_list=["close"]
        )
        
        # 如果数据为空或长度不够，则需要下载
        need_download = not data or not next(iter(data.values())).shape[0]
    except Exception:
        # 如果发生异常，则需要下载
        need_download = True
    
    # 如果需要下载数据
    if need_download:
        logger.info("本地数据不完整，正在下载历史数据...")
        success = data_source_manager.download_history_data(
            stock_list=symbols,
            period=args.period,
            start_time=args.start_date,
            end_time=args.end_date
        )
        
        if not success:
            raise RuntimeError("下载历史数据失败，无法进行回测")
    
    logger.info("回测数据准备完成")
    
    # 创建回测配置
    backtest_config = {
        "symbols": symbols,
        "start_date": args.start_date,
        "end_date": args.end_date,
        "period": args.period,
        "initial_capital": args.initial_capital,
        "commission_rate": args.commission_rate,
        "slippage": args.slippage,
        "output_dir": args.output_dir,
        "plot": args.plot,
        "parallel": args.parallel,
        "processes": args.processes
    }
    
    return backtest_config


def run_backtest(strategy_class, strategy_params: Dict, backtest_config: Dict) -> Dict:
    """
    运行回测
    
    Args:
        strategy_class: 策略类
        strategy_params: 策略参数
        backtest_config: 回测配置
    
    Returns:
        回测结果
    """
    try:
        logger.info("开始回测...")
        
        # 这里仅为示例，具体回测引擎的导入方式可能不同
        from backtest.engine import BacktestEngine

        # 创建回测引擎
        engine = BacktestEngine(
            initial_capital=backtest_config["initial_capital"],
            commission_rate=backtest_config["commission_rate"],
            slippage=backtest_config["slippage"]
        )
        
        # 创建策略实例
        strategy = strategy_class(**(strategy_params or {}))
        
        # 设置回测参数
        engine.set_parameters(
            symbols=backtest_config["symbols"],
            start_date=backtest_config["start_date"],
            end_date=backtest_config["end_date"],
            period=backtest_config["period"]
        )
        
        # 运行回测
        result = engine.run(strategy, parallel=backtest_config["parallel"], processes=backtest_config["processes"])
        
        # 保存结果
        if backtest_config["output_dir"]:
            output_dir = Path(backtest_config["output_dir"])
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # 生成结果文件名
            result_file = output_dir / f"backtest_result_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            # 保存结果
            with open(result_file, "w", encoding="utf-8") as f:
                json.dump(result, f, ensure_ascii=False, indent=4)
            
            logger.info(f"回测结果已保存至: {result_file}")
        
        # 绘制图表
        if backtest_config["plot"]:
            engine.plot_results()
        
        return result
    
    except Exception as e:
        logger.error(f"回测执行失败: {str(e)}")
        raise


def main():
    """
    主函数
    """
    # 初始化系统
    initialize_system()
    
    # 解析命令行参数
    args = parse_args()
    
    # 设置日志级别
    global logger
    setup_unified_logging(
        log_level=args.log_level, 
        default_target=LogTarget.FILE,
        use_table_formatter=True,  # 使用表格式Markdown格式化器
        module_width=30,  # 设置模块名显示宽度为30
        print_init_message=False  # 不打印初始化消息
    )
    logger = get_unified_logger("backtest", level=args.log_level)
    
    try:
        # 加载策略类
        logger.info(f"加载策略: {args.strategy}")
        strategy_class = load_strategy_class(args.strategy)
        
        # 解析策略参数
        strategy_params = {}
        if args.strategy_params:
            try:
                strategy_params = json.loads(args.strategy_params)
            except json.JSONDecodeError:
                logger.error(f"无效的策略参数JSON字符串: {args.strategy_params}")
                return 1
        
        # 准备回测数据
        backtest_config = prepare_backtest_data(args)
        
        # 运行回测
        result = run_backtest(strategy_class, strategy_params, backtest_config)
        
        # 打印回测结果摘要
        logger.info("回测完成！结果摘要:")
        logger.info(f"初始资金: {backtest_config['initial_capital']}")
        logger.info(f"回测周期: {backtest_config['period']}")
        logger.info(f"回测时间: {backtest_config['start_date']} - {backtest_config['end_date']}")
        if "final_value" in result:
            logger.info(f"最终资金: {result['final_value']}")
        if "total_return" in result:
            logger.info(f"总收益率: {result['total_return'] * 100:.2f}%")
        if "annual_return" in result:
            logger.info(f"年化收益率: {result['annual_return'] * 100:.2f}%")
        if "max_drawdown" in result:
            logger.info(f"最大回撤: {result['max_drawdown'] * 100:.2f}%")
        if "sharpe_ratio" in result:
            logger.info(f"夏普比率: {result['sharpe_ratio']:.4f}")
        
        return 0
    
    except Exception as e:
        logger.exception("回测过程中发生异常")
        print(f"回测失败: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main()) 