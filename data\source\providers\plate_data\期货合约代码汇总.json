{"variety_exchange_map": {"rr": "DF", "a": "DF", "p": "DF", "l": "DF", "b": "DF", "fb": "DF", "j": "DF", "bb": "DF", "v": "DF", "lh": "DF", "i": "DF", "jm": "DF", "eb": "DF", "pg": "DF", "y": "DF", "lg": "DF", "cs": "DF", "c": "DF", "m": "DF", "eg": "DF", "pp": "DF", "jd": "DF", "IC": "IF", "IM": "IF", "IH": "IF", "IF": "IF", "TL": "IF", "TS": "IF", "TF": "IF", "T": "IF", "ec": "INE", "nr": "INE", "sc": "INE", "bc": "INE", "lu": "INE", "sp": "SF", "fu": "SF", "hc": "SF", "ss": "SF", "bu": "SF", "ao": "SF", "pb": "SF", "wr": "SF", "rb": "SF", "al": "SF", "zn": "SF", "ru": "SF", "au": "SF", "cu": "SF", "ag": "SF", "br": "SF", "sn": "SF", "ni": "SF", "FG": "ZF", "JR": "ZF", "CY": "ZF", "PF": "ZF", "CF": "ZF", "MA": "ZF", "PK": "ZF", "SA": "ZF", "LR": "ZF", "PR": "ZF", "PM": "ZF", "OI": "ZF", "RM": "ZF", "RS": "ZF", "SF": "ZF", "AP": "ZF", "SH": "ZF", "ZC": "ZF", "UR": "ZF", "RI": "ZF", "CJ": "ZF", "WH": "ZF", "SM": "ZF", "PX": "ZF", "TA": "ZF", "SR": "ZF"}, "main_continuous_codes": ["rr00.DF", "a00.DF", "p00.DF", "l00.DF", "b00.DF", "fb00.DF", "j00.DF", "bb00.DF", "v00.DF", "lh00.DF", "i00.DF", "jm00.DF", "eb00.DF", "pg00.DF", "y00.DF", "lg00.DF", "cs00.DF", "c00.DF", "m00.DF", "eg00.DF", "pp00.DF", "jd00.DF", "IC00.IF", "IM00.IF", "IH00.IF", "IF00.IF", "TL00.IF", "TS00.IF", "TF00.IF", "T00.IF", "ec00.INE", "nr00.INE", "sc00.INE", "bc00.INE", "lu00.INE", "sp00.SF", "fu00.SF", "hc00.SF", "ss00.SF", "bu00.SF", "ao00.SF", "pb00.SF", "wr00.SF", "rb00.SF", "al00.SF", "zn00.SF", "ru00.SF", "au00.SF", "cu00.SF", "ag00.SF", "br00.SF", "sn00.SF", "ni00.SF", "FG00.ZF", "JR00.ZF", "CY00.ZF", "PF00.ZF", "CF00.ZF", "MA00.ZF", "PK00.ZF", "SA00.ZF", "LR00.ZF", "PR00.ZF", "PM00.ZF", "OI00.ZF", "RM00.ZF", "RS00.ZF", "SF00.ZF", "AP00.ZF", "SH00.ZF", "ZC00.ZF", "UR00.ZF", "RI00.ZF", "CJ00.ZF", "WH00.ZF", "SM00.ZF", "PX00.ZF", "TA00.ZF", "SR00.ZF"], "current_main_contracts": {"rr00.DF": "rr2507.DF", "a00.DF": "a2507.DF", "p00.DF": "p2509.DF", "l00.DF": "l2509.DF", "b00.DF": "b2509.DF", "fb00.DF": "fb2506.DF", "j00.DF": "j2509.DF", "bb00.DF": "bb2511.DF", "v00.DF": "v2509.DF", "lh00.DF": "lh2509.DF", "i00.DF": "i2509.DF", "jm00.DF": "jm2509.DF", "eb00.DF": "eb2506.DF", "pg00.DF": "pg2506.DF", "y00.DF": "y2509.DF", "lg00.DF": "lg2507.DF", "cs00.DF": "cs2507.DF", "c00.DF": "c2507.DF", "m00.DF": "m2509.DF", "eg00.DF": "eg2509.DF", "pp00.DF": "pp2509.DF", "jd00.DF": "jd2506.DF", "IC00.IF": "IC2506.IF", "IM00.IF": "IM2506.IF", "IH00.IF": "IH2506.IF", "IF00.IF": "IF2506.IF", "TL00.IF": "TL2506.IF", "TS00.IF": "TS2509.IF", "TF00.IF": "TF2506.IF", "T00.IF": "T2506.IF", "ec00.INE": "ec2508.INE", "nr00.INE": "nr2506.INE", "sc00.INE": "sc2507.INE", "bc00.INE": "bc2506.INE", "lu00.INE": "lu2507.INE", "sp00.SF": "sp2507.SF", "fu00.SF": "fu2507.SF", "hc00.SF": "hc2510.SF", "ss00.SF": "ss2507.SF", "bu00.SF": "bu2506.SF", "ao00.SF": "ao2509.SF", "pb00.SF": "pb2506.SF", "wr00.SF": "wr2510.SF", "rb00.SF": "rb2510.SF", "al00.SF": "al2507.SF", "zn00.SF": "zn2506.SF", "ru00.SF": "ru2509.SF", "au00.SF": "au2508.SF", "cu00.SF": "cu2506.SF", "ag00.SF": "ag2508.SF", "br00.SF": "br2506.SF", "sn00.SF": "sn2506.SF", "ni00.SF": "ni2506.SF", "FG00.ZF": "FG509.ZF", "JR00.ZF": "JR505.ZF", "CY00.ZF": "CY507.ZF", "PF00.ZF": "PF507.ZF", "CF00.ZF": "CF509.ZF", "MA00.ZF": "MA509.ZF", "PK00.ZF": "PK510.ZF", "SA00.ZF": "SA509.ZF", "LR00.ZF": "LR505.ZF", "PR00.ZF": "PR507.ZF", "PM00.ZF": "PM505.ZF", "OI00.ZF": "OI509.ZF", "RM00.ZF": "RM509.ZF", "RS00.ZF": "RS507.ZF", "SF00.ZF": "SF507.ZF", "AP00.ZF": "AP510.ZF", "SH00.ZF": "SH509.ZF", "ZC00.ZF": "ZC506.ZF", "UR00.ZF": "UR509.ZF", "RI00.ZF": "RI505.ZF", "CJ00.ZF": "CJ509.ZF", "WH00.ZF": "WH505.ZF", "SM00.ZF": "SM509.ZF", "PX00.ZF": "PX509.ZF", "TA00.ZF": "TA509.ZF", "SR00.ZF": "SR509.ZF"}}