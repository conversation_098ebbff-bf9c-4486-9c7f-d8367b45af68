#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
异常值处理功能模块

提供异常值检测和处理的各种方法
"""

import os
import sys
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union, Callable, Any
from sklearn.ensemble import IsolationForest

# 将项目根目录添加到Python路径
root_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, root_path)

# 导入日志模块
from utils.logger import get_unified_logger

# 设置日志记录器
logger = get_unified_logger(__name__)


def detect_outliers_zscore(df: pd.DataFrame, columns: Optional[List[str]] = None,
                           threshold: float = 3.0) -> Dict[str, List[int]]:
    """
    使用Z-score方法检测异常值

    Args:
        df: 输入DataFrame
        columns: 要检测的列列表，如果为None则检测所有数值列
        threshold: Z-score阈值，默认为3.0，超过此值的数据点被视为异常值

    Returns:
        检测结果字典，键为列名，值为包含异常值的行索引列表
    """
    if df.empty:
        logger.warning("输入的DataFrame为空")
        return {}

    # 确定要处理的列
    if columns is None:
        # 选择所有数值列
        numeric_columns = df.select_dtypes(include=[np.number]).columns.tolist()
        target_columns = numeric_columns
    else:
        # 检查指定的列是否存在且为数值类型
        target_columns = []
        for col in columns:
            if col not in df.columns:
                logger.warning(f"列 '{col}' 不存在于DataFrame中")
                continue
            if not pd.api.types.is_numeric_dtype(df[col]):
                logger.warning(f"列 '{col}' 不是数值类型，无法使用Z-score方法")
                continue
            target_columns.append(col)

    # 存储结果
    outliers = {}

    # 对每列进行异常值检测
    for col in target_columns:
        # 计算Z-score
        mean = df[col].mean()
        std = df[col].std()
        z_scores = (df[col] - mean) / std

        # 找出超过阈值的索引
        outlier_idx = df.index[np.abs(z_scores) > threshold].tolist()

        # 如果有异常值，添加到结果字典
        if outlier_idx:
            outliers[col] = outlier_idx
            logger.info(f"在列 '{col}' 中检测到 {len(outlier_idx)} 个Z-score异常值")

    return outliers


def detect_outliers_iqr(df: pd.DataFrame, columns: Optional[List[str]] = None,
                        threshold: float = 1.5) -> Dict[str, List[int]]:
    """
    使用IQR方法检测异常值

    Args:
        df: 输入DataFrame
        columns: 要检测的列列表，如果为None则检测所有数值列
        threshold: IQR倍数阈值，默认为1.5

    Returns:
        检测结果字典，键为列名，值为包含异常值的行索引列表
    """
    if df.empty:
        logger.warning("输入的DataFrame为空")
        return {}

    # 确定要处理的列
    if columns is None:
        # 选择所有数值列
        numeric_columns = df.select_dtypes(include=[np.number]).columns.tolist()
        target_columns = numeric_columns
    else:
        # 检查指定的列是否存在且为数值类型
        target_columns = []
        for col in columns:
            if col not in df.columns:
                logger.warning(f"列 '{col}' 不存在于DataFrame中")
                continue
            if not pd.api.types.is_numeric_dtype(df[col]):
                logger.warning(f"列 '{col}' 不是数值类型，无法使用IQR方法")
                continue
            target_columns.append(col)

    # 存储结果
    outliers = {}

    # 对每列进行异常值检测
    for col in target_columns:
        # 计算四分位数
        q1 = df[col].quantile(0.25)
        q3 = df[col].quantile(0.75)
        iqr = q3 - q1

        # 计算上下界
        lower_bound = q1 - threshold * iqr
        upper_bound = q3 + threshold * iqr

        # 找出超出界限的索引
        outlier_idx = df.index[(df[col] < lower_bound) |
                               (df[col] > upper_bound)].tolist()

        # 如果有异常值，添加到结果字典
        if outlier_idx:
            outliers[col] = outlier_idx
            logger.info(f"在列 '{col}' 中检测到 {len(outlier_idx)} 个IQR异常值")

    return outliers


def detect_outliers_percentile(df: pd.DataFrame, columns: Optional[List[str]] = None,
                               lower: float = 0.01, upper: float = 0.99) -> Dict[str, List[int]]:
    """
    使用百分位数方法检测异常值

    Args:
        df: 输入DataFrame
        columns: 要检测的列列表，如果为None则检测所有数值列
        lower: 下限百分位，默认为0.01（1%）
        upper: 上限百分位，默认为0.99（99%）

    Returns:
        检测结果字典，键为列名，值为包含异常值的行索引列表
    """
    if df.empty:
        logger.warning("输入的DataFrame为空")
        return {}

    # 检查百分位参数
    if not (0 <= lower < upper <= 1):
        logger.error(
            f"百分位参数无效: lower={lower}, upper={upper}，必须满足 0 <= lower < upper <= 1")
        return {}

    # 确定要处理的列
    if columns is None:
        # 选择所有数值列
        numeric_columns = df.select_dtypes(include=[np.number]).columns.tolist()
        target_columns = numeric_columns
    else:
        # 检查指定的列是否存在且为数值类型
        target_columns = []
        for col in columns:
            if col not in df.columns:
                logger.warning(f"列 '{col}' 不存在于DataFrame中")
                continue
            if not pd.api.types.is_numeric_dtype(df[col]):
                logger.warning(f"列 '{col}' 不是数值类型，无法使用百分位数方法")
                continue
            target_columns.append(col)

    # 存储结果
    outliers = {}

    # 对每列进行异常值检测
    for col in target_columns:
        # 计算百分位数
        low_percentile = df[col].quantile(lower)
        high_percentile = df[col].quantile(upper)

        # 找出超出界限的索引
        outlier_idx = df.index[(df[col] < low_percentile) |
                               (df[col] > high_percentile)].tolist()

        # 如果有异常值，添加到结果字典
        if outlier_idx:
            outliers[col] = outlier_idx
            logger.info(f"在列 '{col}' 中检测到 {len(outlier_idx)} 个百分位异常值")

    return outliers


def detect_outliers_custom(df: pd.DataFrame, condition: Callable[[pd.DataFrame], pd.Series]) -> List[int]:
    """
    使用自定义条件检测异常值

    Args:
        df: 输入DataFrame
        condition: 接受DataFrame并返回布尔Series的函数，True表示异常值

    Returns:
        包含异常值的行索引列表
    """
    if df.empty:
        logger.warning("输入的DataFrame为空")
        return []

    try:
        # 应用自定义条件
        mask = condition(df)

        # 检查结果是否是布尔Series
        if not isinstance(mask, pd.Series) or mask.dtype != bool:
            logger.error("条件函数必须返回布尔Series")
            return []

        # 找出满足条件的索引
        outlier_idx = df.index[mask].tolist()

        logger.info(f"使用自定义条件检测到 {len(outlier_idx)} 个异常值")
        return outlier_idx

    except Exception as e:
        logger.error(f"应用自定义条件时出错: {str(e)}")
        return []


def remove_outliers(df: pd.DataFrame, outliers: Dict[str, List[int]],
                    method: str = 'drop', inplace: bool = False,
                    replacement_strategy: Optional[str] = None,
                    custom_values: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
    """
    处理检测到的异常值

    Args:
        df: 输入DataFrame
        outliers: 异常值字典，键为列名，值为包含异常值的行索引列表
        method: 处理方法，可选值: 'drop'（删除行）, 'replace'（替换值）, 'clip'（限制在有效范围内）
        inplace: 是否就地修改DataFrame，默认为False
        replacement_strategy: 替换策略，'mean'（均值）, 'median'（中位数）, 'mode'（众数）, 'value'（指定值）
        custom_values: 当replacement_strategy='value'时，指定每列的替换值

    Returns:
        处理后的DataFrame
    """
    if df.empty:
        logger.warning("输入的DataFrame为空")
        return df

    if not outliers:
        logger.info("没有异常值需要处理")
        return df

    # 创建副本（如果不是就地操作）
    result = df if inplace else df.copy()

    # 获取所有需要处理的行的索引
    all_outlier_idx = []
    for idx_list in outliers.values():
        all_outlier_idx.extend(idx_list)
    all_outlier_idx = list(set(all_outlier_idx))  # 去重

    # 根据方法处理异常值
    if method == 'drop':
        # 删除包含异常值的行
        result.drop(index=all_outlier_idx, inplace=True)
        logger.info(f"已删除 {len(all_outlier_idx)} 行包含异常值的数据")

    elif method == 'replace':
        if replacement_strategy is None:
            logger.error("使用'replace'方法时必须指定replacement_strategy")
            return df if not inplace else result

        # 对每列的异常值进行替换
        for col, idx_list in outliers.items():
            if replacement_strategy == 'mean':
                # 使用除异常值外的均值替换
                valid_values = result.loc[~result.index.isin(idx_list), col]
                replacement = valid_values.mean()
            elif replacement_strategy == 'median':
                # 使用除异常值外的中位数替换
                valid_values = result.loc[~result.index.isin(idx_list), col]
                replacement = valid_values.median()
            elif replacement_strategy == 'mode':
                # 使用除异常值外的众数替换
                valid_values = result.loc[~result.index.isin(idx_list), col]
                replacement = valid_values.mode()[0]
            elif replacement_strategy == 'value':
                # 使用指定值替换
                if custom_values is None or col not in custom_values:
                    logger.error(f"使用'value'策略时必须为列 '{col}' 提供替换值")
                    continue
                replacement = custom_values[col]
            else:
                logger.error(f"不支持的替换策略: {replacement_strategy}")
                continue

            # 替换异常值
            result.loc[idx_list, col] = replacement
            logger.info(f"已将列 '{col}' 中的 {len(idx_list)} 个异常值替换为 {replacement}")

    elif method == 'clip':
        # 对每列的异常值进行限制
        for col, idx_list in outliers.items():
            # 计算除异常值外的分位数
            valid_values = result.loc[~result.index.isin(idx_list), col]
            lower_bound = valid_values.quantile(0.05)  # 5%分位数
            upper_bound = valid_values.quantile(0.95)  # 95%分位数

            # 对异常值进行限制
            result.loc[idx_list, col] = result.loc[idx_list,
                                                   col].clip(lower_bound, upper_bound)
            logger.info(
                f"已将列 '{col}' 中的 {len(idx_list)} 个异常值限制在范围 [{lower_bound}, {upper_bound}] 内")

    else:
        logger.error(f"不支持的处理方法: {method}")
        return df if not inplace else result

    return result


def winsorize(df: pd.DataFrame, columns: Optional[List[str]] = None,
              limits: Tuple[float, float] = (0.05, 0.05), inplace: bool = False) -> pd.DataFrame:
    """
    对数据进行winsorize处理（将极端值限制在指定分位数范围内）

    Args:
        df: 输入DataFrame
        columns: 要处理的列列表，如果为None则处理所有数值列
        limits: 下限和上限分位数，默认为(0.05, 0.05)，表示5%和95%
        inplace: 是否就地修改DataFrame，默认为False

    Returns:
        处理后的DataFrame
    """
    if df.empty:
        logger.warning("输入的DataFrame为空")
        return df

    # 检查分位数参数
    lower, upper = limits
    if not (0 <= lower < 1 and 0 <= upper < 1):
        logger.error(
            f"分位数参数无效: lower={lower}, upper={upper}，必须满足 0 <= lower, upper < 1")
        return df

    # 创建副本（如果不是就地操作）
    result = df if inplace else df.copy()

    # 确定要处理的列
    if columns is None:
        # 选择所有数值列
        target_columns = result.select_dtypes(include=[np.number]).columns.tolist()
    else:
        # 检查指定的列是否存在且为数值类型
        target_columns = []
        for col in columns:
            if col not in result.columns:
                logger.warning(f"列 '{col}' 不存在于DataFrame中")
                continue
            if not pd.api.types.is_numeric_dtype(result[col]):
                logger.warning(f"列 '{col}' 不是数值类型，无法进行winsorize处理")
                continue
            target_columns.append(col)

    # 对每列进行winsorize处理
    for col in target_columns:
        # 计算分位数
        low_quantile = result[col].quantile(lower)
        high_quantile = result[col].quantile(1 - upper)

        # 统计被限制的值的数量
        low_count = (result[col] < low_quantile).sum()
        high_count = (result[col] > high_quantile).sum()

        # 对值进行限制
        result[col] = result[col].clip(lower=low_quantile, upper=high_quantile)

        if low_count > 0 or high_count > 0:
            logger.info(f"列 '{col}' 中的 {low_count} 个值被限制在下限 {low_quantile}，"
                        f"{high_count} 个值被限制在上限 {high_quantile}")

    return result
