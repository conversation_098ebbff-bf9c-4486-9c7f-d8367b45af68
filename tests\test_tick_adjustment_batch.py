#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
tick复权数据批量处理功能测试

测试批量处理tick复权数据的功能正确性、性能表现和错误处理能力。
"""

import os
import sys
import unittest
import tempfile
import pandas as pd
from unittest.mock import patch, MagicMock

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from data.批量处理tick复权数据 import process_tick_adjustment_data, _create_initial_result_file


class TestTickAdjustmentBatch(unittest.TestCase):
    """tick复权数据批量处理测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.test_symbol = "000001.SZ"
        self.test_dividend_type = "front"
        
        # 创建测试用的tick数据
        self.test_tick_data = pd.DataFrame({
            'time': [1721000000000, 1721000060000, 1721000120000],
            'lastPrice': [10.50, 10.52, 10.48],
            'volume': [1000, 1500, 800],
            'amount': [10500.0, 15780.0, 8384.0],
            'bid1': [10.49, 10.51, 10.47],
            'ask1': [10.51, 10.53, 10.49]
        }, index=pd.Index(['20250715093000', '20250715093100', '20250715093200'], name='index'))
    
    @patch('data.批量处理tick复权数据.read_partitioned_data_vectorized')
    def test_process_tick_adjustment_data_success(self, mock_read_data):
        """测试成功处理tick复权数据"""
        # 模拟成功读取数据
        mock_read_data.return_value = self.test_tick_data
        
        # 调用处理函数
        result = process_tick_adjustment_data(
            symbol=self.test_symbol,
            dividend_type=self.test_dividend_type,
            show_data=False
        )
        
        # 验证结果
        self.assertTrue(result["success"])
        self.assertEqual(result["symbol"], self.test_symbol)
        self.assertEqual(result["rows"], 3)
        self.assertIsNotNone(result["data"])
        self.assertIsNotNone(result["start_time"])
        self.assertIsNotNone(result["end_time"])
        
        # 验证函数调用
        mock_read_data.assert_called_once()
    
    @patch('data.批量处理tick复权数据.read_partitioned_data_vectorized')
    def test_process_tick_adjustment_data_empty_data(self, mock_read_data):
        """测试处理空数据的情况"""
        # 模拟返回空数据
        mock_read_data.return_value = pd.DataFrame()
        
        # 调用处理函数
        result = process_tick_adjustment_data(
            symbol=self.test_symbol,
            dividend_type=self.test_dividend_type,
            show_data=False
        )
        
        # 验证结果
        self.assertFalse(result["success"])
        self.assertEqual(result["symbol"], self.test_symbol)
        self.assertEqual(result["rows"], 0)
        self.assertEqual(result["error"], "未读取到数据")
    
    @patch('data.批量处理tick复权数据.read_partitioned_data_vectorized')
    def test_process_tick_adjustment_data_exception(self, mock_read_data):
        """测试处理异常情况"""
        # 模拟抛出异常
        mock_read_data.side_effect = Exception("数据读取失败")
        
        # 调用处理函数
        result = process_tick_adjustment_data(
            symbol=self.test_symbol,
            dividend_type=self.test_dividend_type,
            show_data=False
        )
        
        # 验证结果
        self.assertFalse(result["success"])
        self.assertEqual(result["symbol"], self.test_symbol)
        self.assertEqual(result["rows"], 0)
        self.assertEqual(result["error"], "数据读取失败")
    
    def test_process_tick_adjustment_data_with_time_range(self):
        """测试带时间范围的处理"""
        with patch('data.批量处理tick复权数据.read_partitioned_data_vectorized') as mock_read_data:
            mock_read_data.return_value = self.test_tick_data
            
            # 调用处理函数，带时间范围
            result = process_tick_adjustment_data(
                symbol=self.test_symbol,
                dividend_type=self.test_dividend_type,
                start_time="20250715090000",
                end_time="20250715150000",
                show_data=False
            )
            
            # 验证结果
            self.assertTrue(result["success"])
            
            # 验证函数调用参数
            call_args = mock_read_data.call_args
            self.assertEqual(call_args[1]["start_time"], "20250715090000")
            self.assertEqual(call_args[1]["end_time"], "20250715150000")
            self.assertEqual(call_args[1]["dividend_type"], self.test_dividend_type)
            self.assertEqual(call_args[1]["period"], "tick")
    
    def test_create_initial_result_file(self):
        """测试创建初始结果文件"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试股票列表文件
            stock_list_file = os.path.join(temp_dir, "stock_list.txt")
            with open(stock_list_file, 'w', encoding='utf-8') as f:
                f.write("000001.SZ\n")
                f.write("600000.SH\n")
                f.write("# 这是注释\n")
                f.write("000002.SZ\n")
            
            # 创建结果文件
            result_file = os.path.join(temp_dir, "test_results.txt")
            _create_initial_result_file(stock_list_file, result_file)
            
            # 验证结果文件是否创建成功
            self.assertTrue(os.path.exists(result_file))
            
            # 验证文件内容
            with open(result_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.assertIn("tick复权数据批量处理结果", content)
            self.assertIn("000001.SZ", content)
            self.assertIn("600000.SH", content)
            self.assertIn("000002.SZ", content)
            self.assertIn("未处理的股票:", content)
            self.assertIn("总计股票: 3", content)
    
    def test_different_dividend_types(self):
        """测试不同复权类型"""
        dividend_types = ["none", "front", "back"]
        
        for dividend_type in dividend_types:
            with patch('data.批量处理tick复权数据.read_partitioned_data_vectorized') as mock_read_data:
                mock_read_data.return_value = self.test_tick_data
                
                # 调用处理函数
                result = process_tick_adjustment_data(
                    symbol=self.test_symbol,
                    dividend_type=dividend_type,
                    show_data=False
                )
                
                # 验证结果
                self.assertTrue(result["success"], f"复权类型 {dividend_type} 处理失败")
                
                # 验证函数调用参数
                call_args = mock_read_data.call_args
                self.assertEqual(call_args[1]["dividend_type"], dividend_type)
    
    @patch('data.批量处理tick复权数据.read_partitioned_data_vectorized')
    def test_data_preview_display(self, mock_read_data):
        """测试数据预览显示功能"""
        mock_read_data.return_value = self.test_tick_data
        
        # 测试显示数据预览
        with patch('builtins.print') as mock_print:
            result = process_tick_adjustment_data(
                symbol=self.test_symbol,
                dividend_type=self.test_dividend_type,
                show_data=True,
                display_head_rows=2,
                display_tail_rows=1
            )
            
            # 验证结果
            self.assertTrue(result["success"])
            
            # 验证打印输出
            print_calls = [call[0][0] for call in mock_print.call_args_list]
            preview_calls = [call for call in print_calls if "tick复权数据预览" in str(call)]
            self.assertTrue(len(preview_calls) > 0, "应该有数据预览输出")
    
    def test_performance_with_large_data(self):
        """测试大数据量的性能表现"""
        # 创建大量测试数据
        large_data = pd.DataFrame({
            'time': range(1721000000000, 1721000000000 + 10000 * 60000, 60000),
            'lastPrice': [10.50 + i * 0.01 for i in range(10000)],
            'volume': [1000 + i * 10 for i in range(10000)],
            'amount': [10500.0 + i * 100 for i in range(10000)],
            'bid1': [10.49 + i * 0.01 for i in range(10000)],
            'ask1': [10.51 + i * 0.01 for i in range(10000)]
        }, index=pd.Index([f"202507150930{i:02d}" for i in range(10000)], name='index'))
        
        with patch('data.批量处理tick复权数据.read_partitioned_data_vectorized') as mock_read_data:
            mock_read_data.return_value = large_data
            
            import time
            start_time = time.time()
            
            # 调用处理函数
            result = process_tick_adjustment_data(
                symbol=self.test_symbol,
                dividend_type=self.test_dividend_type,
                show_data=False
            )
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            # 验证结果
            self.assertTrue(result["success"])
            self.assertEqual(result["rows"], 10000)
            
            # 验证性能（处理10000行数据应该在合理时间内完成）
            self.assertLess(processing_time, 5.0, "大数据量处理时间过长")


if __name__ == '__main__':
    unittest.main()
