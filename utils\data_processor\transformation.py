#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据转换功能模块

提供数据归一化、标准化、特征工程等功能
"""

import os
import sys
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Set, Tuple, Union, Callable, Any
from sklearn.preprocessing import MinMaxScaler, StandardScaler, RobustScaler
from datetime import datetime, timedelta

# 将项目根目录添加到Python路径
root_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, root_path)

# 导入日志模块
from utils.logger import get_unified_logger

# 设置日志记录器
logger = get_unified_logger(__name__)


def normalize_data(df: pd.DataFrame, columns: Optional[List[str]] = None,
                 method: str = 'minmax', feature_range: Tuple[float, float] = (0, 1),
                 inplace: bool = False) -> <PERSON><PERSON>[pd.DataFrame, Dict[str, Any]]:
    """
    对数据进行归一化处理
    
    Args:
        df: 输入DataFrame
        columns: 要处理的列列表，如果为None则处理所有数值列
        method: 归一化方法，可选值: 'minmax'（最小-最大缩放）, 'zscore'（标准化）, 'robust'（稳健缩放）
        feature_range: 用于minmax方法的特征范围，默认为(0, 1)
        inplace: 是否就地修改DataFrame，默认为False
        
    Returns:
        一个元组，包含(归一化后的DataFrame, 归一化参数字典)
    """
    if df.empty:
        logger.warning("输入的DataFrame为空")
        return df, {}
    
    # 创建副本（如果不是就地操作）
    result = df if inplace else df.copy()
    
    # 确定要处理的列
    if columns is None:
        # 选择所有数值列
        target_columns = result.select_dtypes(include=[np.number]).columns.tolist()
    else:
        # 检查指定的列是否存在且为数值类型
        target_columns = []
        for col in columns:
            if col not in result.columns:
                logger.warning(f"列 '{col}' 不存在于DataFrame中")
                continue
            if not pd.api.types.is_numeric_dtype(result[col]):
                logger.warning(f"列 '{col}' 不是数值类型，无法进行归一化")
                continue
            target_columns.append(col)
    
    # 存储归一化参数
    params = {}
    
    # 根据方法对数据进行归一化
    if method == 'minmax':
        # 创建缩放器
        scaler = MinMaxScaler(feature_range=feature_range)
        
        # 对每列进行归一化
        for col in target_columns:
            # 保存缺失值的位置
            na_mask = result[col].isna()
            
            # 对非缺失值进行缩放
            if not na_mask.all():  # 确保不是全部缺失
                values = result.loc[~na_mask, col].values.reshape(-1, 1)
                scaler.fit(values)
                result.loc[~na_mask, col] = scaler.transform(values).flatten()
                
                # 存储归一化参数
                params[col] = {
                    'method': 'minmax',
                    'min': scaler.data_min_[0],
                    'max': scaler.data_max_[0],
                    'feature_range': feature_range
                }
        
        logger.info(f"已对 {len(target_columns)} 列使用MinMax方法进行归一化")
    
    elif method == 'zscore':
        # 创建缩放器
        scaler = StandardScaler()
        
        # 对每列进行标准化
        for col in target_columns:
            # 保存缺失值的位置
            na_mask = result[col].isna()
            
            # 对非缺失值进行缩放
            if not na_mask.all():  # 确保不是全部缺失
                values = result.loc[~na_mask, col].values.reshape(-1, 1)
                scaler.fit(values)
                result.loc[~na_mask, col] = scaler.transform(values).flatten()
                
                # 存储归一化参数
                params[col] = {
                    'method': 'zscore',
                    'mean': scaler.mean_[0],
                    'std': scaler.scale_[0]
                }
        
        logger.info(f"已对 {len(target_columns)} 列使用Z-score方法进行标准化")
    
    elif method == 'robust':
        # 创建稳健缩放器
        scaler = RobustScaler()
        
        # 对每列进行稳健缩放
        for col in target_columns:
            # 保存缺失值的位置
            na_mask = result[col].isna()
            
            # 对非缺失值进行缩放
            if not na_mask.all():  # 确保不是全部缺失
                values = result.loc[~na_mask, col].values.reshape(-1, 1)
                scaler.fit(values)
                result.loc[~na_mask, col] = scaler.transform(values).flatten()
                
                # 存储归一化参数
                params[col] = {
                    'method': 'robust',
                    'center': scaler.center_[0],
                    'scale': scaler.scale_[0]
                }
        
        logger.info(f"已对 {len(target_columns)} 列使用Robust方法进行稳健缩放")
    
    else:
        logger.error(f"不支持的归一化方法: {method}")
        return df if not inplace else result, {}
    
    return result, params


def apply_normalization(df: pd.DataFrame, params: Dict[str, Dict[str, Any]],
                      inplace: bool = False) -> pd.DataFrame:
    """
    使用已有的归一化参数对数据进行转换
    
    Args:
        df: 输入DataFrame
        params: 归一化参数字典，由normalize_data函数返回
        inplace: 是否就地修改DataFrame，默认为False
        
    Returns:
        转换后的DataFrame
    """
    if df.empty:
        logger.warning("输入的DataFrame为空")
        return df
    
    if not params:
        logger.warning("归一化参数为空")
        return df
    
    # 创建副本（如果不是就地操作）
    result = df if inplace else df.copy()
    
    # 对每列应用归一化参数
    for col, col_params in params.items():
        if col not in result.columns:
            logger.warning(f"列 '{col}' 不存在于DataFrame中")
            continue
        
        if not pd.api.types.is_numeric_dtype(result[col]):
            logger.warning(f"列 '{col}' 不是数值类型，无法应用归一化")
            continue
        
        method = col_params.get('method')
        
        if method == 'minmax':
            # 获取参数
            min_val = col_params.get('min')
            max_val = col_params.get('max')
            feature_min, feature_max = col_params.get('feature_range', (0, 1))
            
            # 检查参数是否完整
            if min_val is None or max_val is None:
                logger.error(f"MinMax归一化参数不完整: {col_params}")
                continue
            
            # 应用公式: X_norm = (X - X_min) / (X_max - X_min) * (feature_max - feature_min) + feature_min
            if max_val != min_val:  # 避免除以零
                # 保存缺失值的位置
                na_mask = result[col].isna()
                
                # 对非缺失值进行转换
                result.loc[~na_mask, col] = (result.loc[~na_mask, col] - min_val) / (max_val - min_val) \
                                          * (feature_max - feature_min) + feature_min
        
        elif method == 'zscore':
            # 获取参数
            mean = col_params.get('mean')
            std = col_params.get('std')
            
            # 检查参数是否完整
            if mean is None or std is None:
                logger.error(f"Z-score标准化参数不完整: {col_params}")
                continue
            
            # 应用公式: X_norm = (X - mean) / std
            if std != 0:  # 避免除以零
                # 保存缺失值的位置
                na_mask = result[col].isna()
                
                # 对非缺失值进行转换
                result.loc[~na_mask, col] = (result.loc[~na_mask, col] - mean) / std
        
        elif method == 'robust':
            # 获取参数
            center = col_params.get('center')
            scale = col_params.get('scale')
            
            # 检查参数是否完整
            if center is None or scale is None:
                logger.error(f"Robust缩放参数不完整: {col_params}")
                continue
            
            # 应用公式: X_norm = (X - center) / scale
            if scale != 0:  # 避免除以零
                # 保存缺失值的位置
                na_mask = result[col].isna()
                
                # 对非缺失值进行转换
                result.loc[~na_mask, col] = (result.loc[~na_mask, col] - center) / scale
        
        else:
            logger.error(f"不支持的归一化方法: {method}")
    
    logger.info(f"已应用归一化参数到 {len(params)} 列")
    return result


def add_lagged_features(df: pd.DataFrame, columns: List[str], lags: List[int],
                       inplace: bool = False) -> pd.DataFrame:
    """
    添加滞后特征
    
    Args:
        df: 输入DataFrame，假定已按时间排序
        columns: 要添加滞后特征的列列表
        lags: 滞后周期列表，正数表示过去的值，负数表示未来的值
        inplace: 是否就地修改DataFrame，默认为False
        
    Returns:
        添加滞后特征后的DataFrame
    """
    if df.empty:
        logger.warning("输入的DataFrame为空")
        return df
    
    # 创建副本（如果不是就地操作）
    result = df if inplace else df.copy()
    
    # 检查列是否存在
    missing_columns = [col for col in columns if col not in result.columns]
    if missing_columns:
        logger.warning(f"以下列不存在于DataFrame中：{missing_columns}")
        columns = [col for col in columns if col in result.columns]
    
    # 为每个列和滞后周期添加特征
    for col in columns:
        for lag in lags:
            # 创建新列名
            lag_col_name = f"{col}_lag_{lag}"
            
            # 添加滞后特征
            result[lag_col_name] = result[col].shift(lag)
            
            logger.debug(f"已添加滞后特征: {lag_col_name}")
    
    logger.info(f"已为 {len(columns)} 列添加了 {len(lags)} 个滞后周期的特征")
    return result


def add_rolling_features(df: pd.DataFrame, columns: List[str], windows: List[int],
                       functions: List[str] = ['mean', 'std', 'min', 'max'],
                       min_periods: Optional[int] = None, inplace: bool = False) -> pd.DataFrame:
    """
    添加滚动窗口特征
    
    Args:
        df: 输入DataFrame，假定已按时间排序
        columns: 要添加滚动特征的列列表
        windows: 窗口大小列表
        functions: 要应用的函数列表，默认为['mean', 'std', 'min', 'max']
        min_periods: 最小观测数，默认为None（等于窗口大小）
        inplace: 是否就地修改DataFrame，默认为False
        
    Returns:
        添加滚动特征后的DataFrame
    """
    if df.empty:
        logger.warning("输入的DataFrame为空")
        return df
    
    # 创建副本（如果不是就地操作）
    result = df if inplace else df.copy()
    
    # 检查列是否存在
    missing_columns = [col for col in columns if col not in result.columns]
    if missing_columns:
        logger.warning(f"以下列不存在于DataFrame中：{missing_columns}")
        columns = [col for col in columns if col in result.columns]
    
    # 定义函数映射
    func_map = {
        'mean': lambda x: x.mean(),
        'std': lambda x: x.std(),
        'min': lambda x: x.min(),
        'max': lambda x: x.max(),
        'median': lambda x: x.median(),
        'sum': lambda x: x.sum(),
        'count': lambda x: x.count(),
        'var': lambda x: x.var(),
        'skew': lambda x: x.skew(),
        'kurt': lambda x: x.kurt()
    }
    
    # 检查函数是否支持
    unsupported_funcs = [f for f in functions if f not in func_map]
    if unsupported_funcs:
        logger.warning(f"以下函数不受支持：{unsupported_funcs}")
        functions = [f for f in functions if f in func_map]
    
    # 为每个列、窗口和函数添加特征
    for col in columns:
        for window in windows:
            rolling = result[col].rolling(window=window, min_periods=min_periods)
            
            for func_name in functions:
                # 创建新列名
                new_col_name = f"{col}_{func_name}_{window}"
                
                # 添加滚动特征
                func = func_map[func_name]
                result[new_col_name] = func(rolling)
                
                logger.debug(f"已添加滚动特征: {new_col_name}")
    
    logger.info(f"已为 {len(columns)} 列添加了 {len(windows)}x{len(functions)} 个滚动特征")
    return result


def add_expanding_features(df: pd.DataFrame, columns: List[str],
                         functions: List[str] = ['mean', 'std', 'min', 'max'],
                         min_periods: int = 1, inplace: bool = False) -> pd.DataFrame:
    """
    添加扩展窗口特征（累积特征）
    
    Args:
        df: 输入DataFrame，假定已按时间排序
        columns: 要添加扩展特征的列列表
        functions: 要应用的函数列表，默认为['mean', 'std', 'min', 'max']
        min_periods: 最小观测数，默认为1
        inplace: 是否就地修改DataFrame，默认为False
        
    Returns:
        添加扩展特征后的DataFrame
    """
    if df.empty:
        logger.warning("输入的DataFrame为空")
        return df
    
    # 创建副本（如果不是就地操作）
    result = df if inplace else df.copy()
    
    # 检查列是否存在
    missing_columns = [col for col in columns if col not in result.columns]
    if missing_columns:
        logger.warning(f"以下列不存在于DataFrame中：{missing_columns}")
        columns = [col for col in columns if col in result.columns]
    
    # 定义函数映射
    func_map = {
        'mean': lambda x: x.mean(),
        'std': lambda x: x.std(),
        'min': lambda x: x.min(),
        'max': lambda x: x.max(),
        'median': lambda x: x.median(),
        'sum': lambda x: x.sum(),
        'count': lambda x: x.count(),
        'var': lambda x: x.var(),
        'skew': lambda x: x.skew(),
        'kurt': lambda x: x.kurt()
    }
    
    # 检查函数是否支持
    unsupported_funcs = [f for f in functions if f not in func_map]
    if unsupported_funcs:
        logger.warning(f"以下函数不受支持：{unsupported_funcs}")
        functions = [f for f in functions if f in func_map]
    
    # 为每个列和函数添加特征
    for col in columns:
        expanding = result[col].expanding(min_periods=min_periods)
        
        for func_name in functions:
            # 创建新列名
            new_col_name = f"{col}_expanding_{func_name}"
            
            # 添加扩展特征
            func = func_map[func_name]
            result[new_col_name] = func(expanding)
            
            logger.debug(f"已添加扩展特征: {new_col_name}")
    
    logger.info(f"已为 {len(columns)} 列添加了 {len(functions)} 个扩展特征")
    return result


def add_ewm_features(df: pd.DataFrame, columns: List[str], spans: List[float],
                   functions: List[str] = ['mean', 'std'],
                   min_periods: int = 1, adjust: bool = True,
                   inplace: bool = False) -> pd.DataFrame:
    """
    添加指数加权移动特征
    
    Args:
        df: 输入DataFrame，假定已按时间排序
        columns: 要添加EWM特征的列列表
        spans: 跨度值列表，用于控制权重衰减
        functions: 要应用的函数列表，默认为['mean', 'std']
        min_periods: 最小观测数，默认为1
        adjust: 是否调整开始观测值的权重，默认为True
        inplace: 是否就地修改DataFrame，默认为False
        
    Returns:
        添加EWM特征后的DataFrame
    """
    if df.empty:
        logger.warning("输入的DataFrame为空")
        return df
    
    # 创建副本（如果不是就地操作）
    result = df if inplace else df.copy()
    
    # 检查列是否存在
    missing_columns = [col for col in columns if col not in result.columns]
    if missing_columns:
        logger.warning(f"以下列不存在于DataFrame中：{missing_columns}")
        columns = [col for col in columns if col in result.columns]
    
    # 定义函数映射
    func_map = {
        'mean': lambda ewm: ewm.mean(),
        'std': lambda ewm: ewm.std(),
        'var': lambda ewm: ewm.var()
    }
    
    # 检查函数是否支持
    unsupported_funcs = [f for f in functions if f not in func_map]
    if unsupported_funcs:
        logger.warning(f"以下函数不受支持（EWM只支持mean、std和var）：{unsupported_funcs}")
        functions = [f for f in functions if f in func_map]
    
    # 为每个列、跨度和函数添加特征
    for col in columns:
        for span in spans:
            # 配置EWM对象
            ewm = result[col].ewm(span=span, min_periods=min_periods, adjust=adjust)
            
            for func_name in functions:
                # 创建新列名
                new_col_name = f"{col}_ewm_{func_name}_span{span}"
                
                # 添加EWM特征
                func = func_map[func_name]
                result[new_col_name] = func(ewm)
                
                logger.debug(f"已添加EWM特征: {new_col_name}")
    
    logger.info(f"已为 {len(columns)} 列添加了 {len(spans)}x{len(functions)} 个EWM特征")
    return result


def add_diff_features(df: pd.DataFrame, columns: List[str], periods: List[int] = [1],
                    inplace: bool = False) -> pd.DataFrame:
    """
    添加差分特征
    
    Args:
        df: 输入DataFrame，假定已按时间排序
        columns: 要添加差分特征的列列表
        periods: 差分周期列表，默认为[1]
        inplace: 是否就地修改DataFrame，默认为False
        
    Returns:
        添加差分特征后的DataFrame
    """
    if df.empty:
        logger.warning("输入的DataFrame为空")
        return df
    
    # 创建副本（如果不是就地操作）
    result = df if inplace else df.copy()
    
    # 检查列是否存在
    missing_columns = [col for col in columns if col not in result.columns]
    if missing_columns:
        logger.warning(f"以下列不存在于DataFrame中：{missing_columns}")
        columns = [col for col in columns if col in result.columns]
    
    # 为每个列和周期添加差分特征
    for col in columns:
        for period in periods:
            # 创建新列名
            new_col_name = f"{col}_diff_{period}"
            
            # 添加差分特征
            result[new_col_name] = result[col].diff(periods=period)
            
            logger.debug(f"已添加差分特征: {new_col_name}")
    
    logger.info(f"已为 {len(columns)} 列添加了 {len(periods)} 个差分特征")
    return result


def add_pct_change_features(df: pd.DataFrame, columns: List[str], periods: List[int] = [1],
                          fill_method: Optional[str] = None, inplace: bool = False) -> pd.DataFrame:
    """
    添加百分比变化特征
    
    Args:
        df: 输入DataFrame，假定已按时间排序
        columns: 要添加百分比变化特征的列列表
        periods: 变化周期列表，默认为[1]
        fill_method: 填充方法，默认为None，可选'ffill', 'bfill'等
        inplace: 是否就地修改DataFrame，默认为False
        
    Returns:
        添加百分比变化特征后的DataFrame
    """
    if df.empty:
        logger.warning("输入的DataFrame为空")
        return df
    
    # 创建副本（如果不是就地操作）
    result = df if inplace else df.copy()
    
    # 检查列是否存在
    missing_columns = [col for col in columns if col not in result.columns]
    if missing_columns:
        logger.warning(f"以下列不存在于DataFrame中：{missing_columns}")
        columns = [col for col in columns if col in result.columns]
    
    # 为每个列和周期添加百分比变化特征
    for col in columns:
        for period in periods:
            # 创建新列名
            new_col_name = f"{col}_pct_change_{period}"
            
            # 添加百分比变化特征
            result[new_col_name] = result[col].pct_change(periods=period, fill_method=fill_method)
            
            logger.debug(f"已添加百分比变化特征: {new_col_name}")
    
    logger.info(f"已为 {len(columns)} 列添加了 {len(periods)} 个百分比变化特征")
    return result


def encode_categorical(df: pd.DataFrame, columns: List[str], method: str = 'onehot',
                     drop_first: bool = True, inplace: bool = False) -> pd.DataFrame:
    """
    对分类特征进行编码
    
    Args:
        df: 输入DataFrame
        columns: 要编码的分类列列表
        method: 编码方法，可选值: 'onehot'（独热编码）, 'label'（标签编码）, 'ordinal'（序数编码）
        drop_first: 是否删除第一个类别（用于避免共线性），仅用于onehot编码，默认为True
        inplace: 是否就地修改DataFrame，默认为False
        
    Returns:
        编码后的DataFrame
    """
    if df.empty:
        logger.warning("输入的DataFrame为空")
        return df
    
    # 创建副本（如果不是就地操作）
    result = df if inplace else df.copy()
    
    # 检查列是否存在
    missing_columns = [col for col in columns if col not in result.columns]
    if missing_columns:
        logger.warning(f"以下列不存在于DataFrame中：{missing_columns}")
        columns = [col for col in columns if col in result.columns]
    
    # 根据方法对分类特征进行编码
    if method == 'onehot':
        # 独热编码
        for col in columns:
            # 创建哑变量
            dummies = pd.get_dummies(result[col], prefix=col, drop_first=drop_first)
            
            # 将哑变量添加到结果DataFrame
            result = pd.concat([result, dummies], axis=1)
            
            # 删除原始列
            result.drop(columns=[col], inplace=True)
            
            logger.debug(f"已对列 '{col}' 进行独热编码，生成了 {dummies.shape[1]} 个新特征")
    
    elif method == 'label':
        # 标签编码
        from sklearn.preprocessing import LabelEncoder
        
        for col in columns:
            # 创建编码器
            encoder = LabelEncoder()
            
            # 对非缺失值进行编码
            na_mask = result[col].isna()
            if not na_mask.all():  # 确保不是全部缺失
                result.loc[~na_mask, col] = encoder.fit_transform(result.loc[~na_mask, col])
            
            logger.debug(f"已对列 '{col}' 进行标签编码")
    
    elif method == 'ordinal':
        # 序数编码（假设分类值已经有序）
        for col in columns:
            # 获取唯一值并排序
            categories = sorted(result[col].dropna().unique())
            
            # 创建映射
            mapping = {category: i for i, category in enumerate(categories)}
            
            # 应用映射
            result[col] = result[col].map(mapping)
            
            logger.debug(f"已对列 '{col}' 进行序数编码，映射为: {mapping}")
    
    else:
        logger.error(f"不支持的编码方法: {method}")
        return df if not inplace else result
    
    logger.info(f"已对 {len(columns)} 列使用 {method} 方法进行编码")
    return result