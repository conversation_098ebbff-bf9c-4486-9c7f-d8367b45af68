#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
索引处理配置文件
定义项目级别的索引处理标准和常量
"""

# 标准索引格式配置
INDEX_CONFIG = {
    # 标准时间戳格式
    "STANDARD_FORMAT": "YYYYMMDDHHMMSS",
    "FORMAT_LENGTH": 14,
    "FORMAT_PATTERN": r'^\d{14}$',
    
    # pandas concat操作的标准参数
    "CONCAT_PARAMS": {
        "ignore_index": False,  # 必须为False以保持索引
        "sort": False,          # 避免不必要的排序
        "copy": False          # 避免不必要的数据复制
    },
    
    # 索引验证配置
    "VALIDATION": {
        "check_duplicates": True,
        "check_monotonic": True,
        "check_format": True,
        "sample_size": 5  # 验证时检查的样本数量
    },
    
    # 日志配置
    "LOGGING": {
        "log_index_info": True,
        "log_validation_results": True,
        "log_concat_operations": True
    }
}

# 错误消息模板
ERROR_MESSAGES = {
    "INVALID_FORMAT": "索引格式不正确，期望{expected}格式，实际为{actual}",
    "DUPLICATE_INDEX": "检测到{count}个重复索引值",
    "NOT_MONOTONIC": "索引未按时间顺序排序",
    "EMPTY_DATAFRAME": "DataFrame为空，无法验证索引",
    "CONCAT_FAILED": "DataFrame合并失败: {error}",
    "INDEX_RESET_DETECTED": "检测到索引被重置为数字序列，这是错误的！"
}

# 推荐操作
RECOMMENDATIONS = {
    "FIX_FORMAT": "使用IndexManager.ensure_proper_index()修复索引格式",
    "REMOVE_DUPLICATES": "使用df[~df.index.duplicated(keep='first')]移除重复索引",
    "SORT_INDEX": "使用df.sort_index()对数据按索引排序",
    "USE_SAFE_CONCAT": "使用IndexManager.safe_concat()进行安全的数据合并"
}
