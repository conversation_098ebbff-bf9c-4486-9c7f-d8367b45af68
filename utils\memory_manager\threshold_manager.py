"""
阈值管理模块 - 提供内存阈值设置和触发管理功能
"""

import time
from typing import Dict, List, Callable, Optional
from enum import Enum, auto

# 导入正确的logger模块
from ..logger import get_unified_logger

# 获取logger实例
logger = get_unified_logger("threshold_manager")


class ThresholdType(Enum):
    """阈值类型枚举"""
    SYSTEM_PERCENT = auto()  # 系统内存使用百分比
    SYSTEM_AVAILABLE_MB = auto()  # 系统可用内存(MB)
    PROCESS_PERCENT = auto()  # 进程内存使用百分比
    PROCESS_RSS_MB = auto()  # 进程物理内存使用(MB)
    GROWTH_RATE_MB_MIN = auto()  # 内存增长率(MB/分钟)


class ThresholdLevel(Enum):
    """阈值级别枚举"""
    INFO = auto()  # 信息级别
    WARNING = auto()  # 警告级别
    CRITICAL = auto()  # 严重级别
    EMERGENCY = auto()  # 紧急级别


class ThresholdDirection(Enum):
    """阈值方向枚举"""
    ABOVE = auto()  # 高于阈值触发
    BELOW = auto()  # 低于阈值触发


class Threshold:
    """
    阈值定义类
    
    表示一个内存阈值条件，当条件满足时触发回调
    """
    
    def __init__(
        self,
        threshold_id: str,
        threshold_type: ThresholdType,
        value: float,
        level: ThresholdLevel = ThresholdLevel.WARNING,
        direction: ThresholdDirection = ThresholdDirection.ABOVE,
        callback: Optional[Callable[[Dict], None]] = None,
        cooldown_seconds: int = 60
    ):
        """
        初始化阈值
        
        Args:
            threshold_id: 阈值唯一标识符
            threshold_type: 阈值类型
            value: 阈值数值
            level: 阈值级别，默认为警告级别
            direction: 阈值方向，默认为高于阈值触发
            callback: 触发回调函数，接收内存信息字典作为参数
            cooldown_seconds: 冷却时间(秒)，防止频繁触发，默认60秒
        """
        self.id = threshold_id
        self.type = threshold_type
        self.value = value
        self.level = level
        self.direction = direction
        self.callback = callback
        self.cooldown_seconds = cooldown_seconds
        
        # 上次触发时间
        self.last_triggered = 0
        
        # 是否已触发(用于状态变化检测)
        self.is_triggered = False
    
    def check(self, memory_info: Dict) -> bool:
        """
        检查阈值是否满足触发条件
        
        Args:
            memory_info: 内存信息字典
            
        Returns:
            是否满足触发条件
        """
        current_value = self._get_current_value(memory_info)
        
        # 检查是否满足条件
        if self.direction == ThresholdDirection.ABOVE:
            condition_met = current_value > self.value
        else:
            condition_met = current_value < self.value
        
        return condition_met
    
    def should_trigger(self, memory_info: Dict) -> bool:
        """
        检查是否应该触发回调
        
        考虑冷却时间和状态变化
        
        Args:
            memory_info: 内存信息字典
            
        Returns:
            是否应该触发回调
        """
        # 检查阈值条件
        condition_met = self.check(memory_info)
        
        # 如果条件不满足，重置触发状态
        if not condition_met:
            if self.is_triggered:
                self.is_triggered = False
            return False
        
        # 如果条件满足但已经触发过，检查冷却时间
        if self.is_triggered:
            current_time = time.time()
            if current_time - self.last_triggered < self.cooldown_seconds:
                return False
        
        # 满足触发条件
        return True
    
    def trigger(self, memory_info: Dict) -> bool:
        """
        触发阈值回调
        
        Args:
            memory_info: 内存信息字典
            
        Returns:
            是否成功触发
        """
        if not self.should_trigger(memory_info):
            return False
        
        # 更新状态
        self.last_triggered = time.time()
        self.is_triggered = True
        
        # 执行回调
        if self.callback:
            try:
                self.callback(memory_info)
            except Exception as e:
                logger.error(f"执行阈值回调出错 [{self.id}]: {e}")
        
        # 记录日志
        current_value = self._get_current_value(memory_info)
        logger.warning(
            f"内存阈值触发 [{self.id}] - 类型: {self.type.name}, "
            f"当前值: {current_value:.1f}, 阈值: {self.value:.1f}, 级别: {self.level.name}"
        )
        
        return True
    
    def _get_current_value(self, memory_info: Dict) -> float:
        """
        从内存信息中获取当前值
        
        Args:
            memory_info: 内存信息字典
            
        Returns:
            当前值
        """
        if self.type == ThresholdType.SYSTEM_PERCENT:
            return memory_info['system']['percent']
        
        elif self.type == ThresholdType.SYSTEM_AVAILABLE_MB:
            return memory_info['system']['available'] / (1024 * 1024)
        
        elif self.type == ThresholdType.PROCESS_PERCENT:
            return memory_info['process']['percent']
        
        elif self.type == ThresholdType.PROCESS_RSS_MB:
            return memory_info['process']['rss'] / (1024 * 1024)
        
        elif self.type == ThresholdType.GROWTH_RATE_MB_MIN:
            trend = memory_info.get('trend', {})
            if trend.get('status') == 'ok':
                return trend.get('growth_rate_mb_min', 0)
            return 0
        
        return 0


class ThresholdManager:
    """
    阈值管理器类
    
    管理多个内存阈值，检查和触发阈值回调
    """
    
    def __init__(self):
        """初始化阈值管理器"""
        # 阈值字典，键为阈值ID
        self._thresholds: Dict[str, Threshold] = {}
        
        # 默认阈值配置
        self._default_thresholds = [
            # 系统内存使用率超过80%
            {
                'id': 'system_memory_high',
                'type': ThresholdType.SYSTEM_PERCENT,
                'value': 80.0,
                'level': ThresholdLevel.WARNING,
                'direction': ThresholdDirection.ABOVE,
                'cooldown_seconds': 60
            },
            # 系统内存使用率超过90%
            {
                'id': 'system_memory_critical',
                'type': ThresholdType.SYSTEM_PERCENT,
                'value': 90.0,
                'level': ThresholdLevel.CRITICAL,
                'direction': ThresholdDirection.ABOVE,
                'cooldown_seconds': 30
            },
            # 系统可用内存低于500MB
            {
                'id': 'system_memory_low',
                'type': ThresholdType.SYSTEM_AVAILABLE_MB,
                'value': 500.0,
                'level': ThresholdLevel.EMERGENCY,
                'direction': ThresholdDirection.BELOW,
                'cooldown_seconds': 15
            },
            # 进程内存增长率超过50MB/分钟
            {
                'id': 'process_growth_high',
                'type': ThresholdType.GROWTH_RATE_MB_MIN,
                'value': 50.0,
                'level': ThresholdLevel.WARNING,
                'direction': ThresholdDirection.ABOVE,
                'cooldown_seconds': 120
            }
        ]
        
        logger.debug("阈值管理器初始化完成")
    
    def add_threshold(self, threshold: Threshold) -> bool:
        """
        添加阈值
        
        Args:
            threshold: 阈值对象
            
        Returns:
            是否成功添加
        """
        if threshold.id in self._thresholds:
            logger.warning(f"阈值ID已存在: {threshold.id}")
            return False
        
        self._thresholds[threshold.id] = threshold
        logger.debug(
            f"已添加阈值 [{threshold.id}] - 类型: {threshold.type.name}, "
            f"值: {threshold.value}, 级别: {threshold.level.name}"
        )
        return True
    
    def remove_threshold(self, threshold_id: str) -> bool:
        """
        移除阈值
        
        Args:
            threshold_id: 阈值ID
            
        Returns:
            是否成功移除
        """
        if threshold_id not in self._thresholds:
            logger.warning(f"阈值ID不存在: {threshold_id}")
            return False
        
        # 移除阈值
        del self._thresholds[threshold_id]
        logger.debug(f"已移除阈值 [{threshold_id}]")
        return True
    
    def get_threshold(self, threshold_id: str) -> Optional[Threshold]:
        """
        获取阈值
        
        Args:
            threshold_id: 阈值ID
            
        Returns:
            阈值对象，不存在则返回None
        """
        return self._thresholds.get(threshold_id)
    
    def get_all_thresholds(self) -> List[Threshold]:
        """
        获取所有阈值
        
        Returns:
            阈值列表
        """
        return list(self._thresholds.values())
    
    def check_thresholds(self, memory_info: Dict) -> List[str]:
        """
        检查所有阈值
        
        Args:
            memory_info: 内存信息字典
            
        Returns:
            触发的阈值ID列表
        """
        triggered_ids = []
        
        for threshold_id, threshold in self._thresholds.items():
            if threshold.trigger(memory_info):
                triggered_ids.append(threshold_id)
        
        return triggered_ids
    
    def add_default_thresholds(
        self, 
        callback: Optional[Callable[[Dict], None]] = None
    ) -> List[str]:
        """
        添加默认阈值
        
        Args:
            callback: 触发回调函数，接收内存信息字典作为参数
            
        Returns:
            添加的阈值ID列表
        """
        added_ids = []
        
        for config in self._default_thresholds:
            threshold = Threshold(
                threshold_id=config['id'],
                threshold_type=config['type'],
                value=config['value'],
                level=config['level'],
                direction=config['direction'],
                callback=callback,
                cooldown_seconds=config['cooldown_seconds']
            )
            
            if self.add_threshold(threshold):
                added_ids.append(threshold.id)
        
        return added_ids
    
    def clear_thresholds(self) -> int:
        """
        清除所有阈值
        
        Returns:
            清除的阈值数量
        """
        count = len(self._thresholds)
        self._thresholds.clear()
        logger.debug(f"已清除所有阈值 ({count}个)")
        return count
    
    def update_threshold(
        self,
        threshold_id: str,
        value: Optional[float] = None,
        level: Optional[ThresholdLevel] = None,
        direction: Optional[ThresholdDirection] = None,
        callback: Optional[Callable[[Dict], None]] = None,
        cooldown_seconds: Optional[int] = None
    ) -> bool:
        """
        更新阈值
        
        Args:
            threshold_id: 阈值ID
            value: 新的阈值数值，None表示不更新
            level: 新的阈值级别，None表示不更新
            direction: 新的阈值方向，None表示不更新
            callback: 新的触发回调函数，None表示不更新
            cooldown_seconds: 新的冷却时间，None表示不更新
            
        Returns:
            是否成功更新
        """
        threshold = self.get_threshold(threshold_id)
        if not threshold:
            logger.warning(f"阈值ID不存在: {threshold_id}")
            return False
        
        # 更新属性
        if value is not None:
            threshold.value = value
        
        if level is not None:
            threshold.level = level
        
        if direction is not None:
            threshold.direction = direction
        
        if callback is not None:
            threshold.callback = callback
        
        if cooldown_seconds is not None:
            threshold.cooldown_seconds = cooldown_seconds
        
        logger.debug(f"已更新阈值 [{threshold_id}]")
        return True
    
    def create_threshold(
        self,
        threshold_id: str,
        threshold_type: ThresholdType,
        value: float,
        level: ThresholdLevel = ThresholdLevel.WARNING,
        direction: ThresholdDirection = ThresholdDirection.ABOVE,
        callback: Optional[Callable[[Dict], None]] = None,
        cooldown_seconds: int = 60
    ) -> bool:
        """
        创建并添加阈值
        
        Args:
            threshold_id: 阈值唯一标识符
            threshold_type: 阈值类型
            value: 阈值数值
            level: 阈值级别，默认为警告级别
            direction: 阈值方向，默认为高于阈值触发
            callback: 触发回调函数，接收内存信息字典作为参数
            cooldown_seconds: 冷却时间(秒)，防止频繁触发，默认60秒
            
        Returns:
            是否成功创建
        """
        threshold = Threshold(
            threshold_id=threshold_id,
            threshold_type=threshold_type,
            value=value,
            level=level,
            direction=direction,
            callback=callback,
            cooldown_seconds=cooldown_seconds
        )
        
        return self.add_threshold(threshold)
    
    def get_triggered_thresholds(self, memory_info: Dict) -> List[Threshold]:
        """
        获取当前触发的阈值列表
        
        Args:
            memory_info: 内存信息字典
            
        Returns:
            触发的阈值列表
        """
        return [
            threshold for threshold in self._thresholds.values()
            if threshold.check(memory_info)
        ]
    
    def get_thresholds_by_level(self, level: ThresholdLevel) -> List[Threshold]:
        """
        获取指定级别的阈值列表
        
        Args:
            level: 阈值级别
            
        Returns:
            指定级别的阈值列表
        """
        return [
            threshold for threshold in self._thresholds.values()
            if threshold.level == level
        ]
    
    def get_thresholds_by_type(self, threshold_type: ThresholdType) -> List[Threshold]:
        """
        获取指定类型的阈值列表
        
        Args:
            threshold_type: 阈值类型
            
        Returns:
            指定类型的阈值列表
        """
        return [
            threshold for threshold in self._thresholds.values()
            if threshold.type == threshold_type
        ] 