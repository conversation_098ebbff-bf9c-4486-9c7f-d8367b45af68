---
description: 
globs: "*"
alwaysApply: true
---
# AI助手命令调用规则 - 量化交易系统

本规则文档为AI助手提供了关于如何调用量化交易系统(Quant)项目中各模块命令行工具的详细指南，包括命令行接口、参数选择和错误处理策略等。

## 一般原则

在处理用户关于量化交易系统的请求时，AI助手应遵循以下原则：

1. **首选命令行工具**：优先使用模块的命令行工具而非直接调用Python代码。
2. **选择最简洁路径**：使用能够实现用户需求的最直接命令。
3. **参数精确性**：提供精确而完整的参数，避免产生交互式提示。
4. **错误处理**：监控命令执行结果，遇到错误时尝试备选方案或提供明确解释。
5. **数据路径管理**：注意数据文件的存储位置，遵循项目的数据存储规范。
6. **解释意图**：在执行命令前简要说明将要执行的操作及其预期结果。
7. **外部数据存储**：严格遵循将数据存储在项目外部的设计原则。

## 统一可安装包

**Quant** 项目提供了简洁、标准化的命令行工具。AI助手在调用项目功能时应使用这些统一的命令行工具。

### 安装方法

```bash
# 在项目根目录下安装
cd /d:/quant
pip install -e .

# 或从特定路径安装
pip install -e /d:/quant
```

### 统一命令行工具

安装后，以下命令将全局可用：

- **`quant-data`** - 数据获取与管理工具
- **`quant-backtest`** - 回测系统工具
- **`quant-strategy`** - 策略管理工具
- **`quant-trade`** - 交易系统工具
- **`quant-risk`** - 风险控制工具
- **`quant-monitor`** - 系统监控工具
- **`quant-config`** - 配置管理工具
- **`quant-main`** - 系统主入口

### 统一选项

所有命令都支持以下通用选项：

- `--help` / `-h` - 显示帮助信息（包含所有可用的子命令和选项）
- `--non-interactive` - 启用非交互式模式（不提示确认，AI助手调用时应始终使用此选项）
- `--output-format` - 指定输出格式，可选 `text`（默认）或 `json`（推荐AI调用时使用）

### 使用JSON输出格式

为了便于AI助手解析命令输出，建议在调用命令时始终使用 `--output-format json` 选项。JSON输出将包含以下标准字段：

- `status` - 状态（"success", "error", "warning"）
- `command` 或 `action_taken` - 执行的命令或操作
- `message` - 人类可读的消息
- `details` - 包含详细信息的对象（如有）

### 命令行工具详细说明

#### 1. quant-data (数据获取与管理工具)

**基本用法**：
```bash
quant-data [选项]
```

**常用选项**：
- `--interactive` / `-i` - 启动交互式界面
- `--non-interactive` - 非交互式模式
- `--output-format` - 输出格式（text或json）

**示例命令**：
```bash
# 启动交互式数据管理界面
quant-data --interactive

# 非交互式模式，使用JSON输出
quant-data --non-interactive --output-format json
```

#### 2. quant-backtest (回测系统工具)

**基本用法**：
```bash
quant-backtest [选项]
```

**常用选项**：
- `--strategy` - 策略名称
- `--symbols` - 交易品种代码，多个用逗号分隔
- `--start-date` - 回测开始日期 (YYYYMMDD)
- `--end-date` - 回测结束日期 (YYYYMMDD)
- `--plot` - 显示图表
- `--output` - 输出结果到指定路径
- `--non-interactive` - 非交互式模式
- `--output-format` - 输出格式（text或json）

**示例命令**：
```bash
# 运行特定策略的回测
quant-backtest --strategy ma_cross --symbols 000001.SZ,600000.SH --start-date ******** --end-date ******** --non-interactive

# 运行回测并生成图表
quant-backtest --strategy ma_cross --symbols 000001.SZ --start-date ******** --end-date ******** --plot --non-interactive

# JSON输出模式
quant-backtest --strategy ma_cross --symbols 000001.SZ --start-date ******** --end-date ******** --non-interactive --output-format json
```

#### 3. quant-strategy (策略管理工具)

**基本用法**：
```bash
quant-strategy [选项]
```

**常用选项**：
- `--list` - 列出可用策略
- `--create` - 创建新策略
- `--edit` - 编辑策略
- `--optimize` - 优化策略参数
- `--non-interactive` - 非交互式模式
- `--output-format` - 输出格式（text或json）

**示例命令**：
```bash
# 列出所有可用策略
quant-strategy --list --non-interactive

# 创建新策略
quant-strategy --create my_strategy --non-interactive

# 优化策略参数
quant-strategy --optimize ma_cross --non-interactive

# JSON输出模式
quant-strategy --list --non-interactive --output-format json
```

#### 4. quant-trade (交易系统工具)

**基本用法**：
```bash
quant-trade [选项]
```

**常用选项**：
- `--account` - 账户名称
- `--order` - 执行订单
- `--status` - 查看交易状态
- `--position` - 查看持仓
- `--non-interactive` - 非交互式模式
- `--output-format` - 输出格式（text或json）

**示例命令**：
```bash
# 查看账户状态
quant-trade --account main --status --non-interactive

# 查看持仓信息
quant-trade --account main --position --non-interactive

# JSON输出模式
quant-trade --account main --status --non-interactive --output-format json
```

#### 5. quant-risk (风险控制工具)

**基本用法**：
```bash
quant-risk [选项]
```

**常用选项**：
- `--check` - 检查风险
- `--limits` - 设置风险限制
- `--report` - 生成风险报告
- `--non-interactive` - 非交互式模式
- `--output-format` - 输出格式（text或json）

**示例命令**：
```bash
# 检查当前风险状况
quant-risk --check --non-interactive

# 生成风险报告
quant-risk --report --non-interactive

# JSON输出模式
quant-risk --check --non-interactive --output-format json
```

#### 6. quant-monitor (系统监控工具)

**基本用法**：
```bash
quant-monitor [选项]
```

**常用选项**：
- `--system` - 监控系统状态
- `--performance` - 监控性能
- `--alert` - 配置告警
- `--non-interactive` - 非交互式模式
- `--output-format` - 输出格式（text或json）

**示例命令**：
```bash
# 监控系统状态
quant-monitor --system --non-interactive

# 监控性能
quant-monitor --performance --non-interactive

# JSON输出模式
quant-monitor --system --non-interactive --output-format json
```

#### 7. quant-config (配置管理工具)

**基本用法**：
```bash
quant-config [选项]
```

**常用选项**：
- `--get` - 获取配置项
- `--set` - 设置配置项
- `--list` - 列出所有配置
- `--reset` - 重置配置
- `--non-interactive` - 非交互式模式
- `--output-format` - 输出格式（text或json）

**示例命令**：
```bash
# 列出所有配置
quant-config --list --non-interactive

# 获取特定配置项
quant-config --get data.save_dir --non-interactive

# 设置配置项
quant-config --set data.save_dir=D:\\data --non-interactive

# JSON输出模式
quant-config --list --non-interactive --output-format json
```

#### 8. quant-main (系统主入口)

**基本用法**：
```bash
quant-main
```

无特殊选项，将启动交互式主菜单。

## 数据模块特殊说明

在数据模块中有一些特殊的使用说明，AI助手在辅助用户时应格外注意：

### 1. 数据存储路径

量化交易系统严格遵循将数据存储在项目外部的设计原则。数据文件通常使用以下标准路径格式：

```
<数据根目录>/<交易所>/<股票代码>/<周期>.parquet

例如：
D:\\data\\SZ\\000001\\tick.parquet
D:\\data\\SH\\600000\\1d.parquet
```

AI助手在建议数据保存位置时，应始终遵循此规范，并提醒用户不要将数据文件存储在项目目录内。

### 2. 使用迅投接口

当使用迅投量化平台接口(xtquant)下载数据时，AI助手应注意：

- 始终使用`xtquant.xtdata`模块，导入时使用`from xtquant import xtdata as xt_data`格式
- 数据下载优先使用`download_history_data2`函数
- 禁用欢迎信息：`xt_data.enable_hello = False`
- 数据获取后应使用`get_local_data`函数读取已下载的数据
- 确保数据保存到项目外部路径

```python
from xtquant import xtdata as xt_data
xt_data.enable_hello = False

# 下载数据示例
xt_data.download_history_data2(
    stock_list=["600000.SH"],
    period="1d",
    start_time="********",
    end_time="********",
    save_dir="D:\\\\data"  # 注意：保存到项目外部
)

# 读取数据示例
data = xt_data.get_local_data(
    field_list=["open", "high", "low", "close", "volume"],
    stock_list=["600000.SH"],
    period="1d",
    start_time="********",
    end_time="********"
)
```

## AI助手决策指南

### 命令选择优先级

统一命令行工具是推荐的使用方式，应始终优先使用 `quant-*` 命令。

### 非交互式优先

AI助手应始终在调用命令时使用 `--non-interactive` 选项，避免需要用户手动输入。

### JSON输出优先

AI助手应尽可能在调用命令时指定 `--output-format json`（如果支持），以便更可靠地解析命令输出和错误信息。

### 何时使用命令行工具

AI助手应在以下情况主动使用模块的命令行工具：

1. **识别任务匹配**：当用户的问题、任务或需求与某个模块的功能相关时，即使用户没有明确请求使用该模块。
2. **自动化任务**：当AI识别到用户需求可以通过某个模块的命令行工具高效完成时。
3. **标准化操作**：当用户描述的操作有明确的标准化流程，可以通过命令行工具执行时。
4. **批处理**：当用户需要处理多个文件或大量数据时，应主动推荐和使用适当的命令行工具。
5. **系统集成**：当用户需要多个功能协同工作，可以通过命令行工具实现自动化集成时。

### 命令选择决策树

当需要选择合适的命令时，AI助手应：

1. **识别需求领域**：分析用户请求，确定属于哪个功能领域(数据管理、策略管理、回测系统、交易系统、风险控制等)。
2. **确定操作类型**：
   - 是简单查询还是复杂操作？
   - 是否需要交互？
   - 是否处理大量数据？
3. **自主选择最佳工具**：
   - 对于数据获取和处理需求，使用 `quant-data` 相关命令
   - 对于策略管理需求，使用 `quant-strategy` 相关命令
   - 对于回测需求，使用 `quant-backtest` 相关命令
   - 对于交易执行需求，使用 `quant-trade` 相关命令
   - 对于风险管理需求，使用 `quant-risk` 相关命令
   - 对于系统监控需求，使用 `quant-monitor` 相关命令
   - 对于配置管理需求，使用 `quant-config` 相关命令
4. **参数优化**：选择最能满足用户需求的参数组合，提供完整准确的命令。

### 错误处理策略

当命令执行失败时，AI助手应：

1. **分析错误**：根据退出码和错误消息分析问题。
2. **重试策略**：
   - 如果是简单错误（如文件路径问题）→ 修正参数并重试
   - 如果是环境问题（如依赖缺失）→ 先解决环境问题再重试
   - 如果是权限问题 → 建议用户以适当权限运行
3. **反馈与解释**：向用户解释错误原因和采取的措施。

### 示例情境与推荐命令

1. **用户需要下载历史行情数据**：
   ```bash
   quant-data --interactive
   ```
   然后指导用户在交互式界面中选择数据下载功能。

2. **用户需要运行策略回测**：
   ```bash
   quant-backtest --strategy ma_cross --symbols 000001.SZ --start-date ******** --end-date ******** --plot --non-interactive
   ```

3. **用户需要查看可用策略列表**：
   ```bash
   quant-strategy --list --non-interactive --output-format json
   ```

4. **用户需要检查账户持仓**：
   ```bash
   quant-trade --account main --position --non-interactive --output-format json
   ```

## 总结

通过遵循本规则文档，AI助手能够:
1. 有效地选择和调用合适的命令行工具，使用统一可安装包提供的 `quant-*` 命令
2. 提供精确而完整的参数，使用非交互式模式和JSON输出
3. 处理可能出现的错误，并提供明确解释
4. 为用户提供最佳的自动化体验
5. 正确管理数据文件的存储位置

在实际应用中，AI助手应优先考虑用户的具体需求和上下文，灵活调整命令参数和策略，确保命令行工具的使用既有效又符合项目标准。





