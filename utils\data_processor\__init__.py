#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据处理工具模块

提供数据清洗、转换、验证和准备的功能，包括：
1. 数据清洗与预处理
2. 异常值检测与处理
3. 数据转换与特征工程
4. 数据验证与质量评估
5. 技术指标计算
6. 数据准备与划分
7. 数据合并与增量更新
8. K线周期转换（新增）
"""

from utils.data_processor.data_merger import (
    get_data_time_range,
    calculate_incremental_start_time,
    merge_dataframes,
    validate_merged_data
)
from utils.data_processor.preparation import (
    ensure_datetime_index,
    resample_data,
    split_train_test,
    create_time_features,
    prepare_data_for_ml
)
from utils.data_processor.technical import (
    add_moving_average,
    add_exponential_moving_average,
    add_macd,
    add_rsi,
    add_bollinger_bands,
    add_stochastic_oscillator,
    add_obv,
    add_atr
)
from utils.data_processor.validation import (
    detect_data_issues,
    data_quality_score,
    check_column_values
)
from utils.data_processor.transformation import (
    normalize_data,
    apply_normalization,
    add_lagged_features,
    add_rolling_features,
    add_expanding_features,
    add_ewm_features,
    add_diff_features,
    add_pct_change_features,
    encode_categorical
)
from utils.data_processor.outliers import (
    detect_outliers_zscore,
    detect_outliers_iqr,
    detect_outliers_percentile,
    detect_outliers_custom,
    remove_outliers,
    winsorize
)
from utils.data_processor.cleaning import (
    convert_dtypes,
    drop_duplicates,
    standardize_column_names,
    fill_missing_values
)
from utils.data_processor.tick_time_filter import (
    filter_tick_data_for_storage,
    filter_kline_data_for_storage,
    should_apply_time_filter,
    is_tick_data_period,
    is_kline_data_period,
    get_filter_statistics
)
# 导入新增的周期转换模块
from utils.data_processor.period_converter import (
    convert_kline_period,
    resample_1m_kline,
    validate_period_string,
    is_supported_by_xtquant,
    parse_period_to_minutes,
    get_supported_periods,
    get_recommended_base_period
)
# 从周期转换模块导出函数
from .period_converter import (
    validate_period_string,
    parse_period_to_minutes,
    get_recommended_base_period,
    resample_1m_kline,
    convert_kline_period,
)

# 从周期支持模块导出函数和上下文
from .period_support import (
    is_period_supported,
    is_supported_by_xtquant,
    get_supported_periods,
    period_support_context,
    PeriodSupportContext,
)

# 从周期处理模块导出函数
from .period_handler import (
    is_valid_period,
    is_custom_period,
    get_native_periods,
    convert_period_to_days,
    get_synthesis_strategy,
    synthesize_period_data,
    synthesize_from_local_data,
    extract_timestamp_from_data
)

import os
import sys

# 将项目根目录添加到Python路径
root_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, root_path)

# 导入各个子模块的功能


# 导入数据合并模块

# 导出所有公共接口
__all__ = [
    # 数据清洗
    'convert_dtypes',
    'drop_duplicates',
    'standardize_column_names',
    'fill_missing_values',

    # 异常值处理
    'detect_outliers_zscore',
    'detect_outliers_iqr',
    'detect_outliers_percentile',
    'detect_outliers_custom',
    'remove_outliers',
    'winsorize',

    # 数据转换
    'normalize_data',
    'apply_normalization',
    'add_lagged_features',
    'add_rolling_features',
    'add_expanding_features',
    'add_ewm_features',
    'add_diff_features',
    'add_pct_change_features',
    'encode_categorical',

    # 数据验证
    'detect_data_issues',
    'data_quality_score',
    'check_column_values',

    # 技术指标
    'add_moving_average',
    'add_exponential_moving_average',
    'add_macd',
    'add_rsi',
    'add_bollinger_bands',
    'add_stochastic_oscillator',
    'add_obv',
    'add_atr',

    # 数据准备
    'ensure_datetime_index',
    'resample_data',
    'split_train_test',
    'create_time_features',
    'prepare_data_for_ml',

    # 数据合并与增量更新
    'get_data_time_range',
    'calculate_incremental_start_time',
    'merge_dataframes',
    'validate_merged_data',

    # K线周期转换（新增）
    'convert_kline_period',
    'resample_1m_kline',
    'validate_period_string',
    'is_supported_by_xtquant',
    'parse_period_to_minutes',
    'get_supported_periods',
    'get_recommended_base_period',

    # 周期支持判断函数和类
    'is_period_supported',
    'period_support_context',
    'PeriodSupportContext',
    
    # 周期处理函数
    'is_valid_period',
    'is_custom_period',
    'get_native_periods',
    'convert_period_to_days',
    'get_synthesis_strategy',
    'synthesize_period_data',
    'synthesize_from_local_data',
    'extract_timestamp_from_data',
]
