#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完全重构系统综合测试

验证整个时间戳处理重构系统的功能完整性和正确性。
"""

import pandas as pd
import numpy as np
from datetime import datetime
import sys
import os
import tempfile
import shutil

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.smart_timestamp_processor import (
    SmartTimestampProcessor, 
    get_smart_timestamp_processor,
    extract_partition_timestamp,
    analyze_data_time_range
)
from utils.path_manager import build_partitioned_path
from data.storage.parquet_storage import save_to_partition, save_data_by_partition


def test_end_to_end_workflow():
    """测试端到端工作流程"""
    print("=== 测试端到端工作流程 ===")
    
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 模拟复权数据处理场景
        adjusted_data = pd.DataFrame({
            'time': [1752562262000, 1752562265000, 1752562268000, 1752629698000],  # 跨越7月15-16日
            'lastPrice': [13.53, 13.54, 13.55, 13.45],
            'open': [13.74, 13.74, 13.74, 13.54],
            'high': [13.79, 13.79, 13.79, 13.60],
            'low': [13.43, 13.43, 13.43, 13.45],
            'volume': [100, 200, 300, 400]
        })
        
        print(f"测试数据时间范围: {adjusted_data['time'].min()} - {adjusted_data['time'].max()}")
        
        # 测试复权数据保存（模拟data/批量合成复权数据.py的调用）
        success = save_to_partition(
            df=adjusted_data,
            data_root=temp_dir,
            symbol="600000.SH",
            period="tick",
            timestamp=None,  # 不传递timestamp，让系统自动提取
            data_type="adjusted",
            adj_type="front"
        )
        
        if success:
            print("✅ 复权数据保存成功")
            
            # 验证文件保存位置
            expected_path = os.path.join(temp_dir, "adjusted", "front", "SH", "600000", "tick", "2025", "07", "15.parquet")
            
            if os.path.exists(expected_path):
                print(f"✅ 文件保存到正确位置: {expected_path}")
                
                # 验证保存的数据
                saved_data = pd.read_parquet(expected_path)
                print(f"保存的数据行数: {len(saved_data)}")
                
                if len(saved_data) == len(adjusted_data):
                    print("✅ 数据完整性验证通过")
                    return True
                else:
                    print("❌ 数据完整性验证失败")
                    return False
            else:
                print(f"❌ 文件未保存到预期位置")
                return False
        else:
            print("❌ 复权数据保存失败")
            return False
            
    except Exception as e:
        print(f"❌ 端到端测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)


def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n=== 测试向后兼容性 ===")
    
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 创建测试数据
        test_data = pd.DataFrame({
            'time': [1752562262000, 1752562265000],
            'price': [13.53, 13.54]
        })
        
        # 测试传统的timestamp参数方式（向后兼容）
        success = save_to_partition(
            df=test_data,
            data_root=temp_dir,
            symbol="600000.SH",
            period="tick",
            timestamp="20250805",  # 明确指定timestamp
            data_type="raw"
        )
        
        if success:
            # 验证使用了指定的timestamp
            expected_path = os.path.join(temp_dir, "raw", "SH", "600000", "tick", "2025", "08", "05.parquet")
            
            if os.path.exists(expected_path):
                print("✅ 向后兼容性测试通过")
                return True
            else:
                print("❌ 向后兼容性测试失败")
                return False
        else:
            print("❌ 向后兼容性保存失败")
            return False
            
    except Exception as e:
        print(f"❌ 向后兼容性测试失败: {e}")
        return False
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)


def test_performance_comparison():
    """测试性能对比"""
    print("\n=== 测试性能对比 ===")
    
    try:
        import time
        
        # 创建较大的测试数据
        large_data = pd.DataFrame({
            'time': np.arange(1752562262000, 1752562262000 + 10000 * 3000, 3000),  # 10000条数据
            'price': np.random.uniform(13.0, 14.0, 10000),
            'volume': np.random.randint(100, 1000, 10000)
        })
        
        print(f"测试数据大小: {len(large_data)} 行")
        
        # 测试智能时间戳提取性能
        start_time = time.time()
        timestamp = extract_partition_timestamp(large_data, "tick")
        extraction_time = time.time() - start_time
        
        print(f"时间戳提取耗时: {extraction_time:.4f} 秒")
        print(f"提取的时间戳: {timestamp}")
        
        if extraction_time < 1.0:  # 应该在1秒内完成
            print("✅ 性能测试通过")
            return True
        else:
            print("❌ 性能测试失败，耗时过长")
            return False
            
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False


def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    try:
        # 测试空数据处理
        empty_data = pd.DataFrame()
        timestamp = extract_partition_timestamp(empty_data, "tick", "20250805")
        
        if timestamp == "20250805":
            print("✅ 空数据处理正确")
        else:
            print("❌ 空数据处理失败")
            return False
        
        # 测试无时间列数据处理
        no_time_data = pd.DataFrame({
            'price': [13.53, 13.54],
            'volume': [100, 200]
        })
        
        timestamp = extract_partition_timestamp(no_time_data, "tick", "20250805")
        
        if timestamp == "20250805":
            print("✅ 无时间列数据处理正确")
        else:
            print("❌ 无时间列数据处理失败")
            return False
        
        # 测试异常数据处理
        bad_time_data = pd.DataFrame({
            'time': ['invalid', 'data'],
            'price': [13.53, 13.54]
        })
        
        timestamp = extract_partition_timestamp(bad_time_data, "tick", "20250805")
        
        if timestamp == "20250805":
            print("✅ 异常数据处理正确")
            return True
        else:
            print("❌ 异常数据处理失败")
            return False
            
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False


def test_integration_with_existing_systems():
    """测试与现有系统的集成"""
    print("\n=== 测试与现有系统的集成 ===")
    
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 创建测试数据
        test_data = pd.DataFrame({
            'time': [1752562262000, 1752562265000, 1752562268000],
            'lastPrice': [13.53, 13.54, 13.55],
            'volume': [100, 200, 300]
        })
        
        # 测试save_data_by_partition函数（集成智能时间戳处理器）
        results = save_data_by_partition(
            df=test_data,
            data_root=temp_dir,
            symbol="600000.SH",
            period="tick",
            data_type="adjusted",
            adj_type="front"
        )
        
        if len(results) > 0:
            print(f"✅ save_data_by_partition集成成功，保存了 {len(results)} 个分区")
            
            # 验证保存的文件
            for date_str, path in results.items():
                print(f"分区 {date_str}: {path}")
                
            return True
        else:
            print("❌ save_data_by_partition集成失败")
            return False
            
    except Exception as e:
        print(f"❌ 系统集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)


def run_comprehensive_tests():
    """运行综合测试"""
    print("开始完全重构系统综合测试...")
    
    tests = [
        test_end_to_end_workflow,
        test_backward_compatibility,
        test_performance_comparison,
        test_error_handling,
        test_integration_with_existing_systems
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试执行失败: {e}")
    
    print(f"\n=== 综合测试结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 完全重构系统综合测试全部通过！")
        print("✅ 时间戳处理重构成功完成")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步优化")
        return False


if __name__ == "__main__":
    run_comprehensive_tests()
