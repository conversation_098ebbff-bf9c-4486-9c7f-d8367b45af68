常见问题 | 迅投知识库


[![迅投知识库](/images/logo.png)迅投知识库](/)

[首页](/)

APIAPI

* [投研新手教程](/freshman/ty_rookie.html)
* [QMT新手教程](/freshman/rookie.html)
* [内置Python](/innerApi/start_now.html)
* [XtQuant文档](/nativeApi/start_now.html)
* [VBA](/VBA/start_now.html)

数据字典数据字典

* [快速开始](/dictionary/)
* [股票数据](/dictionary/stock.html)
* [行业概念数据](/dictionary/industry.html)
* [指数数据](/dictionary/indexes.html)
* [期货数据](/dictionary/future.html)
* [期权数据](/dictionary/option.html)
* [场内基金数据](/dictionary/floorfunds.html)
* [债券数据](/dictionary/bond.html)
* [常见问题](/dictionary/question_answer.html)
* [场景化示例](/dictionary/scenario_based_example.html)
* [迅投因子](/dictionary/xuntou_factor.html)

策略迁移策略迁移

* [聚宽策略](/strategy/JoinQuant2QMT.html)
* #### 

  + [客服QQ: 810315303](/nativeApi/question_function.html)
  + [联系方式: 18309226715](/nativeApi/question_function.html)

视频教程视频教程

* [投研端教程](/videos/touyan/ty_register_install.html)

投研平台 在新窗口打开

迅投社区 在新窗口打开

[迅投官网 在新窗口打开](http://www.thinktrader.net)

注册

登录

![微信扫码联系客服](/assets/wechat-d90fd08f.png "点击联系客服")

![分享链接](data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAHqADAAQAAAABAAAAHgAAAADKQTcFAAADjElEQVRIDcVXW0hVQRRdM/fce/OVpfRA8dENDckkMILsYRG9PnqQQUkg9NFfBCFEJJSFRj8R+BP4URREGEVGRNSXWEiE1odoDx+lhkoWpTe1+zrT7KPnes59ddQbDujM7D17rbNn9uzZl8FCqxaC36l1l4iAekgIFDOwDEDIP2psUEAMMoY2ZuONFVUpLdWMqVO66P9ZdBWw/ZZY9GXAfZqpolKCL4+1VtfJj/omOLuWm5VS13SC/dHloX1UYtcld5lA4Lr0MCvUyMpc7sAAg+1M78WUh5HW81ChEIKtqh6rVUXgwVxJCZNsCYOwCDOUxySgBa7LY/dkfzR04XzmjLGG3guLy2UvdByTx3J7a+JNSkSESdg6KfVBj+lMaWuMyniPObMd0c9c85iilwIpHkSZqQyudNNGBmGJg7hIoK2gKzOfQKJt27xawc41dtytSELesijEMuCISyOm5ED3lCazbXaJv6fAjvrjyShcaUPlDidy0mzoHI6eP4hL43TVjG1R/erL2ZAm2IF9ax0oW+9EWiLH0w4PSl02bMhW4PYIFF0diwnHFb5VoTQYc5VBmZrAcLDIgf2FTiQ7p+LyxQcvijO5RkpLO4cDBovIQ+JU5NkWR1bPSFekMByW3u0tcMChBC8Cmrq8yF0iU2ue3ILpZolYckoYliHzsG5n6rOWchwrdqJUAttkDjS2ll4fkuwCB9Y5jWJLHhOnMvPKmOy1yfndichNt4Up2vp9mPAEcGqbdjNM+o6hf281cUaO+2mo2ucTaB/ym4DbB/34/MMfkdQXEOgeiR7RQSAGIYnZYFAQMvj6S8XZR+Ooa5rAuFfg/bAfrX1eVO0K95RMuySpzwIvBBtS6BGXNvkhnKbps04fmrt92CivS315ImSyN+n1iZXAorXEyaly0A1j9eNeYJNLgcIjk5KtVWKJ0CrzNm+MRWjUvekP4KPcztHJyLfAMrHCH3OqkahcMRLEGguZ3uuaPWh466XnzrTUCjFxESenwoxqJBNClEnPSAA3Xk3i5msPzj2ZRPntcfR8n7o+Az9VmS6jGBrExEWc2oHRU9XXP/ppLi+UQ17zkyVOjPxWcf+dz0ARPqQ6LCc7NZ+KwGCkLEghQN9GlQEDvxL+nfGRELZefRBi0GOayGBZmGKPqkCtGoyj55qnIRVmmMck0Bud+f8s6E1brZPq/YL8hNHJqacaKd4/2v4CgdaZJ2zGqYAAAAAASUVORK5CYII= "分享链接")

[首页](/)

APIAPI

* [投研新手教程](/freshman/ty_rookie.html)
* [QMT新手教程](/freshman/rookie.html)
* [内置Python](/innerApi/start_now.html)
* [XtQuant文档](/nativeApi/start_now.html)
* [VBA](/VBA/start_now.html)

数据字典数据字典

* [快速开始](/dictionary/)
* [股票数据](/dictionary/stock.html)
* [行业概念数据](/dictionary/industry.html)
* [指数数据](/dictionary/indexes.html)
* [期货数据](/dictionary/future.html)
* [期权数据](/dictionary/option.html)
* [场内基金数据](/dictionary/floorfunds.html)
* [债券数据](/dictionary/bond.html)
* [常见问题](/dictionary/question_answer.html)
* [场景化示例](/dictionary/scenario_based_example.html)
* [迅投因子](/dictionary/xuntou_factor.html)

策略迁移策略迁移

* [聚宽策略](/strategy/JoinQuant2QMT.html)
* #### 

  + [客服QQ: 810315303](/nativeApi/question_function.html)
  + [联系方式: 18309226715](/nativeApi/question_function.html)

视频教程视频教程

* [投研端教程](/videos/touyan/ty_register_install.html)

投研平台 在新窗口打开

迅投社区 在新窗口打开

[迅投官网 在新窗口打开](http://www.thinktrader.net)

* xtquant文档

  + [快速开始](/nativeApi/start_now.html)
  + [XtQuant.XtData 行情模块](/nativeApi/xtdata.html)
  + [XtQuant.Xttrade 交易模块](/nativeApi/xttrader.html)
  + [完整实例](/nativeApi/code_examples.html)
  + [常见问题](/nativeApi/question_function.html) 
    - [导入xtquant库时提示 NO module named 'xtquant.IPythonAPiClient'](/nativeApi/question_function.html#导入xtquant库时提示-no-module-named-xtquant-ipythonapiclient)
    - [连接 xtquant 时失败，返回-1及解决方法](/nativeApi/question_function.html#连接-xtquant-时失败-返回-1及解决方法)
    - [执行xtdatacenter.init时提示监听58609端口失败](/nativeApi/question_function.html#执行xtdatacenter-init时提示监听58609端口失败)
    - [下单后，查询委托的投资备注只有前半部分](/nativeApi/question_function.html#下单后-查询委托的投资备注只有前半部分)
    - [userdata\_mini目录下，生成大量down\_queue文件](/nativeApi/question_function.html#userdata-mini目录下-生成大量down-queue文件)
  + [xtquant版本下载](/nativeApi/download_xtquant.html)

### [#](#导入xtquant库时提示-no-module-named-xtquant-ipythonapiclient) 导入xtquant库时提示 `NO module named 'xtquant.IPythonAPiClient'`

1、目前xtquant支持的python版本为 64位python3.6----3.11，请使用支持的python版本重试

### [#](#连接-xtquant-时失败-返回-1及解决方法) 连接 xtquant 时失败，返回-1及解决方法

1. 客户端是否以极简模式登录（登录qmt系统时需要勾选极简模式）
2. 检查路径是否正确

* `miniqmt`：路径指定到安装目录下`\userdata_mini`文件夹
* `投研端`：路径指定到安装目录下`\userdata`文件夹

3. 客户端安装在C盘的话，每次都需要用管理员权限运行策略，才能正常连接，否则有权限问题。

提示

不建议安装在C盘。

可以通过以下测试来验证是否有写入权限

```

file_path = r"d:\qmt\userdata_mini\example.txt"  # 设置文件路径和名称

# 使用open函数创建文件，并指定写入模式("w"表示写入模式)
with open(file_path, "w") as file:
    file.write("123")  # 向文件写入内容


```

如果出现`PermissionError`，则说明存在文件权限问题

4. 路径正确时换个`session`（任意整数即可）

提示

由于机制限制，同一个session的两次python进程 connect之间必须超过3秒钟

5. 如果miniqmt开启后, userdata\_mini文件夹内没有`up_queue_xtquant`文件，说明没有用户没有对应函数下单的权限，需要联系券商开启

### [#](#执行xtdatacenter-init时提示监听58609端口失败) 执行xtdatacenter.init时提示监听58609端口失败

说明当前环境的58609端口被其他程序占用,通常是启动了两个xtdc服务导致的

**方法1.** 可用通过指定`xtdc.init(False)`后,使用`xtdc.listen(port)`指定自己需要的端口

```
from xtquant import xtdatacenter as xtdc 
xtdc.set_token("这里输入token")
xtdc.init(False)
port = 58601
xtdc.listen(port=port)
print(f"服务启动,开放端口：{port}")


```

**方法2.** 关闭所有py程序，或重启电脑，再执行xtdc.init

### [#](#下单后-查询委托的投资备注只有前半部分) 下单后，查询委托的投资备注只有前半部分

极简客户端的`order_remark`字段有长度限制，最大 24 个英文字符(一个中文占3个)， 超出的部分会丢弃。大qmt 没有长度限制。

### [#](#userdata-mini目录下-生成大量down-queue文件) userdata\_mini目录下，生成大量down\_queue文件

该文件是xttrade指定新的session产生的文件，可以参考下方示例来控制session产生的范围，避免该文件大量产生，**该文件可以删除**

[指定session id范围连接交易在新窗口打开](https://dict.thinktrader.net/nativeApi/code_examples.html?id=7zqjlm#%E6%8C%87%E5%AE%9Asession-id%E8%8C%83%E5%9B%B4%E8%BF%9E%E6%8E%A5%E4%BA%A4%E6%98%93)

上次更新: 2025/4/22 14:38:17

邀请注册送VIP优惠券

分享下方的内容给好友、QQ群、微信群,好友注册您即可获得VIP优惠券

玩转qmt,上迅投qmt知识库

登录后获取

[完整实例](/nativeApi/code_examples.html)  [xtquant版本下载](/nativeApi/download_xtquant.html)