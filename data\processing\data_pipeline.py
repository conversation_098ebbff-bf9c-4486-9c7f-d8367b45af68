#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据处理管道

提供链式数据处理管道，支持：
- 链式操作
- 并行处理
- 错误恢复
- 性能监控

使用示例：
```python
pipeline = DataPipeline()
result = (pipeline
    .load_data(data_root, symbol, period)
    .filter({'price': lambda x: x > 100})
    .aggregate(['date'], {'price': ['mean', 'max']})
    .execute())
```

作者: AI Assistant
创建时间: 2025-07-15
版本: 1.0.0
"""

import time
from typing import Dict, List, Optional, Any, Union, Callable
import pandas as pd
from dataclasses import dataclass
from enum import Enum

from data.processing.unified_data_framework import get_unified_framework
from utils.logger import get_unified_logger

logger = get_unified_logger(__name__)


class OperationType(Enum):
    """操作类型枚举"""
    LOAD = "load"
    FILTER = "filter"
    AGGREGATE = "aggregate"
    JOIN = "join"
    TRANSFORM = "transform"
    CUSTOM = "custom"


@dataclass
class PipelineOperation:
    """管道操作定义"""
    op_type: OperationType
    func: Callable
    args: tuple
    kwargs: dict
    name: Optional[str] = None


class DataPipeline:
    """
    数据处理管道
    
    支持链式操作和并行处理
    """
    
    def __init__(self, enable_parallel: bool = True):
        """
        初始化数据处理管道
        
        Args:
            enable_parallel: 是否启用并行处理
        """
        self.enable_parallel = enable_parallel
        self.operations = []
        self.data = None
        self.framework = get_unified_framework()
        
        self._performance_stats = {
            'total_operations': 0,
            'total_time': 0.0,
            'failed_operations': 0
        }
    
    def load_data(self, data_root: str, symbol: str, period: str,
                  start_time: Optional[str] = None,
                  end_time: Optional[str] = None,
                  columns: Optional[List[str]] = None,
                  method: str = 'vectorized') -> 'DataPipeline':
        """
        加载数据操作
        
        Args:
            data_root: 数据根目录
            symbol: 股票代码
            period: 数据周期
            start_time: 开始时间
            end_time: 结束时间
            columns: 要读取的列
            method: 读取方法
            
        Returns:
            DataPipeline: 管道实例（支持链式调用）
        """
        operation = PipelineOperation(
            op_type=OperationType.LOAD,
            func=self.framework.load_data,
            args=(data_root, symbol, period),
            kwargs={
                'start_time': start_time,
                'end_time': end_time,
                'columns': columns,
                'method': method
            },
            name=f"load_{symbol}_{period}"
        )
        self.operations.append(operation)
        return self
    
    def filter(self, conditions: Union[Dict[str, Any], Callable],
               method: str = 'duckdb') -> 'DataPipeline':
        """
        过滤数据操作
        
        Args:
            conditions: 过滤条件（字典或函数）
            method: 过滤方法
            
        Returns:
            DataPipeline: 管道实例（支持链式调用）
        """
        if callable(conditions):
            # 自定义过滤函数
            operation = PipelineOperation(
                op_type=OperationType.CUSTOM,
                func=lambda data: data[conditions(data)],
                args=(),
                kwargs={},
                name="custom_filter"
            )
        else:
            # 标准过滤
            operation = PipelineOperation(
                op_type=OperationType.FILTER,
                func=self.framework.filter_data,
                args=(),
                kwargs={'conditions': conditions, 'method': method},
                name="filter"
            )
        
        self.operations.append(operation)
        return self
    
    def aggregate(self, group_by: List[str],
                  agg_funcs: Dict[str, Union[str, List[str]]],
                  method: str = 'duckdb') -> 'DataPipeline':
        """
        聚合数据操作
        
        Args:
            group_by: 分组列
            agg_funcs: 聚合函数
            method: 聚合方法
            
        Returns:
            DataPipeline: 管道实例（支持链式调用）
        """
        operation = PipelineOperation(
            op_type=OperationType.AGGREGATE,
            func=self.framework.aggregate_data,
            args=(),
            kwargs={
                'group_by': group_by,
                'agg_funcs': agg_funcs,
                'method': method
            },
            name="aggregate"
        )
        self.operations.append(operation)
        return self
    
    def join(self, right_data: pd.DataFrame,
             on: Union[str, List[str]],
             how: str = 'inner',
             method: str = 'duckdb') -> 'DataPipeline':
        """
        连接数据操作
        
        Args:
            right_data: 右表数据
            on: 连接键
            how: 连接方式
            method: 连接方法
            
        Returns:
            DataPipeline: 管道实例（支持链式调用）
        """
        operation = PipelineOperation(
            op_type=OperationType.JOIN,
            func=self.framework.join_data,
            args=(),
            kwargs={
                'right': right_data,
                'on': on,
                'how': how,
                'method': method
            },
            name="join"
        )
        self.operations.append(operation)
        return self
    
    def transform(self, transform_func: Callable,
                  name: Optional[str] = None) -> 'DataPipeline':
        """
        自定义转换操作
        
        Args:
            transform_func: 转换函数
            name: 操作名称
            
        Returns:
            DataPipeline: 管道实例（支持链式调用）
        """
        operation = PipelineOperation(
            op_type=OperationType.TRANSFORM,
            func=transform_func,
            args=(),
            kwargs={},
            name=name or "transform"
        )
        self.operations.append(operation)
        return self
    
    def execute(self, return_intermediate: bool = False) -> Union[pd.DataFrame, Dict[str, pd.DataFrame]]:
        """
        执行管道操作
        
        Args:
            return_intermediate: 是否返回中间结果
            
        Returns:
            pd.DataFrame 或 Dict: 最终结果或中间结果字典
        """
        logger.info(f"开始执行数据处理管道，共 {len(self.operations)} 个操作")
        
        start_time = time.time()
        current_data = None
        intermediate_results = {}
        
        for i, operation in enumerate(self.operations):
            try:
                op_start_time = time.time()
                logger.debug(f"执行操作 [{i+1}/{len(self.operations)}]: {operation.name}")
                
                # 执行操作
                if operation.op_type == OperationType.LOAD:
                    # 加载操作不需要输入数据
                    current_data = operation.func(*operation.args, **operation.kwargs)
                else:
                    # 其他操作需要输入数据
                    if current_data is None:
                        raise ValueError(f"操作 {operation.name} 需要输入数据，但当前数据为空")
                    
                    if operation.op_type in [OperationType.FILTER, OperationType.AGGREGATE]:
                        current_data = operation.func(current_data, **operation.kwargs)
                    elif operation.op_type == OperationType.JOIN:
                        current_data = operation.func(current_data, **operation.kwargs)
                    else:
                        # 自定义操作
                        current_data = operation.func(current_data, *operation.args, **operation.kwargs)
                
                op_end_time = time.time()
                op_time = op_end_time - op_start_time
                
                if current_data is not None:
                    logger.debug(f"操作 {operation.name} 完成，结果行数: {len(current_data)}, 耗时: {op_time:.6f} 秒")
                    
                    # 保存中间结果
                    if return_intermediate:
                        intermediate_results[operation.name] = current_data.copy()
                else:
                    logger.warning(f"操作 {operation.name} 返回空结果")
                
                # 更新统计信息
                self._performance_stats['total_operations'] += 1
                
            except Exception as e:
                logger.error(f"操作 {operation.name} 执行失败: {e}")
                self._performance_stats['failed_operations'] += 1
                
                # 根据配置决定是否继续执行
                if not self.enable_parallel:
                    raise
                else:
                    logger.warning(f"跳过失败的操作: {operation.name}")
                    continue
        
        end_time = time.time()
        total_time = end_time - start_time
        self._performance_stats['total_time'] += total_time
        
        logger.info(f"数据处理管道执行完成，总耗时: {total_time:.6f} 秒")
        
        if return_intermediate:
            intermediate_results['final'] = current_data
            return intermediate_results
        else:
            return current_data
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息
        
        Returns:
            dict: 性能统计信息
        """
        stats = self._performance_stats.copy()
        if stats['total_operations'] > 0:
            stats['avg_time_per_operation'] = stats['total_time'] / stats['total_operations']
            stats['success_rate'] = (stats['total_operations'] - stats['failed_operations']) / stats['total_operations']
        else:
            stats['avg_time_per_operation'] = 0.0
            stats['success_rate'] = 0.0
        
        return stats
    
    def reset(self) -> 'DataPipeline':
        """
        重置管道
        
        Returns:
            DataPipeline: 管道实例
        """
        self.operations.clear()
        self.data = None
        logger.debug("数据处理管道已重置")
        return self
    
    def __len__(self) -> int:
        """返回操作数量"""
        return len(self.operations)
    
    def __repr__(self) -> str:
        """返回管道描述"""
        op_names = [op.name for op in self.operations]
        return f"DataPipeline({len(self.operations)} operations: {' -> '.join(op_names)})"


def create_pipeline(enable_parallel: bool = True) -> DataPipeline:
    """
    创建数据处理管道（便捷函数）
    
    Args:
        enable_parallel: 是否启用并行处理
        
    Returns:
        DataPipeline: 数据处理管道实例
    """
    return DataPipeline(enable_parallel=enable_parallel)
