# 数据显示模块 (Data Display)

本模块提供了统一的数据格式化和显示功能，用于在控制台和日志中美观地展示各种数据结构。

## 模块结构

- **table.py**: 表格显示功能，包含将DataFrame转换为文本表格的核心函数
- **formatting.py**: 数据格式化功能，提供各种数据格式化函数
- **text.py**: 文本处理功能，提供文本美化和处理函数
- **console.py**: 控制台显示功能，提供在控制台中显示数据的函数
- **menus.py**: 菜单显示功能，提供交互式菜单的创建和显示

## 重要说明 - 导入路径

**请注意**：应该直接从`table`模块导入`dataframe_to_text_table`函数，而不是从`formatting`模块导入。

```python
# 正确的导入方式
from utils.data_display.table import dataframe_to_text_table

# 不推荐的导入方式（可能导致参数传递问题）
from utils.data_display.formatting import dataframe_to_text_table  # 不推荐
from utils.data_display import dataframe_to_text_table  # 不推荐
```

`formatting`模块中的`dataframe_to_text_table`只是一个包装器，用于向后兼容，它会延迟导入`table`模块中的实际函数。直接从`table`模块导入可以确保所有参数能够正确传递。

## 主要功能

### 表格显示

核心函数是 `dataframe_to_text_table`，用于将 DataFrame 转换为文本表格：

```python
from utils.data_display.table import dataframe_to_text_table

# 基本用法
table_text = dataframe_to_text_table(df, title="数据表格")
print(table_text)

# 高级用法
table_text = dataframe_to_text_table(
    df,
    title="详细数据表格",
    max_rows=10,
    max_cols=5,
    smart_float=True,
    float_precision=4,
    format_time_columns=True
)
print(table_text)

# 处理列表类型数据
table_text = dataframe_to_text_table(
    df,
    title="Tick数据表格",
    list_format='truncate',  # 只显示列表的前几个元素
    raw_display=False        # 控制是否显示原始数据
)
print(table_text)
```

#### 列表数据处理选项

对于包含列表类型数据的DataFrame（如Tick数据），可以使用以下参数：

- `list_format`: 控制列表的显示方式
  - `'full'`: 完整显示列表（默认）
  - `'compact'`: 使用紧凑格式显示列表，如 `[1,2,3]`
  - `'truncate'`: 只显示列表的前几个元素，如 `[1, 2, 3, ...]`
  - `'length'`: 只显示列表的长度，如 `[len=5]`

- `raw_display`: 控制是否显示原始数据
  - `True`: 显示原始数据，不进行任何格式化
  - `False`: 显示格式化后的数据（默认）

### 文本美化

```python
from utils.data_display import print_boxed_text, create_text_box

# 打印带边框的文本
print_boxed_text("这是一段重要信息", title="通知")

# 创建文本框字符串
box_text = create_text_box("这是一段重要信息", width=40, padding=1, border_char="#")
print(box_text)
```

### 数据格式化

```python
from utils.data_display import format_number, format_percentage, format_float_smart

# 显示原始数据（不格式化数值）
display_dataframe(
    df,
    title="原始数据",
    format_values=False,
    list_format='truncate'
)
```

### 参数说明

- `df`: 要显示的DataFrame
- `title`: 表格标题，如果为None且提供了symbol和period，则自动生成标题
- `symbol`: 股票代码，用于自动生成标题
- `period`: 数据周期，用于自动生成标题
- `display_mode`: 显示模式，可选值为:
  - `"head"`: 只显示头部数据
  - `"tail"`: 只显示尾部数据
  - `"both"`: 同时显示头部和尾部数据（默认）
  - `"all"`: 显示全部数据
- `head_rows`: 头部显示的行数
- `tail_rows`: 尾部显示的行数
- `log_target`: 日志目标，可选值为:
  - `LogTarget.CONSOLE`: 只输出到控制台
  - `LogTarget.FILE`: 只输出到日志文件（默认）
  - `LogTarget.BOTH`: 同时输出到控制台和日志文件
- `format_values`: 是否格式化数值，如果为False，则显示原始数据（默认False）
- `logger_name`: 日志记录器名称，默认为"data_commands"
- `show_progress`: 是否显示进度信息（如"【当前进度：显示原始数据】"）
- `list_format`: 列表显示方式，默认为'full'，完整显示列表数据
- `raw_display`: 是否显示原始数据，与format_values参数相反

### 注意事项

1. 推荐使用`display_dataframe`函数替代直接使用`dataframe_to_text_table`函数
2. 对于tick数据等包含列表的DataFrame，默认使用`format_values=False`，保持原始数据显示
3. 函数内部会自动处理日志记录器和刷新日志文件等操作
4. 可以通过`symbol`和`period`参数自动生成标题，也可以直接提供`title`参数 