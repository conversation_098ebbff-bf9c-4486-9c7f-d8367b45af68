#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
废弃函数标记

标记项目中废弃的数据保存函数，提供迁移指南和向后兼容性支持。

设计理念：
- 逐步废弃重复函数
- 提供清晰的迁移路径
- 保持向后兼容性
- 引导用户使用统一接口

版本: v3.0
作者: Augment AI
日期: 2025-08-05
"""

import warnings
import functools
from typing import Callable, Any

from utils.logger import get_unified_logger, LogTarget

# 创建统一日志记录器
logger = get_unified_logger(__name__, enhanced=True)


def deprecated(reason: str, replacement: str = None, version: str = "v3.0"):
    """
    废弃函数装饰器
    
    Args:
        reason: 废弃原因
        replacement: 推荐的替代方案
        version: 废弃版本
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            # 发出废弃警告
            warning_msg = f"{func.__name__} 已在 {version} 版本中废弃。{reason}"
            if replacement:
                warning_msg += f" 请使用 {replacement} 替代。"
            
            warnings.warn(
                warning_msg,
                DeprecationWarning,
                stacklevel=2
            )
            
            # 记录到日志
            logger.warning(LogTarget.FILE, f"使用了废弃函数: {func.__name__} - {warning_msg}")
            
            # 执行原函数
            return func(*args, **kwargs)
        
        return wrapper
    return decorator


# 废弃函数统计
DEPRECATED_FUNCTIONS = {
    "save_to_partition": {
        "reason": "功能重复，已统一到 UnifiedDataSaver",
        "replacement": "save_data_unified(strategy=SaveStrategy.SINGLE_PARTITION)",
        "migration_complexity": "简单",
        "estimated_usage": "高"
    },
    "save_data_by_partition": {
        "reason": "功能重复，已统一到 UnifiedDataSaver", 
        "replacement": "save_data_unified(strategy=SaveStrategy.AUTO)",
        "migration_complexity": "简单",
        "estimated_usage": "高"
    },
    "append_to_partition": {
        "reason": "功能重复，追加逻辑已统一",
        "replacement": "save_data_unified() # 自动处理追加逻辑",
        "migration_complexity": "简单",
        "estimated_usage": "低"
    },
    "save_to_partition_wrapper": {
        "reason": "内部包装器函数，不再需要",
        "replacement": "直接使用 save_data_unified()",
        "migration_complexity": "简单",
        "estimated_usage": "低"
    },
    "ParquetStorage.save_data_by_partition": {
        "reason": "类方法版本，功能重复",
        "replacement": "save_data_unified() # 无需创建类实例",
        "migration_complexity": "中等",
        "estimated_usage": "中等"
    },
    "ParquetStorage.save_data_by_partition_parallel": {
        "reason": "并行版本类方法，功能重复",
        "replacement": "save_data_unified(parallel=True)",
        "migration_complexity": "中等", 
        "estimated_usage": "中等"
    },
    "save_data_to_parquet": {
        "reason": "模块级便利函数，功能重复",
        "replacement": "循环调用 save_data_unified()",
        "migration_complexity": "中等",
        "estimated_usage": "低"
    }
}


def show_deprecation_summary():
    """显示废弃函数汇总"""
    print("========== 废弃函数汇总 ==========")
    print(f"总计废弃函数: {len(DEPRECATED_FUNCTIONS)}")
    print()
    
    for func_name, info in DEPRECATED_FUNCTIONS.items():
        print(f"函数: {func_name}")
        print(f"  废弃原因: {info['reason']}")
        print(f"  推荐替代: {info['replacement']}")
        print(f"  迁移复杂度: {info['migration_complexity']}")
        print(f"  预估使用量: {info['estimated_usage']}")
        print()
    
    print("详细迁移指南: docs/数据保存架构重构指南.md")
    print("=====================================")


def get_migration_guide(function_name: str) -> str:
    """获取特定函数的迁移指南"""
    if function_name in DEPRECATED_FUNCTIONS:
        info = DEPRECATED_FUNCTIONS[function_name]
        return f"""
迁移指南 - {function_name}:

废弃原因: {info['reason']}
推荐替代: {info['replacement']}
迁移复杂度: {info['migration_complexity']}

示例:
# 旧代码
{function_name}(df, data_root, symbol, period, ...)

# 新代码  
{info['replacement']}
"""
    else:
        return f"未找到函数 {function_name} 的迁移指南"


def check_deprecated_usage(file_path: str = None):
    """检查废弃函数使用情况"""
    if file_path:
        logger.info(LogTarget.FILE, f"检查文件 {file_path} 中的废弃函数使用")
        # 这里可以添加代码扫描逻辑
    else:
        logger.info(LogTarget.FILE, "建议运行全项目废弃函数使用检查")


# 重构收益统计
REFACTOR_BENEFITS = {
    "代码减少": "预计减少70%的重复代码",
    "维护成本": "降低多个函数的同步维护成本",
    "接口统一": "从8个函数统一到1个核心接口",
    "功能增强": "智能策略选择和跨日期数据处理",
    "性能提升": "统一优化，整体性能提升",
    "错误减少": "减少函数选择错误和参数不一致问题"
}


def show_refactor_benefits():
    """显示重构收益"""
    print("========== 重构收益统计 ==========")
    for benefit, description in REFACTOR_BENEFITS.items():
        print(f"{benefit}: {description}")
    print("=================================")


if __name__ == "__main__":
    show_deprecation_summary()
    print()
    show_refactor_benefits()
