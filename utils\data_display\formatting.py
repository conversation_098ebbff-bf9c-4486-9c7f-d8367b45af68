#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据格式化模块

提供DataFrame和其他数据结构的格式化和文本展示功能
"""

import os
import sys
import pandas as pd
from typing import Optional, Dict, Any, Union, List

# 注意：为了避免循环导入，不直接从table模块导入dataframe_to_text_table函数
# 将在需要时使用延迟导入

# 将项目根目录添加到Python路径
root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, root_dir)


def dataframe_to_text_table(df: pd.DataFrame, title: str = "", **kwargs) -> str:
    """
    将DataFrame转换为文本表格形式（向后兼容函数）
    
    注意：这是一个简单的实现，用于避免循环导入。
    实际使用时会延迟导入table模块中的完整实现。
    
    Args:
        df: 要转换的DataFrame
        title: 表格标题
        **kwargs: 其他参数，传递给完整实现
        
    Returns:
        str: 格式化后的文本表格
    """
    # 延迟导入，避免循环导入问题
    from utils.data_display.table import dataframe_to_text_table as real_func
    
    # 处理废弃的参数，确保向后兼容
    deprecated_params = [
        'highlight_max', 'highlight_min', 'column_width', 
        'min_column_width', 'max_column_width', 'add_border', 
        'max_width', 'column_align'
    ]
    
    # 从kwargs中移除废弃的参数
    for param in deprecated_params:
        if param in kwargs:
            kwargs.pop(param)
    
    return real_func(df, title=title, **kwargs)


def get_display_width(text: str) -> int:
    """
    获取文本在终端中的显示宽度
    
    Args:
        text: 要计算宽度的文本
        
    Returns:
        int: 文本的显示宽度
    """
    width = 0
    for char in text:
        if ord(char) > 127:
            width += 2  # 中文和其他宽字符占两个位置
        else:
            width += 1  # ASCII字符占一个位置
    return width


def format_column(value: Any, width: int = 15, align: str = 'left') -> str:
    """
    格式化单个列的值，使其在给定宽度内对齐
    
    Args:
        value: 要格式化的值
        width: 输出列的宽度
        align: 对齐方式，'left'、'right'或'center'
        
    Returns:
        str: 格式化后的字符串
    """
    # 将值转换为字符串
    val_str = str(value)
    
    # 计算显示宽度
    display_width = get_display_width(val_str)
    
    # 计算需要的填充量
    padding = max(0, width - display_width)
    
    # 根据对齐方式进行填充
    if align == 'left':
        return val_str + ' ' * padding
    elif align == 'right':
        return ' ' * padding + val_str
    elif align == 'center':
        left_padding = padding // 2
        right_padding = padding - left_padding
        return ' ' * left_padding + val_str + ' ' * right_padding
    else:
        raise ValueError(f"不支持的对齐方式: {align}")


def write_dataframe_to_file(
    df: pd.DataFrame,
    file_path: str,
    format: str = 'csv',
    **kwargs
) -> bool:
    """
    将DataFrame写入文件
    
    Args:
        df: 要写入的DataFrame
        file_path: 文件路径
        format: 文件格式，'csv'或'excel'
        **kwargs: 传递给to_csv或to_excel的额外参数
        
    Returns:
        bool: 是否成功写入
    """
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(os.path.abspath(file_path)), exist_ok=True)
        
        # 根据格式写入文件
        if format.lower() == 'csv':
            df.to_csv(file_path, **kwargs)
        elif format.lower() in ['excel', 'xlsx', 'xls']:
            df.to_excel(file_path, **kwargs)
        else:
            raise ValueError(f"不支持的文件格式: {format}")
        return True
    except Exception as e:
        print(f"写入文件时发生错误: {e}")
        return False


def print_progress_bar(
    iteration: int,
    total: int,
    prefix: str = '',
    suffix: str = '',
    decimals: int = 1,
    length: int = 50,
    fill: str = '█',
    print_end: str = "\r"
) -> None:
    """
    在控制台打印进度条
    
    Args:
        iteration: 当前迭代次数
        total: 总迭代次数
        prefix: 前缀字符串
        suffix: 后缀字符串
        decimals: 百分比的小数位数
        length: 进度条字符长度
        fill: 进度条填充字符
        print_end: 结束字符串
    """
    percent = ("{0:." + str(decimals) + "f}").format(100 * (iteration / float(total)))
    filled_length = int(length * iteration // total)
    bar = fill * filled_length + '-' * (length - filled_length)
    print(f'\r{prefix} |{bar}| {percent}% {suffix}', end=print_end)
    
    # 当迭代完成时打印换行符
    if iteration == total:
        print()


def create_console_table(
    headers: List[str],
    rows: List[List[Any]],
    title: Optional[str] = None,
    column_widths: Optional[List[int]] = None
) -> str:
    """
    创建简单的控制台表格
    
    Args:
        headers: 表头
        rows: 表格行数据
        title: 表格标题
        column_widths: 列宽度
        
    Returns:
        str: 格式化的表格
    """
    if not column_widths:
        # 计算每列最大宽度
        column_widths = []
        for i in range(len(headers)):
            header_width = get_display_width(str(headers[i]))
            # 计算数据中该列的最大宽度
            data_width = 0
            for row in rows:
                if i < len(row):
                    width = get_display_width(str(row[i]))
                    data_width = max(data_width, width)
            # 列宽为表头宽度和数据宽度中的最大值，再加2个空格
            column_widths.append(max(header_width, data_width) + 2)
    
    # 计算表格总宽度
    total_width = sum(column_widths) + len(headers) + 1
    
    # 创建表格
    lines = []
    
    # 添加标题
    if title:
        lines.append(f" {title} ".center(total_width, '='))
    
    # 添加表头分隔线
    header_line = '+'
    for width in column_widths:
        header_line += '-' * width + '+'
    lines.append(header_line)
    
    # 添加表头
    header_str = '|'
    for i, header in enumerate(headers):
        header_str += format_column(header, width=column_widths[i], align='center') + '|'
    lines.append(header_str)
    
    # 添加表头与数据的分隔线
    lines.append(header_line)
    
    # 添加数据行
    for row in rows:
        row_str = '|'
        for i in range(len(headers)):
            if i < len(row):
                value = row[i]
            else:
                value = ''
            row_str += format_column(value, width=column_widths[i]) + '|'
        lines.append(row_str)
    
    # 添加底部分隔线
    lines.append(header_line)
    
    return '\n'.join(lines)


def truncate_text(text: str, max_length: int = 80, suffix: str = '...') -> str:
    """
    截断过长的文本
    
    Args:
        text: 要截断的文本
        max_length: 最大长度
        suffix: 截断后添加的后缀
        
    Returns:
        str: 截断后的文本
    """
    if len(text) <= max_length:
        return text
    return text[:max_length - len(suffix)] + suffix


def print_boxed_text(text: str, width: int = 60, padding: int = 1, title: Optional[str] = None) -> None:
    """
    打印带边框的文本
    
    Args:
        text: 要打印的文本
        width: 边框宽度
        padding: 内边距
        title: 标题
    """
    # 调整为适当的宽度
    box_width = max(width, get_display_width(text) + padding * 2 + 2)
    
    # 创建水平线
    h_line = '+' + '-' * (box_width - 2) + '+'
    
    # 创建内容行的格式
    content_format = '|' + ' ' * padding + '{text}' + ' ' * padding + '|'
    
    # 打印上边框
    if title:
        # 如果有标题，在上边框中添加标题
        title_line = f"| {title} |".center(box_width, '-')
        print('+' + title_line + '+')
    else:
        print(h_line)
    
    # 分割文本为多行
    lines = text.split('\n')
    
    # 确保每一行不会超过内容区域的宽度
    content_width = box_width - 2 - padding * 2
    wrapped_lines = []
    
    for line in lines:
        if get_display_width(line) <= content_width:
            wrapped_lines.append(line)
        else:
            # 简单的截断，可以改进为更好的换行算法
            current_line = ''
            for char in line:
                if get_display_width(current_line + char) > content_width:
                    wrapped_lines.append(current_line)
                    current_line = char
                else:
                    current_line += char
            
            if current_line:
                wrapped_lines.append(current_line)
    
    # 打印内容
    for line in wrapped_lines:
        # 计算需要填充的空格数
        padding_right = content_width - get_display_width(line)
        formatted_line = content_format.format(text=line + ' ' * padding_right)
        print(formatted_line)
    
    # 打印下边框
    print(h_line)


def format_number(number: Union[int, float], thousands_sep: bool = True, precision: int = 2) -> str:
    """
    格式化数字
    
    Args:
        number: 要格式化的数字
        thousands_sep: 是否使用千位分隔符
        precision: 小数点后的位数
        
    Returns:
        str: 格式化后的数字
    """
    if isinstance(number, int):
        if thousands_sep:
            return f"{number:,}"
        return str(number)
    elif isinstance(number, float):
        if thousands_sep:
            return f"{number:,.{precision}f}"
        return f"{number:.{precision}f}"
    else:
        return str(number)


def format_percentage(
    value: float,
    precision: int = 2,
    include_sign: bool = True,
    space_before: bool = False
) -> str:
    """
    格式化百分比
    
    Args:
        value: 要格式化的值（小数形式，如0.1234表示12.34%）
        precision: 小数点后的位数
        include_sign: 是否包含正负号
        space_before: 是否在百分号前添加空格
        
    Returns:
        str: 格式化后的百分比
    """
    # 转换为百分比形式
    percentage = value * 100
    
    # 创建格式字符串
    format_str = '{:'
    if include_sign:
        format_str += '+'
    format_str += f'.{precision}f}}'
    
    # 格式化百分比
    formatted = format_str.format(percentage)
    
    # 添加百分号
    if space_before:
        return f"{formatted} %"
    else:
        return f"{formatted}%"


def print_dict_as_table(data: Dict[str, Any], title: Optional[str] = None, sort_keys: bool = False) -> None:
    """
    将字典打印为表格形式
    
    Args:
        data: 要打印的字典
        title: 表格标题
        sort_keys: 是否按键排序
    """
    if title:
        print(f"\n=== {title} ===")
    
    # 获取最长键的长度
    max_key_len = max([get_display_width(str(k)) for k in data.keys()]) if data else 0
    
    # 创建格式化字符串
    format_str = "{:<" + str(max_key_len + 2) + "}: {}"
    
    # 排序键
    keys = sorted(data.keys()) if sort_keys else data.keys()
    
    # 打印键值对
    for key in keys:
        print(format_str.format(str(key), str(data[key])))


def animate_text(text: str, delay: float = 0.05) -> None:
    """
    逐字打印文本，产生动画效果
    
    Args:
        text: 要打印的文本
        delay: 每个字符间的延迟时间（秒）
    """
    import sys
    import time
    
    for char in text:
        sys.stdout.write(char)
        sys.stdout.flush()
        time.sleep(delay)
    print()


def highlight_text(text: str, highlight: bool = True) -> str:
    """
    高亮显示文本
    
    Args:
        text: 要高亮的文本
        highlight: 是否进行高亮，用于条件控制
        
    Returns:
        str: 高亮后的文本
    """
    if not highlight:
        return text
    
    # ANSI转义序列
    BOLD = "\033[1m"
    RED = "\033[91m"
    RESET = "\033[0m"
    
    return f"{BOLD}{RED}{text}{RESET}"


def create_text_box(text: str, width: int = 60, style: str = 'single') -> str:
    """
    创建文本边框
    
    Args:
        text: 要放入边框的文本
        width: 边框宽度
        style: 边框样式，'single'或'double'
        
    Returns:
        str: 边框文本
    """
    # 边框样式
    styles = {
        'single': {
            'top_left': '┌', 'top_right': '┐',
            'bottom_left': '└', 'bottom_right': '┘',
            'horizontal': '─', 'vertical': '│'
        },
        'double': {
            'top_left': '╔', 'top_right': '╗',
            'bottom_left': '╚', 'bottom_right': '╝',
            'horizontal': '═', 'vertical': '║'
        }
    }
    
    # 使用指定样式，如果不存在则使用默认
    box = styles.get(style, styles['single'])
    
    # 创建边框
    top = box['top_left'] + box['horizontal'] * (width - 2) + box['top_right']
    bottom = box['bottom_left'] + box['horizontal'] * (width - 2) + box['bottom_right']
    
    # 拆分文本为多行
    lines = text.split('\n')
    
    # 处理每一行，确保不超过边框宽度
    content_width = width - 4  # 减去边框和内边距
    box_content = []
    
    for line in lines:
        # 如果行太长，进行截断
        if get_display_width(line) > content_width:
            box_content.append(line[:content_width] + '...')
        else:
            # 计算填充
            padding = width - 4 - get_display_width(line)
            box_content.append(line + ' ' * padding)
    
    # 组装边框内容
    result = [top]
    for line in box_content:
        result.append(f"{box['vertical']} {line} {box['vertical']}")
    result.append(bottom)
    
    return '\n'.join(result)


def parse_special_timestamp(x):
    """解析各种特殊格式的时间戳"""
    # 将输入转换为字符串
    str_x = str(x)
    
    # 处理17位整数时间戳 (YYYYMMDDHHMMSSMMM)
    if str_x.isdigit() and len(str_x) == 17:
        try:
            year = int(str_x[0:4])
            month = int(str_x[4:6])
            day = int(str_x[6:8])
            hour = int(str_x[8:10])
            minute = int(str_x[10:12])
            second = int(str_x[12:14])
            millisecond = int(str_x[14:17])
            
            # 检查日期有效性
            if 1900 <= year <= 2100 and 1 <= month <= 12 and 1 <= day <= 31:
                return f"{year:04d}-{month:02d}-{day:02d} {hour:02d}:{minute:02d}:{second:02d}:{millisecond:03d}"
        except (ValueError, IndexError):
            pass
    
    # 处理标准14位整数时间戳 (YYYYMMDDHHMMSS)
    elif str_x.isdigit() and len(str_x) == 14:
        try:
            year = int(str_x[0:4])
            month = int(str_x[4:6])
            day = int(str_x[6:8])
            hour = int(str_x[8:10])
            minute = int(str_x[10:12])
            second = int(str_x[12:14])
            
            # 检查日期有效性
            if 1900 <= year <= 2100 and 1 <= month <= 12 and 1 <= day <= 31:
                return f"{year:04d}-{month:02d}-{day:02d} {hour:02d}:{minute:02d}:{second:02d}"
        except (ValueError, IndexError):
            pass
    
    # 处理带小数点格式的时间戳 (YYYYMMDDHHMMSS.mmm)
    elif '.' in str_x:
        parts = str_x.split('.')
        base_part = parts[0]
        ms_part = parts[1].ljust(3, '0')[:3]  # 确保毫秒为3位
        
        # 处理基础部分时间戳
        # YYYYMMDDHHMMSS 格式
        if len(base_part) >= 14 and base_part.isdigit():
            try:
                year = int(base_part[0:4])
                month = int(base_part[4:6])
                day = int(base_part[6:8])
                hour = int(base_part[8:10])
                minute = int(base_part[10:12])
                second = int(base_part[12:14])
                
                # 检查日期有效性
                if 1900 <= year <= 2100 and 1 <= month <= 12 and 1 <= day <= 31:
                    return f"{year:04d}-{month:02d}-{day:02d} {hour:02d}:{minute:02d}:{second:02d}:{ms_part}"
            except (ValueError, IndexError):
                pass
        # YYYYMMDD 格式 - 处理只有日期部分的情况
        elif len(base_part) == 8 and base_part.isdigit():
            try:
                year = int(base_part[0:4])
                month = int(base_part[4:6])
                day = int(base_part[6:8])
                
                # 检查日期有效性
                if 1900 <= year <= 2100 and 1 <= month <= 12 and 1 <= day <= 31:
                    return f"{year:04d}-{month:02d}-{day:02d} 00:00:00:{ms_part}"
            except (ValueError, IndexError):
                pass
    
    # 处理纯数字格式 - 不含小数点的情况
    elif str_x.isdigit() and 8 <= len(str_x) <= 12:  # 只包含日期或包含日期+部分时间
        try:
            year = int(str_x[0:4])
            month = int(str_x[4:6])
            day = int(str_x[6:8])
            
            # 检查日期有效性
            if 1900 <= year <= 2100 and 1 <= month <= 12 and 1 <= day <= 31:
                if len(str_x) == 8:  # 只有日期 YYYYMMDD
                    return f"{year:04d}-{month:02d}-{day:02d}"
                elif len(str_x) == 10:  # 日期+小时 YYYYMMDDHH
                    hour = int(str_x[8:10])
                    return f"{year:04d}-{month:02d}-{day:02d} {hour:02d}:00:00"
                elif len(str_x) == 12:  # 日期+小时+分钟 YYYYMMDDHHMM
                    hour = int(str_x[8:10])
                    minute = int(str_x[10:12])
                    return f"{year:04d}-{month:02d}-{day:02d} {hour:02d}:{minute:02d}:00"
                # 长度不是14或17，按照自定义格式处理
                else:
                    time_part = str_x[8:].ljust(6, '0')[:6]  # 标准化到HHMMSS
                    hour = int(time_part[0:2])
                    minute = int(time_part[2:4])
                    second = int(time_part[4:6])
                    return f"{year:04d}-{month:02d}-{day:02d} {hour:02d}:{minute:02d}:{second:02d}"
        except (ValueError, IndexError):
            pass
    
    # 如果无法识别，返回原始值
    return str_x


def format_float_smart(value: float, precision: int = 1) -> str:
    """
    智能格式化浮点数，保留必要的小数位数
    
    Args:
        value: 要格式化的浮点数
        precision: 最大精度（小数位数）
        
    Returns:
        str: 格式化后的字符串
    """
    # 处理 None 和 NaN 值
    if value is None:
        return ""
    
    # 检查值是否为NaN
    try:
        if pd.isna(value):
            return ""
    except Exception:  # 明确指定异常类型
        if isinstance(value, float) and value != value:  # NaN不等于自身
            return ""
    
    # 处理整数
    if isinstance(value, int) or (isinstance(value, float) and value.is_integer()):
        return f"{int(value)}"
    
    # 处理零值
    if abs(value) < 1e-10:
        return "0"
    
    # 根据数值范围确定小数位数
    if abs(value) < 0.01:
        return f"{value:.6f}"
    elif abs(value) < 0.1:
        return f"{value:.4f}"
    elif abs(value) < 1:
        return f"{value:.3f}"
    elif abs(value) < 100:
        return f"{value:.2f}"
    elif abs(value) < 10000:
        return f"{value:.1f}"
    else:
        return f"{int(value)}"