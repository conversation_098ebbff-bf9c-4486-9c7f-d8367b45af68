#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据源管理模块 - 重构版

精简的数据源管理器，职责清晰，代码简洁
主要功能：
1. 数据源管理和协调  
2. 统一的对外接口
3. 任务分发给专门的处理器
"""

import os
import sys
from typing import Dict, List, Optional, Any
from datetime import datetime
from pathlib import Path

# 添加项目根目录到系统路径
sys.path.insert(0, str(Path(__file__).parent.parent))

# 导入配置和工具
from config.settings import DATA_ROOT
from utils.logger import get_unified_logger, LogTarget
from utils.path_utils.file_operations import ensure_dir_exists

# 导入处理器
from data.handlers import (
    DownloadHandler,
    LocalDataHandler, 
    CustomPeriodHandler,
    DataProcessor
)

logger = get_unified_logger(__name__, enhanced=True)

# 数据源类型常量
DATA_SOURCE_XTQUANT = "xtquant"
DATA_SOURCE_LOCAL = "local"

# 默认配置
DEFAULT_CONFIG = {
    "primary_source": DATA_SOURCE_XTQUANT,
    "fallback_sources": [DATA_SOURCE_LOCAL],
    "data_root": DATA_ROOT,
    "max_retries": 3,
    "retry_delay": 2,
    "sources": {
        DATA_SOURCE_XTQUANT: {
            "enabled": True,
            "priority": 1,
        },
        DATA_SOURCE_LOCAL: {
            "enabled": True,
            "priority": 99,
        }
    }
}


class DataSourceError(Exception):
    """数据源异常"""
    pass


class DataSourceManager:
    """数据源管理器 - 精简版"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化数据源管理器
        
        Args:
            config: 配置字典，如果为None则使用默认配置
        """
        logger.info("初始化数据源管理器")
        
        self.config = config or DEFAULT_CONFIG.copy()
        self.data_root = self.config.get("data_root", DATA_ROOT)
        
        # 确保数据根目录存在
        ensure_dir_exists(self.data_root)
        
        # 初始化数据源
        self._init_sources()
        
        # 初始化处理器
        self._init_handlers()
        
        logger.info("数据源管理器初始化完成")
    
    def _init_sources(self):
        """初始化数据源"""
        sources_config = self.config.get("sources", {})
        self.available_sources = {}
        self.source_instances = {}
        
        for source_name, source_config in sources_config.items():
            if source_config.get("enabled", False):
                self.available_sources[source_name] = source_config
                logger.info(f"数据源 {source_name} 已启用")
        
        # 导入迅投数据源
        if DATA_SOURCE_XTQUANT in self.available_sources:
            try:
                from data.source import xtquant_data
                self.source_instances[DATA_SOURCE_XTQUANT] = xtquant_data.xtquant_fetcher
                logger.info("迅投数据源加载成功")
            except ImportError as e:
                logger.warning(f"无法导入迅投数据源: {e}")
                if DATA_SOURCE_XTQUANT in self.available_sources:
                    del self.available_sources[DATA_SOURCE_XTQUANT]
    
    def _init_handlers(self):
        """初始化处理器"""
        # 获取迅投数据源实例
        fetcher_instance = self.source_instances.get(DATA_SOURCE_XTQUANT)
        
        # 初始化各个处理器
        self.download_handler = DownloadHandler(
            data_root=self.data_root,
            fetcher_instance=fetcher_instance
        )
        
        self.local_data_handler = LocalDataHandler(
            data_root=self.data_root
        )
        
        self.custom_period_handler = CustomPeriodHandler(
            data_root=self.data_root,
            download_handler=self.download_handler
        )
        
        self.data_processor = DataProcessor()
        
        logger.info("所有处理器初始化完成")
    
    def download_history_data(self, **kwargs) -> Dict[str, Any]:
        """
        下载历史数据 - 统一入口
        """
        logger.info(f"开始下载历史数据: {len(kwargs.get('stock_list', []))}个股票")

        period = kwargs.get('period', '')

        # tick数据直接下载，不进入自定义周期处理
        if period.lower() == 'tick':
            logger.info(f"检测到tick数据请求，使用直接下载方式")
            return self.download_handler.download_history_data(**kwargs)

        # 检查是否为自定义周期
        handle_custom_period = kwargs.get('handle_custom_period', True)
        if handle_custom_period:
            from utils.data_processor.period_handler import is_custom_period
            if is_custom_period(period):
                logger.info(f"检测到自定义周期: {period}，使用自定义周期处理器")
                return self.custom_period_handler.handle_custom_period_download(**kwargs)

        # 使用标准下载处理器
        return self.download_handler.download_history_data(**kwargs)
    
    def get_local_data(self, **kwargs) -> Dict[str, Any]:
        """获取本地数据 - 统一入口"""
        logger.info(f"获取本地数据: {len(kwargs.get('stock_list', []))}个股票")
        return self.local_data_handler.get_local_data(**kwargs)
    
    def check_data_exists(self, symbol: str, period: str) -> bool:
        """检查数据是否存在"""
        return self.local_data_handler.check_data_exists(symbol, period)
    
    def get_data_path(self, symbol: str, period: str, timestamp: Optional[str] = None) -> str:
        """获取数据文件路径"""
        from data.storage.path_manager import get_save_path
        return get_save_path(self.data_root, symbol, period, timestamp)
    
    def get_latest_trade_date(self, format: str = "YYYYMMDD") -> str:
        """获取最新交易日期"""
        try:
            from data.source.xtquant_data import get_latest_trade_date as xt_get_latest_trade_date
            return xt_get_latest_trade_date()
        except Exception as e:
            logger.warning(f"获取最新交易日期失败: {e}")
            current_date = datetime.now().strftime("%Y%m%d")
            if format == "YYYY-MM-DD":
                return f"{current_date[:4]}-{current_date[4:6]}-{current_date[6:8]}"
            return current_date


# 创建单例实例
_manager = None


def get_manager() -> DataSourceManager:
    """获取数据源管理器单例实例"""
    global _manager
    if _manager is None:
        _manager = DataSourceManager()
    return _manager


# 简化的模块级便利函数
def download_history_data(**kwargs) -> Dict[str, Any]:
    """下载历史行情数据 - 简化的模块级便利函数"""
    logger.info(f"模块级下载历史数据")
    
    try:
        result = get_manager().download_history_data(**kwargs)
        
        # 清理全局进程池（如果需要）
        close_pool = kwargs.get('close_pool', True)
        if close_pool:
            try:
                from config.settings import ENABLE_GLOBAL_PROCESS_POOL
                if ENABLE_GLOBAL_PROCESS_POOL:
                    from utils.multiprocessing.global_process_pool import GlobalProcessPool
                    pool = GlobalProcessPool.get_instance()
                    pool.close()
                    logger.info("全局进程池已清理")
            except Exception as e:
                logger.debug(f"清理全局进程池时出错: {e}")
        
        return result
        
    except Exception as e:
        logger.error(f"模块级下载历史数据失败: {e}")
        return {"success": False, "message": str(e)}


def get_local_data(**kwargs) -> Dict[str, Any]:
    """获取本地数据 - 简化的模块级便利函数"""
    logger.info(f"模块级获取本地数据")
    
    try:
        return get_manager().get_local_data(**kwargs)
    except Exception as e:
        logger.error(f"模块级获取本地数据失败: {e}")
        return {}


def check_data_exists(symbol: str, period: str) -> bool:
    """检查数据是否存在 - 模块级便利函数"""
    try:
        return get_manager().check_data_exists(symbol, period)
    except Exception as e:
        logger.error(f"检查数据存在性失败: {e}")
        return False


def get_latest_trade_date(format: str = "YYYYMMDD") -> str:
    """获取最新交易日期 - 模块级便利函数"""
    try:
        return get_manager().get_latest_trade_date(format)
    except Exception as e:
        logger.error(f"获取最新交易日期失败: {e}")
        current_date = datetime.now().strftime("%Y%m%d")
        if format == "YYYY-MM-DD":
            return f"{current_date[:4]}-{current_date[4:6]}-{current_date[6:8]}"
        return current_date


def pd_format(df, data: str = "both", head_rows: int = 5, tail_rows: int = 5, **kwargs):
    """格式化DataFrame - 模块级便利函数"""
    return DataProcessor.format_display_data(df, data, head_rows, tail_rows)


def save_data_to_parquet(data: Dict, period: str, compression: str = 'snappy', 
                        has_new_data: bool = True) -> Dict[str, str]:
    """保存数据到Parquet - 简化的模块级便利函数"""
    logger.info(f"模块级保存数据: {len(data)}个股票")
    
    try:
        from data.storage.parquet_storage import ParquetStorage
        storage = ParquetStorage(base_dir=get_manager().data_root)
        
        save_paths = {}
        for symbol, df in data.items():
            if hasattr(df, 'empty') and not df.empty:
                success = storage.save_data_by_partition_parallel(
                    dataframe=df,
                    symbol=symbol,
                    period=period
                )
                if success:
                    save_paths[symbol] = get_manager().get_data_path(symbol, period)
                    
        return save_paths
        
    except Exception as e:
        logger.error(f"模块级保存数据失败: {e}")
        return {}


def extract_timestamp_from_data(df, end_time: Optional[str] = None) -> Optional[str]:
    """提取时间戳 - 模块级便利函数（使用统一日期提取模块）"""
    return DataProcessor.extract_timestamp_from_data(df, end_time)


def get_data_root() -> str:
    """获取数据根目录 - 模块级便利函数"""
    return get_manager().data_root


if __name__ == "__main__":
    # 测试代码
    print(f"最新交易日期: {get_latest_trade_date()}")
    print(f"数据根目录: {get_data_root()}")