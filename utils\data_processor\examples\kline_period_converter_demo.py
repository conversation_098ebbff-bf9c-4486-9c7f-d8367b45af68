#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
K线周期转换示例脚本

展示如何使用period_converter模块中的功能
将1分钟K线数据转换为任意自定义周期的K线数据
"""

from utils.data_processor.period_converter import (
    convert_kline_period,
    is_supported_by_xtquant,
    get_supported_periods,
    validate_period_string
)
import os
import sys
import pandas as pd
import time
from datetime import datetime

# 导入数据获取功能
from xtquant import xtdata

# 直接使用相对导入
sys.path.append('../../..')  # 添加项目根目录
from data.data_source_manager import pd_format, LogTarget


def download_1m_data(stock_code, start_time, end_time):
    """下载1分钟K线数据"""
    print(f"正在下载 {stock_code} 的1分钟K线数据...")

    # 下载1分钟数据
    xtdata.download_history_data(
        stock_code=stock_code,
        period='1m',
        start_time=start_time,
        end_time=end_time
    )

    # 等待数据下载完成
    time.sleep(2)

    # 获取本地数据
    result = xtdata.get_local_data(
        field_list=[],
        stock_list=[stock_code],
        period='1m',
        start_time=start_time,
        end_time=end_time
    )

    if result and stock_code in result:
        data = result[stock_code]
        if isinstance(data, pd.DataFrame) and not data.empty:
            print(f"成功获取 {len(data)} 行1分钟K线数据!")
            return data

    print(f"未能获取 {stock_code} 的1分钟K线数据")
    return None


def convert_and_save(data_1m, custom_period, output_dir=None):
    """转换并保存自定义周期的K线数据"""
    # 合成自定义周期数据
    print(f"正在将1分钟K线数据转换为{custom_period}周期...")
    data_custom = convert_kline_period(data_1m, custom_period)

    if data_custom is None or data_custom.empty:
        print(f"转换失败，未能生成{custom_period}周期数据")
        return None

    print(f"成功生成 {len(data_custom)} 行 {custom_period} 周期数据!")

    # 保存到CSV文件（如果指定了输出目录）
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"kline_{custom_period}_{timestamp}.csv"
        filepath = os.path.join(output_dir, filename)
        data_custom.to_csv(filepath, index=False)
        print(f"数据已保存到: {filepath}")

    return data_custom


def main():
    """主函数"""
    print("=" * 50)
    print("K线周期转换示例")
    print("=" * 50)

    # 显示原生支持的周期
    try:
        native_periods = get_supported_periods()
        print(f"迅投API原生支持的周期: {', '.join(native_periods)}")
    except Exception as e:
        print(f"获取支持的周期列表失败: {e}")
        native_periods = ["1m", "5m", "15m", "30m", "60m", "1d", "1w", "1M"]
        print(f"使用默认周期列表: {', '.join(native_periods)}")

    # 获取用户输入的股票代码
    stock_code = input("请输入股票代码 (默认: 600000.SH): ").strip() or "600000.SH"

    # 获取用户输入的时间范围
    start_time = input("请输入开始日期 (格式: YYYYMMDD, 默认: 20250401): ").strip() or "20250401"
    end_time = input("请输入结束日期 (格式: YYYYMMDD, 默认: 20250430): ").strip() or "20250430"

    # 获取用户输入的目标周期
    target_period = input("请输入目标周期 (如 '3m', 默认: 3m): ").strip() or "3m"

    # 验证周期格式
    if not validate_period_string(target_period):
        print(f"无效的周期格式: {target_period}")
        return

    # 提示用户
    if is_supported_by_xtquant(target_period):
        print(f"'{target_period}'是迅投API原生支持的周期，可以直接使用API获取")
    else:
        print(f"'{target_period}'不是原生支持的周期，将使用1分钟数据合成")

    # 下载1分钟数据
    data_1m = download_1m_data(stock_code, start_time, end_time)

    if data_1m is None or data_1m.empty:
        print("无法获取1分钟数据，程序退出")
        return

    # 转换并保存数据
    # 查找项目根目录
    current_file = os.path.abspath(__file__)
    project_root = os.path.dirname(os.path.dirname(
        os.path.dirname(os.path.dirname(current_file))))
    output_dir = os.path.join(project_root, "data_output")

    data_custom = convert_and_save(data_1m, target_period, output_dir=output_dir)

    if data_custom is not None and not data_custom.empty:
        # 展示一些数据样例
        print("\n原始1分钟数据样例(前5行):")
        data_1m_formatted = pd_format(
            df=data_1m,
            data="head",
            head_rows=5
        )
        print(f"原始1分钟数据 ({stock_code} - 1m):\n{data_1m_formatted}")

        print(f"\n合成的{target_period}数据样例(前5行):")
        data_custom_formatted = pd_format(
            df=data_custom,
            data="head",
            head_rows=5
        )
        print(f"合成的{target_period}数据 ({stock_code} - {target_period}):\n{data_custom_formatted}")

    print("\n示例运行完成!")


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"示例运行出错: {e}")
        import traceback
        traceback.print_exc()
