#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
时间处理规范强制执行器

在运行时自动检测和阻止使用有时区问题的时间处理方法，确保项目时间处理的统一性。
"""

import warnings
import sys
import functools
from typing import Any, Callable


class TimeProcessingEnforcer:
    """时间处理规范强制执行器"""
    
    def __init__(self):
        self.enabled = True
        self.strict_mode = False  # 严格模式：直接抛出异常而不是警告
        
    def enable_strict_mode(self):
        """启用严格模式：违反规范时直接抛出异常"""
        self.strict_mode = True
        
    def disable_strict_mode(self):
        """禁用严格模式：违反规范时只发出警告"""
        self.strict_mode = False
        
    def _handle_violation(self, message: str, category=UserWarning):
        """处理规范违反"""
        if self.strict_mode:
            raise RuntimeError(f"时间处理规范违反: {message}")
        else:
            warnings.warn(f"⚠️ 时间处理规范违反: {message}", category, stacklevel=3)
    
    def install_pandas_hooks(self):
        """安装pandas时间处理钩子"""
        try:
            import pandas as pd
            
            # 保存原始方法
            original_to_datetime = pd.to_datetime
            original_timestamp = pd.Timestamp.timestamp if hasattr(pd.Timestamp, 'timestamp') else None
            
            def safe_to_datetime(*args, **kwargs):
                self._handle_violation(
                    "使用了pd.to_datetime，请使用utils.smart_time_converter.smart_to_datetime代替"
                )
                return original_to_datetime(*args, **kwargs)
            
            def safe_timestamp(self, *args, **kwargs):
                self._handle_violation(
                    "使用了pd.Timestamp.timestamp()，请使用time.mktime()处理本地时区"
                )
                if original_timestamp:
                    return original_timestamp(self, *args, **kwargs)
                else:
                    # 如果原始方法不存在，使用安全的替代方法
                    import time
                    dt_naive = self.replace(tzinfo=None)
                    return time.mktime(dt_naive.timetuple())
            
            # 替换方法
            pd.to_datetime = safe_to_datetime
            if hasattr(pd.Timestamp, 'timestamp'):
                pd.Timestamp.timestamp = safe_timestamp
                
            print("✅ pandas时间处理安全钩子已安装")
            
        except ImportError:
            print("ℹ️ pandas未安装，跳过pandas钩子安装")
    
    def install_import_hooks(self):
        """安装导入钩子，监控危险的时间处理导入"""
        import builtins
        
        # 保存原始import函数
        original_import = builtins.__import__
        
        def safe_import(name, *args, **kwargs):
            """安全的import函数"""
            module = original_import(name, *args, **kwargs)
            
            # 检查pandas导入
            if name == 'pandas' or name.startswith('pandas.'):
                warnings.warn(
                    "⚠️ 检测到pandas导入。请避免使用pd.to_datetime、pd.Timestamp.timestamp()等有时区问题的方法。\n"
                    "推荐使用:\n"
                    "- utils.smart_time_converter.smart_to_datetime\n"
                    "- utils.time_utils.fast_ms_to_datetime_index\n"
                    "- time.mktime()处理本地时区",
                    UserWarning,
                    stacklevel=2
                )
            
            return module
        
        # 替换import函数
        builtins.__import__ = safe_import
        print("✅ 导入安全钩子已安装")
    
    def create_safe_time_utils(self):
        """创建安全的时间处理工具函数"""
        
        def safe_timestamp_ms(dt):
            """安全的毫秒时间戳转换"""
            import time
            if hasattr(dt, 'replace'):
                # pandas Timestamp或datetime
                dt_naive = dt.replace(tzinfo=None) if hasattr(dt, 'tzinfo') else dt
                return int(time.mktime(dt_naive.timetuple()) * 1000)
            elif isinstance(dt, (int, float)):
                return int(dt * 1000) if dt < 1e10 else int(dt)  # 处理秒和毫秒时间戳
            else:
                raise ValueError(f"无法转换类型 {type(dt)} 为时间戳")
        
        def safe_timestamp_s(dt):
            """安全的秒时间戳转换"""
            import time
            if hasattr(dt, 'replace'):
                # pandas Timestamp或datetime
                dt_naive = dt.replace(tzinfo=None) if hasattr(dt, 'tzinfo') else dt
                return time.mktime(dt_naive.timetuple())
            elif isinstance(dt, (int, float)):
                return dt if dt < 1e10 else dt / 1000  # 处理秒和毫秒时间戳
            else:
                raise ValueError(f"无法转换类型 {type(dt)} 为时间戳")
        
        def safe_datetime_index_to_ms(datetime_index):
            """安全的DatetimeIndex到毫秒时间戳转换"""
            import time
            result = []
            for dt in datetime_index:
                dt_naive = dt.replace(tzinfo=None)
                timestamp_ms = int(time.mktime(dt_naive.timetuple()) * 1000)
                result.append(timestamp_ms)
            return result
        
        # 将这些函数添加到全局命名空间，方便使用
        import builtins
        builtins.safe_timestamp_ms = safe_timestamp_ms
        builtins.safe_timestamp_s = safe_timestamp_s
        builtins.safe_datetime_index_to_ms = safe_datetime_index_to_ms
        
        print("✅ 安全时间处理工具函数已创建")
    
    def install_all_hooks(self):
        """安装所有安全钩子"""
        if not self.enabled:
            return
            
        print("🔧 正在安装时间处理安全钩子...")
        self.install_import_hooks()
        self.install_pandas_hooks()
        self.create_safe_time_utils()
        print("✅ 所有时间处理安全钩子已安装完成")
        print("ℹ️ 现在系统会自动检测和警告时区不安全的时间处理方法")
        
        if self.strict_mode:
            print("⚠️ 严格模式已启用：违反规范将直接抛出异常")
        else:
            print("ℹ️ 警告模式：违反规范将发出警告但继续执行")


# 全局执行器实例
_enforcer = TimeProcessingEnforcer()


def install_time_safety_hooks(strict_mode: bool = False):
    """
    安装时间处理安全钩子
    
    Args:
        strict_mode: 是否启用严格模式（违反规范时抛出异常）
    """
    if strict_mode:
        _enforcer.enable_strict_mode()
    else:
        _enforcer.disable_strict_mode()
        
    _enforcer.install_all_hooks()


def enable_strict_time_safety():
    """启用严格的时间处理安全模式"""
    _enforcer.enable_strict_mode()
    print("⚠️ 时间处理严格模式已启用")


def disable_strict_time_safety():
    """禁用严格的时间处理安全模式"""
    _enforcer.disable_strict_mode()
    print("ℹ️ 时间处理严格模式已禁用")


# 装饰器：用于标记使用了安全时间处理的函数
def safe_time_processing(func: Callable) -> Callable:
    """
    装饰器：标记函数使用了安全的时间处理方法
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        return func(*args, **kwargs)
    
    wrapper._safe_time_processing = True
    return wrapper


# 自动安装钩子（可以通过环境变量控制）
import os
if os.getenv('ENABLE_TIME_SAFETY_HOOKS', '1') == '1':
    install_time_safety_hooks(strict_mode=os.getenv('TIME_SAFETY_STRICT_MODE', '0') == '1')


if __name__ == '__main__':
    # 命令行使用
    if len(sys.argv) > 1:
        if sys.argv[1] == '--strict':
            install_time_safety_hooks(strict_mode=True)
        elif sys.argv[1] == '--test':
            # 测试钩子是否工作
            install_time_safety_hooks(strict_mode=False)
            
            print("\n🧪 测试时间处理安全钩子...")
            try:
                import pandas as pd
                print("测试pd.to_datetime...")
                pd.to_datetime('2025-01-01')  # 应该触发警告
            except Exception as e:
                print(f"测试异常: {e}")
        else:
            install_time_safety_hooks(strict_mode=False)
    else:
        install_time_safety_hooks(strict_mode=False)
