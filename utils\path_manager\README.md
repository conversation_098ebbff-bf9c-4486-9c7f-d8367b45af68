# 路径管理器模块

统一路径管理器 v2.0，支持原始数据和复权数据的分区存储路径管理。

## 功能特性

1. **统一路径构建**：支持原始数据和复权数据的路径构建
2. **复权数据支持**：支持前复权和后复权数据的路径管理
3. **智能路径检测**：自动检测和修复路径问题
4. **性能优化**：路径缓存和批量处理优化
5. **单例模式**：全局统一的路径管理实例

## 路径格式（v2.0 新格式）

```
data/
├── raw/                    # 原始数据
│   ├── SZ/000001/1d/      # 股票原始数据
│   ├── SH/600000/1d/      # 股票原始数据
│   └── DF/pp00/1d/        # 期货原始数据
└── adjusted/               # 复权数据（新格式）
    ├── front/              # 前复权数据
    │   ├── SZ/000001/1d/  # 前复权股票数据
    │   └── SH/600000/1d/  # 前复权股票数据
    └── back/               # 后复权数据
        ├── SZ/000001/1d/  # 后复权股票数据
        └── SH/600000/1d/  # 后复权股票数据
```

**路径格式变更说明：**
- 旧格式：`adjusted/{market}/{code}/{adj_type}/{period}`
- 新格式：`adjusted/{adj_type}/{market}/{code}/{period}`
- 优势：按复权类型分组，便于批量处理和管理

## 使用方法

### 基本使用

```python
from utils.path_manager.unified_path_manager import get_path_manager

# 获取路径管理器实例
path_manager = get_path_manager()

# 获取原始数据路径
raw_path = path_manager.get_file_path(
    symbol="600000.SH",
    period="1d",
    date=datetime(2025, 1, 15),
    data_type="raw"
)

# 获取前复权数据路径
front_path = path_manager.get_file_path(
    symbol="600000.SH",
    period="1d",
    date=datetime(2025, 1, 15),
    data_type="adjusted",
    adj_type="front"
)

# 获取后复权数据路径
back_path = path_manager.get_file_path(
    symbol="600000.SH",
    period="1d",
    date=datetime(2025, 1, 15),
    data_type="adjusted",
    adj_type="back"
)
```

### 批量处理优化

```python
from utils.performance.adjustment_batch_optimizer import AdjustmentBatchOptimizer

# 创建批量优化器
optimizer = AdjustmentBatchOptimizer()

# 按复权类型扫描数据
front_data = optimizer.scan_adjustment_data_by_type("front")
back_data = optimizer.scan_adjustment_data_by_type("back")

# 批量加载数据
loaded_data = optimizer.batch_load_by_adjustment_type("front")

# 获取访问统计
stats = optimizer.get_access_statistics()
```

## 数据迁移

### 路径格式迁移

如需从旧格式迁移到新格式，使用迁移工具：

```python
from utils.data_migration import PathFormatMigrator

# 创建迁移器
migrator = PathFormatMigrator(data_root)

# 扫描旧格式文件
old_files = migrator.scan_old_format_files()

# 执行迁移
migration_result = migrator.migrate_all_files(old_files)

# 清理旧文件（可选）
cleanup_result = migrator.cleanup_old_files(
    migration_result["migration_results"], 
    confirm=True
)
```

### 使用迁移脚本

```bash
# 模拟运行
python scripts/migrate_path_format.py --dry-run

# 实际迁移
python scripts/migrate_path_format.py --max-workers 4

# 迁移但不清理旧文件
python scripts/migrate_path_format.py --no-cleanup
```

## 性能优化

### 新格式优势

1. **批量处理**：按复权类型分组，便于批量加载和处理
2. **存储管理**：不同复权类型独立管理，便于维护
3. **访问模式**：支持按复权类型的访问模式优化
4. **缓存策略**：可针对不同复权类型制定缓存策略

### 性能监控

```python
# 获取访问统计
stats = optimizer.get_access_statistics()

# 获取优化建议
recommendations = optimizer.optimize_cache_strategy()

# 清理未使用数据
cleanup_stats = optimizer.cleanup_unused_data(days_threshold=30)
```

## 测试验证

运行完整的系统验证测试：

```bash
python tests/test_path_format_migration_verification.py
```

测试包括：
1. 统一路径管理器功能验证
2. 数据存储和读取功能验证
3. 批量优化功能验证
4. 性能对比测试

## 注意事项

1. **向后兼容性**：新版本不保持向后兼容，需要完整迁移
2. **数据备份**：迁移前请确保数据备份
3. **系统停机**：建议在系统停机时进行迁移
4. **完整性验证**：迁移后进行完整性验证

## 版本历史

- **v2.0**：支持新路径格式，优化复权数据管理
- **v1.0**：基础路径管理功能

## 相关文档

- [数据迁移工具文档](../data_migration/README.md)
- [性能优化文档](../performance/README.md)
- [项目主文档](../../README.md)
