# Period Converter 故障排除指南

## 概述

本文档提供period_converter模块常见问题的诊断和解决方案，特别是针对2025-07-27修复的self调用错误问题。

## 常见问题

### 1. NameError: name 'self' is not defined

**问题描述：**
```
【ETT2L0】2025-07-27 02:43:58,316 | ..._processor.period_converter【             resample_1m_kline】(  524)|      ERROR | 创建time列时出错: name 'self' is not defined
```

**根本原因：**
- resample_1m_kline是独立函数，不是类方法
- 错误使用了self._safe_datetime_index_to_ms()调用

**解决方案：**
已在2025-07-27修复，使用utils.time_utils模块的标准函数：
```python
# 修复前（错误）
result['time'] = self._safe_datetime_index_to_ms(result.index) // 10**6

# 修复后（正确）
timestamp_ms_list = datetime_index_to_ms_list(result.index)
result['time'] = [ts // 1000 for ts in timestamp_ms_list]
```

### 2. 时间格式不一致错误

**问题描述：**
```
时间戳 (毫秒): 1753061700, (秒): 1753061.7
UTC时间: 1970-01-21 14:57:41.700000
Cannot convert tz-naive timestamps, use tz_localize to localize
```

**根本原因：**
time列格式不一致，错误地将毫秒时间戳转换为秒级时间戳

**解决方案：**
使用智能时间转换器并保持毫秒时间戳格式：
```python
from utils.smart_time_converter import smart_to_datetime

# 正确的时间转换方式
result['time'] = timestamp_ms_list  # 保持毫秒时间戳格式
temp_datetime = smart_to_datetime(result['time'], unit='ms')
```

### 3. K线重采样参数错误

**问题描述：**
合成的K线数据与交易软件数据不匹配，时间包含范围错误

**根本原因：**
pandas resample的closed参数设置错误

**解决方案：**
使用正确的交易软件标准参数：
```python
# 正确的重采样参数
resampled = df.resample(rule, closed='right', label='right')

# 含义：
# closed='right': 区间右闭左开 (09:35, 09:40]
# label='right': K线标签位于区间右侧
# 09:40的5分钟K线包含09:36-09:40的数据
```

### 4. 数据合成失败

**问题描述：**
股票数据合成失败，返回空DataFrame

**诊断步骤：**
1. 检查输入数据格式
2. 验证时间列是否存在
3. 确认索引类型是否正确

**解决方案：**
```python
# 确保输入数据包含必要的列
required_columns = ['open', 'high', 'low', 'close', 'volume']
if not all(col in df.columns for col in required_columns):
    logger.error(f"缺少必要的列: {missing_columns}")
```

## 最佳实践

### 1. 时间处理标准

- 统一使用智能时间转换器 utils.smart_time_converter
- 使用smart_to_datetime进行所有时间转换
- time列保持毫秒时间戳格式（13位数字）
- 避免直接使用pd.to_datetime（存在时区偏移问题）

### 2. K线重采样标准

- 使用closed='right', label='right'参数
- 确保K线包含范围与交易软件一致
- 验证重采样结果的OHLCV逻辑

### 3. 错误处理

- 捕获TimeConversionError异常
- 添加详细的调试日志
- 验证输入数据的有效性

### 3. 测试验证

运行测试确保功能正常：
```bash
python tests/test_period_converter_fix.py
```

## 调试技巧

### 1. 启用详细日志

```python
import logging
logging.getLogger('utils.data_processor.period_converter').setLevel(logging.DEBUG)
```

### 2. 检查数据格式

```python
print(f"数据形状: {df.shape}")
print(f"列名: {list(df.columns)}")
print(f"索引类型: {type(df.index)}")
print(f"time列类型: {type(df['time'].iloc[0])}")
```

### 3. 验证时间转换

```python
from utils.time_utils import datetime_to_ms
import datetime

# 测试时间转换
dt = datetime.datetime(2025, 7, 27, 9, 30, 0)
ts = datetime_to_ms(dt)
print(f"转换结果: {dt} -> {ts}")
```

## 版本历史

### v2.1 (2025-07-27)
- 修复resample_1m_kline函数中的self调用错误
- 标准化时间处理模块使用
- 增强错误处理和日志记录
- 添加单元测试验证

### v2.0 (2025-07-23)
- 修复索引名称与time列冲突问题
- 修复A股集合竞价成交量处理逻辑

## 联系支持

如果遇到本文档未涵盖的问题，请：
1. 检查日志文件中的详细错误信息
2. 运行测试用例验证环境
3. 查看相关模块的文档说明
