---
description: 
globs: "*"
alwaysApply: true
---
# 量化交易系统

这是一个主要基于迅投接口，其他接口为辅的量化交易系统项目。

## 项目结构

```
quant/
│
├── .gitignore                # Git忽略文件配置
├── .gitmessage               # 提交信息模板
├── .pre-commit-config.yaml   # 预提交钩子配置
│
├── config/                   # 配置目录
│   ├── __init__.py
│   ├── settings.py           # 全局设置
│   ├── symbols.py            # 交易品种配置
│   └── logging_config.py     # 日志配置
│
├── data/                     # 数据层
│   ├── __init__.py
│   ├── data_source_manager.py # 数据源管理器，提供统一数据源访问入口
│   ├── data_main.py          # 数据模块主入口，提供交互式功能菜单
│   ├── fetcher/              # 数据获取模块
│   ├── processor/            # 数据处理模块
│   ├── storage/              # 数据存储管理模块
│   ├── quoter/               # 行情引擎模块
│   └── log/                  # 数据日志模块
│
├── utils/                    # 工具模块
│   ├── __init__.py
│   ├── logger.py             # 日志工具
│   ├── metrics.py            # 指标计算工具
│   ├── datetime_utils.py     # 日期时间工具
│   ├── formatter.py          # 文本格式化和表格生成工具
│   └── text_parser.py        # 文本解析工具
│
├── strategy/                 # 策略层
│   ├── __init__.py
│   ├── base.py               # 策略基类
│   ├── factors/              # 因子模块
│   │   ├── __init__.py
│   │   ├── technical_factors.py   # 技术因子
│   │   └── fundamental_factors.py # 基本面因子
│   ├── signal/               # 信号生成模块
│   │   ├── __init__.py
│   │   ├── signal_generator.py    # 信号生成器
│   │   └── signal_combiner.py     # 信号合成器
│   └── strategies/           # 策略实现
│       ├── __init__.py
│       ├── trend_following.py     # 趋势跟踪策略
│       └── mean_reversion.py      # 均值回归策略
│
├── backtest/                 # 回测层
│   ├── __init__.py
│   ├── engine.py             # 回测引擎
│   ├── performance.py        # 性能分析
│   ├── optimizer.py          # 参数优化器
│   └── report_generator.py   # 报告生成器
│
├── trading/                  # 交易层
│   ├── __init__.py
│   ├── order/                # 订单管理
│   │   ├── __init__.py
│   │   ├── order_manager.py       # 订单管理器
│   │   └── order_types.py         # 订单类型
│   ├── execution/            # 执行管理
│   │   ├── __init__.py
│   │   ├── executor.py            # 执行器
│   │   └── router.py              # 路由器
│   ├── account/              # 账户管理
│   │   ├── __init__.py
│   │   ├── account_manager.py     # 账户管理器
│   │   └── position_manager.py    # 持仓管理器
│   └── broker/               # 券商接口
│       ├── __init__.py
│       └── xtquant_broker.py     # 迅投券商接口
│
├── risk/                     # 风控层
│   ├── __init__.py
│   ├── pre_trade.py          # 交易前风控
│   ├── position_control.py   # 仓位控制
│   ├── fund_control.py       # 资金控制
│   └── risk_manager.py       # 风险管理器
│
├── monitor/                  # 监控层
│   ├── __init__.py
│   ├── system_monitor.py     # 系统监控
│   ├── performance_monitor.py     # 性能监控
│   └── alert.py              # 告警模块
│
├── tests/                    # 测试目录
│   ├── __init__.py
│   ├── test_data.py
│   ├── test_strategy.py
│   ├── test_backtest.py
│   └── test_trading.py
│
├── examples/                 # 示例目录
│   ├── __init__.py
│   ├── simple_strategy.py
│   └── backtest_example.py
│
├── cursor_config/            # Cursor编辑器配置
│   ├── user_rules.txt        # Cursor用户规则
│   └── mcp.txt               # MCP配置
│
├── reference_files/          # 参考文件
│
├── docs/                     # 文档目录
│   └── ...                   # 各种文档
│
├── logs/                     # 日志目录
│   └── ...                   # 日志文件
│
├── main.py                   # 主入口
└── run_backtest.py           # 回测入口
```

572783748537

### Git相关文件说明

#### 根目录Git配置文件
- `.gitignore`: 配置需要排除跟踪的文件和目录
- `.gitmessage`: 标准化提交信息的模板
- `.pre-commit-config.yaml`: 配置提交前的自动检查

### 缓存管理系统
项目中使用的缓存机制已移至项目外部管理。

## 开发环境

- Python 3.12.9
- Conda 环境管理

## 安装说明

详细安装步骤请参考项目文档。 (如果项目有更详细的独立安装文档，此处可链接)

## 系统架构概述

系统采用模块化设计，主要包含以下组件：

- **数据层**: 负责数据的获取、处理和存储
- **策略层**: 实现各类交易策略
- **回测层**: 提供历史数据回测功能
- **交易层**: 实现交易执行和账户管理
- **风控层**: 进行风险控制
- **工具层**: 提供各类辅助工具
- **监控层**: 监控系统和交易表现

## 工具模块 (utils)

### 工具模块概述
工具模块提供全项目共享的辅助功能，包含多个专用工具，支持日志记录、日期时间处理、文本格式化及解析等功能。

### 主要工具组件

#### 日志工具 (logger.py)
- 提供统一的日志记录接口
- 支持多级别日志
- 可配置日志输出格式和目标

#### 日期时间工具 (datetime_utils.py)
- 提供日期时间转换功能，支持多种格式
- 实现交易日历功能
- 提供时区转换和时间戳处理功能
- 支持标准化时间序列数据

##### 日期时间工具主要功能
1. **时间戳与日期时间转换**:
   - `timestamp_to_datetime`: 将时间戳转换为datetime对象，支持多种时间单位和时区
   - `datetime_to_timestamp`: 将datetime对象转换为时间戳

2. **时区处理**:
   - `localize_datetime`: 为datetime对象添加时区信息
   - `convert_timezone`: 在不同时区间转换datetime对象

3. **周期格式化**:
   - `get_date_format_for_period`: 根据数据周期获取合适的日期格式
   - `format_datetime_with_period`: 根据数据周期格式化日期时间

4. **时间序列处理**:
   - `standardize_timestamp_series`: 将时间戳Series标准化为datetime64[ns, tz]类型

#### 文本格式化工具 (formatter.py)
- 计算文本显示宽度，支持中英文混合场景
- 格式化列文本，确保对齐
- 将DataFrame转换为格式化文本表格
- 支持将DataFrame以表格形式写入文件

##### 文本格式化工具主要功能
1. **文本宽度计算**:
   - `get_display_width`: 计算字符串在等宽字体终端中的显示宽度，中文字符占用2个单位宽度

2. **列格式化**:
   - `format_column`: 根据文本的实际显示宽度，确保列对齐

3. **表格生成**:
   - `dataframe_to_text_table`: 将DataFrame转换为格式化的文本表格，自动对齐列
   - `write_dataframe_to_file`: 将DataFrame以格式化文本表格的形式写入文件

#### 文本解析工具 (text_parser.py)
- 清理输入文本，去除无关内容
- 从文本中提取代码标识符
- 从文本中提取名称文本
- 解析表格样式文本和日期格式
- 标准化时间周期字符串

##### 文本解析工具主要功能
1. **文本清理**:
   - `clean_input_text`: 清理输入文本，去除常见的无关内容

2. **代码提取**:
   - `extract_codes`: 从文本中提取代码标识符（如股票代码、期货合约代码等）

3. **名称提取**:
   - `extract_names`: 从文本中提取名称（如板块名称、概念名称等）

4. **表格解析**:
   - `parse_table_like_text`: 解析类表格文本（如CSV、TSV或空格分隔的表格）

5. **日期解析**:
   - `extract_date_patterns`: 从文本中提取日期格式（如YYYYMMDD、YYYY-MM-DD等）
   - `parse_period_string`: 解析时间周期字符串，标准化为一致的格式

### 工具使用示例

#### 文本格式化示例
```python
from utils.formatter import dataframe_to_text_table
import pandas as pd

# 创建示例DataFrame
df = pd.DataFrame({
    'Code': ['000001.SZ', '600000.SH'],
    'Name': ['平安银行', '浦发银行'],
    'Price': [15.23, 8.45],
    'Change': [0.05, -0.02]
})

# 转换为文本表格
table_text = dataframe_to_text_table(
    df=df,
    title="股票行情",
    include_index=False,
    float_precision=2
)
print(table_text)
```

#### 日期时间处理示例
```python
from utils.datetime_utils import timestamp_to_datetime, format_datetime_with_period

# 将时间戳转换为datetime对象
dt = timestamp_to_datetime(1651324800000, unit='ms', tz='Asia/Shanghai')
print(dt)  # 2022-05-01 00:00:00+08:00

# 根据数据周期格式化日期时间
formatted = format_datetime_with_period(dt, period='1d')
print(formatted)  # 2022-05-01
```

#### 文本解析示例
```python
from utils.text_parser import extract_codes, extract_names

# 从文本中提取股票代码
text = """
我的股票列表:
1. 600000.SH (浦发银行)
2. 000001.SZ (平安银行)
"""
codes = extract_codes(text)
print(codes)  # ['600000.SH', '000001.SZ']

# 从文本中提取名称
names = extract_names(text, min_length=3)
print(names)  # ['浦发银行', '平安银行']
```

## 数据存储与管理

### 数据存储策略与规范

- **外部数据存储设计**:
    - **严格禁止**在项目内存储任何数据文件，所有数据必须存放在项目外部。
- **数据路径配置**:
  1. 在`config/settings.py`中配置默认数据根目录路径。
  2. 支持通过环境变量、命令行参数或配置文件覆盖默认路径。
  3. 路径管理器(例如，项目中规划的`path_manager.py`或使用专门的路径管理工具/模块)负责处理所有数据路径相关逻辑。
- **标准路径格式**:
  系统采用标准化的数据存储结构，数据文件保存在独立的数据目录中：
  ```
  <数据根目录>/<交易所>/<股票代码>/<周期>.parquet
  
  例如:
  D:\\data\\SZ\\000001\\tick.parquet
  D:\\data\\SH\\600000\\1d.parquet
  ```
- **存储格式标准**:
    - 使用列式存储格式`parquet`，支持高效的数据压缩和随机访问。
    - **时间序列标准化**：所有时间数据使用统一时区（上海时区 Asia/Shanghai）。
    - **字段命名标准**：使用一致的字段命名约定（如open, high, low, close, volume）。

### 数据获取与使用方式

系统提供两种主要方式获取和存储数据：

1.  **Parquet文件存储**: 主要用于大型历史数据存储，支持按需加载，高效压缩。
2.  **数据库存储**: 用于需要高频查询的数据，支持更丰富的查询功能 (可根据项目实际情况补充具体方案，如`database_handler.py`, `database_storage.py`)。

### 数据使用示例

#### 从外部路径下载数据

```python
from xtquant import xtdata as xt_data
xt_data.enable_hello = False

# 下载数据示例
xt_data.download_history_data2(
    stock_list=["600000.SH"],
    period="1d",
    start_time="20230101",
    end_time="20231231",
    save_dir="D:\\\\data"  # 注意：保存到项目外部
)

# 读取数据示例
data = xt_data.get_local_data(
    field_list=["open", "high", "low", "close", "volume"],
    stock_list=["600000.SH"],
    period="1d",
    start_time="20230101",
    end_time="20231231"
)
```

#### 从外部路径加载数据

```python
from data.data_manager import DataManager # 假设有此管理器

# 初始化数据管理器
dm = DataManager() # 假设的管理器

# 从指定目录加载数据
data = dm.load_data(
    symbols=['000001', '600000'],
    period='1d',
    start_date='20230101',
    end_date='20231231',
    data_dir='d:\\data' # 示例路径
)

# 使用数据
if data:
    for symbol, df in data.items():
        print(f"{symbol} 数据行数: {len(df)}")
        print(df.head())
```

## 数据获取与处理架构

### 数据模块主要组件
- **数据源管理器** (例如 `data/data_source_manager.py`): 
  1. 提供统一的数据源访问入口。
  2. 集成管理各种数据源，支持多数据源切换。
  3. 实现数据源回落机制，保证数据获取可靠性。
  4. 简化数据获取流程，作为获取各类市场数据的中心接口。

- **模块交互入口** (例如 `data/data_main.py`):
  1. 提供交互式菜单界面，管理数据模块下的各项功能。
  2. 支持历史数据下载、数据存储管理、数据处理和查询等功能。
  3. 统一封装子模块功能，提供友好的用户界面。

### 多元化数据源支持
- **统一数据源接口设计**:
  1. (例如 `data/fetcher/base.py`) 定义数据源基类接口，所有数据源实现必须继承并实现统一接口。
  2. 支持无缝切换不同数据源，保证策略代码稳定性。
  3. 通过工厂模式 (例如 `data/fetcher/factory.py`) 动态创建和管理数据源实例。

- **主要数据源**:
  1. **迅投量化API** (例如 `data/fetcher/xtquant_fetcher.py`): 作为主要数据源。
     - 支持实时行情、历史数据和财务数据获取。
     - 默认使用 `download_history_data2` 方法下载数据，提供更高性能和稳定性。
     - 支持多种数据周期，包括tick、分钟、日线等。
     - 提供丰富的level1/level2市场数据。

- **数据源扩展机制**:
  1. 支持通过实现基类接口快速接入新数据源。
  2. 数据源优先级和故障切换配置化管理。
  3. 提供数据源性能监控和统计。

### 数据存储组件
- **存储路径管理** (例如 `data/storage/path_manager.py` 或通过 `config/settings.py` 和 `data_source_manager.py` 协同管理):
  1. 管理所有数据文件的存储路径。
  2. 提供标准化路径生成和解析功能。
  3. 支持多种路径配置方式。

- **Parquet文件存储** (例如 `data/storage/parquet_handler.py`, `data/storage/parquet_storage.py`):
  1. 实现高效的列式存储方案。
  2. 支持数据压缩和快速随机访问。
  3. 针对量化交易数据特点优化存储结构。

- **数据库存储方案** (例如 `data/storage/database_handler.py`, `data/storage/database_storage.py`):
  1. 支持将数据存储到关系型或时序数据库。
  2. 提供高效的查询和过滤功能。
  3. 适用于需要频繁查询的场景。

### 数据获取与存储流程
- **数据获取**:
  1. 数据源管理器根据配置从多个数据源获取数据，默认优先使用迅投API。
  2. 下载的数据统一存储到外部配置的路径，保持相同存储结构。
  3. 所有数据源支持增量更新，避免重复下载。
  4. 支持数据源故障时自动切换到备用数据源。

- **数据读取**:
  1. 所有模块通过数据源管理器统一访问数据，无需关心底层数据源。
  2. 支持按需加载，减少内存占用。
  3. 提供数据缓存机制，优化频繁访问的数据。

## 数据处理优化建议

### Tick数据处理性能优化
- **使用`datatable`替代`pandas`**: 针对tick级高频数据，推荐使用`datatable`处理，其处理速度比`pandas`快3-10倍，内存占用仅为`pandas`的30-50%。
- **处理流程优化**:
  1. 使用xtquant下载历史tick数据并保存为外部parquet格式。
  2. 使用`datatable`从外部路径加载parquet数据进行高性能计算。
  3. 对于实时tick数据，使用xtquant的`subscribe_quote()`接口订阅。
- **内存管理**:
  1. 实现数据流式处理，边处理边释放资源。
  2. 设置内存使用上限，避免OOM。
- **并行计算**:
  1. 利用`datatable`原生多线程特性处理大规模tick数据。
  2. 实现数据并行加载，提高回测速度。

## 安装与配置

### 安装依赖

```
pip install -r requirements.txt
```
(确保 `requirements.txt` 文件是最新的且在项目根目录或正确路径下)

### 配置参数

编辑 `config/settings.py` 文件以配置系统参数，主要配置项：

- **数据目录**: 设置数据存储的根目录。
- **日志配置**: 设置日志记录方式。
- **交易参数**: 设置交易相关参数。

## 使用示例

### 下载数据示例

运行示例脚本下载数据：
```
python examples/download_data.py
```
(确保 `examples/download_data.py` 脚本存在且功能正确)

### 加载与分析数据示例
```
python examples/load_data.py -s 600000 -p 1d -d d:\\data
```
(确保 `examples/load_data.py` 脚本存在且能按此方式调用)

## 贡献方式

欢迎通过以下方式贡献：

1.  提交Issue报告bugs或提出新功能
2.  提交Pull Request贡献代码

## 许可证

[MIT License](mdc:LICENSE) 
(确保项目中包含一个 `LICENSE` 文件，通常是 `LICENSE.md` 或 `LICENSE.txt`)

