#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
指标计算模块，提供各类金融和回测指标的计算功能
"""

from typing import Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd


def calculate_returns(
    prices: Union[pd.Series, np.ndarray], log_returns: bool = False
) -> pd.Series:
    """
    计算收益率序列

    Args:
        prices: 价格序列
        log_returns: 是否计算对数收益率，默认为False

    Returns:
        pd.Series: 收益率序列
    """
    if isinstance(prices, np.ndarray):
        prices = pd.Series(prices)

    if log_returns:
        return np.log(prices / prices.shift(1)).dropna()
    else:
        return (prices / prices.shift(1) - 1).dropna()


def calculate_cumulative_returns(returns: pd.Series) -> pd.Series:
    """
    计算累积收益率

    Args:
        returns: 收益率序列

    Returns:
        pd.Series: 累积收益率序列
    """
    return (1 + returns).cumprod() - 1


def calculate_annualized_return(
    returns: pd.Series, periods_per_year: int = 252
) -> float:
    """
    计算年化收益率

    Args:
        returns: 收益率序列
        periods_per_year: 一年的周期数，日线为252，小时线为252*6.5，分钟线需要相应调整

    Returns:
        float: 年化收益率
    """
    cumulative_return = calculate_cumulative_returns(returns).iloc[-1]
    n_periods = len(returns)
    return float(float((1 + cumulative_return) ** (periods_per_year / n_periods) - 1))


def calculate_volatility(returns: pd.Series, periods_per_year: int = 252) -> float:
    """
    计算波动率（年化标准差）

    Args:
        returns: 收益率序列
        periods_per_year: 一年的周期数

    Returns:
        float: 年化波动率
    """
    return float(float(returns.std() * np.sqrt(periods_per_year)))


def calculate_sharpe_ratio(
    returns: pd.Series, risk_free_rate: float = 0.0, periods_per_year: int = 252
) -> float:
    """
    计算夏普比率

    Args:
        returns: 收益率序列
        risk_free_rate: 无风险利率，年化，默认为0
        periods_per_year: 一年的周期数

    Returns:
        float: 夏普比率
    """
    # 将年化无风险利率转换为对应周期的利率
    rf_period = (1 + risk_free_rate) ** (1 / periods_per_year) - 1

    excess_returns = returns - rf_period
    annual_excess_return = calculate_annualized_return(excess_returns, periods_per_year)
    annual_volatility = calculate_volatility(returns, periods_per_year)

    if annual_volatility == 0:
        return 0.0

    return annual_excess_return / annual_volatility


def calculate_sortino_ratio(
    returns: pd.Series, risk_free_rate: float = 0.0, periods_per_year: int = 252
) -> float:
    """
    计算索提诺比率

    Args:
        returns: 收益率序列
        risk_free_rate: 无风险利率，年化，默认为0
        periods_per_year: 一年的周期数

    Returns:
        float: 索提诺比率
    """
    # 将年化无风险利率转换为对应周期的利率
    rf_period = (1 + risk_free_rate) ** (1 / periods_per_year) - 1

    excess_returns = returns - rf_period
    annual_excess_return = calculate_annualized_return(excess_returns, periods_per_year)

    # 计算下行波动率
    negative_returns = excess_returns[excess_returns < 0]
    if len(negative_returns) == 0:
        return float(float(float("inf")))  # 没有负收益，返回无限大

    downside_deviation = np.sqrt(np.mean(negative_returns**2)) * np.sqrt(
        periods_per_year
    )

    if downside_deviation == 0:
        return float(float(0.0))

    return float(float(annual_excess_return / downside_deviation))


def calculate_max_drawdown(
    returns: pd.Series,
) -> Tuple[float, pd.Timestamp, pd.Timestamp]:
    """
    计算最大回撤及其发生的时间段

    Args:
        returns: 收益率序列

    Returns:
        Tuple[float, pd.Timestamp, pd.Timestamp]: (最大回撤, 峰值时间, 谷值时间)
    """
    # 计算累积收益
    wealth_index = (1 + returns).cumprod()

    # 计算累积最大值
    rolling_max = wealth_index.cummax()

    # 计算回撤
    drawdown = wealth_index / rolling_max - 1

    # 寻找最大回撤及其对应时间
    max_drawdown = drawdown.min()

    # 获取峰值和谷值的时间
    if isinstance(max_drawdown, pd.Series):
        max_drawdown = max_drawdown.iloc[0]  # 如果是Series，取第一个值

    valley_idx = drawdown.idxmin()

    # 从谷值向前找到对应的峰值
    if pd.isna(valley_idx):
        peak_idx = pd.Timestamp("2000-01-01")  # 一个默认值
    else:
        wealth_idx_till_valley = wealth_index.loc[:valley_idx]
        peak_idx = rolling_max.loc[valley_idx]
        peak_idx = wealth_idx_till_valley[wealth_idx_till_valley == peak_idx].index[0]

    return max_drawdown, peak_idx, valley_idx


def calculate_calmar_ratio(returns: pd.Series, periods_per_year: int = 252) -> float:
    """
    计算卡玛比率 (年化收益率 / 最大回撤)

    Args:
        returns: 收益率序列
        periods_per_year: 一年的周期数

    Returns:
        float: 卡玛比率
    """
    annual_return = calculate_annualized_return(returns, periods_per_year)
    max_drawdown, _, _ = calculate_max_drawdown(returns)

    if max_drawdown == 0:
        return float("inf")  # 避免除以零

    return -annual_return / max_drawdown


def calculate_omega_ratio(
    returns: pd.Series,
    risk_free_rate: float = 0.0,
    target_return: float = 0.0,
    periods_per_year: int = 252,
) -> float:
    """
    计算欧米伽比率

    Args:
        returns: 收益率序列
        risk_free_rate: 无风险利率，年化，默认为0
        target_return: 目标收益率，年化，默认为0
        periods_per_year: 一年的周期数

    Returns:
        float: 欧米伽比率
    """
    # 将年化目标收益率转换为对应周期的收益率
    target_return_period = (1 + target_return) ** (1 / periods_per_year) - 1

    # 计算超额收益
    excess_returns = returns - target_return_period

    # 将收益分为正收益和负收益
    positive_returns = excess_returns[excess_returns > 0]
    negative_returns = excess_returns[excess_returns < 0]

    # 如果没有负收益，返回无穷大
    if len(negative_returns) == 0:
        return float(float(float("inf")))

    # 如果没有正收益，返回0
    if len(positive_returns) == 0:
        return float(float(0.0))

    # 计算正收益和负收益的期望值
    expected_positive = positive_returns.mean()
    expected_negative = -negative_returns.mean()  # 注意取负值

    if expected_negative == 0:
        return float(float(float("inf")))

    return float(float(expected_positive / expected_negative))


def calculate_win_rate(returns: pd.Series) -> float:
    """
    计算胜率

    Args:
        returns: 收益率序列

    Returns:
        float: 胜率（0-1之间）
    """
    total_trades = len(returns)
    if total_trades == 0:
        return 0.0

    winning_trades = len(returns[returns > 0])
    return winning_trades / total_trades


def calculate_profit_factor(returns: pd.Series) -> float:
    """
    计算盈亏比

    Args:
        returns: 收益率序列

    Returns:
        float: 盈亏比
    """
    gains = returns[returns > 0].sum()
    losses = -returns[returns < 0].sum()  # 取绝对值

    if losses == 0:
        return float(float(float("inf") if gains > 0 else 0.0))

    return float(float(gains / losses))


def calculate_expectancy(returns: pd.Series) -> float:
    """
    计算期望值

    Args:
        returns: 收益率序列

    Returns:
        float: 期望值
    """
    win_rate = calculate_win_rate(returns)

    # 计算平均盈利和平均亏损
    gains = returns[returns > 0]
    losses = returns[returns < 0]

    avg_gain = gains.mean() if len(gains) > 0 else 0
    avg_loss = losses.mean() if len(losses) > 0 else 0

    # 计算期望值
    return win_rate * avg_gain + (1 - win_rate) * avg_loss


def calculate_cagr(returns: pd.Series, periods_per_year: int = 252) -> float:
    """
    计算复合年增长率 (CAGR)

    Args:
        returns: 收益率序列
        periods_per_year: 一年的周期数

    Returns:
        float: CAGR
    """
    # 计算累积收益
    total_return = calculate_cumulative_returns(returns).iloc[-1]

    # 计算投资时间（年）
    n_periods = len(returns)
    n_years = n_periods / periods_per_year

    if n_years == 0:
        return float(float(0.0))

    # 计算CAGR
    return float(float((1 + total_return) ** (1 / n_years) - 1))


def calculate_var(returns: pd.Series, alpha: float = 0.05) -> float:
    """
    计算风险价值 (VaR)

    Args:
        returns: 收益率序列
        alpha: 置信水平，默认为0.05（95%置信度）

    Returns:
        float: VaR值（正值表示损失）
    """
    return float(float(-np.percentile(returns, alpha * 100)))


def calculate_cvar(returns: pd.Series, alpha: float = 0.05) -> float:
    """
    计算条件风险价值 (CVaR)

    Args:
        returns: 收益率序列
        alpha: 置信水平，默认为0.05（95%置信度）

    Returns:
        float: CVaR值（正值表示损失）
    """
    var = calculate_var(returns, alpha)
    return float(float(-returns[returns <= -var].mean()))


def calculate_beta(returns: pd.Series, market_returns: pd.Series) -> float:
    """
    计算beta系数

    Args:
        returns: 策略收益率序列
        market_returns: 市场收益率序列

    Returns:
        float: beta系数
    """
    # 确保日期对齐
    aligned_returns = returns.align(market_returns, join="inner")

    # 计算beta
    covariance = np.cov(aligned_returns[0], aligned_returns[1])[0, 1]
    market_variance = np.var(aligned_returns[1])

    if market_variance == 0:
        return float(float(0.0))

    return float(float(covariance / market_variance))


def calculate_alpha(
    returns: pd.Series,
    market_returns: pd.Series,
    risk_free_rate: float = 0.0,
    periods_per_year: int = 252,
) -> float:
    """
    计算Alpha值(与市场相比的超额收益)

    Args:
        returns: 策略收益率序列
        market_returns: 市场基准收益率序列
        risk_free_rate: 无风险利率(年化)
        periods_per_year: 一年的交易周期数,默认252个交易日

    Returns:
        Alpha值
    """
    if returns.empty or market_returns.empty:
        return 0.0

    # 确保两个序列有相同的索引
    common_idx = returns.index.intersection(market_returns.index)
    if len(common_idx) == 0:
        return 0.0

    returns = returns.loc[common_idx]
    market_returns = market_returns.loc[common_idx]

    # 将无风险利率转换为每期收益率
    # 注释掉未使用的变量rf_period
    # rf_period = (1 + risk_free_rate) ** (1 / periods_per_year) - 1

    # 计算Beta
    beta = calculate_beta(returns, market_returns)

    # 计算年化收益率
    ann_return = calculate_annualized_return(returns, periods_per_year)
    ann_market_return = calculate_annualized_return(market_returns, periods_per_year)

    # 计算Alpha
    alpha = ann_return - risk_free_rate - beta * (ann_market_return - risk_free_rate)

    return alpha


def calculate_information_ratio(
    returns: pd.Series, benchmark_returns: pd.Series, periods_per_year: int = 252
) -> float:
    """
    计算信息比率

    Args:
        returns: 策略收益率序列
        benchmark_returns: 基准收益率序列
        periods_per_year: 一年的周期数

    Returns:
        float: 信息比率
    """
    # 确保日期对齐
    aligned_returns = returns.align(benchmark_returns, join="inner")
    strategy_returns = aligned_returns[0]
    benchmark_returns = aligned_returns[1]

    # 计算超额收益
    excess_returns = strategy_returns - benchmark_returns

    # 计算年化超额收益率
    annual_excess_return = calculate_annualized_return(excess_returns, periods_per_year)

    # 计算超额收益的波动率（年化）
    tracking_error = excess_returns.std() * np.sqrt(periods_per_year)

    if tracking_error == 0:
        return float(float(0.0))

    return float(float(annual_excess_return / tracking_error))


def calculate_drawdowns(returns: pd.Series) -> pd.Series:
    """
    计算回撤序列

    Args:
        returns: 收益率序列

    Returns:
        pd.Series: 回撤序列
    """
    # 计算累积收益
    wealth_index = (1 + returns).cumprod()

    # 计算累积最大值
    rolling_max = wealth_index.cummax()

    # 计算回撤
    drawdown = wealth_index / rolling_max - 1

    return drawdown


def calculate_underwater_periods(
    returns: pd.Series,
) -> List[Tuple[pd.Timestamp, pd.Timestamp, float]]:
    """
    计算水下期间(从新高到恢复的时间段)

    Args:
        returns: 收益率序列

    Returns:
        水下期间列表,每个元素为(开始时间,结束时间,最大回撤)
    """
    if returns.empty:
        return []

    # 计算累计收益率
    cum_returns = calculate_cumulative_returns(returns)

    # 计算累计最大值
    cum_max = cum_returns.cummax()

    # 计算回撤
    drawdowns = (cum_returns / cum_max) - 1
    drawdowns = np.round(drawdowns, 6)  # 处理浮点精度问题

    # 寻找水下期间
    periods = []
    in_drawdown = False
    start_date = None
    # 注释掉未使用的变量min_idx
    # min_idx = None
    min_value = 0

    for idx, value in drawdowns.items():
        if not in_drawdown and value < 0:
            # 开始新的回撤期
            in_drawdown = True
            start_date = idx
            min_value = value
        elif in_drawdown:
            if value < min_value:
                # 更新最大回撤
                min_value = value
                # min_idx = idx
            elif value == 0:
                # 回撤结束
                end_date = idx
                periods.append((start_date, end_date, min_value))
                in_drawdown = False

    # 检查最后一个还未结束的回撤期
    if in_drawdown:
        periods.append((start_date, returns.index[-1], min_value))

    return periods


def calculate_rolling_sharpe(
    returns: pd.Series,
    window: int = 126,
    risk_free_rate: float = 0.0,
    periods_per_year: int = 252,
) -> pd.Series:
    """
    计算滚动夏普比率

    Args:
        returns: 收益率序列
        window: 滚动窗口大小
        risk_free_rate: 无风险利率，年化，默认为0
        periods_per_year: 一年的周期数

    Returns:
        pd.Series: 滚动夏普比率序列
    """
    # 将年化无风险利率转换为对应周期的利率
    rf_period = (1 + risk_free_rate) ** (1 / periods_per_year) - 1

    # 计算超额收益
    excess_returns = returns - rf_period

    # 计算滚动平均超额收益率
    rolling_mean = excess_returns.rolling(window=window).mean() * periods_per_year

    # 计算滚动标准差
    rolling_std = excess_returns.rolling(window=window).std() * np.sqrt(
        periods_per_year
    )

    # 计算滚动夏普比率
    rolling_sharpe = rolling_mean / rolling_std

    return rolling_sharpe


def calculate_monthly_returns(returns: pd.Series) -> pd.DataFrame:
    """
    计算月度收益率表

    Args:
        returns: 日收益率序列

    Returns:
        pd.DataFrame: 月度收益率表，行为年份，列为月份
    """
    # 确保索引为日期类型
    if not isinstance(returns.index, pd.DatetimeIndex):
        raise ValueError("收益率序列的索引必须是DatetimeIndex类型")

    # 计算月度收益率
    monthly_returns = (returns + 1).resample("M").prod() - 1

    # 创建月度收益率表
    monthly_table = pd.DataFrame(
        {
            "Year": monthly_returns.index.year,
            "Month": monthly_returns.index.month,
            "Return": monthly_returns.values,
        }
    )

    # 透视表转换
    pivot_table = monthly_table.pivot(index="Year", columns="Month", values="Return")

    # 添加年度总收益列
    yearly_returns = (returns + 1).resample("Y").prod() - 1
    pivot_table["年度收益"] = yearly_returns.values

    # 重命名列
    month_names = [
        "一月",
        "二月",
        "三月",
        "四月",
        "五月",
        "六月",
        "七月",
        "八月",
        "九月",
        "十月",
        "十一月",
        "十二月",
        "年度收益",
    ]
    pivot_table.columns = month_names

    return pivot_table


def calculate_metrics(
    returns: pd.Series,
    benchmark_returns: Optional[pd.Series] = None,
    risk_free_rate: float = 0.0,
    periods_per_year: int = 252,
) -> Dict[str, float]:
    """
    计算策略的综合评价指标

    Args:
        returns: 策略收益率序列
        benchmark_returns: 基准收益率序列，可选
        risk_free_rate: 无风险利率，年化，默认为0
        periods_per_year: 一年的周期数

    Returns:
        Dict[str, float]: 包含各项指标的字典
    """
    metrics = {}

    # 基础收益指标
    metrics["累积收益率"] = calculate_cumulative_returns(returns).iloc[-1]
    metrics["年化收益率"] = calculate_annualized_return(returns, periods_per_year)
    metrics["复合年增长率"] = calculate_cagr(returns, periods_per_year)

    # 风险指标
    metrics["年化波动率"] = calculate_volatility(returns, periods_per_year)
    max_dd, peak_date, valley_date = calculate_max_drawdown(returns)
    metrics["最大回撤"] = max_dd
    metrics["最大回撤起始日期"] = peak_date
    metrics["最大回撤结束日期"] = valley_date
    metrics["95%风险价值(VaR)"] = calculate_var(returns)
    metrics["95%条件风险价值(CVaR)"] = calculate_cvar(returns)

    # 风险调整收益指标
    metrics["夏普比率"] = calculate_sharpe_ratio(
        returns, risk_free_rate, periods_per_year
    )
    metrics["索提诺比率"] = calculate_sortino_ratio(
        returns, risk_free_rate, periods_per_year
    )
    metrics["卡玛比率"] = calculate_calmar_ratio(returns, periods_per_year)
    metrics["欧米伽比率"] = calculate_omega_ratio(
        returns, risk_free_rate, 0.0, periods_per_year
    )

    # 交易统计指标
    metrics["胜率"] = calculate_win_rate(returns)
    metrics["盈亏比"] = calculate_profit_factor(returns)
    metrics["期望值"] = calculate_expectancy(returns)

    # 如果提供了基准收益率，计算相对指标
    if benchmark_returns is not None:
        metrics["Alpha"] = calculate_alpha(
            returns, benchmark_returns, risk_free_rate, periods_per_year
        )
        metrics["Beta"] = calculate_beta(returns, benchmark_returns)
        metrics["信息比率"] = calculate_information_ratio(
            returns, benchmark_returns, periods_per_year
        )

        # 计算超额收益
        aligned_returns = returns.align(benchmark_returns, join="inner")
        excess_returns = aligned_returns[0] - aligned_returns[1]
        metrics["超额累积收益率"] = calculate_cumulative_returns(excess_returns).iloc[
            -1
        ]
        metrics["超额年化收益率"] = calculate_annualized_return(
            excess_returns, periods_per_year
        )

    return metrics


def calculate_drawdown_stats(returns: pd.Series) -> pd.DataFrame:
    """
    计算回撤统计信息

    Args:
        returns: 收益率序列

    Returns:
        pd.DataFrame: 回撤统计表
    """
    # 计算水下期
    underwater_periods = calculate_underwater_periods(returns)

    if not underwater_periods:
        return pd.DataFrame()

    # 创建统计表
    stats = []
    for start, end, max_dd in underwater_periods:
        recovery_days = (end - start).days
        stats.append(
            {
                "开始日期": start,
                "结束日期": end,
                "最大回撤": max_dd,
                "恢复天数": recovery_days,
            }
        )

    # 转换为DataFrame并排序
    dd_stats = pd.DataFrame(stats)
    dd_stats = dd_stats.sort_values("最大回撤")

    return dd_stats
