#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
批量合成历史数据模块

将交互界面的合成功能移动到此文件中，支持多周期、多股票的批量配置
遵循"一个功能一个实现"原则，提供高效的批量数据合成方案

基于下载历史数据.py的成功模式设计，支持：
- 多股票批量处理
- 多周期配置
- 增量更新检测
- 进度跟踪和性能监控
- 错误处理和重试机制
"""

import os
import sys
import datetime
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from data.core.operations import synthesize_data
from utils.logger import get_unified_logger, LogTarget
from config.settings import DATA_ROOT
from utils.data_processor.period_handler import is_valid_period
from utils.data_processor.period_converter import get_recommended_base_period, validate_period_string
from typing import List, Dict, Any

logger = get_unified_logger(__name__)


def get_optimal_source_period(target_period: str) -> str:
    """
    为目标周期推荐最优源周期

    Args:
        target_period: 目标周期，如 '3m', '2h', '1d' 等

    Returns:
        str: 推荐的源周期
    """
    logger.debug(LogTarget.FILE, f"为目标周期 {target_period} 推荐最优源周期")

    # 特殊处理1分钟周期，强制使用tick作为源周期
    if target_period == '1m':
        logger.debug(LogTarget.FILE, f"目标周期 {target_period} 特殊处理，使用tick作为源周期")
        return 'tick'

    # 使用现有的推荐算法
    recommended_base = get_recommended_base_period(target_period)

    logger.debug(LogTarget.FILE, f"目标周期 {target_period} 推荐源周期: {recommended_base}")
    return recommended_base


def generate_config_name(source_period: str, target_period: str) -> str:
    """
    自动生成配置名称

    Args:
        source_period: 源周期
        target_period: 目标周期

    Returns:
        str: 生成的配置名称
    """
    # 周期名称映射，用于生成友好的中文名称
    period_names = {
        'tick': 'tick',
        '1s': '1秒',
        '30s': '30秒',
        '1m': '1分钟',
        '3m': '3分钟',
        '5m': '5分钟',
        '15m': '15分钟',
        '30m': '30分钟',
        '1h': '1小时',
        '2h': '2小时',
        '4h': '4小时',
        '6h': '6小时',
        '1d': '日线',
        '1w': '周线',
        '1M': '月线'
    }

    source_name = period_names.get(source_period, source_period)
    target_name = period_names.get(target_period, target_period)

    config_name = f"{source_name}→{target_name}"
    logger.debug(LogTarget.FILE, f"生成配置名称: {source_period} → {target_period} = {config_name}")

    return config_name


def generate_synthesis_configs(target_periods: List[str],
                             start_date: str = "",
                             end_date: str = "",
                             show_data: bool = True,
                             display_rows: int = 5,
                             dividend_type: str = "none") -> List[Dict[str, Any]]:
    """
    根据目标周期列表自动生成合成配置（支持复权数据）

    Args:
        target_periods: 目标周期列表，如 ['1m', '3m', '5m', '15m', '1h']
        start_date: 开始日期
        end_date: 结束日期
        show_data: 是否显示数据
        display_rows: 显示行数
        dividend_type: 复权类型，"none"（原始数据）、"front"（前复权）、"back"（后复权）

    Returns:
        List[Dict]: 生成的合成配置列表
    """
    logger.info(LogTarget.FILE, f"开始生成合成配置，目标周期: {target_periods}")

    configs = []
    valid_periods = []
    invalid_periods = []

    for target_period in target_periods:
        # 验证周期格式
        if not validate_period_string(target_period):
            invalid_periods.append(target_period)
            logger.warning(LogTarget.FILE, f"无效的周期格式: {target_period}")
            continue

        # 获取最优源周期
        source_period = get_optimal_source_period(target_period)

        # 生成配置名称
        config_name = generate_config_name(source_period, target_period)

        # 创建配置
        config = {
            "source_period": source_period,
            "target_period": target_period,
            "config_name": config_name,
            "start_date": start_date,
            "end_date": end_date,
            "show_data": show_data,
            "display_rows": display_rows,
            "dividend_type": dividend_type  # 添加复权类型配置
        }

        configs.append(config)
        valid_periods.append(target_period)
        logger.debug(LogTarget.FILE, f"生成配置: {config_name}")

    # 记录统计信息
    logger.info(LogTarget.FILE, f"配置生成完成: 有效周期 {len(valid_periods)} 个, 无效周期 {len(invalid_periods)} 个")
    if invalid_periods:
        logger.warning(LogTarget.FILE, f"无效周期列表: {invalid_periods}")

    return configs


def _create_initial_synthesis_result_file(stock_list_file: str, result_file_path: str):
    """从股票列表文件创建初始合成结果文件"""
    try:
        # 读取股票列表
        with open(stock_list_file, 'r', encoding='utf-8') as f:
            stock_lines = f.readlines()

        # 解析股票代码 - 使用专业文本解析工具处理带注释的代码
        from utils.text_parser import parse_stock_code_input

        stocks = []
        for line in stock_lines:
            line = line.strip()
            if line and not line.startswith('#'):
                # 使用专业解析函数处理可能包含注释的股票代码
                parsed_codes = parse_stock_code_input(line)
                stocks.extend(parsed_codes)

        # 使用智能配置生成，支持常用的合成周期
        default_target_periods = ["1m", "5m", "15m", "30m", "1h", "1w", "1M"]
        synthesis_periods = generate_synthesis_configs(default_target_periods)

        # 创建初始合成结果文件
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        with open(result_file_path, 'w', encoding='utf-8') as f:
            f.write(f"批量数据合成结果 - {timestamp}\n")
            f.write("="*50 + "\n\n")

            # 为每个合成配置创建初始结果分段
            for period_config in synthesis_periods:
                source_period = period_config["source_period"]
                target_period = period_config["target_period"]
                period_name = period_config["config_name"]
                
                f.write(f"========== {period_name}合成结果 ==========\n")

                # 写入各部分（初始状态：所有股票都是未合成）
                sections = [
                    ("合成成功的股票:", []),
                    ("合成失败的股票:", []),
                    ("未合成的股票:", sorted(stocks)),
                    ("无需合成的股票:", [])
                ]

                for title, stock_list in sections:
                    f.write(f"{title}\n")
                    for stock in stock_list:
                        f.write(f"{stock}\n")
                    f.write("\n")

                # 写入统计信息
                total = len(stocks)
                f.write(f"总计股票: {total}\n")
                f.write(f"合成成功: 0\n")
                f.write(f"合成失败: 0\n")
                f.write(f"未合成: {total}\n")
                f.write(f"无需合成: 0\n")
                f.write(f"最后更新时间: {timestamp}\n\n")

        print(f"✅ 已创建批量合成初始结果文件，包含 {len(stocks)} 只股票")
        print(f"📊 已为 {len(synthesis_periods)} 个合成配置初始化状态")

    except Exception as e:
        print(f"❌ 创建初始结果文件失败: {e}")


def _read_stock_list_from_file(stock_list_file: str) -> list:
    """从文件读取股票列表"""
    try:
        if not os.path.exists(stock_list_file):
            print(f"❌ 股票列表文件不存在: {stock_list_file}")
            return []

        with open(stock_list_file, 'r', encoding='utf-8') as f:
            stock_lines = f.readlines()

        # 解析股票代码 - 使用专业文本解析工具处理带注释的代码
        from utils.text_parser import parse_stock_code_input

        stocks = []
        for line in stock_lines:
            line = line.strip()
            if line and not line.startswith('#'):
                # 使用专业解析函数处理可能包含注释的股票代码
                parsed_codes = parse_stock_code_input(line)
                stocks.extend(parsed_codes)

        print(f"📋 从文件读取到 {len(stocks)} 只股票")
        return stocks

    except Exception as e:
        print(f"❌ 读取股票列表文件失败: {e}")
        return []


def _get_default_stock_list():
    """获取默认股票列表"""
    return ['000001.SZ']  # 默认使用平安银行作为示例


def main():
    """主函数"""
    # ==================== 股票列表配置 ====================

    # 尝试从文件读取股票列表
    stock_list_file = os.path.join(DATA_ROOT, "stock_list.txt")
    stock_list = _read_stock_list_from_file(stock_list_file)

    # 如果文件不存在或为空，使用默认列表
    if not stock_list:
        stock_list = _get_default_stock_list()
        print(f"📋 使用默认股票列表: {stock_list}")
        print(f"💡 提示: 可创建 {stock_list_file} 文件来自定义股票列表")

    # ==================== 多周期合成配置 ====================

    # 🎯 用户配置区域：只需修改此处的目标周期列表
    #
    # 支持的周期格式：
    # - 秒: 30s, 60s
    # - 分钟: 1m, 3m, 5m, 15m, 30m
    # - 小时: 1h, 2h, 4h, 6h
    # - 天: 1d, 2d
    # - 周: 1w, 2w
    # - 月: 1M, 3M
    #
    # 系统将自动：
    # 1. 为每个目标周期推荐最优的源周期
    # 2. 生成友好的配置名称
    # 3. 验证周期格式的有效性
    """
        "3m",    # 系统自动选择: 1m → 3分钟
        "5m",    # 系统自动选择: 1m → 5分钟
        "15m",   # 系统自动选择: 1m → 15分钟
        "30m",   # 系统自动选择: 1m → 30分钟
        "1h",    # 系统自动选择: 1m → 1小时
        "2h",    # 系统自动选择: 1m → 2小时
        "4h",    # 系统自动选择: 1m → 4小时
    """
    target_periods = [
        "1m",    # 系统自动选择: tick → 1分钟
    ]

    # ==================== 复权配置 ====================

    # 🎯 复权类型配置：选择数据复权方式
    #
    # 支持的复权类型：
    # - "none": 原始数据（不进行复权处理）
    # - "front": 前复权（向前调整价格，保持最新价格不变）
    # - "back": 后复权（向后调整价格，保持历史价格不变）
    #
    # 推荐设置：
    # - 技术分析：使用 "front" 前复权
    # - 历史回测：使用 "front" 前复权
    # - 原始数据分析：使用 "none" 不复权
    dividend_type = "front"  # 默认使用前复权数据

    # 使用智能配置生成器自动生成合成配置
    print(f"\n🤖 智能配置生成中...")
    print(f"📋 目标周期列表: {target_periods}")

    synthesis_configs = generate_synthesis_configs(
        target_periods=target_periods,
        start_date="20250715145600",
        end_date="20250716093300",
        show_data=True,
        display_rows=5,
        dividend_type=dividend_type  # 传递复权类型配置
    )

    # 显示生成的配置信息
    print(f"✅ 配置生成完成，共生成 {len(synthesis_configs)} 个合成配置:")
    for i, config in enumerate(synthesis_configs, 1):
        print(f"   {i}. {config['config_name']}")
    print()

    # 通用合成选项
    delay_between_configs = 3  # 配置间延时（秒）
    
    # 股票代码来源配置
    result_file = "synthesis_results.txt"  # 统一结果文件

    # ==================== 开始批量合成 ====================

    total_configs = len(synthesis_configs)
    print(f"\n🚀 开始批量数据合成，共 {total_configs} 个配置")

    # 显示结果文件路径信息
    result_file_path = os.path.join(DATA_ROOT, result_file)
    print(f"📋 统一结果文件: {result_file_path}")

    # 检查并创建初始结果文件
    if not os.path.exists(result_file_path):
        # 尝试从stock_list.txt创建初始结果文件
        stock_list_file = os.path.join(DATA_ROOT, "stock_list.txt")
        if os.path.exists(stock_list_file):
            print(f"📋 从股票列表文件创建初始结果文件: {stock_list_file}")
            _create_initial_synthesis_result_file(stock_list_file, result_file_path)
        else:
            print(f"❌ 结果文件和股票列表文件都不存在")
            print(f"请创建 {stock_list_file} 文件并添加股票代码，或直接创建 {result_file_path}")
            return

    # 初始化统计变量
    total_start_time = time.time()
    overall_stats = {
        "total_configs": total_configs,
        "completed_configs": 0,
        "total_stocks_processed": 0,
        "total_successful": 0,
        "total_failed": 0,
        "config_results": []
    }

    # 多配置合成循环
    for i, config in enumerate(synthesis_configs, 1):
        source_period = config["source_period"]
        target_period = config["target_period"]
        config_name = config["config_name"]
        start_date = config["start_date"]
        end_date = config["end_date"]
        show_data = config["show_data"]
        display_rows = config["display_rows"]

        print(f"\n{'='*60}")
        print(f"📈 [{i}/{total_configs}] 开始合成 {config_name} 数据...")
        print(f"⏰ 时间范围: {start_date or '最早可用'} ~ {end_date or '今天'}")
        print(f"📊 数据预览: {'是' if show_data else '否'}")
        print(f"📁 结果文件: {result_file} (配置: {source_period}→{target_period})")
        print(f"{'='*60}")

        # 记录单个配置开始时间
        config_start_time = time.time()

        # 调用现有的合成函数，使用读取的股票列表
        try:
            # 从配置中获取复权类型
            config_dividend_type = config.get("dividend_type", "none")

            result = synthesize_data(
                symbols=stock_list,  # 使用读取的股票列表
                source_period=source_period,
                target_period=target_period,
                start_date=start_date,
                end_date=end_date,
                data_dir=DATA_ROOT,
                show_data=show_data,
                display_rows=display_rows,
                result_file=result_file_path,  # 传递结果文件路径
                real_time_save=True,  # 启用即时保存
                dividend_type=config_dividend_type  # 传递复权类型参数
            )
        except Exception as e:
            print(f"❌ {config_name}合成过程中发生错误: {e}")
            logger.error(LogTarget.FILE, f"{config_name}合成错误: {e}")
            result = None

        # 处理结果并显示详细统计
        if result and result.get("success"):
            successful_count = len(result.get("successful_symbols", []))
            failed_count = len(result.get("failed_symbols", []))
            total_count = successful_count + failed_count

            print(f"✅ {config_name}数据合成完成")
            print(f"📊 合成统计: 总计 {total_count} 只股票, 成功 {successful_count} 只, 失败 {failed_count} 只")

            if failed_count > 0:
                print("❌ 失败的股票:")
                failed_reasons = result.get("failed_reasons", {})
                for symbol in result.get("failed_symbols", []):
                    reason = failed_reasons.get(symbol, "未知原因")
                    print(f"   - {symbol}: {reason}")

            logger.info(LogTarget.FILE, f"{config_name}数据合成完成: 成功{successful_count}只, 失败{failed_count}只")

            # 更新总体统计
            overall_stats["total_stocks_processed"] += total_count
            overall_stats["total_successful"] += successful_count
            overall_stats["total_failed"] += failed_count
        else:
            print(f"❌ {config_name}数据合成失败")
            if result and "message" in result:
                print(f"   失败原因: {result['message']}")
            logger.error(LogTarget.FILE, f"{config_name}数据合成失败")

        # 记录配置完成时间和统计
        config_end_time = time.time()
        config_duration = config_end_time - config_start_time
        overall_stats["completed_configs"] += 1

        config_result = {
            "config_name": config_name,
            "duration": config_duration,
            "success": result and result.get("success", False),
            "stocks_processed": len(stock_list) if result else 0
        }
        overall_stats["config_results"].append(config_result)

        print(f"⏱️  配置耗时: {config_duration:.2f}秒")
        print(f"📈 总体进度: {overall_stats['completed_configs']}/{overall_stats['total_configs']} 配置已完成")

        # 添加延时，避免系统负载过高
        if i < total_configs:  # 最后一个配置不需要延时
            print(f"⏳ 等待{delay_between_configs}秒后继续合成下一个配置...")
            time.sleep(delay_between_configs)

    # 计算总体耗时
    total_end_time = time.time()
    total_duration = total_end_time - total_start_time

    # 合成完成总结
    print(f"\n🎉 {'='*60}")
    print(f"🎉 批量数据合成完成！")
    print(f"🎉 {'='*60}")

    # 总体统计
    print(f"\n📊 总体统计:")
    print(f"   ⏱️  总耗时: {total_duration:.2f}秒 ({total_duration/60:.1f}分钟)")
    print(f"   📈 完成配置: {overall_stats['completed_configs']}/{overall_stats['total_configs']}")
    print(f"   📊 处理股票: {overall_stats['total_stocks_processed']} 只")
    print(f"   ✅ 成功合成: {overall_stats['total_successful']} 只")
    print(f"   ❌ 失败合成: {overall_stats['total_failed']} 只")

    if overall_stats['total_stocks_processed'] > 0:
        success_rate = (overall_stats['total_successful'] / overall_stats['total_stocks_processed']) * 100
        print(f"   📈 成功率: {success_rate:.1f}%")

    # 各配置详细结果
    print(f"\n📋 各配置详细结果:")
    for config_result in overall_stats["config_results"]:
        status = "✅" if config_result["success"] else "❌"
        print(f"   {status} {config_result['config_name']}: {config_result['duration']:.2f}秒, {config_result['stocks_processed']}只股票")

    # 时间范围信息
    print(f"\n📅 各配置时间范围:")
    for config in synthesis_configs:
        start_date = config['start_date'] or "最早可用"
        end_date = config['end_date'] or "今天"
        print(f"   - {config['config_name']}: {start_date} ~ {end_date}")

    print(f"\n🎉 {'='*60}")
    print(f"🎉 批量合成任务全部完成！")
    print(f"📋 详细日志请查看日志文件")
    print(f"🎉 {'='*60}")


if __name__ == "__main__":
    main()
