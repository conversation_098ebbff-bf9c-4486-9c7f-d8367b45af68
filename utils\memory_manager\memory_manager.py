"""
内存管理器 - 整合内存管理组件，提供统一的内存管理接口
"""

import time
import threading
from typing import Dict, List, Callable, Optional, TypeVar
from datetime import datetime

# 导入内存管理组件
from .memory_monitor import MemoryMonitor
from .threshold_manager import (
    ThresholdManager, 
    ThresholdType, 
    ThresholdLevel, 
    ThresholdDirection
)
from .resource_optimizer import ResourceOptimizer
from .batch_controller import BatchController, BatchResult

# 导入logger
from ..logger import get_unified_logger, LogTarget

# 获取logger实例
logger = get_unified_logger("memory_manager")

# 定义泛型类型变量
T = TypeVar('T')  # 输入数据类型
R = TypeVar('R')  # 结果数据类型


class MemoryManager:
    """
    内存管理器类
    
    整合内存监控、阈值管理、资源优化和批处理控制组件，
    提供统一的内存管理接口
    """
    
    def __init__(
        self,
        memory_warning_threshold: float = 75.0,
        memory_critical_threshold: float = 85.0,
        memory_pause_threshold: float = 90.0,
        check_interval: float = 5.0,
        auto_optimize: bool = True,
        default_batch_size: int = 100,
        max_workers: int = 1
    ):
        """
        初始化内存管理器
        
        Args:
            memory_warning_threshold: 内存警告阈值百分比，默认75%
            memory_critical_threshold: 内存临界阈值百分比，默认85%
            memory_pause_threshold: 内存暂停阈值百分比，默认90%
            check_interval: 内存检查间隔(秒)，默认5秒
            auto_optimize: 是否自动优化内存，默认True
            default_batch_size: 默认批处理大小，默认100
            max_workers: 默认最大工作线程数，默认1
        """
        # 创建内存监控组件
        self.monitor = MemoryMonitor(check_interval=check_interval)
        
        # 创建阈值管理组件
        self.threshold_manager = ThresholdManager()
        
        # 创建资源优化组件
        self.optimizer = ResourceOptimizer()
        
        # 批处理控制器配置
        self._default_batch_size = default_batch_size
        self._max_workers = max_workers
        self._memory_threshold = memory_critical_threshold
        self._pause_threshold = memory_pause_threshold
        
        # 状态变量
        self._is_monitoring = False
        self._monitor_thread = None
        self._last_optimization_time = 0
        self._optimization_interval = 60  # 默认优化间隔(秒)
        
        # 回调函数
        self._on_warning = None
        self._on_critical = None
        self._on_normal = None
        
        # 注册阈值回调
        self._register_threshold_callbacks()
        
        logger.debug(LogTarget.FILE, "内存管理器初始化完成")
    
    def _register_threshold_callbacks(self) -> None:
        """注册阈值回调函数"""
        # 为警告阈值注册回调
        self.threshold_manager.create_threshold(
            threshold_id="system_memory_warning",
            threshold_type=ThresholdType.SYSTEM_PERCENT,
            value=75.0,
            level=ThresholdLevel.WARNING,
            direction=ThresholdDirection.ABOVE,
            callback=self._handle_warning_threshold
        )
        
        # 为临界阈值注册回调
        self.threshold_manager.create_threshold(
            threshold_id="system_memory_critical",
            threshold_type=ThresholdType.SYSTEM_PERCENT,
            value=85.0,
            level=ThresholdLevel.CRITICAL,
            direction=ThresholdDirection.ABOVE,
            callback=self._handle_critical_threshold
        )
    
    def _handle_warning_threshold(self, memory_info: Dict) -> None:
        """
        处理警告阈值回调
        
        Args:
            memory_info: 内存信息字典
        """
        # 记录警告日志
        logger.warning(
            LogTarget.FILE,
            f"内存使用率达到警告阈值: {memory_info['percent']}%"
        )
        
        # 尝试轻度优化内存
        self._optimize_memory(level='light')
        
        # 调用用户自定义回调
        if self._on_warning:
            try:
                self._on_warning(memory_info)
            except Exception as e:
                logger.error(LogTarget.FILE, f"执行警告回调出错: {e}")
    
    def _handle_critical_threshold(self, memory_info: Dict) -> None:
        """
        处理临界阈值回调
        
        Args:
            memory_info: 内存信息字典
        """
        # 记录严重警告日志
        logger.warning(
            LogTarget.FILE,
            f"内存使用率达到临界阈值: {memory_info['percent']}%"
        )
        
        # 尝试深度优化内存
        self._optimize_memory(level='deep')
        
        # 调用用户自定义回调
        if self._on_critical:
            try:
                self._on_critical(memory_info)
            except Exception as e:
                logger.error(LogTarget.FILE, f"执行临界回调出错: {e}")
    
    def _handle_normal_threshold(self, memory_info: Dict) -> None:
        """
        处理正常阈值回调
        
        Args:
            memory_info: 内存信息字典
        """
        # 记录恢复正常日志
        logger.info(
            LogTarget.FILE,
            f"内存使用率恢复正常: {memory_info['percent']}%"
        )
        
        # 调用用户自定义回调
        if self._on_normal:
            try:
                self._on_normal(memory_info)
            except Exception as e:
                logger.error(LogTarget.FILE, f"执行正常回调出错: {e}")
    
    def _optimize_memory(self, level: str = 'light') -> None:
        """
        优化内存使用
        
        Args:
            level: 优化级别，'light'或'deep'
        """
        # 检查是否需要优化
        current_time = time.time()
        if current_time - self._last_optimization_time < self._optimization_interval:
            return
        
        # 更新优化时间
        self._last_optimization_time = current_time
        
        try:
            if level == 'light':
                # 轻度优化：清理未使用对象和缓存
                self.optimizer.trigger_garbage_collection()
                self.optimizer.clean_all_caches()
                logger.debug(LogTarget.FILE, "执行轻度内存优化")
            else:
                # 深度优化：包括检测大型对象
                self.optimizer.trigger_garbage_collection(full=True)
                self.optimizer.clean_all_caches()
                large_objects = self.optimizer.detect_large_objects(threshold_mb=10)
                
                if large_objects:
                    logger.warning(
                        LogTarget.FILE,
                        f"检测到大型对象: {len(large_objects)} 个"
                    )
                    for i, obj_info in enumerate(large_objects[:5], 1):
                        logger.warning(
                            LogTarget.FILE,
                            f"大型对象 #{i}: 类型={obj_info['type']}, "
                            f"大小={obj_info['size_mb']:.2f}MB"
                        )
                
                logger.debug(LogTarget.FILE, "执行深度内存优化")
        
        except Exception as e:
            logger.error(LogTarget.FILE, f"内存优化出错: {e}")
    
    def start_monitoring(self) -> None:
        """启动内存监控"""
        if self._is_monitoring:
            logger.warning(LogTarget.FILE, "内存监控已在运行中")
            return
        
        # 启动内存监控
        self.monitor.start_monitoring()
        
        # 创建监控线程
        self._monitor_thread = threading.Thread(
            target=self._monitoring_thread,
            name="MemoryMonitorThread",
            daemon=True
        )
        
        # 启动线程
        self._is_monitoring = True
        self._monitor_thread.start()
        
        logger.info(LogTarget.FILE, "内存监控已启动")
    
    def stop_monitoring(self) -> None:
        """停止内存监控"""
        if not self._is_monitoring:
            return
        
        # 停止内存监控
        self.monitor.stop_monitoring()
        self._is_monitoring = False
        
        # 等待监控线程结束
        if self._monitor_thread and self._monitor_thread.is_alive():
            self._monitor_thread.join(timeout=1.0)
        
        logger.info(LogTarget.FILE, "内存监控已停止")
    
    def _monitoring_thread(self) -> None:
        """内存监控线程函数"""
        while self._is_monitoring:
            try:
                # 获取当前内存使用情况
                memory_info = self.monitor.get_memory_info()
                
                # 检查内存阈值
                self.threshold_manager.check_thresholds(memory_info)
                
                # 休眠一段时间
                time.sleep(self.monitor._check_interval)
                
            except Exception as e:
                logger.error(LogTarget.FILE, f"内存监控线程出错: {e}")
                time.sleep(1.0)  # 出错后短暂休眠
    
    def create_batch_controller(
        self,
        process_func: Callable[[List[T]], List[R]],
        batch_size: Optional[int] = None,
        max_workers: Optional[int] = None
    ) -> BatchController[T, R]:
        """
        创建批处理控制器
        
        Args:
            process_func: 处理函数，接收数据列表，返回结果列表
            batch_size: 批次大小，默认使用内存管理器的默认值
            max_workers: 最大工作线程数，默认使用内存管理器的默认值
            
        Returns:
            批处理控制器实例
        """
        # 使用默认值
        if batch_size is None:
            batch_size = self._default_batch_size
        
        if max_workers is None:
            max_workers = self._max_workers
        
        # 创建批处理控制器
        controller = BatchController(
            process_func=process_func,
            batch_size=batch_size,
            max_workers=max_workers,
            memory_threshold_percent=self._memory_threshold,
            pause_threshold_percent=self._pause_threshold
        )
        
        logger.debug(
            LogTarget.FILE,
            f"创建批处理控制器: 批次大小={batch_size}, 工作线程数={max_workers}"
        )
        
        return controller
    
    def process_batch(
        self,
        data: List[T],
        process_func: Callable[[List[T]], List[R]],
        batch_size: Optional[int] = None,
        max_workers: Optional[int] = None
    ) -> BatchResult[R]:
        """
        批量处理数据
        
        Args:
            data: 要处理的数据列表
            process_func: 处理函数，接收数据列表，返回结果列表
            batch_size: 批次大小，默认使用内存管理器的默认值
            max_workers: 最大工作线程数，默认使用内存管理器的默认值
            
        Returns:
            处理结果
        """
        # 创建批处理控制器
        controller = self.create_batch_controller(
            process_func=process_func,
            batch_size=batch_size,
            max_workers=max_workers
        )
        
        # 处理数据
        return controller.process_data(data)
    
    def optimize_memory(self, deep: bool = False) -> Dict:
        """
        手动优化内存
        
        Args:
            deep: 是否进行深度优化，默认False
            
        Returns:
            优化结果信息
        """
        start_time = time.time()
        start_memory = self.monitor.get_memory_info()
        
        # 执行优化
        level = 'deep' if deep else 'light'
        self._optimize_memory(level=level)
        
        # 获取优化后内存信息
        end_memory = self.monitor.get_memory_info()
        end_time = time.time()
        
        # 计算优化效果
        memory_diff = start_memory['system']['used'] - end_memory['system']['used']
        memory_diff_mb = memory_diff / (1024 * 1024)
        time_taken = end_time - start_time
        
        result = {
            'start_memory_percent': start_memory['system']['percent'],
            'end_memory_percent': end_memory['system']['percent'],
            'memory_freed_mb': memory_diff_mb,
            'time_taken_seconds': time_taken,
            'optimization_level': level
        }
        
        logger.info(
            LogTarget.FILE,
            f"内存优化完成: 释放 {memory_diff_mb:.2f}MB, "
            f"使用率: {start_memory['system']['percent']}% -> {end_memory['system']['percent']}%"
        )
        
        return result
    
    def get_memory_status(self) -> Dict:
        """
        获取当前内存状态
        
        Returns:
            内存状态信息字典
        """
        # 获取内存信息
        memory_info = self.monitor.get_memory_info()
        
        # 获取内存趋势
        trend = self.monitor.get_memory_trend(minutes=1)
        
        # 组装结果
        status = {
            'timestamp': datetime.now(),
            'memory': {
                'total': memory_info['system']['total'],
                'available': memory_info['system']['available'],
                'used': memory_info['system']['used'],
                'percent': memory_info['system']['percent'],
                'total_gb': memory_info['system']['total'] / (1024**3),
                'available_gb': memory_info['system']['available'] / (1024**3),
                'used_gb': memory_info['system']['used'] / (1024**3)
            },
            'process': {
                'rss': memory_info['process']['rss'],
                'vms': memory_info['process']['vms'],
                'percent': memory_info['process']['percent'],
                'rss_mb': memory_info['process']['rss'] / (1024 * 1024),
                'vms_mb': memory_info['process']['vms'] / (1024 * 1024)
            },
            'trend': trend,
            'thresholds': {
                'warning': 75.0,
                'critical': 85.0,
                'current_state': 'normal'
            }
        }
        
        # 确定当前状态
        if memory_info['system']['percent'] >= 85.0:
            status['thresholds']['current_state'] = 'critical'
        elif memory_info['system']['percent'] >= 75.0:
            status['thresholds']['current_state'] = 'warning'
        
        return status
    
    def register_warning_callback(self, callback: Callable[[Dict], None]) -> None:
        """
        注册内存警告回调函数
        
        Args:
            callback: 回调函数，接收内存信息字典
        """
        self._on_warning = callback
    
    def register_critical_callback(self, callback: Callable[[Dict], None]) -> None:
        """
        注册内存临界回调函数
        
        Args:
            callback: 回调函数，接收内存信息字典
        """
        self._on_critical = callback
    
    def register_normal_callback(self, callback: Callable[[Dict], None]) -> None:
        """
        注册内存正常回调函数
        
        Args:
            callback: 回调函数，接收内存信息字典
        """
        self._on_normal = callback
    
    def set_optimization_interval(self, seconds: float) -> None:
        """
        设置内存优化间隔
        
        Args:
            seconds: 优化间隔(秒)
        """
        if seconds < 1.0:
            logger.warning(LogTarget.FILE, f"优化间隔无效: {seconds}，使用默认值 1.0")
            seconds = 1.0
        
        self._optimization_interval = seconds
        logger.debug(LogTarget.FILE, f"内存优化间隔已设置为: {seconds}秒")
    
    def set_batch_parameters(self, batch_size: int, max_workers: int) -> None:
        """
        设置批处理参数
        
        Args:
            batch_size: 批次大小
            max_workers: 最大工作线程数
        """
        if batch_size < 1:
            logger.warning(LogTarget.FILE, f"批次大小无效: {batch_size}，使用默认值 1")
            batch_size = 1
        
        if max_workers < 1:
            logger.warning(LogTarget.FILE, f"工作线程数无效: {max_workers}，使用默认值 1")
            max_workers = 1
        
        self._default_batch_size = batch_size
        self._max_workers = max_workers
        
        logger.debug(
            LogTarget.FILE,
            f"批处理参数已更新: 批次大小={batch_size}, 工作线程数={max_workers}"
        )
    
    def set_memory_thresholds(
        self,
        warning_threshold: float,
        critical_threshold: float,
        pause_threshold: float
    ) -> None:
        """
        设置内存阈值
        
        Args:
            warning_threshold: 警告阈值百分比
            critical_threshold: 临界阈值百分比
            pause_threshold: 暂停阈值百分比
        """
        # 更新阈值管理器
        self.threshold_manager.set_thresholds(
            warning_threshold=warning_threshold,
            critical_threshold=critical_threshold
        )
        
        # 更新批处理阈值
        self._memory_threshold = critical_threshold
        self._pause_threshold = pause_threshold
        
        logger.debug(
            LogTarget.FILE,
            f"内存阈值已更新: 警告={warning_threshold}%, "
            f"临界={critical_threshold}%, 暂停={pause_threshold}%"
        )
    
    def __enter__(self):
        """上下文管理器入口"""
        self.start_monitoring()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器退出"""
        self.stop_monitoring()
        
        # 如果发生异常，尝试优化内存
        if exc_type is not None:
            logger.warning(
                LogTarget.FILE,
                f"上下文管理器捕获异常: {exc_type.__name__}: {exc_val}"
            )
            self.optimize_memory(deep=True)
        
        return False  # 不抑制异常 