#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行时索引格式监控模块

提供运行时监控功能，实时检测和报告索引格式违规行为
支持定期统计报告和异常告警机制
"""

import threading
import time
import schedule
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable
import json
from pathlib import Path

from utils.logger import get_unified_logger
from utils.data_processor.index_monitor import get_monitor_statistics, reset_monitor_statistics

logger = get_unified_logger(__name__, enhanced=True)


class RuntimeIndexMonitor:
    """运行时索引格式监控器"""
    
    def __init__(self, 
                 report_interval: int = 3600,  # 报告间隔（秒）
                 alert_threshold: int = 10,    # 告警阈值
                 log_file: Optional[str] = None):
        """
        初始化运行时监控器
        
        Args:
            report_interval: 统计报告间隔（秒）
            alert_threshold: 违规次数告警阈值
            log_file: 监控日志文件路径
        """
        self.report_interval = report_interval
        self.alert_threshold = alert_threshold
        self.log_file = log_file or "logs/index_monitor.log"
        
        self.is_running = False
        self.monitor_thread = None
        self.last_report_time = datetime.now()
        self.violation_history = []
        self.alert_callbacks = []
        
        # 确保日志目录存在
        Path(self.log_file).parent.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"运行时索引监控器初始化完成，报告间隔: {report_interval}秒，告警阈值: {alert_threshold}")
    
    def start(self) -> None:
        """启动运行时监控"""
        if self.is_running:
            logger.warning("运行时监控器已在运行中")
            return
        
        self.is_running = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        logger.info("运行时索引格式监控器已启动")
    
    def stop(self) -> None:
        """停止运行时监控"""
        if not self.is_running:
            logger.warning("运行时监控器未在运行")
            return
        
        self.is_running = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        
        logger.info("运行时索引格式监控器已停止")
    
    def add_alert_callback(self, callback: Callable[[Dict], None]) -> None:
        """
        添加告警回调函数
        
        Args:
            callback: 告警回调函数，接收告警信息字典
        """
        self.alert_callbacks.append(callback)
        logger.debug(f"已添加告警回调函数，当前回调数量: {len(self.alert_callbacks)}")
    
    def _monitor_loop(self) -> None:
        """监控循环"""
        logger.debug("开始运行时监控循环")
        
        while self.is_running:
            try:
                # 获取当前统计信息
                current_stats = get_monitor_statistics()
                
                # 检查是否需要生成报告
                if self._should_generate_report():
                    self._generate_report(current_stats)
                
                # 检查是否需要发送告警
                if self._should_send_alert(current_stats):
                    self._send_alert(current_stats)
                
                # 记录历史数据
                self._record_history(current_stats)
                
                # 等待下一次检查
                time.sleep(60)  # 每分钟检查一次
                
            except Exception as e:
                logger.error(f"运行时监控循环出错: {e}")
                time.sleep(60)  # 出错后等待1分钟再继续
        
        logger.debug("运行时监控循环结束")
    
    def _should_generate_report(self) -> bool:
        """判断是否应该生成报告"""
        return (datetime.now() - self.last_report_time).total_seconds() >= self.report_interval
    
    def _should_send_alert(self, stats: Dict) -> bool:
        """判断是否应该发送告警"""
        violation_count = stats.get('violation_count', 0)
        return violation_count >= self.alert_threshold
    
    def _generate_report(self, stats: Dict) -> None:
        """生成统计报告"""
        try:
            report_time = datetime.now()
            
            # 计算时间间隔内的变化
            time_delta = (report_time - self.last_report_time).total_seconds()
            
            # 生成报告内容
            report = {
                "timestamp": report_time.isoformat(),
                "time_interval_seconds": time_delta,
                "current_stats": stats,
                "violation_rate": stats.get('violation_count', 0) / max(time_delta / 3600, 0.01),  # 每小时违规次数
                "fix_rate": stats.get('fix_count', 0) / max(stats.get('violation_count', 1), 1)  # 修复率
            }
            
            # 记录到日志
            logger.info(f"索引格式监控报告: {json.dumps(report, indent=2, ensure_ascii=False)}")
            
            # 写入监控日志文件
            self._write_monitor_log(report)
            
            # 更新最后报告时间
            self.last_report_time = report_time
            
            # 重置统计（可选，根据需求决定）
            # reset_monitor_statistics()
            
        except Exception as e:
            logger.error(f"生成监控报告失败: {e}")
    
    def _send_alert(self, stats: Dict) -> None:
        """发送告警"""
        try:
            alert_info = {
                "timestamp": datetime.now().isoformat(),
                "alert_type": "INDEX_FORMAT_VIOLATION",
                "violation_count": stats.get('violation_count', 0),
                "fix_count": stats.get('fix_count', 0),
                "threshold": self.alert_threshold,
                "message": f"索引格式违规次数 ({stats.get('violation_count', 0)}) 超过告警阈值 ({self.alert_threshold})"
            }
            
            # 记录告警日志
            logger.warning(f"索引格式违规告警: {json.dumps(alert_info, ensure_ascii=False)}")
            
            # 调用告警回调函数
            for callback in self.alert_callbacks:
                try:
                    callback(alert_info)
                except Exception as e:
                    logger.error(f"告警回调函数执行失败: {e}")
            
        except Exception as e:
            logger.error(f"发送告警失败: {e}")
    
    def _record_history(self, stats: Dict) -> None:
        """记录历史数据"""
        try:
            history_entry = {
                "timestamp": datetime.now().isoformat(),
                "stats": stats.copy()
            }
            
            self.violation_history.append(history_entry)
            
            # 保持历史记录在合理范围内（最多保留24小时的记录）
            cutoff_time = datetime.now() - timedelta(hours=24)
            self.violation_history = [
                entry for entry in self.violation_history
                if datetime.fromisoformat(entry["timestamp"]) > cutoff_time
            ]
            
        except Exception as e:
            logger.error(f"记录历史数据失败: {e}")
    
    def _write_monitor_log(self, report: Dict) -> None:
        """写入监控日志文件"""
        try:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(report, ensure_ascii=False) + '\n')
        except Exception as e:
            logger.error(f"写入监控日志文件失败: {e}")
    
    def get_violation_history(self, hours: int = 24) -> List[Dict]:
        """
        获取违规历史记录
        
        Args:
            hours: 获取最近多少小时的记录
            
        Returns:
            历史记录列表
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [
            entry for entry in self.violation_history
            if datetime.fromisoformat(entry["timestamp"]) > cutoff_time
        ]
    
    def get_current_status(self) -> Dict:
        """获取当前监控状态"""
        return {
            "is_running": self.is_running,
            "report_interval": self.report_interval,
            "alert_threshold": self.alert_threshold,
            "last_report_time": self.last_report_time.isoformat(),
            "violation_history_count": len(self.violation_history),
            "alert_callbacks_count": len(self.alert_callbacks),
            "current_stats": get_monitor_statistics()
        }


# 全局运行时监控器实例
_global_runtime_monitor = None


def start_runtime_monitoring(report_interval: int = 3600, 
                           alert_threshold: int = 10,
                           log_file: Optional[str] = None) -> RuntimeIndexMonitor:
    """
    启动全局运行时监控
    
    Args:
        report_interval: 报告间隔（秒）
        alert_threshold: 告警阈值
        log_file: 监控日志文件路径
        
    Returns:
        运行时监控器实例
    """
    global _global_runtime_monitor
    
    if _global_runtime_monitor is not None:
        logger.warning("全局运行时监控器已存在，停止现有监控器")
        _global_runtime_monitor.stop()
    
    _global_runtime_monitor = RuntimeIndexMonitor(
        report_interval=report_interval,
        alert_threshold=alert_threshold,
        log_file=log_file
    )
    
    _global_runtime_monitor.start()
    return _global_runtime_monitor


def stop_runtime_monitoring() -> None:
    """停止全局运行时监控"""
    global _global_runtime_monitor
    
    if _global_runtime_monitor is not None:
        _global_runtime_monitor.stop()
        _global_runtime_monitor = None
    else:
        logger.warning("全局运行时监控器不存在")


def get_runtime_monitor() -> Optional[RuntimeIndexMonitor]:
    """获取全局运行时监控器实例"""
    return _global_runtime_monitor


def add_alert_callback(callback: Callable[[Dict], None]) -> None:
    """
    为全局监控器添加告警回调函数
    
    Args:
        callback: 告警回调函数
    """
    if _global_runtime_monitor is not None:
        _global_runtime_monitor.add_alert_callback(callback)
    else:
        logger.warning("全局运行时监控器未启动，无法添加告警回调")
