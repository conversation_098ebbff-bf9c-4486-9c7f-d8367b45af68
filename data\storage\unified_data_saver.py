#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一数据保存器

解决项目中数据保存功能重复问题，提供统一的数据保存接口。
遵循DRY原则，消除功能重复，提升架构质量。

核心特性：
1. 统一接口：一个接口解决所有数据保存需求
2. 智能分组：自动处理跨日期数据分组
3. 策略模式：支持不同保存场景的最优策略
4. 向后兼容：通过适配器模式保持API兼容性
5. 性能优化：智能选择最优保存策略

设计理念：
- 单一职责：每个组件只负责一个明确功能
- 开闭原则：对扩展开放，对修改封闭
- 依赖倒置：依赖抽象而非具体实现
- 接口隔离：提供最小必要接口

版本: v3.0
作者: Augment AI
日期: 2025-08-05
"""

import pandas as pd
import numpy as np
from datetime import datetime
from typing import Optional, Union, List, Tuple, Dict, Any, Protocol
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
import os

from utils.logger import get_unified_logger, LogTarget
from utils.smart_timestamp_processor import get_smart_timestamp_processor

# 创建统一日志记录器
logger = get_unified_logger(__name__, enhanced=True)


class SaveStrategy(Enum):
    """保存策略枚举"""
    AUTO = "auto"                    # 自动选择最优策略
    SINGLE_PARTITION = "single"      # 单分区保存
    MULTI_PARTITION = "multi"        # 多分区保存（跨日期）
    PARALLEL = "parallel"            # 并行保存


@dataclass
class SaveConfig:
    """保存配置"""
    strategy: SaveStrategy = SaveStrategy.AUTO
    parallel: bool = True
    use_threading: bool = True
    max_workers: int = 4
    engine: str = 'pyarrow'
    compression: str = 'snappy'
    data_type: str = "raw"
    adj_type: Optional[str] = None
    
    def __post_init__(self):
        """配置验证"""
        if self.data_type == "adjusted" and self.adj_type is None:
            raise ValueError("复权数据必须指定adj_type参数")


@dataclass
class SaveResult:
    """保存结果"""
    success: bool
    saved_partitions: Dict[str, str]  # 分区标识 -> 文件路径
    total_rows: int
    processing_time: float
    strategy_used: SaveStrategy
    error_message: Optional[str] = None


class SaveStrategyProtocol(Protocol):
    """保存策略协议"""
    
    def can_handle(self, df: pd.DataFrame, config: SaveConfig) -> bool:
        """判断是否能处理该数据"""
        ...
    
    def save(self, df: pd.DataFrame, data_root: str, symbol: str, 
             period: str, config: SaveConfig) -> SaveResult:
        """执行保存操作"""
        ...


class SinglePartitionSaveStrategy:
    """单分区保存策略"""
    
    def can_handle(self, df: pd.DataFrame, config: SaveConfig, period: str = None) -> bool:
        """判断是否适用单分区保存"""
        if df is None or df.empty:
            return False

        # 强制指定单分区策略
        if config.strategy == SaveStrategy.SINGLE_PARTITION:
            return True

        # 自动判断：非跨日期数据且非大数据量
        if config.strategy == SaveStrategy.AUTO:
            # 首先检查是否跨日期数据
            if period:
                processor = get_smart_timestamp_processor()
                time_range = processor.extract_time_range_from_data(df)
                if time_range and time_range.span_days > 1:
                    # 跨日期数据不适用单分区
                    return False

            # 非跨日期数据，且数据量不是特别大
            return len(df) < 50000  # 提高阈值，避免小数据量使用并行策略

        return False
    
    def save(self, df: pd.DataFrame, data_root: str, symbol: str, 
             period: str, config: SaveConfig) -> SaveResult:
        """执行单分区保存"""
        start_time = datetime.now()
        
        try:
            logger.debug(LogTarget.FILE, f"使用单分区策略保存 {symbol} {period} 数据")
            
            # 动态导入避免循环依赖
            from data.storage.parquet_storage import save_to_partition as _legacy_save_to_partition

            # 对于单分区策略，强制提取主要日期时间戳
            try:
                from utils.smart_timestamp_processor import extract_partition_timestamp
                timestamp = extract_partition_timestamp(df, period, force_single_partition=True)
            except Exception as e:
                logger.warning(LogTarget.FILE, f"提取时间戳失败，使用当前日期: {e}")
                timestamp = datetime.now().strftime('%Y%m%d')

            # 使用现有的save_to_partition函数
            success = _legacy_save_to_partition(
                df=df,
                data_root=data_root,
                symbol=symbol,
                period=period,
                timestamp=timestamp,  # 使用强制提取的时间戳
                engine=config.engine,
                compression=config.compression,
                data_type=config.data_type,
                adj_type=config.adj_type
            )
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            if success:
                # 构建保存路径用于结果返回
                from utils.smart_timestamp_processor import extract_partition_timestamp
                timestamp = extract_partition_timestamp(df, period, force_single_partition=True)
                from utils.path_manager import build_partitioned_path
                
                # 临时设置data_root
                from utils.path_manager import get_path_manager
                pm = get_path_manager()
                original_root = pm._data_root
                pm._data_root = data_root
                try:
                    partition_path = build_partitioned_path(
                        symbol=symbol,
                        period=period,
                        timestamp=timestamp,
                        data_type=config.data_type,
                        adj_type=config.adj_type
                    )
                finally:
                    pm._data_root = original_root
                
                return SaveResult(
                    success=True,
                    saved_partitions={timestamp: partition_path},
                    total_rows=len(df),
                    processing_time=processing_time,
                    strategy_used=SaveStrategy.SINGLE_PARTITION
                )
            else:
                return SaveResult(
                    success=False,
                    saved_partitions={},
                    total_rows=len(df),
                    processing_time=processing_time,
                    strategy_used=SaveStrategy.SINGLE_PARTITION,
                    error_message="单分区保存失败"
                )
                
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            logger.error(LogTarget.FILE, f"单分区保存出错: {e}")
            return SaveResult(
                success=False,
                saved_partitions={},
                total_rows=len(df) if df is not None else 0,
                processing_time=processing_time,
                strategy_used=SaveStrategy.SINGLE_PARTITION,
                error_message=str(e)
            )


class MultiPartitionSaveStrategy:
    """多分区保存策略（跨日期数据）"""
    
    def can_handle(self, df: pd.DataFrame, config: SaveConfig, period: str = None) -> bool:
        """判断是否适用多分区保存"""
        if df is None or df.empty:
            return False

        # 强制指定多分区策略
        if config.strategy == SaveStrategy.MULTI_PARTITION:
            return True

        # 自动判断：任何跨日期数据都使用多分区策略（移除数据量限制）
        if config.strategy == SaveStrategy.AUTO and period:
            # 检查是否跨日期数据
            processor = get_smart_timestamp_processor()
            time_range = processor.extract_time_range_from_data(df)
            if time_range and time_range.span_days > 1:
                logger.info(LogTarget.FILE,
                           f"检测到跨日期数据（{time_range.span_days}天: "
                           f"{time_range.start_timestamp[:8]} - {time_range.end_timestamp[:8]}），"
                           f"自动选择多分区策略")
                return True

        return False
    
    def save(self, df: pd.DataFrame, data_root: str, symbol: str, 
             period: str, config: SaveConfig) -> SaveResult:
        """执行多分区保存"""
        start_time = datetime.now()
        
        try:
            logger.debug(LogTarget.FILE, f"使用多分区策略保存 {symbol} {period} 数据")
            
            # 使用智能时间戳处理器进行分组
            processor = get_smart_timestamp_processor()
            grouped_data = processor.analyze_cross_date_data(df)
            
            saved_partitions = {}
            total_success = True
            
            for date_str, group_data in grouped_data.items():
                logger.debug(LogTarget.FILE, f"\n保存分区 {date_str}，数据行数: {len(group_data)}")
                
                # 动态导入避免循环依赖
                from data.storage.parquet_storage import save_to_partition as _legacy_save_to_partition

                success = _legacy_save_to_partition(
                    df=group_data,
                    data_root=data_root,
                    symbol=symbol,
                    period=period,
                    timestamp=date_str,  # 明确指定时间戳
                    engine=config.engine,
                    compression=config.compression,
                    data_type=config.data_type,
                    adj_type=config.adj_type
                )
                
                if success:
                    # 构建保存路径
                    from utils.path_manager import build_partitioned_path, get_path_manager
                    pm = get_path_manager()
                    original_root = pm._data_root
                    pm._data_root = data_root
                    try:
                        partition_path = build_partitioned_path(
                            symbol=symbol,
                            period=period,
                            timestamp=date_str,
                            data_type=config.data_type,
                            adj_type=config.adj_type
                        )
                        saved_partitions[date_str] = partition_path
                    finally:
                        pm._data_root = original_root
                else:
                    total_success = False
                    logger.error(LogTarget.FILE, f"分区 {date_str} 保存失败")
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return SaveResult(
                success=total_success,
                saved_partitions=saved_partitions,
                total_rows=len(df),
                processing_time=processing_time,
                strategy_used=SaveStrategy.MULTI_PARTITION,
                error_message=None if total_success else "部分分区保存失败"
            )
            
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            logger.error(LogTarget.FILE, f"多分区保存出错: {e}")
            return SaveResult(
                success=False,
                saved_partitions={},
                total_rows=len(df) if df is not None else 0,
                processing_time=processing_time,
                strategy_used=SaveStrategy.MULTI_PARTITION,
                error_message=str(e)
            )


class ParallelSaveStrategy:
    """并行保存策略"""

    def can_handle(self, df: pd.DataFrame, config: SaveConfig, period: str = None) -> bool:
        """判断是否适用并行保存"""
        if df is None or df.empty:
            return False

        # 强制指定并行策略
        if config.strategy == SaveStrategy.PARALLEL:
            return True

        # 自动判断：大数据量且启用并行，且非跨日期数据
        if config.strategy == SaveStrategy.AUTO:
            # 检查是否跨日期数据
            if period:
                processor = get_smart_timestamp_processor()
                time_range = processor.extract_time_range_from_data(df)
                if time_range and time_range.span_days > 1:
                    # 跨日期数据优先使用多分区策略
                    return False

            # 大数据量且启用并行
            return len(df) >= 50000 and config.parallel

        return False

    def save(self, df: pd.DataFrame, data_root: str, symbol: str,
             period: str, config: SaveConfig) -> SaveResult:
        """执行并行保存"""
        start_time = datetime.now()

        try:
            logger.debug(LogTarget.FILE, f"使用并行策略保存 {symbol} {period} 数据")

            # 动态导入避免循环依赖
            from data.storage.parquet_storage import save_data_by_partition

            results = save_data_by_partition(
                df=df,
                data_root=data_root,
                symbol=symbol,
                period=period,
                engine=config.engine,
                compression=config.compression,
                parallel=config.parallel,
                use_threading=config.use_threading,
                max_workers=config.max_workers,
                data_type=config.data_type,
                adj_type=config.adj_type
            )

            processing_time = (datetime.now() - start_time).total_seconds()
            success = len(results) > 0

            return SaveResult(
                success=success,
                saved_partitions=results,
                total_rows=len(df),
                processing_time=processing_time,
                strategy_used=SaveStrategy.PARALLEL,
                error_message=None if success else "并行保存失败"
            )

        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            logger.error(LogTarget.FILE, f"并行保存出错: {e}")
            return SaveResult(
                success=False,
                saved_partitions={},
                total_rows=len(df) if df is not None else 0,
                processing_time=processing_time,
                strategy_used=SaveStrategy.PARALLEL,
                error_message=str(e)
            )


class UnifiedDataSaver:
    """
    统一数据保存器

    解决项目中数据保存功能重复问题，提供统一的数据保存接口。
    """

    def __init__(self):
        """初始化统一数据保存器"""
        self._strategies = [
            ParallelSaveStrategy(),      # 优先使用并行策略
            MultiPartitionSaveStrategy(),
            SinglePartitionSaveStrategy()
        ]
        logger.debug(LogTarget.FILE, "统一数据保存器初始化完成")
    
    def save(self, 
             df: pd.DataFrame,
             data_root: str,
             symbol: str,
             period: str,
             config: Optional[SaveConfig] = None) -> SaveResult:
        """
        统一数据保存接口
        
        Args:
            df: 要保存的数据
            data_root: 数据根目录
            symbol: 股票代码
            period: 数据周期
            config: 保存配置
            
        Returns:
            SaveResult: 保存结果
        """
        if config is None:
            config = SaveConfig()
        
        if df is None or df.empty:
            logger.warning(LogTarget.FILE, "数据为空，无法保存")
            return SaveResult(
                success=False,
                saved_partitions={},
                total_rows=0,
                processing_time=0.0,
                strategy_used=SaveStrategy.AUTO,
                error_message="数据为空"
            )
        
        logger.info(LogTarget.FILE, f"开始保存 {symbol} {period} 数据，行数: {len(df)}")
        
        # 选择最适合的保存策略
        selected_strategy = self._select_strategy(df, period, config)
        
        if selected_strategy is None:
            logger.error(LogTarget.FILE, "未找到适合的保存策略")
            return SaveResult(
                success=False,
                saved_partitions={},
                total_rows=len(df),
                processing_time=0.0,
                strategy_used=SaveStrategy.AUTO,
                error_message="未找到适合的保存策略"
            )
        
        # 执行保存
        result = selected_strategy.save(df, data_root, symbol, period, config)
        
        if result.success:
            logger.info(LogTarget.FILE, 
                       f"数据保存成功，策略: {result.strategy_used.value}，"
                       f"分区数: {len(result.saved_partitions)}，"
                       f"耗时: {result.processing_time:.3f}秒")
        else:
            logger.error(LogTarget.FILE, f"数据保存失败: {result.error_message}")
        
        return result
    
    def _select_strategy(self, df: pd.DataFrame, period: str, config: SaveConfig) -> Optional[SaveStrategyProtocol]:
        """选择最适合的保存策略"""
        for strategy in self._strategies:
            if strategy.can_handle(df, config, period):
                logger.debug(LogTarget.FILE, f"选择保存策略: {strategy.__class__.__name__}")
                return strategy

        return None


# 全局实例
_unified_data_saver = None


def get_unified_data_saver() -> UnifiedDataSaver:
    """获取全局统一数据保存器实例"""
    global _unified_data_saver
    if _unified_data_saver is None:
        _unified_data_saver = UnifiedDataSaver()
    return _unified_data_saver


# 便捷函数
def save_data_unified(
    df: pd.DataFrame,
    data_root: str,
    symbol: str,
    period: str,
    strategy: SaveStrategy = SaveStrategy.AUTO,
    parallel: bool = True,
    data_type: str = "raw",
    adj_type: Optional[str] = None,
    **kwargs
) -> SaveResult:
    """
    统一数据保存便捷函数

    Args:
        df: 要保存的数据
        data_root: 数据根目录
        symbol: 股票代码
        period: 数据周期
        strategy: 保存策略
        parallel: 是否启用并行
        data_type: 数据类型
        adj_type: 复权类型
        **kwargs: 其他配置参数

    Returns:
        SaveResult: 保存结果
    """
    config = SaveConfig(
        strategy=strategy,
        parallel=parallel,
        data_type=data_type,
        adj_type=adj_type,
        **kwargs
    )

    saver = get_unified_data_saver()
    return saver.save(df, data_root, symbol, period, config)


def create_save_config(
    strategy: SaveStrategy = SaveStrategy.AUTO,
    parallel: bool = True,
    use_threading: bool = True,
    max_workers: int = 4,
    engine: str = 'pyarrow',
    compression: str = 'snappy',
    data_type: str = "raw",
    adj_type: Optional[str] = None
) -> SaveConfig:
    """
    创建保存配置的便捷函数

    Args:
        strategy: 保存策略
        parallel: 是否启用并行
        use_threading: 是否使用多线程
        max_workers: 最大工作线程数
        engine: 存储引擎
        compression: 压缩方式
        data_type: 数据类型
        adj_type: 复权类型

    Returns:
        SaveConfig: 保存配置
    """
    return SaveConfig(
        strategy=strategy,
        parallel=parallel,
        use_threading=use_threading,
        max_workers=max_workers,
        engine=engine,
        compression=compression,
        data_type=data_type,
        adj_type=adj_type
    )


# 预定义配置
class SaveConfigs:
    """预定义的保存配置"""

    @staticmethod
    def for_tick_data(parallel: bool = True) -> SaveConfig:
        """tick数据保存配置"""
        return SaveConfig(
            strategy=SaveStrategy.AUTO,
            parallel=parallel,
            use_threading=True,
            max_workers=4,
            data_type="raw"
        )

    @staticmethod
    def for_adjusted_data(adj_type: str, parallel: bool = True) -> SaveConfig:
        """复权数据保存配置"""
        return SaveConfig(
            strategy=SaveStrategy.AUTO,
            parallel=parallel,
            use_threading=True,
            max_workers=4,
            data_type="adjusted",
            adj_type=adj_type
        )

    @staticmethod
    def for_large_dataset(max_workers: int = 8) -> SaveConfig:
        """大数据集保存配置"""
        return SaveConfig(
            strategy=SaveStrategy.PARALLEL,
            parallel=True,
            use_threading=False,  # 大数据集使用多进程
            max_workers=max_workers,
            data_type="raw"
        )

    @staticmethod
    def for_single_partition() -> SaveConfig:
        """单分区保存配置"""
        return SaveConfig(
            strategy=SaveStrategy.SINGLE_PARTITION,
            parallel=False,
            data_type="raw"
        )
