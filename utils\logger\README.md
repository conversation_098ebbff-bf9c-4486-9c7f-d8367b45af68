# 增强型日志系统

这个日志系统扩展了Python标准库的logging模块，提供了更灵活的日志记录功能，特别是允许精确控制每条日志的输出位置（文件/控制台/两者）。

## 主要特性

1. **目标选择**: 为每条日志指定输出目标（文件/控制台/两者）
2. **增强型API**: 兼容现有API，同时提供新的目标选择功能
3. **前置内容**: 支持为日志添加自定义前置内容（如分隔线、换行符等）
4. **统一配置**: 集中管理日志格式和输出
5. **目标过滤**: 提供目标过滤器，用于控制日志输出位置
6. **会话管理**: 支持会话开始/结束标记，便于跟踪相关日志
7. **统一日志管理**: 提供中央日志管理系统，确保所有模块使用统一的日志配置
8. **任务ID标记**: 自动为每条日志添加任务ID，便于跟踪和关联
9. **默认只输出到文件**: 默认情况下日志只输出到文件，保持终端界面清爽
10. **标题和正文分离**: 支持分别控制标题和正文的输出位置，提高日志的可读性和灵活性
11. **函数级别日志**: 自动检测并显示调用日志的实际函数名，格式为"模块名【函数名】(行号)"

## 日志系统统一

项目中的日志系统已完全统一，所有模块现在都使用统一的日志API：

1. **中央日志管理模块**: `utils/logger/manager.py` 提供了统一的日志系统初始化和管理功能
2. **统一日志接口**: `get_unified_logger` 是推荐使用的日志获取函数
3. **默认输出到文件**: 所有日志默认只输出到文件，除非明确指定输出到控制台
4. **统一日志初始化**: 所有模块都使用 `setup_unified_logging` 初始化日志系统，支持配置默认输出目标

这样的统一确保了所有日志都使用相同的格式和配置，便于管理和分析，同时保持终端界面的清爽。

## 最近优化

日志系统最近进行了一系列优化，以提高代码质量和减少冗余：

1. **格式化器优化**：移除了旧的格式化器，只保留EnhancedFormatter和TableMarkdownFormatter，减少了代码冗余。
2. **日志目标表示统一**：推荐使用LogTarget枚举（LogTarget.FILE/CONSOLE/BOTH）代替数字代码（1/2/3）来指定日志输出目标，提高代码可读性。
3. **日志前置内容功能**：增加了为日志添加自定义前置内容的功能，可以在日志前添加分隔线、标题或换行符等，使用户可以完全控制日志的显示格式。
4. **移除自动分隔线**：移除了自动分隔线功能，改为由用户通过前置内容参数自行控制。
5. **日志解析增强**：日志查看工具现在支持多种日志格式，减少了对特定格式的依赖，提高了兼容性。
6. **代码清理**：移除了未使用的代码，添加了更详细的文档和警告，便于开发者正确使用API。

## 快速开始

### 基本用法

```python
from utils.logger import get_unified_logger, LogTarget, setup_unified_logging

# 初始化日志系统（默认日志只输出到文件）
setup_unified_logging()

# 获取日志记录器
logger = get_unified_logger("my_module")

# 使用目标代码记录日志
logger.info(LogTarget.FILE, "这条信息只记录到文件")
logger.info(LogTarget.CONSOLE, "这条信息只显示在控制台")
logger.info(LogTarget.BOTH, "这条信息同时记录到文件和显示在控制台")

# 使用前置内容
logger.info(LogTarget.FILE, "=======================", "这条信息前面会有分隔线")
logger.info(LogTarget.FILE, "\n\n\n", "这条信息前面会有多个换行符")
logger.info(LogTarget.FILE, "=====================数据下载=====================", "这是数据下载部分的日志")

# 使用标题和正文分离功能（新增）
logger.info(title_target=LogTarget.BOTH, title="【数据下载开始】", 
            content_target=LogTarget.FILE, content="下载详细数据...")

# 只设置标题，不设置正文
logger.info(title_target=LogTarget.BOTH, title="【操作完成】")

# 只设置正文，不设置标题
logger.info(content_target=LogTarget.FILE, content="详细的操作日志...")

# 使用标准日志级别方法
logger.debug(LogTarget.FILE, "这条调试信息只记录到文件")
logger.warning(LogTarget.CONSOLE, "这条警告只显示在控制台")
logger.error(LogTarget.BOTH, "这条错误同时记录到文件和显示在控制台")

# 默认行为（只输出到文件）
logger.debug("这条调试信息默认只记录到文件")
logger.info("这条信息默认只记录到文件")
```

### 设置统一日志系统

```python
from utils.logger import setup_unified_logging, LogTarget

# 设置统一的日志系统
setup_unified_logging()

# 可以自定义配置
setup_unified_logging(
    logger_names=["my_module", "another_module"],
    log_level="debug",
    task_id="CUSTOM1",
    clean_old_logs=True,
    days_to_keep=14
)

# 配置默认日志输出目标为只输出到文件（减少终端输出）
setup_unified_logging(default_target=LogTarget.FILE)

# 配置默认日志输出目标为只输出到控制台（不写入文件）
setup_unified_logging(default_target=LogTarget.CONSOLE)

# 配置默认日志输出目标为同时输出到文件和控制台
setup_unified_logging(default_target=LogTarget.BOTH)

# 控制是否打印初始化消息（默认为False）
setup_unified_logging(
    default_target=LogTarget.FILE,
    print_init_message=True  # 打印初始化完成消息
)
```

## 高级功能

### 自定义过滤器

```python
from utils.logger import TargetFilter, LogTarget
import logging

# 创建一个只处理文件目标日志的处理器
file_handler = logging.FileHandler("app.log")
file_filter = TargetFilter(LogTarget.FILE)
file_handler.addFilter(file_filter)
```

### 统一日志管理

```python
from utils.logger import setup_unified_logging, get_unified_logger

# 初始化统一日志系统
setup_unified_logging()

# 获取统一配置的日志记录器
logger = get_unified_logger("my_module")
logger.info(LogTarget.BOTH, "记录日志")

# 清理旧日志文件
from utils.logger import clean_old_log_files
deleted_count = clean_old_log_files(days_to_keep=30)
print(f"已删除 {deleted_count} 个旧日志文件")
```

### 使用标题和正文分离功能

增强型日志系统支持分别控制标题和正文的输出位置，这对于需要在控制台显示简洁信息但在日志文件中记录详细内容的场景特别有用：

```python
from utils.logger import get_unified_logger, LogTarget

logger = get_unified_logger("my_module")

# 标题显示在控制台和文件中，正文只记录到文件
logger.info(
    title_target=LogTarget.BOTH, 
    title="【当前进度：显示保存数据】", 
    content_target=LogTarget.FILE, 
    content="详细的数据内容...\n数据行1\n数据行2\n..."
)

# 标题只显示在控制台，正文只记录到文件
logger.info(
    title_target=LogTarget.CONSOLE, 
    title="【操作提示】数据处理中，请稍候...", 
    content_target=LogTarget.FILE, 
    content="正在处理数据，当前进度：25%"
)

# 标题和正文都显示在控制台和文件中，但内容不同
logger.info(
    title_target=LogTarget.BOTH, 
    title="【操作完成】数据下载成功", 
    content_target=LogTarget.BOTH, 
    content="共下载了1000条记录，耗时5.2秒"
)

# 只设置标题，不设置正文
logger.info(title_target=LogTarget.BOTH, title="【分隔线】" + "=" * 50)

# 只设置正文，不设置标题
logger.info(content_target=LogTarget.FILE, content="这是一条详细的日志记录...")
```

#### 实际应用场景

标题和正文分离功能在以下场景特别有用：

1. **数据展示**：在控制台显示简洁的标题，同时在日志文件中记录完整的数据表格
   ```python
   logger.info(
       title_target=LogTarget.BOTH, 
       title=f"\n【当前进度：显示保存数据】{symbol} - {period} (数据头部{head_rows}行)",
       content_target=LogTarget.FILE, 
       content=f"{head_table}\n"
   )
   ```

2. **进度报告**：在控制台实时更新进度，同时在日志文件中记录详细信息
   ```python
   logger.info(
       title_target=LogTarget.CONSOLE, 
       title=f"处理进度: {progress}%", 
       content_target=LogTarget.FILE, 
       content=f"详细处理状态: 已处理{processed_count}/{total_count}项，当前处理: {current_item}"
   )
   ```

3. **错误报告**：在控制台显示简洁的错误提示，同时在日志文件中记录详细的错误信息和堆栈跟踪
   ```python
   logger.error(
       title_target=LogTarget.BOTH, 
       title=f"处理{file_name}时发生错误: {e}", 
       content_target=LogTarget.FILE, 
       content=f"详细错误信息:\n{traceback.format_exc()}"
   )
   ```

### 新增：纯关键字参数调用方式

从 v2.3.0 版本开始，增强型日志系统支持纯关键字参数调用方式，无需提供位置参数，使代码更加清晰可读：

```python
# 旧方式 - 必须提供位置参数 target_or_msg
logger.info(LogTarget.BOTH, "这条日志同时输出到文件和控制台")

# 新方式 - 使用纯关键字参数，不需要位置参数
logger.info(title_target=LogTarget.BOTH, title="这条日志同时输出到文件和控制台")
logger.info(content_target=LogTarget.FILE, content="这条日志只记录到文件")

# 同时使用标题和正文
logger.info(
    title_target=LogTarget.CONSOLE,
    title="【控制台标题】进度更新",
    content_target=LogTarget.FILE,
    content="详细进度数据..."
)
```

#### 纯关键字参数调用的优势

1. **更清晰的代码**：明确指定每个参数的作用，提高代码可读性
2. **更灵活的控制**：可以只指定需要的参数，其他参数使用默认值
3. **减少错误**：避免位置参数顺序错误导致的问题
4. **更好的IDE支持**：IDE可以提供更好的参数提示

#### 兼容性说明

新的纯关键字参数调用方式与旧的位置参数调用方式完全兼容，您可以根据喜好选择使用方式。为了代码一致性和可读性，建议在新代码中使用纯关键字参数调用方式。

## 格式化器

本模块提供了几种不同的日志格式化器：

1. **默认格式化器**：使用标准格式输出日志
2. **增强型格式化器**（推荐使用）：提供更多高级功能，包括：
   - 根据日志级别添加颜色
   - 根据模块名称添加颜色
   - 支持用户自定义前置内容
   - 为相关日志添加缩进，表示层次关系
   - 突出显示重要信息
3. **表格式Markdown格式化器**：使用表格式布局，各字段用竖线(|)分隔，提供更清晰的日志格式

可以通过`get_formatter`函数获取不同类型的格式化器：

```python
from utils.logger.formatters import get_formatter

# 获取默认格式化器
default_formatter = get_formatter('default')

# 获取增强型格式化器 (推荐)
enhanced_formatter = get_formatter(
    'enhanced', 
    task_id='TASK123',
    use_colors=True,
    use_icons=True,
    module_width=30,
    function_width=30
)

# 获取表格式Markdown格式化器
table_formatter = get_formatter(
    'table_markdown',
    task_id='TASK123',
    use_colors=True,
    module_width=30,
    function_width=30
)
```

## 使用增强型日志系统

通过设置`use_enhanced_formatter=True`可以启用增强型日志格式化器：

```python
from utils.logger import setup_unified_logging

# 启用增强型格式化器
setup_unified_logging(
    use_enhanced_formatter=True,
    use_colors=True,
    module_width=30,  # 设置模块名显示宽度
    function_width=30  # 设置函数名显示宽度
)
```

增强型格式化器的主要特点：

1. **前置内容**：允许用户为日志添加自定义前置内容，如分隔线、换行符或标题
2. **颜色标识**：根据日志级别和模块名称添加颜色，便于快速识别
3. **层次缩进**：自动为相关操作的日志添加缩进，表示层次关系
4. **操作分组**：自动识别操作的开始和结束，将相关日志分组显示
5. **函数级别日志**：自动检测并显示调用日志的实际函数名，格式为"模块名【函数名】"

### 使用日志前置内容

增强型日志系统支持为日志添加自定义前置内容，这对于创建视觉上的分隔或组织日志很有用：

```python
from utils.logger import get_unified_logger, LogTarget

logger = get_unified_logger("my_module")

# 添加简单分隔线
logger.info(LogTarget.FILE, "========================================", "开始处理数据")

# 添加标题分隔线
logger.info(LogTarget.FILE, "======================数据下载======================", "开始下载数据")

# 添加多个换行符，在日志中创建空白区域
logger.info(LogTarget.FILE, "\n\n\n\n\n", "这条日志前面有多个空行")

# 混合内容
logger.info(LogTarget.FILE, "\n\n========================================\n", "新的处理阶段")
```

#### 常见的前置内容模式

以下是一些常见的前置内容模式，可根据需要自定义：

```python
# 简单分隔线
prefix_separator = "=" * 80

# 带标题的分隔线
def title_separator(title):
    line = "=" * 80
    start_pos = (len(line) - len(title)) // 2 - 1
    if start_pos < 0:
        start_pos = 0
    return line[:start_pos] + " " + title + " " + line[start_pos + len(title) + 2:]

# 多行空白
prefix_blank_lines = "\n" * 10

# 使用示例
logger.info(LogTarget.FILE, prefix_separator, "开始新的处理阶段")
logger.info(LogTarget.FILE, title_separator("数据处理"), "开始处理数据")
logger.info(LogTarget.FILE, prefix_blank_lines, "在日志中创建一个视觉分隔区")
```

## API参考

### 核心函数

- `get_unified_logger(name, level="info", enhanced=True, ...)`: 获取统一配置的日志记录器（推荐）
- `setup_unified_logging(...)`: 设置统一的日志系统
  - `default_target`: 默认日志输出目标，可以是`LogTarget.FILE`（只输出到文件，默认值）、`LogTarget.CONSOLE`（只输出到控制台）或`LogTarget.BOTH`（两者都输出）
- `clean_old_log_files(days_to_keep=7)`: 清理指定天数之前的日志文件

### 常量

- `LogTarget.FILE`: 只输出到文件
- `LogTarget.CONSOLE`: 只输出到控制台
- `LogTarget.BOTH`: 同时输出到文件和控制台

### 过滤器

- `TargetFilter`: 根据目标类型过滤日志记录

## 最佳实践

1. **使用统一日志系统**：优先使用`setup_unified_logging`和`get_unified_logger`
2. **明确指定目标**：为每条日志明确指定输出目标，避免不必要的重复
3. **集中配置**：使用`setup_unified_logging`集中配置日志系统
4. **使用任务ID**：利用自动生成的任务ID关联相关日志，便于跟踪和调试
5. **适当使用过滤器**：使用过滤器控制日志流向，避免信息过载
6. **定期清理日志**：使用`clean_old_log_files`定期清理旧日志文件
7. **使用LogTarget枚举**：使用`LogTarget.FILE`/`LogTarget.CONSOLE`/`LogTarget.BOTH`代替数字代码
8. **使用EnhancedFormatter**：优先使用增强型格式化器，而非旧的格式化器
9. **分离标题和正文**：对于需要在控制台显示简洁信息但在日志文件中记录详细内容的场景，使用标题和正文分离功能
10. **利用函数级别日志**：利用自动显示的函数名信息，更容易追踪代码执行流程和定位问题

# 统一日志系统

本模块提供了一个统一的日志系统，用于整个项目的日志记录。

## 主要功能

- 统一的日志配置和管理
- 支持多种日志输出目标（控制台、文件、网络等）
- 支持日志级别控制
- 支持日志格式化
- 支持日志文件轮转
- 支持日志过滤
- 支持任务ID关联，便于跟踪相关日志
- 支持日志文件清理

## 主要组件

- `manager.py`: 日志管理器，提供统一的日志接口
- `handlers.py`: 自定义日志处理器（计划中）
- `formatters.py`: 自定义日志格式化器
- `filters.py`: 自定义日志过滤器
- `decorators.py`: 日志装饰器
- `config.py`: 日志配置
- `examples.py`: 日志系统示例和测试

## 使用方法

### 基本使用

```python
from utils.logger import get_unified_logger

# 获取日志记录器
logger = get_unified_logger(__name__)

# 记录日志
logger.info("这是一条信息日志")
logger.warning("这是一条警告日志")
logger.error("这是一条错误日志")
```

### 增强日志

```python
from utils.logger import get_unified_logger, LogTarget

# 获取增强日志记录器
logger = get_unified_logger(__name__, enhanced=True)

# 同时记录到控制台和文件
logger.info(LogTarget.BOTH, "这条日志会同时记录到控制台和文件")

# 只记录到文件
logger.debug(LogTarget.FILE, "这条日志只会记录到文件")

# 只记录到控制台
logger.warning(LogTarget.CONSOLE, "这条日志只会记录到控制台")
```

### 设置日志级别

```python
from utils.logger import setup_unified_logging

# 设置全局日志级别
setup_unified_logging(log_level="debug")
```

### 获取日志文件路径

```python
from utils.logger import get_log_file_path

# 获取特定模块的日志文件路径
log_path = get_log_file_path("my_module")
```

### 清理旧日志文件

```python
from utils.logger import clean_old_log_files

# 清理7天前的日志文件
clean_old_log_files(days_to_keep=7)
```

### 测试日志系统

可以通过以下方式测试日志系统是否正常工作：

1. 使用命令行参数：

```bash
# 在项目根目录下运行
python data/data_main.py --test-logging
```

2. 直接运行测试模块：

```bash
# 在项目根目录下运行
python utils/logger/examples.py --test
```

3. 在代码中调用测试函数：

```python
from utils.logger.examples import test_logging_system

# 运行日志系统测试
test_logging_system()
```

## 最近更新

- **统一化改进**: 整合了项目中的多个日志实现，确保所有模块使用统一的日志系统
  - 将直接使用 `logging.getLogger()` 的代码替换为 `get_unified_logger()`
  - 删除了旧的日志记录器实现文件和兼容层
  - 更新了 `config/logging_config.py` 作为简化的兼容层
- **格式化器优化**: 移除了旧的格式化器，只保留EnhancedFormatter和TableMarkdownFormatter
- **日志目标表示统一**: 推荐使用LogTarget枚举代替数字代码来指定日志输出目标
- **日志解析增强**: 日志查看工具现在支持多种日志格式，减少了对特定格式的依赖

## 最佳实践

1. 始终使用 `get_unified_logger()` 获取日志记录器
2. 为每个模块使用唯一的日志记录器名称（通常是 `__name__`）
3. 适当使用日志级别（DEBUG, INFO, WARNING, ERROR, CRITICAL）
4. 使用 `LogTarget` 枚举指定日志输出目标
5. 定期清理旧日志文件
6. 使用增强型格式化器（EnhancedFormatter）而非旧的格式化器
7. 使用LogTarget枚举：使用`LogTarget.FILE`/`LogTarget.CONSOLE`/`LogTarget.BOTH`代替数字代码
8. 使用EnhancedFormatter：优先使用增强型格式化器，而非旧的格式化器
9. 分离标题和正文：对于需要在控制台显示简洁信息但在日志文件中记录详细内容的场景，使用标题和正文分离功能
10. 利用函数级别日志：利用自动显示的函数名信息，更容易追踪代码执行流程和定位问题

## 日志查看工具

本模块提供了一个强大的日志查看工具，用于方便地查看、过滤、搜索和分析日志文件。

### 主要功能

- **多种显示模式**：支持平铺、树形和按操作分组三种查看模式
- **过滤功能**：可按时间、任务ID、日志级别、模块名称和消息内容进行过滤
- **彩色显示**：根据日志级别使用不同颜色显示，提高可读性
- **操作分组**：自动识别操作的开始和结束，对相关日志进行分组
- **详细模式**：可显示日志行号等额外信息，便于定位问题
- **导出功能**：支持导出为文本或HTML格式
- **摘要模式**：只显示关键信息，减少输出量
- **多格式支持**：支持多种日志格式，包括标准格式、无任务ID格式和简单格式

### 使用方法

#### 命令行使用

```bash
# 查看最新日志文件
python -m utils.logger.viewer

# 查看特定日志文件
python -m utils.logger.viewer --log-file=logs/quant_20250606.log

# 按操作分组查看
python -m utils.logger.viewer --view-mode=grouped

# 查看特定模块的日志
python -m utils.logger.viewer --module=data_source_manager

# 按树形结构查看
python -m utils.logger.viewer --view-mode=tree

# 导出日志为HTML
python -m utils.logger.viewer --export-to=log_report.html

# 搜索特定内容
python -m utils.logger.viewer --message="下载数据"

# 显示详细信息
python -m utils.logger.viewer --detailed
```

#### 可用参数

- `--log-file`: 指定要查看的日志文件路径
- `--start-time`: 过滤开始时间，格式：YYYY-MM-DD HH:MM:SS
- `--end-time`: 过滤结束时间，格式：YYYY-MM-DD HH:MM:SS
- `--task-id`: 过滤特定任务ID
- `--level`: 过滤特定日志级别
- `--module`: 过滤特定模块
- `--message`: 过滤包含特定内容的日志
- `--export-to`: 导出日志到指定文件
- `--no-color`: 禁用颜色显示
- `--operations`: 查看操作及其相关日志
- `--show-details`: 显示操作的详细日志
- `--group-by-operation`: 按操作分组显示日志
- `--detailed`: 显示详细信息，包括日志条目的行号
- `--summary`: 仅显示操作摘要，不显示详细日志
- `--view-mode`: 日志查看模式，可选：flat（平铺显示）、tree（树形显示）、grouped（按操作分组）

### 在代码中使用

```python
from utils.logger.viewer import view_logs, view_operations, LogViewer

# 查看最新日志
view_logs()

# 查看特定模块的日志
view_logs(module="data_source_manager")

# 查看操作及其相关日志
view_operations(show_details=True)

# 使用LogViewer类进行高级操作
viewer = LogViewer("logs/quant_20250606.log")
viewer.parse_log_file()

# 过滤日志
filtered_logs = viewer.filter_logs(level="INFO", module="data_source_manager")

# 搜索日志
search_results = viewer.search("下载完成")

# 导出为HTML
viewer.export_logs("log_report.html", filtered_logs, use_colors=True)
```

## API参考

### 日志查看工具函数

- `view_logs(...)`: 查看日志文件，支持多种过滤选项和导出功能
- `view_operations(...)`: 查看操作及其相关日志
- `get_latest_log_file(...)`: 获取最新的日志文件路径

## 最佳实践

1. **使用日志查看工具**：使用`utils.logger.viewer`模块查看和分析日志文件
2. **按操作分组查看**：使用`--view-mode=grouped`选项按操作分组查看日志，更容易理解流程
3. **导出为HTML**：使用`--export-to=file.html`选项导出日志为HTML格式，便于在浏览器中查看

### 新功能：标题前置显示

从 v2.4.0 版本开始，当使用标题和正文分离功能时，标题会显示在日志头部之前，使其更加突出和易于识别。这对于需要在大量日志中快速定位重要信息的场景特别有用。

示例输出格式：
```
【当前进度：标准化需要下载的股票代码】2025-06-09 03:40:28,832 - data_commands - INFO -
```

日志标题与后面的时间戳信息直接相连，不进行换行，这样便于查看和检索。

### 函数级别日志

增强型日志系统现在支持自动检测并显示调用日志的实际函数名和行号，格式为"模块名【函数名】(行号)"。这对于调试和追踪代码执行流程非常有用：

```python
def process_data(data):
    logger = get_unified_logger("my_module")
    logger.debug("开始处理数据")
    # 输出: 【TASK123】2023-06-15 12:34:56 | my_module【process_data】(45)| DEBUG | 开始处理数据
    
    # 处理数据...
    
    logger.info("数据处理完成")
    # 输出: 【TASK123】2023-06-15 12:34:57 | my_module【process_data】(50)| INFO | 数据处理完成
```

#### 函数名显示的特点

1. **自动检测**：系统会自动检测调用日志方法的实际函数名和行号，无需手动指定
2. **跳过内部调用**：系统会跳过日志系统内部的调用，找到实际的用户代码调用
3. **格式统一**：函数名使用【函数名】(行号)格式显示，便于识别和定位
4. **固定宽度显示**：函数名使用固定宽度显示，过长时从左侧截断，保持视觉一致性
5. **支持所有格式化器**：增强型格式化器和表格式Markdown格式化器都支持此功能

## 新功能

### 表格式Markdown日志格式化器

新增了`TableMarkdownFormatter`格式化器，提供以下功能：

1. **表格式布局**：使用竖线(|)分隔各字段，使日志更整洁清晰
2. **模块名右对齐**：模块名设置固定宽度并右对齐，超长时从左侧截断并添加省略号
3. **Markdown层级结构**：使用#、##等符号表示层级结构，直观展示操作的嵌套关系
4. **颜色支持**：可选择是否使用颜色，支持为不同模块和日志级别应用不同颜色
5. **函数名显示**：自动显示调用函数名和行号，格式为【函数名】(行号)，与增强型格式化器一致

使用示例：

```python
from utils.logger import setup_unified_logging, get_unified_logger

# 使用表格式Markdown格式化器初始化日志系统
setup_unified_logging(
    use_enhanced_formatter=False,  # 不使用默认的增强型格式化器
    use_table_formatter=True,      # 使用表格式Markdown格式化器
    module_width=30,               # 设置模块名显示宽度
    function_width=30              # 设置函数名显示宽度
)

# 获取日志记录器并记录日志
logger = get_unified_logger("my_module")
logger.info("这是一条普通日志")
logger.info("开始处理数据")  # 自动增加层级
logger.info("读取文件")      # 自动增加缩进
logger.info("处理完成")      # 自动减少层级
```

输出效果示例：

```
【1QLEKQ】2025-06-08 21:43:10,221 |                    test_module【test_function】(45)| INFO | # 这是一条测试日志     
【1QLEKQ】2025-06-08 21:43:10,221 |                    test_module【process_data】(67)| INFO | ## 开始处理数据        
【1QLEKQ】2025-06-08 21:43:10,221 |                    test_module【read_file】(89)| INFO | ## 读取文件
【1QLEKQ】2025-06-08 21:43:10,221 |                    test_module【process_data】(120)| INFO | # 处理完成
```

使用表格式Markdown格式化器的优点：

1. **整齐一致的布局**：所有字段按照固定宽度对齐，使日志更易于阅读和理解
2. **清晰的层级结构**：使用Markdown风格的#、##等符号表示层级关系，直观反映操作的嵌套层级
3. **模块名右对齐**：模块名固定宽度并右对齐，超长时从左侧截断，保持视觉一致性
4. **更好的可读性**：使用竖线分隔各字段，即使在不同终端环境下也能保持良好的可读性
5. **函数名显示**：自动显示调用函数名，便于追踪代码执行流程

要启用表格式Markdown格式化器，只需在初始化日志系统时设置`use_table_formatter=True`：

```python
from utils.logger import setup_unified_logging, LogTarget

# 在主程序入口处初始化日志系统
setup_unified_logging(
    default_target=LogTarget.FILE,
    use_table_formatter=True,  # 启用表格式Markdown格式化器
    module_width=30,  # 设置模块名显示宽度为30
    function_width=30  # 设置函数名显示宽度为30
)
```

### 控制初始化消息

日志系统初始化时可以控制是否打印初始化完成消息：

```python
from utils.logger import setup_unified_logging, LogTarget

# 默认不打印初始化消息
setup_unified_logging()

# 显式指定不打印初始化消息
setup_unified_logging(print_init_message=False)

# 显式指定打印初始化消息
setup_unified_logging(print_init_message=True)
```

建议在主程序中设置`print_init_message=True`，而在其他模块中设置`print_init_message=False`，以避免重复打印初始化消息。

## 使用标题和内容

您可以使用title和content参数为日志添加标题和内容：

```python
logger.info(
    title_target=LogTarget.BOTH,
    title="【操作完成】",
    content_target=LogTarget.FILE,
    content="详细数据..."
)
```

这将在控制台和文件中显示标题，但只在文件中显示详细内容。

日志标题格式示例：
```
【当前进度：标准化需要下载的股票代码】2025-06-09 03:40:28,832 - data_commands - INFO -
```

日志标题与后面的时间戳信息直接相连，不进行换行，这样便于查看和检索。