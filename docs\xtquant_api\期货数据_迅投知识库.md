期货数据 | 迅投知识库


[![迅投知识库](/images/logo.png)迅投知识库](/)

[首页](/)

APIAPI

* [投研新手教程](/freshman/ty_rookie.html)
* [QMT新手教程](/freshman/rookie.html)
* [内置Python](/innerApi/start_now.html)
* [XtQuant文档](/nativeApi/start_now.html)
* [VBA](/VBA/start_now.html)

数据字典数据字典

* [快速开始](/dictionary/)
* [股票数据](/dictionary/stock.html)
* [行业概念数据](/dictionary/industry.html)
* [指数数据](/dictionary/indexes.html)
* [期货数据](/dictionary/future.html)
* [期权数据](/dictionary/option.html)
* [场内基金数据](/dictionary/floorfunds.html)
* [债券数据](/dictionary/bond.html)
* [常见问题](/dictionary/question_answer.html)
* [场景化示例](/dictionary/scenario_based_example.html)
* [迅投因子](/dictionary/xuntou_factor.html)

策略迁移策略迁移

* [聚宽策略](/strategy/JoinQuant2QMT.html)
* #### 

  + [客服QQ: 810315303](/dictionary/future.html)
  + [联系方式: 18309226715](/dictionary/future.html)

视频教程视频教程

* [投研端教程](/videos/touyan/ty_register_install.html)

投研平台 在新窗口打开

迅投社区 在新窗口打开

[迅投官网 在新窗口打开](http://www.thinktrader.net)

注册

登录

![微信扫码联系客服](/assets/wechat-d90fd08f.png "点击联系客服")

![分享链接](data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAHqADAAQAAAABAAAAHgAAAADKQTcFAAADjElEQVRIDcVXW0hVQRRdM/fce/OVpfRA8dENDckkMILsYRG9PnqQQUkg9NFfBCFEJJSFRj8R+BP4URREGEVGRNSXWEiE1odoDx+lhkoWpTe1+zrT7KPnes59ddQbDujM7D17rbNn9uzZl8FCqxaC36l1l4iAekgIFDOwDEDIP2psUEAMMoY2ZuONFVUpLdWMqVO66P9ZdBWw/ZZY9GXAfZqpolKCL4+1VtfJj/omOLuWm5VS13SC/dHloX1UYtcld5lA4Lr0MCvUyMpc7sAAg+1M78WUh5HW81ChEIKtqh6rVUXgwVxJCZNsCYOwCDOUxySgBa7LY/dkfzR04XzmjLGG3guLy2UvdByTx3J7a+JNSkSESdg6KfVBj+lMaWuMyniPObMd0c9c85iilwIpHkSZqQyudNNGBmGJg7hIoK2gKzOfQKJt27xawc41dtytSELesijEMuCISyOm5ED3lCazbXaJv6fAjvrjyShcaUPlDidy0mzoHI6eP4hL43TVjG1R/erL2ZAm2IF9ax0oW+9EWiLH0w4PSl02bMhW4PYIFF0diwnHFb5VoTQYc5VBmZrAcLDIgf2FTiQ7p+LyxQcvijO5RkpLO4cDBovIQ+JU5NkWR1bPSFekMByW3u0tcMChBC8Cmrq8yF0iU2ue3ILpZolYckoYliHzsG5n6rOWchwrdqJUAttkDjS2ll4fkuwCB9Y5jWJLHhOnMvPKmOy1yfndichNt4Up2vp9mPAEcGqbdjNM+o6hf281cUaO+2mo2ucTaB/ym4DbB/34/MMfkdQXEOgeiR7RQSAGIYnZYFAQMvj6S8XZR+Ooa5rAuFfg/bAfrX1eVO0K95RMuySpzwIvBBtS6BGXNvkhnKbps04fmrt92CivS315ImSyN+n1iZXAorXEyaly0A1j9eNeYJNLgcIjk5KtVWKJ0CrzNm+MRWjUvekP4KPcztHJyLfAMrHCH3OqkahcMRLEGguZ3uuaPWh466XnzrTUCjFxESenwoxqJBNClEnPSAA3Xk3i5msPzj2ZRPntcfR8n7o+Az9VmS6jGBrExEWc2oHRU9XXP/ppLi+UQ17zkyVOjPxWcf+dz0ARPqQ6LCc7NZ+KwGCkLEghQN9GlQEDvxL+nfGRELZefRBi0GOayGBZmGKPqkCtGoyj55qnIRVmmMck0Bud+f8s6E1brZPq/YL8hNHJqacaKd4/2v4CgdaZJ2zGqYAAAAAASUVORK5CYII= "分享链接")

[首页](/)

APIAPI

* [投研新手教程](/freshman/ty_rookie.html)
* [QMT新手教程](/freshman/rookie.html)
* [内置Python](/innerApi/start_now.html)
* [XtQuant文档](/nativeApi/start_now.html)
* [VBA](/VBA/start_now.html)

数据字典数据字典

* [快速开始](/dictionary/)
* [股票数据](/dictionary/stock.html)
* [行业概念数据](/dictionary/industry.html)
* [指数数据](/dictionary/indexes.html)
* [期货数据](/dictionary/future.html)
* [期权数据](/dictionary/option.html)
* [场内基金数据](/dictionary/floorfunds.html)
* [债券数据](/dictionary/bond.html)
* [常见问题](/dictionary/question_answer.html)
* [场景化示例](/dictionary/scenario_based_example.html)
* [迅投因子](/dictionary/xuntou_factor.html)

策略迁移策略迁移

* [聚宽策略](/strategy/JoinQuant2QMT.html)
* #### 

  + [客服QQ: 810315303](/dictionary/future.html)
  + [联系方式: 18309226715](/dictionary/future.html)

视频教程视频教程

* [投研端教程](/videos/touyan/ty_register_install.html)

投研平台 在新窗口打开

迅投社区 在新窗口打开

[迅投官网 在新窗口打开](http://www.thinktrader.net)

* 数据字典

  + [快速开始](/dictionary/)
  + [股票数据](/dictionary/stock.html)
  + [行业概念数据](/dictionary/industry.html)
  + [指数数据](/dictionary/indexes.html)
  + [期货数据](/dictionary/future.html) 
    - [获取期货合约信息](/dictionary/future.html#获取期货合约信息) 
      * [市场简称代码](/dictionary/future.html#市场简称代码)
      * [获取期货交易日历](/dictionary/future.html#获取期货交易日历)
      * [获取当前所有期货代码](/dictionary/future.html#获取当前所有期货代码)
      * [获取合约基础信息](/dictionary/future.html#获取合约基础信息)
      * [获取当前主力合约](/dictionary/future.html#获取当前主力合约)
      * [获取历史主力合约](/dictionary/future.html#获取历史主力合约) 
        + [内置python](/dictionary/future.html#内置python)
        + [原生python](/dictionary/future.html#原生python)
    - [获取期货行情数据](/dictionary/future.html#获取期货行情数据) 
      * [获取行情数据](/dictionary/future.html#获取行情数据) 
        + [获取日线行情数据](/dictionary/future.html#获取日线行情数据)
        + [获取tick行情](/dictionary/future.html#获取tick行情)
        + [获取5档盘口行情](/dictionary/future.html#获取5档盘口行情)
        + [期货结算价与持仓量](/dictionary/future.html#期货结算价与持仓量)
      * [期货仓单](/dictionary/future.html#期货仓单)
      * [期货席位](/dictionary/future.html#期货席位)
      * [现货数据](/dictionary/future.html#现货数据)
    - [期货列表](/dictionary/future.html#期货列表) 
      * [金融期货列表](/dictionary/future.html#金融期货列表)
      * [支持的金融期货种类](/dictionary/future.html#支持的金融期货种类) 
        + [沪深 300 指数期货合约表](/dictionary/future.html#沪深-300-指数期货合约表)
        + [上证 50 股指期货合约表](/dictionary/future.html#上证-50-股指期货合约表)
        + [中证 500 股指期货合约表](/dictionary/future.html#中证-500-股指期货合约表)
        + [中证 1000 股指期货合约表](/dictionary/future.html#中证-1000-股指期货合约表)
        + [2 年期国债期货合约表](/dictionary/future.html#_2-年期国债期货合约表)
        + [5 年期国债期货合约表](/dictionary/future.html#_5-年期国债期货合约表)
        + [10 年期国债期货合约表](/dictionary/future.html#_10-年期国债期货合约表)
      * [商品期货列表](/dictionary/future.html#商品期货列表)
      * [主力连续合约及加权](/dictionary/future.html#主力连续合约及加权) 
        + [中金所](/dictionary/future.html#中金所)
        + [大商所](/dictionary/future.html#大商所)
        + [上海国际能源交易中心](/dictionary/future.html#上海国际能源交易中心)
        + [广期所](/dictionary/future.html#广期所)
        + [上期所](/dictionary/future.html#上期所)
        + [郑商所](/dictionary/future.html#郑商所)
  + [期权数据](/dictionary/option.html)
  + [场内基金](/dictionary/floorfunds.html)
  + [债券数据](/dictionary/bond.html)
  + [常见问题](/dictionary/question_answer.html)
  + [场景化示例](/dictionary/scenario_based_example.html)
  + [迅投因子](/dictionary/xuntou_factor.html)

[#](#获取期货合约信息) 获取期货合约信息
-----------------------

### [#](#市场简称代码) 市场简称代码

| 市场 | 市场代码 | 迅投市场代码 |
| --- | --- | --- |
| `上期所` | `SHFE` | `SF` |
| `大商所` | `DCE` | `DF` |
| `郑商所` | `CZCE` | `ZF` |
| `中金所` | `CFFEX` | `IF` |
| `能源中心` | `INE` | `INE` |
| `广期所` | `GFEX` | `GF` |

### [#](#获取期货交易日历) 获取期货交易日历

获取期货交易日历

python

```
from xtquant import xtdata
xtdata.get_trading_dates(market, start_time='', end_time='', count=-1)

```

**参数**

| 参数名称 | 数据类型 | 描述 |
| --- | --- | --- |
| `market` | `string` | 市场代码 |
| `start_time` | `string` | 起始时间（格式：%Y%m%d） |
| `end_time` | `string` | 结束时间（格式：%Y%m%d） |
| `count` | `int` | 数据个数 (-1代表获取所有日历) |

**返回值**

* `list` 时间戳列表，[ date1, date2, ... ]

**示例：**

示例返回值

```
from xtquant import xtdata
date_list = xtdata.get_trading_dates('SF', start_time='', end_time='', count=30)
print(date_list)

```

```
[1692288000000,
 1692547200000,
 1692633600000,
 1692720000000,
 1692806400000,
 1692892800000,
 1693152000000,
 1693238400000,
 1693324800000,
 1693411200000,
 1693497600000,
 1693756800000,
 1693843200000,
 1693929600000,
 1694016000000,
 1694102400000,
 1694361600000,
 1694448000000,
 1694534400000,
 1694620800000,
 1694707200000,
 1694966400000,
 1695052800000,
 1695139200000,
 1695225600000,
 1695312000000,
 1695571200000,
 1695657600000,
 1695744000000,
 1695830400000]

```

### [#](#获取当前所有期货代码) 获取当前所有期货代码

提示

需要先下载板块分类信息

python

```
from xtquant import xtdata
xtdata.get_stock_list_in_sector(sector_name)

```

**参数**

| 参数名称 | 数据类型 | 描述 |
| --- | --- | --- |
| `sector_name` | `string` | 市场代码 |

**返回值**

`list` 成分股列表，[ code1, code2, ... ]

**示例**

示例返回值

```
from xtquant import xtdata
# 获取上海期货交易所所有标的的代码
SF_list = xtdata.get_stock_list_in_sector("SF")
SF_list = xtdata.get_stock_list_in_sector("上期所期货")

# 主力合约列表
main_list = xtdata.get_stock_list_in_sector("主力板块")

# 显示列表返回值的前十个代码
print(SF_list[:10])

print(main_list[:10])

```

```
['ag00.SHFE', 'ag01.SHFE', 'ag02.SHFE', 'ag03.SHFE', 'ag04.SHFE', 'ag05.SHFE', 'ag06.SHFE', 'ag07.SHFE', 'ag08.SHFE', 'ag09.SHFE']

```

### [#](#获取合约基础信息) 获取合约基础信息

python

```
from xtquant import xtdata
xtdata.get_instrument_detail(stock_code)

```

**参数**

| 参数名称 | 数据类型 | 描述 |
| --- | --- | --- |
| `stock_code` | `string` | 合约代码 |

**返回值**

`dict` 数据字典，{ field1 : value1, field2 : value2, ... }，找不到指定合约时返回`None`

| 参数名称 | 数据类型 | 描述 |
| --- | --- | --- |
| `ExchangeID` | `string` | 合约市场代码 |
| `InstrumentID` | `string` | 合约代码 |
| `InstrumentName` | `string` | 合约名称 |
| `ProductID` | `string` | 合约的品种ID(期货) |
| `ProductName` | `string` | 合约的品种名称(期货) |
| `CreateDate` | `str` | 上市日期(期货) |
| `OpenDate` | `str` | IPO日期(股票) |
| `ExpireDate` | `int` | 退市日或者到期日 |
| `PreClose` | `float` | 前收盘价格 |
| `SettlementPrice` | `float` | 前结算价格 |
| `UpStopPrice` | `float` | 当日涨停价 |
| `DownStopPrice` | `float` | 当日跌停价 |
| `FloatVolume` | `float` | 流通股本 |
| `TotalVolume` | `float` | 总股本 |
| `LongMarginRatio` | `float` | 多头保证金率 |
| `ShortMarginRatio` | `float` | 空头保证金率 |
| `PriceTick` | `float` | 最小价格变动单位 |
| `VolumeMultiple` | `int` | 合约乘数(对期货以外的品种，默认是1) |
| `MainContract` | `int` | 主力合约标记，1、2、3分别表示第一主力合约，第二主力合约，第三主力合约 |
| `LastVolume` | `int` | 昨日持仓量 |
| `InstrumentStatus` | `int` | 合约已停牌日期（停牌第一天值为0，第二天为1，以此类推。注意，正常交易的股票该值也是0） |
| `IsTrading` | `bool` | 合约是否可交易 |
| `IsRecent` | `bool` | 是否是近月合约 |

**示例**

示例返回值

```
from xtquant import xtdata
print(xtdata.get_instrument_detail("rb2401.SF"))

```

```
{"ExchangeID": "SHFE",
 "InstrumentID": "rb2401",
 "InstrumentName": "螺纹钢2401",
 "ProductID": "rb",
 "ProductName": "",
 "ExchangeCode": None,
 "UniCode": None,
 "CreateDate": "0",
 "OpenDate": "20230117",
 "ExpireDate": 20240115,
 "PreClose": 3682.0,
 "SettlementPrice": 3684.0,
 "UpStopPrice": 3941.0,
 "DownStopPrice": 3426.0,
 "FloatVolume": 0.0,
 "TotalVolume": 0.0,
 "LongMarginRatio": 0.09,
 "ShortMarginRatio": 0.09,
 "PriceTick": 1.0,
 "VolumeMultiple": 10,
 "MainContract": 1,
 "LastVolume": 1767758,
 "InstrumentStatus": 2147483647,
 "IsTrading": True,
 "IsRecent": False,
 "ProductTradeQuota": 0,
 "ContractTradeQuota": 0,
 "ProductOpenInterestQuota": 0,
 "ContractOpenInterestQuota": 176776}

```

### [#](#获取当前主力合约) 获取当前主力合约

python

```
xtdata.get_main_contract(code)

```

| 参数名称 | 数据类型 | 描述 |
| --- | --- | --- |
| `code` | `str` | 品种代码如rb00.SF(螺纹钢连续合约) |

**返回值**

* `str`,主力合约代码

**示例**

示例返回值

```
from xtquant import xtdata
main_symbol = xtdata.get_main_contract('rb00.SF')
print(main_symbol)

```

```
# 返回的主力合约代码
"rb2401.SF"

```

### [#](#获取历史主力合约) 获取历史主力合约

#### [#](#内置python) 内置python

提示

1. 该数据为[VIP数据在新窗口打开](https://xuntou.net/#/productvip)
2. 该函数支持实盘/回测两种模式
3. 若要使用该函数获取历史主力合约，必须要先下载`历史主力合约`数据
4. `历史主力合约`数据目前通过`界面端数据管理 - 过期合约数据 - 历史主力合约`下载

**原型**

内置python

```
ContextInfo.get_main_contract(codemarket)
ContextInfo.get_main_contract(codemarket,date="")
ContextInfo.get_main_contract(codemarket,startDate="",endDate="")

```

**释义**

获取当前期货主力合约

**参数**

| 字段名 | 数据类型 | 解释 |
| --- | --- | --- |
| `codemarket` | `string` | 合约和市场，合约格式为品种名加00，如IF00.IF，zn00.SF |
| `startDate` | `string` | 开始日期(可以不写),如20180608 |
| `endDate` | `string` | 结束日期(可以不写),如20190608 |

**返回值**

`str`，合约代码

**示例**

示例返回值

```
# coding:gbk
def init(C):
	pass
	
def handlebar(C):
	symbol1 = C.get_main_contract('IF00.IF')# 获取当前主力合约

	symbol2 = C.get_main_contract('IF00.IF',"20190101")# 获取指定日期主力合约

	symbol3 = C.get_main_contract('IF00.IF',"20181101","20190101") # 获取时间段内全部主力合约

	print(symbol1, symbol2)
	print("="*10)
	print(symbol3)

```

```
IF2312
==========
IF1901
==========
1540137600000    IF1811
1545321600000    IF1901
dtype: object

```

#### [#](#原生python) 原生python

提示

1. 获取该数据前，需要通过`download_history_data`接口下载数据，下载时`period`参数需指定为`historymaincontract`
2. 该数据通过get\_market\_data\_ex函数进行获取
3. 该数据为[VIP数据在新窗口打开](https://xuntou.net/#/productvip)

python

```
period = "historymaincontract" # 通过指定period参数 从gmd_ex接口获取历史主力合约信息
symbol = "IF00.IF" # 示例合约
xtdata.get_market_data_ex([], [symbol], period=period, start_time='', end_time='', count=-1,dividend_type='none', fill_data=False)

```

**参数**

* period参数需填写为`"historymaincontract"`
* stock\_list里的合约需为主力连续合约，如`IF00.IF`

其余参数与`get_market_data_ex`一致

**返回值**

`dict`类型数据,其中:

* key为symbol
* values为pandas.DataFrame
  + values.index为自增行，columns为[time,合约在交易所的代码]

**示例**

python返回值

```
from xtquant import xtdata


symbol = 'IF00.IF' # 合约需要是主连合约
period = "historymaincontract" # period需指定为 "historymaincontract"
# 下载历史主力合约
xtdata.download_history_data(symbol, period, '', '') # 获取之前需要先下载到本地

his_main_contract = xtdata.get_market_data_ex([],[symbol],period) # 获取数据查看

print(his_main_contract)


```

```
{'IF00.IF':               time 合约在交易所的代码
 0    1366128000000    IF1304
 1    1366300800000    IF1305
 2    1368633600000    IF1306
 3    1371657600000    IF1307
 4    1374163200000    IF1308
 ..             ...       ...
 130  1694966400000    IF2310
 131  1697731200000    IF2311
 132  1700064000000    IF2312
 133  1702828800000    IF2401
 134  1705593600000    IF2402
 
 [135 rows x 2 columns]}


```

[#](#获取期货行情数据) 获取期货行情数据
-----------------------

### [#](#获取行情数据) 获取行情数据

提示

使用该接口时，需要先订阅实时行情(`subscribe_quote`)或下载过历史行情(`download_history_data`)

python

```
from xtquant import xtdata

xtdata.get_market_data_ex(field_list=[], stock_list=[], period='1d', start_time='', end_time='', count=-1, dividend_type='none', fill_data=True)

```

**参数**

| 名称 | 类型 | 描述 |
| --- | --- | --- |
| `field` | `list` | `数据字段，详情见下方field字段表` |
| `stock_list` | `list` | `合约代码列表` |
| `period` | `str` | `数据周期——1m、5m、1d、tick、10m、15m、30m、1h、1w` |
| `start_time` | `str` | `数据起始时间，格式为 %Y%m%d 或 %Y%m%d%H%M%S，填""为获取历史最早一天` |
| `end_time` | `str` | `数据结束时间，格式为 %Y%m%d 或 %Y%m%d%H%M%S ，填""为截止到最新一天` |
| `count` | `int` | `数据个数` |
| `dividend_type` | `str` | `除权方式` |
| `fill_data` | `bool` | `是否填充数据` |

* `field`字段可选：

| field | 数据类型 | 含义 |
| --- | --- | --- |
| `time` | `int` | `时间` |
| `open` | `float` | `开盘价` |
| `high` | `float` | `最高价` |
| `low` | `float` | `最低价` |
| `close` | `float` | `收盘价` |
| `volume` | `float` | `成交量` |
| `amount` | `float` | `成交额` |
| `settle` | `float` | `今结算` |
| `openInterest` | `float` | `持仓量` |
| `preClose` | `float` | `前收盘价` |
| `suspendFlag` | `int` | `停牌` 1停牌，0 不停牌 |

* `period`周期为tick时，`field`字段可选:

| 字段名 | 数据类型 | 含义 |
| --- | --- | --- |
| `time` | `int` | `时间戳` |
| `stime` | `string` | `时间戳字符串形式` |
| `lastPrice` | `float` | `最新价` |
| `open` | `float` | `开盘价` |
| `high` | `float` | `最高价` |
| `low` | `float` | `最低价` |
| `lastClose` | `float` | `前收盘价` |
| `amount` | `float` | `成交总额` |
| `volume` | `int` | `成交总量（手）` |
| `pvolume` | `int` | `原始成交总量(未经过股手转换的成交总量)【不推荐使用】` |
| `stockStatus` | `int` | `证券状态` |
| `openInterest` | `int` | `若是股票，则openInt含义为股票状态，非股票则是持仓量`[openInt字段说明](/innerApi/data_structure.html#openint-%E8%AF%81%E5%88%B8%E7%8A%B6%E6%80%81) |
| `transactionNum` | `float` | `成交笔数(期货没有，单独计算)` |
| `lastSettlementPrice` | `float` | `前结算(股票为0)` |
| `settlementPrice` | `float` | `今结算(股票为0)` |
| `askPrice` | `list[float]` | `多档委卖价` |
| `askVol` | `list[int]` | `多档委卖量` |
| `bidPrice` | `list[float]` | `多档委买价` |
| `bidVol` | `list[int]` | `多档委买量` |

**返回值**

* period为`1m` `5m` `1d`K线周期时
  + 返回dict { field1 : value1, field2 : value2, ... }
  + value1, value2, ... ：pd.DataFrame 数据集，index为stock\_list，columns为time\_list
  + 各字段对应的DataFrame维度相同、索引相同
* period为`tick`分笔周期时
  + 返回dict { stock1 : value1, stock2 : value2, ... }
  + stock1, stock2, ... ：合约代码
  + value1, value2, ... ：np.ndarray 数据集，按数据时间戳`time`增序排列

#### [#](#获取日线行情数据) 获取日线行情数据

**示例**

示例返回值

```
from xtquant import xtdata
xtdata.get_market_data_ex([],['rb2401.SF'],period='1d')

```

```
# 返回结果
{'rb2401.SF':                    time    open    high     low   close   volume  \
 20230117  1673884800000  4001.0  4027.0  3973.0  4011.0     1038   
 20230118  1673971200000  4027.0  4051.0  4014.0  4037.0      314   
 20230119  1674057600000  4043.0  4085.0  4043.0  4080.0      352   
 20230120  1674144000000  4075.0  4076.0  4050.0  4070.0      502   
 20230130  1675008000000  4127.0  4157.0  4080.0  4084.0      992   
 ...                 ...     ...     ...     ...     ...      ...   
 20231017  1697472000000  3658.0  3672.0  3637.0  3647.0  1068036   
 20231018  1697558400000  3652.0  3660.0  3605.0  3615.0  1361935   
 20231019  1697644800000  3615.0  3650.0  3595.0  3644.0  1313338   
 20231020  1697731200000  3650.0  3659.0  3601.0  3610.0  1418587   
 20231023  1697990400000  3600.0  3616.0  3558.0  3573.0  1513440   
 
                 amount  settelementPrice  openInterest  preClose  suspendFlag  
 20230117  4.148817e+07            3996.0           573    4061.0            0  
 20230118  1.267393e+07            4036.0           713    4011.0            0  
 20230119  1.431537e+07            4066.0           821    4037.0            0  
 20230120  2.040859e+07            4065.0           944    4080.0            0  
 20230130  4.090941e+07            4123.0          1201    4070.0            0  
 ...                ...               ...           ...       ...          ...  
 20231017  3.900789e+10            3652.0       1870289    3657.0            0  
 20231018  4.950385e+10            3634.0       1951142    3647.0            0  
 20231019  4.759753e+10            3624.0       1886883    3615.0            0  
 20231020  5.149242e+10            3629.0       1880167    3644.0            0  
 20231023  5.423026e+10               0.0       1961524    3610.0            0  
 
 [183 rows x 11 columns]}

```

#### [#](#获取tick行情) 获取tick行情

**示例**

示例返回值

```
from xtquant import xtdata
xtdata.get_market_data_ex([],['rb2401.SF'],period='tick')

```

```

time	lastPrice	open	high	low	lastClose	amount	volume	pvolume	stockStatus	openInt	lastSettlementPrice	askPrice	bidPrice	askVol	bidVol	settlementPrice	transactionNum
20230925085900	1695603540500	3778.0	3786.0	3787.0	3766.0	3779.0	1.291532e+10	341961	0	0	1651554	3773.0	[3779.0, 3780.0, 3781.0, 3782.0, 3783.0]	[3777.0, 3776.0, 3775.0, 3774.0, 3773.0]	[635, 0, 0, 0, 0]	[138, 0, 0, 0, 0]	0.0	0
20230925090000	1695603600500	3779.0	3786.0	3787.0	3766.0	3779.0	1.296989e+10	343405	0	0	1652373	3773.0	[3780.0, 3781.0, 3782.0, 3783.0, 3784.0]	[3778.0, 3777.0, 3776.0, 3775.0, 3774.0]	[916, 0, 0, 0, 0]	[168, 0, 0, 0, 0]	0.0	0
20230925090001	1695603601000	3780.0	3786.0	3787.0	3766.0	3779.0	1.307600e+10	346211	0	0	1651646	3773.0	[3787.0, 3788.0, 3789.0, 3790.0, 3791.0]	[3779.0, 3778.0, 3777.0, 3776.0, 3775.0]	[420, 0, 0, 0, 0]	[20, 0, 0, 0, 0]	0.0	0
20230925090001	1695603601500	3783.0	3786.0	3787.0	3766.0	3779.0	1.309460e+10	346703	0	0	1651496	3773.0	[3784.0, 3785.0, 3786.0, 3787.0, 3788.0]	[3776.0, 3775.0, 3774.0, 3773.0, 3772.0]	[46, 0, 0, 0, 0]	[89, 0, 0, 0, 0]	0.0	0
20230925090002	1695603602000	3783.0	3786.0	3787.0	3766.0	3779.0	1.312842e+10	347597	0	0	1651258	3773.0	[3784.0, 3785.0, 3786.0, 3787.0, 3788.0]	[3782.0, 3781.0, 3780.0, 3779.0, 3778.0]	[41, 0, 0, 0, 0]	[7, 0, 0, 0, 0]	0.0	0
...	...	...	...	...	...	...	...	...	...	...	...	...	...	...	...	...	...	...
20230928145958	1695884398500	3690.0	3690.0	3717.0	3684.0	3682.0	3.781059e+10	1021634	0	0	1697198	3684.0	[3690.0, 0.0, 0.0, 0.0, 0.0]	[3690.0, 0.0, 0.0, 0.0, 0.0]	[54, 0, 0, 0, 0]	[126, 0, 0, 0, 0]	0.0	0
20230928145959	1695884399000	3690.0	3690.0	3717.0	3684.0	3682.0	3.781148e+10	1021658	0	0	1697179	3684.0	[3690.0, 0.0, 0.0, 0.0, 0.0]	[3690.0, 0.0, 0.0, 0.0, 0.0]	[20, 0, 0, 0, 0]	[112, 0, 0, 0, 0]	0.0	0
20230928145959	1695884399500	3690.0	3690.0	3717.0	3684.0	3682.0	3.781395e+10	1021725	0	0	1697158	3684.0	[3690.0, 0.0, 0.0, 0.0, 0.0]	[3690.0, 0.0, 0.0, 0.0, 0.0]	[20, 0, 0, 0, 0]	[46, 0, 0, 0, 0]	0.0	0
20230928150000	1695884400000	3690.0	3690.0	3717.0	3684.0	3682.0	3.781502e+10	1021754	0	0	1697143	3684.0	[3690.0, 0.0, 0.0, 0.0, 0.0]	[3690.0, 0.0, 0.0, 0.0, 0.0]	[10, 0, 0, 0, 0]	[63, 0, 0, 0, 0]	0.0	0
20230928150000	1695884400500	3690.0	3690.0	3717.0	3684.0	3682.0	3.781502e+10	1021754	0	0	1697143	3684.0	[3690.0, 0.0, 0.0, 0.0, 0.0]	[3690.0, 0.0, 0.0, 0.0, 0.0]	[10, 0, 0, 0, 0]	[63, 0, 0, 0, 0]	3700.0	0
149943 rows × 18 columns

```

#### [#](#获取5档盘口行情) 获取5档盘口行情

提示

1. 该数据为[VIP数据在新窗口打开](https://xuntou.net/#/productvip)

示例五档返回值

```
from xtquant import xtdata
import time

symbol_list = ["rb2405.SF","ec2404.INE"] # 五档行情支持上期所，上期能源

period = "l2quote" # 获取5档盘口tick

for symbol in symbol_list:
    xtdata.subscribe_quote(symbol,period = period,count=-1)
time.sleep(1)

data = xtdata.get_market_data_ex(["askPrice","bidPrice"],symbol_list,period = period,count=-1)

print(data)


```

```

{'ec2404.INE':                                                          askPrice  \
 20240115085900  [2300.0, 2300.2, 2304.0, 2306.0, 2310.0, 0.0, ...   
 20240115090000  [2266.0, 2280.0, 2280.9, 2285.0, 2287.9, 0.0, ...   
 20240115090001  [2261.6000000000004, 2262.0000000000005, 2262....   
 20240115090001  [2253.4, 2253.5, 2253.6, 2254.6, 2255.0, 0.0, ...   
 20240115090002  [2244.6, 2246.6, 2246.7999999999997, 2248.8999...   
 ...                                                           ...   
 20240115140227  [2138.3, 2138.6000000000004, 2138.700000000000...   
 20240115140228  [2138.0, 2138.3, 2138.6000000000004, 2138.7000...   
 20240115140228  [2137.7999999999997, 2137.8999999999996, 2137....   
 20240115140229  [2137.2999999999997, 2137.7999999999997, 2137....   
 20240115140229  [2136.4, 2137.1, 2137.7999999999997, 2137.8999...   
 
                                                          bidPrice  
 20240115085900  [2288.0, 2280.0, 2266.0, 2265.0, 2262.1, 0.0, ...  
 20240115090000  [2222.1, 2222.0, 2220.0, 2219.0, 2216.0, 0.0, ...  
 20240115090001  [2227.0000000000005, 2226.8000000000006, 2226....  
 20240115090001  [2230.2000000000003, 2230.0000000000005, 2229....  
 20240115090002  [2233.2000000000003, 2223.4, 2222.0, 2220.0, 2...  
 ...                                                           ...  
 20240115140227  [2137.1, 2135.2999999999997, 2134.999999999999...  
 20240115140228  [2137.1, 2135.2999999999997, 2134.999999999999...  
 20240115140228  [2137.1, 2135.2999999999997, 2134.999999999999...  
 20240115140229  [2137.1, 2135.2999999999997, 2134.999999999999...  
 20240115140229  [2135.0, 2134.0, 2132.4, 2132.0, 2131.0, 0.0, ...  
 
 [15942 rows x 2 columns],
 'rb2405.SF':                                                          askPrice  \
 20240112205900  [3906.0, 3907.0, 3908.0, 3909.0, 3910.0, 0.0, ...   
 20240112210000  [3904.0, 3905.0, 3906.0, 3907.0, 3908.0, 0.0, ...   
 20240112210001  [3905.0, 3906.0, 3907.0, 3908.0, 3909.0, 0.0, ...   
 20240112210001  [3905.0, 3906.0, 3907.0, 3908.0, 3909.0, 0.0, ...   
 20240112210002  [3905.0, 3906.0, 3907.0, 3908.0, 3909.0, 0.0, ...   
 ...                                                           ...   
 20240115140227  [3911.0, 3912.0, 3913.0, 3914.0, 3915.0, 0.0, ...   
 20240115140227  [3911.0, 3912.0, 3913.0, 3914.0, 3915.0, 0.0, ...   
 20240115140228  [3911.0, 3912.0, 3913.0, 3914.0, 3915.0, 0.0, ...   
 20240115140228  [3911.0, 3912.0, 3913.0, 3914.0, 3915.0, 0.0, ...   
 20240115140229  [3911.0, 3912.0, 3913.0, 3914.0, 3915.0, 0.0, ...   
 
                                                          bidPrice  
 20240112205900  [3905.0, 3904.0, 3903.0, 3902.0, 3901.0, 0.0, ...  
 20240112210000  [3903.0, 3902.0, 3901.0, 3900.0, 3899.0, 0.0, ...  
 20240112210001  [3904.0, 3903.0, 3902.0, 3901.0, 3900.0, 0.0, ...  
 20240112210001  [3904.0, 3903.0, 3902.0, 3901.0, 3900.0, 0.0, ...  
 20240112210002  [3904.0, 3903.0, 3902.0, 3901.0, 3900.0, 0.0, ...  
 ...                                                           ...  
 20240115140227  [3910.0, 3909.0, 3908.0, 3907.0, 3906.0, 0.0, ...  
 20240115140227  [3910.0, 3909.0, 3908.0, 3907.0, 3906.0, 0.0, ...  
 20240115140228  [3910.0, 3909.0, 3908.0, 3907.0, 3906.0, 0.0, ...  
 20240115140228  [3910.0, 3909.0, 3908.0, 3907.0, 3906.0, 0.0, ...  
 20240115140229  [3910.0, 3909.0, 3908.0, 3907.0, 3906.0, 0.0, ...  
 
 [35329 rows x 2 columns]}


```

#### [#](#期货结算价与持仓量) 期货结算价与持仓量

| 字段 | 数据类型 | 含义 |
| --- | --- | --- |
| `settelementPrice` | `float` | `结算价` |
| `openInterest` | `float` | `持仓量` |
| **示例** |  |  |

示例返回值

```
from xtquant import xtdata
xtdata.get_market_data_ex(['settelementPrice','openInterest'],['rb2401.SF'],period='1d')


```

```
{'rb2401.SF':           settelementPrice  openInterest
 20230117            3996.0           573
 20230118            4036.0           713
 20230119            4066.0           821
 20230120            4065.0           944
 20230130            4123.0          1201
 ...                    ...           ...
 20230922            3773.0       1643925
 20230925            3741.0       1710023
 20230926            3697.0       1772900
 20230927            3684.0       1767758
 20230928            3700.0       1697143
 
 [172 rows x 2 columns]}

```

### [#](#期货仓单) 期货仓单

提示

1.该数据通过`get_market_data`和`get_market_data_ex`接口获取  
 2.获取数据前需要先用`download_history_data`下载历史数据  
 3.[VIP 权限数据在新窗口打开](https://xuntou.net/#/productvip)

**参数**

* `period` 参数传入 `warehousereceipt`

**返回值**

* 返回dict { field1 : value1, field2 : value2, ... }
* value1, value2, ... ：pd.DataFrame 数据集，index为stock\_list，columns为time\_list

**示例**

示例返回值

```
from xtquant import xtdata
# 下载期货仓单
xtdata.download_history_data('','warehousereceipt','','')
#查询螺纹钢仓单数据
xtdata.get_market_data_ex([],['rb.SF'],period='warehousereceipt')

```

```
{'rb.SF':                    time warehouse  receipt
 20200102  1577894400000    上港物流共青        0
 20200102  1577894400001      广物物流        0
 20200102  1577894400002      中储无锡        0
 20200102  1577894400003       惠龙港        0
 20200102  1577894400004      金驹物流        0
 ...                 ...       ...      ...
 20230928  1695830400007      鞍钢股份        0
 20230928  1695830400008      沙钢集团        0
 20230928  1695830400009      镔钢集团     4500
 20230928  1695830400010    河钢集团承钢        0
 20230928  1695830400011    河钢集团邯钢        0
 
 [11104 rows x 3 columns]}

```

### [#](#期货席位) 期货席位

提示

1.该数据通过`get_market_data`和`get_market_data_ex`接口获取  
 2.获取数据前需要先用`download_history_data`下载历史数据  
 3.[VIP 权限数据在新窗口打开](https://xuntou.net/#/productvip)

**参数**

* `period` 参数传入 `futureholderrank`

**返回值**

* 返回dict { field1 : value1, field2 : value2, ... }
* value1, value2, ... ：pd.DataFrame 数据集，index为stock\_list，columns为time\_list

**示例**

示例返回值

```
from xtquant import xtdata
# 下载期货席位
xtdata.download_history_data('','futureholderrank','','')
#查询螺纹钢席位数据,period参数填写'futureholderrank'
print(xtdata.get_market_data_ex([],['rb2401.SF'],period='futureholderrank')['rb2401.SF'])

```

```
{'rb2401.SF':                    time                                  tradingVolumeRank  \
 20230406  1680710400000  [{'4': '国泰君安', '5': 2400, '6': 382}, {'4': '东证...   
 20230407  1680796800000  [{'4': '中信期货', '5': 2847, '6': 2079}, {'4': '东...   
 20230410  1681056000000  [{'4': '中泰期货', '5': 4791, '6': 4239}, {'4': '东...   
 20230411  1681142400000  [{'4': '东证期货', '5': 4925, '6': 1081}, {'4': '中...   
 20230412  1681228800000  [{'4': '华泰期货', '5': 6743, '6': 5298}, {'4': '浙...   
 ...                 ...                                                ...   
 20230922  1695312000000  [{'4': '东证期货', '5': 363143, '6': -235457}, {'4...   
 20230925  1695571200000  [{'4': '东证期货', '5': 470528, '6': 107385}, {'4'...   
 20230926  1695657600000  [{'4': '东证期货', '5': 427734, '6': -42794}, {'4'...   
 20230927  1695744000000  [{'4': '东证期货', '5': 291031, '6': -136703}, {'4...   
 20230928  1695830400000  [{'4': '东证期货', '5': 323971, '6': 32940}, {'4':...   
 
                                             buyPositionRank  \
 20230406  [{'4': '海证期货', '5': 18021, '6': 11}, {'4': '中泰...   
 20230407  [{'4': '海证期货', '5': 18035, '6': 14}, {'4': '中泰...   
 20230410  [{'4': '海证期货', '5': 18048, '6': 13}, {'4': '中泰...   
 20230411  [{'4': '海证期货', '5': 18048, '6': 0}, {'4': '中泰期...   
 20230412  [{'4': '海证期货', '5': 18083, '6': 35}, {'4': '浙商...   
 ...                                                     ...   
 20230922  [{'4': '国泰君安', '5': 119476, '6': -8964}, {'4':...   
 20230925  [{'4': '国泰君安', '5': 118274, '6': -1202}, {'4':...   
 20230926  [{'4': '国泰君安', '5': 129658, '6': 11384}, {'4':...   
 20230927  [{'4': '国泰君安', '5': 123275, '6': -6383}, {'4':...   
 20230928  [{'4': '国泰君安', '5': 118341, '6': -4934}, {'4':...   
 
                                            sellPositionRank  
 20230406  [{'4': '中泰期货', '5': 13816, '6': 99}, {'4': '光大...  
 20230407  [{'4': '中泰期货', '5': 13695, '6': -121}, {'4': '...  
 20230410  [{'4': '中泰期货', '5': 16135, '6': 2440}, {'4': '...  
 20230411  [{'4': '中泰期货', '5': 15623, '6': -512}, {'4': '...  
 20230412  [{'4': '中泰期货', '5': 15620, '6': -3}, {'4': '财信...  
 ...                                                     ...  
 20230922  [{'4': '国泰君安', '5': 152616, '6': -6835}, {'4':...  
 20230925  [{'4': '国泰君安', '5': 159635, '6': 7019}, {'4': ...  
 20230926  [{'4': '国泰君安', '5': 152998, '6': -6637}, {'4':...  
 20230927  [{'4': '国泰君安', '5': 148873, '6': -4125}, {'4':...  
 20230928  [{'4': '国泰君安', '5': 146983, '6': -1890}, {'4':...  
 
 [121 rows x 4 columns]}

```

### [#](#现货数据) 现货数据

提示

1.该数据通过`get_market_data`和`get_market_data_ex`接口获取  
 2.获取数据前需要先用`download_history_data`下载历史数据  
 3.[VIP 权限数据在新窗口打开](https://xuntou.net/#/productvip)

**返回值**

* 返回dict { field1 : value1, field2 : value2, ... }
* value1, value2, ... ：pd.DataFrame 数据集，index为stock\_list，columns为time\_list

**示例**

示例返回值

```
from xtquant import xtdata
# 获取现货市场代码
spot_list = xtdata.get_stock_list_in_sector('现货市场指数')
print(spot_list)
# 获取现货数据
spot_data = xtdata.get_market_data_ex(field_list=[], 
                                  stock_list=['S0010020001.spot'],
                                  period='1d',
                                  fill_data=False)
print(spot_data)

```

```
# 预计输出
['S0010010001.SPOT', 'S0010020001.SPOT', 'S0010030001.SPOT', 'S0010030002.SPOT', 'S0010040001.SPOT', 'S0010050001.SPOT', 'S0010050002.SPOT', 'S0010050003.SPOT', 'S0010050004.SPOT', 'S0010070001.SPOT', 'S0010080001.SPOT', 'S0010090001.SPOT', 'S0020010001.SPOT', 'S0020020001.SPOT', 'S0020030001.SPOT', 'S0020040001.SPOT', 'S0020050001.SPOT', 'S0020060001.SPOT', 'S0020070001.SPOT', 'S0020080001.SPOT', 'S0020090001.SPOT', 'S0020100001.SPOT', 'S0030010001.SPOT', 'S0030010002.SPOT', 'S0030020001.SPOT', 'S0030030001.SPOT', 'S0030040001.SPOT', 'S0030070001.SPOT', 'S0030080001.SPOT', 'S0030090001.SPOT', 'S0030090002.SPOT', 'S0030100001.SPOT', 'S0030110001.SPOT', 'S0030120001.SPOT', 'S0030120002.SPOT', 'S0030120003.SPOT', 'S0030120004.SPOT', 'S0030120005.SPOT', 'S0030140001.SPOT', 'S0030150001.SPOT', 'S0030150002.SPOT', 'S0030150003.SPOT', 'S0030150004.SPOT', 'S0030160001.SPOT', 'S0030170001.SPOT', 'S0030180001.SPOT', 'S0030180002.SPOT', 'S0030180003.SPOT', 'S0030190001.SPOT', 'S0030200001.SPOT', 'S0040010001.SPOT', 'S0040020001.SPOT', 'S0040030001.SPOT', 'S0040040001.SPOT', 'S0040050001.SPOT', 'S0040060001.SPOT', 'S0040070001.SPOT', 'S0040080001.SPOT', 'S0040090001.SPOT', 'S0040100001.SPOT', 'S0040110001.SPOT', 'S0040120001.SPOT', 'S0040130001.SPOT', 'S0040150001.SPOT', 'S0040160001.SPOT', 'S0040170001.SPOT']

{'S0010020001.SPOT':           amount   close    high     low    open  openInterest  preClose  \
19970701     0.0  2748.0  2748.0  2748.0  2748.0             0       0.0   
19970801     0.0  2720.0  2720.0  2720.0  2720.0             0       0.0   
19970901     0.0  2692.0  2692.0  2692.0  2692.0             0       0.0   
19971001     0.0  2720.0  2720.0  2720.0  2720.0             0       0.0   
19971101     0.0  2720.0  2720.0  2720.0  2720.0             0       0.0   
19971201     0.0  2720.0  2720.0  2720.0  2720.0             0       0.0   
          settelementPrice  suspendFlag           time  volume  
19970701            2748.0            0   867686400000       0  
19970801            2720.0            0   870364800000       0  
19970901            2692.0            0   873043200000       0  
19971001            2720.0            0   875635200000       0  
19971101            2720.0            0   878313600000       0  
19971201            2720.0            0   880905600000       0  
...

```

[#](#期货列表) 期货列表
---------------

### [#](#金融期货列表) 金融期货列表

提供当前时间段内有效的金融期货合约数据（如行情数据等），

### [#](#支持的金融期货种类) 支持的金融期货种类

#### [#](#沪深-300-指数期货合约表) 沪深 300 指数期货合约表

| 信息 | 内容 |
| --- | --- |
| 合约标的 | 沪深 300 指数 |
| 合约乘数 | 每点 300 元 |
| 报价单位 | 指数点 |
| 最小变动价位 | 0.2 点 |
| 合约月份 | 当月、下月及随后两个季月 |
| 交易时间 | 上午：9:30-11:30，下午：13:00-15:00 |
| 每日价格最大波动限制 | 上一个交易日结算价的±10% |
| 最低交易保证金 | 合约价值的 8% |
| 最后交易日 | 合约到期月份的第三个周五，遇国家法定假日顺延 |
| 交割日期 | 同最后交易日 |
| 交割方式 | 现金交割 |
| 交易代码 | IF |
| 上市交易所 | 中国金融期货交易所 |

#### [#](#上证-50-股指期货合约表) 上证 50 股指期货合约表

| 信息 | 内容 |
| --- | --- |
| 合约标的 | 上证 50 指数 |
| 合约乘数 | 每点 300 元 |
| 报价单位 | 指数点 |
| 最小变动价位 | 0.2 点 |
| 合约月份 | 当月、下月及随后两个季月 |
| 交易时间 | 上午： 9:30-11:30，下午：13:00-15:00 |
| 每日价格最大波动限制 | 上一个交易日结算价的±10% |
| 最低交易保证金 | 合约价值的 8% |
| 最后交易日 | 合约到期月份的第三个周五，遇国家法定假日顺延 |
| 交割日期 | 同最后交易日 |
| 交割方式 | 现金交割 |
| 交易代码 | IH |
| 上市交易所 | 中国金融期货交易所 |

#### [#](#中证-500-股指期货合约表) 中证 500 股指期货合约表

| 信息 | 内容 |
| --- | --- |
| 合约标的 | 中证 500 指数 |
| 合约乘数 | 每点 200 元 |
| 报价单位 | 指数点 |
| 最小变动价位 | 0.2 点 |
| 合约月份 | 当月、下月及随后两个季月 |
| 交易时间 | 上午： 9:30-11:30, 下午：13:00-15:00 |
| 每日价格最大波动限制 | 上一个交易日结算价的±10% |
| 最低交易保证金 | 合约价值的 8% |
| 最后交易日 | 合约到期月份的第三个周五，遇国家法定假日顺延 |
| 交割日期 | 同最后交易日 |
| 交割方式 | 现金交割 |
| 交易代码 | IC |
| 上市交易所 | 中国金融期货交易所 |

#### [#](#中证-1000-股指期货合约表) 中证 1000 股指期货合约表

| 信息 | 内容 |
| --- | --- |
| 合约标的 | 中证 1000 指数 |
| 合约乘数 | 每点 200 元 |
| 报价单位 | 指数点 |
| 最小变动价位 | 0.2 点 |
| 合约月份 | 当月、下月及随后两个季月 |
| 交易时间 | 上午： 9:30-11:30, 下午：13:00-15:00 |
| 每日价格最大波动限制 | 上一个交易日结算价的±10% |
| 最低交易保证金 | 合约价值的 8% |
| 最后交易日 | 合约到期月份的第三个周五，遇国家法定假日顺延 |
| 交割日期 | 同最后交易日 |
| 交割方式 | 现金交割 |
| 交易代码 | IM |
| 上市交易所 | 中国金融期货交易所 |

#### [#](#_2-年期国债期货合约表) 2 年期国债期货合约表

| 信息 | 内容 |
| --- | --- |
| 合约标的 | 面值为 200 万元人民币、票面利率为 3%的名义中短期国债 |
| 可交割国债 | 发行期限不高于 5 年，合约到期月份首日剩余期限为 1.5-2.25 年的记账式附息国债 |
| 报价方式 | 百元净价报价 |
| 最小变动价位 | 0.005 元 |
| 合约月份 | 最近的三个季月（3 月、6 月、9 月、12 月中的最近三个月循环） |
| 交易时间 | 9:15 - 11:30, 13:00 - 15:15 |
| 最后交易日交易时间 | 9:15 - 11:30 |
| 每日价格最大波动限制 | 上一交易日结算价的±0.5% |
| 最低交易保证金 | 合约价值的 0.5% |
| 最后交易日 | 合约到期月份的第二个星期五 |
| 最后交割日 | 最后交易日后的第三个交易日 |
| 交割方式 | 实物交割 |
| 交易代码 | TS |
| 上市交易所 | 中国金融期货交易所 |

#### [#](#_5-年期国债期货合约表) 5 年期国债期货合约表

| 信息 | 内容 |
| --- | --- |
| 合约标的 | 面值为 100 万元人民币、票面利率为 3%的名义中期国债 |
| 可交割国债 | 合约到期月份首日剩余期限为 4-5.25 年的记账式附息国债 |
| 报价方式 | 百元净价报价 |
| 最小变动价位 | 0.005 元 |
| 合约月份 | 最近的三个季月（3 月、6 月、9 月、12 月中的最近三个月循环） |
| 交易时间 | 09:15—11:30， 13:00—15:15 |
| 最后交易日交易时间 | 09:15—11:30 |
| 每日价格最大波动限制 | 上一交易日结算价的±1.2% |
| 最低交易保证金 | 合约价值的 1% |
| 最后交易日 | 合约到期月份的第二个星期五 |
| 最后交割日 | 最后交易日后的第三个交易日 |
| 交割方式 | 实物交割 |
| 交易代码 | TF |
| 上市交易所 | 中国金融期货交易所 |

#### [#](#_10-年期国债期货合约表) 10 年期国债期货合约表

| 信息 | 内容 |
| --- | --- |
| 合约标的 | 面值为 100 万元人民币、票面利率为 3%的名义长期国债 |
| 可交割国债 | 合约到期月份首日剩余期限为 6.5-10.25 年的记账式附息国债 |
| 报价方式 | 百元净价报价 |
| 最小变动价位 | 0.005 元 |
| 合约月份 | 最近的三个季月（3 月、6 月、9 月、12 月中的最近三个月循环） |
| 交易时间 | 9:15 - 11:30，13:00 - 15:15 |
| 最后交易日交易时间 | 9:15 - 11:30 |
| 每日价格最大波动限制 | 上一交易日结算价的±2% |
| 最低交易保证金 | 合约价值的 2% |
| 最后交易日 | 合约到期月份的第二个星期五 |
| 最后交割日 | 最后交易日后的第三个交易日 |
| 交割方式 | 实物交割 |
| 交易代码 | T |
| 上市交易所 | 中国金融期货交易所 |

### [#](#商品期货列表) 商品期货列表

### [#](#主力连续合约及加权) 主力连续合约及加权

**主力合约生成规则**

每个品种只有一个主连合约。主连合约于下一个交易日进行指向切换，切换前主连合约不变。当日成交量和持仓量都为最大的合约，确定为下一日主连合约，若当日成交量和持仓量都为最大的合约有多个，取其中的近月合约确定为下一日主连合约，若无当日成交量和持仓量同时最大的合约，取成交量最大的合约确定为下一日主连合约。

**加权品种的价格规则**

由当前品种全部可交易合约以实时持仓量为权重加权平均计算得到。 具体的计算方式是将该品种下所有交易合约价格和持仓量的乘积相加，得到的结果再除以这些合约持仓量的总和。

![](/assets/迅投数据服务_加权公式-b1b27ec5.png)

**月份连续合约生成规则**

月份连续合约是指将所有的相同月份合约连续拼接起来，生成的连续合约；

以`AP01.ZF`为例：ap01的合成规则是将`AP401.ZF`,`AP301.ZF`,`AP201.ZF`,`AP101.ZF`.....等苹果一月份合约拼接起来的连续合约

#### [#](#中金所) 中金所

| 名称 | 主力连续代码 | 加权合约代码 | 开始时间 | 退市时间 | 备注 |
| --- | --- | --- | --- | --- | --- |
| 沪深 300 合约 | IF00.IF | IFJQ00.IF | 2010-04-16 |  |  |
| 5 年期国债合约 | TF00.IF | TFJQ00.IF | 2013-09-06 |  |  |
| 10 年期国债合约 | T00.IF | TJQ00.IF | 2015-03-20 |  |  |
| 中证 500 合约 | IC00.IF | ICJQ00.IF | 2015-04-16 |  |  |
| 上证 50 合约 | IH00.IF | IHJQ00.IF | 2015-04-16 |  |  |
| 2 年期国债合约 | TS00.IF | TSJQ00.IF | 2018-08-17 |  |  |
| 中证 1000 合约 | IM00.IF | IMJQ00.IF | 2022-07-22 |  |  |
| 30 年期国债合约 | TL00.IF | TLJQ00.IF | 2023-04-21 |  |  |

#### [#](#大商所) 大商所

| 名称 | 主力连续代码 | 加权合约代码 | 开始时间 | 退市时间 | 备注 |
| --- | --- | --- | --- | --- | --- |
| 豆一合约 | a00.DF | aJQ00.DF | 2005-01-03 |  |  |
| 豆二合约 | b00.DF | bJQ00.DF | 2005-01-03 |  |  |
| 豆粕合约 | m00.DF | mJQ00.DF | 2005-01-03 |  |  |
| 玉米合约 | c00.DF | cJQ00.DF | 2005-01-03 |  |  |
| 豆油合约 | y00.DF | yJQ00.DF | 2006-01-09 |  |  |
| 聚乙烯合约 | l00.DF | lJQ00.DF | 2007-07-31 |  |  |
| 棕榈油合约 | p00.DF | pJQ00.DF | 2007-10-29 |  |  |
| 聚氯乙烯合约 | v00.DF | vJQ00.DF | 2009-05-25 |  |  |
| 焦炭合约 | j00.DF | jJQ00.DF | 2011-04-15 |  |  |
| 焦煤合约 | jm00.DF | jmJQ00.DF | 2013-03-22 |  |  |
| 铁矿石合约 | i00.DF | iJQ00.DF | 2013-10-18 |  |  |
| 鸡蛋合约 | jd00.DF | jdJQ00.DF | 2013-11-08 |  |  |
| 胶合板合约 | bb00.DF | bbJQ00.DF | 2013-12-06 |  |  |
| 纤维板合约 | fb00.DF | fbJQ00.DF | 2013-12-06 |  |  |
| 聚丙烯合约 | pp00.DF | ppJQ00.DF | 2014-02-28 |  |  |
| 玉米淀粉合约 | cs00.DF | csJQ00.DF | 2014-12-19 |  |  |
| 乙二醇合约 | eg00.DF | egJQ00.DF | 2018-12-10 |  |  |
| 粳米合约 | rr00.DF | rrJQ00.DF | 2019-08-16 |  |  |
| 苯乙烯合约 | eb00.DF | ebJQ00.DF | 2019-09-26 |  |  |
| 液化石油气合约 | pg00.DF | pgJQ00.DF | 2020-03-30 |  |  |
| 生猪合约 | lh00.DF | lhJQ00.DF | 2021-01-08 |  |  |

#### [#](#上海国际能源交易中心) 上海国际能源交易中心

| 名称 | 主力连续代码 | 加权合约代码 | 开始时间 | 退市时间 | 备注 |
| --- | --- | --- | --- | --- | --- |
| 原油合约 | sc00.INE | scJQ00.INE | 2018-03-27 |  |  |
| 20 号胶合约 | nr00.INE | nrJQ00.INE | 2019-08-12 |  |  |
| 低硫燃料油合约 | lu00.INE | luJQ00.INE | 2020-06-22 |  |  |
| 阴极铜合约 | bc00.INE | bcJQ00.INE | 2020-11-19 |  |  |
| 集运指数（欧线）合约 | ec00.INE | ecJQ00.INE | 2023-08-18 |  |  |

#### [#](#广期所) 广期所

| 名称 | 主力连续代码 | 加权合约代码 | 开始时间 | 退市时间 | 备注 |
| --- | --- | --- | --- | --- | --- |
| 碳酸锂 | lc00.GF | lcJQ00.GF | 2023-07-21 |  |  |
| 工业硅 | si00.GF | siJQ00.GF | 2022-12-22 |  |  |

#### [#](#上期所) 上期所

| 名称 | 主力连续代码 | 加权合约代码 | 开始时间 | 退市时间 | 备注 |
| --- | --- | --- | --- | --- | --- |
| 铝合约 | al00.SF | alJQ00.SF | 2005-01-03 |  |  |
| 天然橡胶合约 | ru00.SF | ruJQ00.SF | 2005-01-03 |  |  |
| 燃料油合约 | fu00.SF | fuJQ00.SF | 2005-01-03 |  |  |
| 铜合约 | cu00.SF | cuJQ00.SF | 2005-01-03 |  |  |
| 锌合约 | zn00.SF | znJQ00.SF | 2007-03-26 |  |  |
| 黄金合约 | au00.SF | auJQ00.SF | 2008-01-09 |  |  |
| 线材合约 | wr00.SF | wrJQ00.SF | 2009-03-27 |  |  |
| 螺纹钢合约 | rb00.SF | rbJQ00.SF | 2009-03-27 |  |  |
| 铅合约 | pb00.SF | pbJQ00.SF | 2011-03-24 |  |  |
| 白银合约 | ag00.SF | agJQ00.SF | 2012-05-10 |  |  |
| 石油沥青合约 | bu00.SF | buJQ00.SF | 2013-10-09 |  |  |
| 热轧卷板合约 | hc00.SF | hcJQ00.SF | 2014-03-21 |  |  |
| 锡合约 | sn00.SF | snJQ00.SF | 2015-03-27 |  |  |
| 镍合约 | ni00.SF | niJQ00.SF | 2015-03-27 |  |  |
| 纸浆合约 | sp00.SF | spJQ00.SF | 2018-11-27 |  |  |
| 不锈钢合约 | ss00.SF | ssJQ00.SF | 2019-09-25 |  |  |
| 氧化铝合约 | ao00.SF | aoJQ00.SF | 2023-06-19 |  |  |
| 丁二烯橡胶合约 | br00.SF | brJQ00.SF | 2023-07-28 |  |  |

#### [#](#郑商所) 郑商所

| 名称 | 主力连续代码 | 加权合约代码 | 开始时间 | 退市时间 | 备注 |
| --- | --- | --- | --- | --- | --- |
| 硬白小麦合约 | WT00.ZF | WTJQ00.ZF | 2005-01-03 | 2012-11-22 |  |
| 强麦合约 | WS00.ZF | WSJQ00.ZF | 2005-01-03 | 2013-05-23 | WS为旧的强麦合约代码，自WS 1305 停止执行 |
| 绿豆合约 | GN00.ZF | GNJQ00.ZF | 2005-01-03 | 2010-03-23 |  |
| 棉花合约 | CF00.ZF | CFJQ00.ZF | 2005-01-03 |  |  |
| 白糖合约 | SR00.ZF | SRJQ00.ZF | 2006-01-06 |  |  |
| PTA合约 | TA00.ZF | TAJQ00.ZF | 2006-12-18 |  |  |
| 菜籽油合约 | RO00.ZF | ROJQ00.ZF | 2007-06-08 | 2013-05-15 | RO为旧的菜籽油合约, 自RO 1305 停止执行 |
| 早籼稻合约 | ER00.ZF | ERJQ00.ZF | 2009-04-20 | 2013-05-23 | ER为旧的早籼稻合约, 自ER 1305 停止执行 |
| 甲醇合约 | MA00.ZF | MAJQ00.ZF | 2011-10-28 | 2015-05-15 |  |
| 普麦合约 | PM00.ZF | PMJQ00.ZF | 2012-01-17 |  |  |
| 菜籽油合约 | OI00.ZF | OIJQ00.ZF | 2012-07-16 |  | OI为新的菜籽油合约, 自OI 1307 开始执行 |
| 强麦合约 | WH00.ZF | WHJQ00.ZF | 2012-07-24 |  | WH为新的强麦合约代码，自WH 1307 开始执行 |
| 早籼稻合约 | RI00.ZF | RIJQ00.ZF | 2012-07-24 |  | RI为新的早籼稻合约, 自RI 1307 开始执行 |
| 玻璃合约 | FG00.ZF | FGJQ00.ZF | 2012-12-03 |  |  |
| 菜籽粕合约 | RM00.ZF | RMJQ00.ZF | 2012-12-28 |  |  |
| 油菜籽合约 | RS00.ZF | RSJQ00.ZF | 2012-12-28 |  |  |
| 动力煤合约 | TC00.ZF | TCJQ00.ZF | 2013-09-26 | 2016-04-08 | TC为旧的动力煤合约，自TC 1605 停止执行 |
| 粳稻谷合约 | JR00.ZF | JRJQ00.ZF | 2013-11-18 |  |  |
| 甲醇合约 | MA00.ZF | MAJQ00.ZF | 2014-06-17 |  | MA为新的甲醇合约代码，自MA 1506 开始执行 |
| 晚籼稻合约 | LR00.ZF | LRJQ00.ZF | 2014-07-08 |  |  |
| 硅铁合约 | SF00.ZF | SFJQ00.ZF | 2014-08-08 |  |  |
| 锰硅合约 | SM00.ZF | SMJQ00.ZF | 2014-08-08 |  |  |
| 动力煤合约 | ZC00.ZF | ZCJQ00.ZF | 2015-05-18 |  | ZC为新的动力煤合约，自ZC 1605 开始执行 |
| 棉纱合约 | CY00.ZF | CYJQ00.ZF | 2017-08-18 |  |  |
| 苹果合约 | AP00.ZF | APJQ00.ZF | 2017-12-22 |  |  |
| 红枣合约 | CJ00.ZF | CJJQ00.ZF | 2019-04-30 |  |  |
| 尿素合约 | UR00.ZF | URJQ00.ZF | 2019-08-09 |  |  |
| 纯碱合约 | SA00.ZF | SAJQ00.ZF | 2019-12-06 |  |  |
| 短纤合约 | PF00.ZF | PFJQ00.ZF | 2020-10-12 |  |  |
| 花生合约 | PK00.ZF | PKJQ00.ZF | 2021-02-01 |  |  |
| 烧碱合约 | SH00.ZF | SHJQ00.ZF | 2023-09-15 |  |  |
| 对二甲苯合约 | PX00.ZF | PXJQ00.ZF | 2023-09-15 |  |  |

上次更新: 2025/4/22 14:38:17

邀请注册送VIP优惠券

分享下方的内容给好友、QQ群、微信群,好友注册您即可获得VIP优惠券

玩转qmt,上迅投qmt知识库

登录后获取

[指数数据](/dictionary/indexes.html)  [期权数据](/dictionary/option.html)