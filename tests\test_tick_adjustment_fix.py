#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试修复后的tick复权数据合成功能
"""

import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from data.storage.vectorized_reader import read_partitioned_data_vectorized
from config.settings import DATA_ROOT

def test_tick_adjustment_fix():
    """测试修复后的tick复权数据功能"""
    symbol = "600000.SH"
    
    print("🧪 测试修复后的tick复权数据功能")
    print(f"📋 测试股票: {symbol}")
    print(f"📁 数据根目录: {DATA_ROOT}")
    
    # 测试1：读取原始数据
    print("\n📊 测试1：读取原始tick数据")
    raw_df = read_partitioned_data_vectorized(
        data_root=DATA_ROOT,
        symbol=symbol,
        period="tick",
        dividend_type="none"
    )
    
    if raw_df is not None and not raw_df.empty:
        print(f"✅ 原始数据读取成功: {len(raw_df)} 行")
        print(f"   时间范围: {raw_df.index[0]} ~ {raw_df.index[-1]}")
    else:
        print("❌ 原始数据读取失败")
        return False
    
    # 测试2：读取复权数据
    print("\n📊 测试2：读取复权数据")
    adj_df = read_partitioned_data_vectorized(
        data_root=DATA_ROOT,
        symbol=symbol,
        period="tick",
        dividend_type="front"
    )
    
    if adj_df is not None and not adj_df.empty:
        print(f"✅ 复权数据读取成功: {len(adj_df)} 行")
        print(f"   时间范围: {adj_df.index[0]} ~ {adj_df.index[-1]}")
        
        # 比较数据
        if len(raw_df) == len(adj_df):
            print("✅ 数据行数一致")
        else:
            print(f"⚠️  数据行数不一致: 原始{len(raw_df)} vs 复权{len(adj_df)}")
            
        # 检查价格差异
        if 'lastPrice' in raw_df.columns and 'lastPrice' in adj_df.columns:
            price_diff = abs(raw_df['lastPrice'].iloc[0] - adj_df['lastPrice'].iloc[0])
            if price_diff > 0.001:
                print(f"✅ 复权计算生效，价格差异: {price_diff:.4f}")
            else:
                print("ℹ️  价格无明显差异（可能无除权除息事件）")
    else:
        print("❌ 复权数据读取失败")
        return False
    
    print("\n🎉 所有测试通过！修复成功！")
    return True

if __name__ == "__main__":
    test_tick_adjustment_fix()
