#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
结果管理模块

负责处理下载结果的保存、读取、合并等操作
"""

import os
import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from config.settings import DATA_ROOT
from utils.logger import get_unified_logger, LogTarget

logger = get_unified_logger(__name__, enhanced=True)


@dataclass
class DownloadResult:
    """下载结果数据类"""
    success: bool = True
    successful_symbols: List[str] = None
    failed_symbols: List[str] = None
    not_downloaded_symbols: List[str] = None
    no_download_needed: List[str] = None
    data: Dict[str, Any] = None
    save_paths: Dict[str, str] = None
    logged_data_details: Dict[str, Any] = None
    failed_reasons: Dict[str, str] = None
    
    def __post_init__(self):
        """初始化默认值"""
        if self.successful_symbols is None:
            self.successful_symbols = []
        if self.failed_symbols is None:
            self.failed_symbols = []
        if self.not_downloaded_symbols is None:
            self.not_downloaded_symbols = []
        if self.no_download_needed is None:
            self.no_download_needed = []
        if self.data is None:
            self.data = {}
        if self.save_paths is None:
            self.save_paths = {}
        if self.logged_data_details is None:
            self.logged_data_details = {}
        if self.failed_reasons is None:
            self.failed_reasons = {}


class ResultManager:
    """结果管理器"""

    def __init__(self, data_dir: Optional[str] = None, result_file: Optional[str] = None):
        """
        初始化结果管理器

        Args:
            data_dir: 数据目录，默认使用DATA_ROOT
            result_file: 结果文件路径，如果提供则使用此路径，否则使用data_dir/download_results.txt
        """
        self.data_dir = data_dir or DATA_ROOT
        if result_file:
            self.result_file = result_file
            # 从结果文件路径推导数据目录
            self.data_dir = os.path.dirname(result_file)
        else:
            self.result_file = os.path.join(self.data_dir, "download_results.txt")
    
    def merge_results(self, result1: DownloadResult, result2: DownloadResult) -> DownloadResult:
        """
        合并两个下载结果
        
        Args:
            result1: 第一个结果
            result2: 第二个结果
            
        Returns:
            合并后的结果
        """
        merged = DownloadResult()
        
        # 合并状态
        merged.success = result1.success and result2.success
        
        # 合并列表
        merged.successful_symbols = list(set(result1.successful_symbols + result2.successful_symbols))
        merged.failed_symbols = list(set(result1.failed_symbols + result2.failed_symbols))
        merged.not_downloaded_symbols = list(set(result1.not_downloaded_symbols + result2.not_downloaded_symbols))
        merged.no_download_needed = list(set(result1.no_download_needed + result2.no_download_needed))
        
        # 合并字典
        merged.data = {**result1.data, **result2.data}
        merged.save_paths = {**result1.save_paths, **result2.save_paths}
        merged.logged_data_details = {**result1.logged_data_details, **result2.logged_data_details}
        merged.failed_reasons = {**result1.failed_reasons, **result2.failed_reasons}
        
        return merged
    
    def get_undownloaded_stocks_from_file(self, period: Optional[str] = None) -> List[str]:
        """
        从结果文件中读取未下载的股票列表

        Args:
            period: 数据周期，如果指定则从对应周期的结果中读取，否则从所有周期合并读取

        Returns:
            未下载的股票列表
        """
        undownloaded_stocks = []

        if os.path.exists(self.result_file):
            try:
                with open(self.result_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()

                if period:
                    # 从指定周期的结果中读取
                    undownloaded_stocks = self._get_period_undownloaded_stocks(lines, period)
                else:
                    # 从所有周期的未下载股票中合并读取
                    undownloaded_stocks = self._get_all_periods_undownloaded_stocks(lines)

                logger.debug(LogTarget.FILE, f"从结果文件中读取到未下载的股票: {len(undownloaded_stocks)}只 (周期: {period or '全部'})")
            except Exception as e:
                logger.error(LogTarget.FILE, f"读取下载结果文件失败: {e}", exc_info=True)

        return undownloaded_stocks

    def _get_all_periods_undownloaded_stocks(self, lines: List[str]) -> List[str]:
        """从所有周期的未下载股票中合并读取"""
        all_undownloaded_stocks = set()

        # 定义支持的周期
        periods = ["1d", "1m", "tick"]

        for period in periods:
            period_stocks = self._get_period_undownloaded_stocks(lines, period)
            all_undownloaded_stocks.update(period_stocks)

        return list(all_undownloaded_stocks)

    def _get_period_undownloaded_stocks(self, lines: List[str], period: str) -> List[str]:
        """从指定周期的结果中读取未下载的股票"""
        undownloaded_stocks = []
        period_section = f"========== {self._get_period_display_name(period)}数据下载结果 =========="
        in_period_section = False
        in_not_downloaded_section = False

        for line in lines:
            line = line.strip()
            if line == period_section:
                in_period_section = True
                continue
            elif line.startswith("==========") and in_period_section:
                # 遇到下一个分段，结束当前周期读取
                break
            elif in_period_section:
                if line == "未下载的股票:":
                    in_not_downloaded_section = True
                elif line in ["下载成功的股票:", "下载失败的股票:", "无需下载的股票:"]:
                    in_not_downloaded_section = False
                elif line.startswith(("总计股票:", "下载成功:", "下载失败:", "未下载:", "无需下载:", "最后更新时间:")):
                    in_not_downloaded_section = False
                elif in_not_downloaded_section and line and "." in line:
                    # 解析股票代码
                    from utils.text_parser import parse_stock_code_input
                    parsed_codes = parse_stock_code_input(line)
                    undownloaded_stocks.extend(parsed_codes)

        return undownloaded_stocks

    def _get_period_display_name(self, period: str) -> str:
        """获取周期的显示名称"""
        period_names = {
            "1d": "日线",
            "1m": "1分钟",
            "tick": "tick"
        }
        return period_names.get(period, period)

    def save_download_results(self, successful_symbols: List[str], failed_symbols: List[str],
                            not_downloaded_symbols: List[str], period: Optional[str] = None,
                            start_time: Optional[str] = None, end_time: Optional[str] = None,
                            no_download_needed_symbols: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        保存下载结果到文件

        Args:
            successful_symbols: 成功的股票列表
            failed_symbols: 失败的股票列表
            not_downloaded_symbols: 未下载的股票列表
            period: 数据周期（必需，用于多周期结果管理）
            start_time: 开始时间
            end_time: 结束时间
            no_download_needed_symbols: 无需下载的股票列表

        Returns:
            包含保存结果信息的字典
        """
        if not period:
            logger.warning(LogTarget.FILE, "未指定周期参数，使用默认周期")
            period = "unknown"

        # 读取之前的结果
        previous_result = self._read_previous_results_multi_period()

        # 处理无需下载的股票
        no_download_needed_symbols = no_download_needed_symbols or []

        # 获取当前周期的之前结果
        period_key = period
        if period_key not in previous_result["periods"]:
            previous_result["periods"][period_key] = {
                "successful": [],
                "failed": [],
                "not_downloaded": [],
                "no_download_needed": []
            }

        period_previous = previous_result["periods"][period_key]

        # 合并当前周期的结果
        combined_successful = list(set(period_previous["successful"] + successful_symbols))
        combined_failed = list(set(period_previous["failed"] + failed_symbols))
        combined_no_download_needed = list(set(period_previous["no_download_needed"] + no_download_needed_symbols))

        # 对于未下载的股票，移除已成功、已失败和无需下载的股票
        all_not_downloaded = list(set(period_previous["not_downloaded"] + not_downloaded_symbols))
        combined_not_downloaded = [s for s in all_not_downloaded
                                  if s not in combined_successful
                                  and s not in combined_failed
                                  and s not in combined_no_download_needed]

        # 更新当前周期的结果
        previous_result["periods"][period_key] = {
            "successful": combined_successful,
            "failed": combined_failed,
            "not_downloaded": combined_not_downloaded,
            "no_download_needed": combined_no_download_needed
        }

        # 写入文件
        self._write_results_file_multi_period(previous_result, period, start_time, end_time)

        logger.info(LogTarget.FILE, f"下载结果已保存到: {self.result_file} (周期: {period})")

        # 计算新增数量
        new_successful = len(combined_successful) - len(period_previous["successful"])

        return {
            "result_file": self.result_file,
            "combined_successful": combined_successful,
            "combined_failed": combined_failed,
            "combined_not_downloaded": combined_not_downloaded,
            "combined_no_download_needed": combined_no_download_needed,
            "total": len(combined_successful) + len(combined_failed) + len(combined_not_downloaded) + len(combined_no_download_needed),
            "new_successful": new_successful
        }

    def _read_previous_results_multi_period(self) -> Dict[str, Any]:
        """读取多周期下载结果"""
        result = {
            "periods": {},
            "header": "",
            "timestamp": ""
        }

        if not os.path.exists(self.result_file):
            return result

        try:
            with open(self.result_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            if len(lines) >= 2:
                result["header"] = lines[0].strip()
                if "股票下载结果 - " in result["header"]:
                    result["timestamp"] = result["header"].split("- ")[1]

            # 读取各周期的结果
            current_period = None
            current_section = None

            for line in lines:
                line = line.strip()

                # 检查是否是周期分段
                if line.startswith("==========") and "数据下载结果" in line:
                    # 提取周期名称
                    period_display = line.replace("==========", "").replace("数据下载结果", "").strip()
                    current_period = self._get_period_code_from_display(period_display)
                    if current_period not in result["periods"]:
                        result["periods"][current_period] = {
                            "successful": [],
                            "failed": [],
                            "not_downloaded": [],
                            "no_download_needed": []
                        }
                    current_section = None
                elif current_period:
                    if line == "下载成功的股票:":
                        current_section = "successful"
                    elif line == "下载失败的股票:":
                        current_section = "failed"
                    elif line == "未下载的股票:":
                        current_section = "not_downloaded"
                    elif line == "无需下载的股票:":
                        current_section = "no_download_needed"
                    elif line.startswith(("总计股票:", "下载成功:", "下载失败:", "未下载:", "无需下载:", "最后更新时间:")):
                        current_section = None
                    elif current_section and line and "." in line:
                        # 解析股票代码
                        from utils.text_parser import parse_stock_code_input
                        parsed_codes = parse_stock_code_input(line)
                        result["periods"][current_period][current_section].extend(parsed_codes)

        except Exception as e:
            logger.error(LogTarget.FILE, f"读取多周期下载结果文件失败: {e}", exc_info=True)

        return result

    def _get_period_code_from_display(self, display_name: str) -> str:
        """从显示名称获取周期代码"""
        display_to_code = {
            "日线": "1d",
            "1分钟": "1m",
            "tick": "tick"
        }
        return display_to_code.get(display_name, display_name)

    def _read_previous_results(self) -> Dict[str, Any]:
        """读取之前的下载结果"""
        result = {
            "successful": [],
            "failed": [],
            "not_downloaded": [],
            "no_download_needed": [],
            "header": "",
            "timestamp": ""
        }
        
        if not os.path.exists(self.result_file):
            return result
        
        try:
            with open(self.result_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            if len(lines) >= 2:
                result["header"] = lines[0].strip()
                if "股票下载结果 - " in result["header"]:
                    result["timestamp"] = result["header"].split("- ")[1]
            
            current_section = None
            for line in lines:
                line = line.strip()
                if line == "下载成功的股票:":
                    current_section = "successful"
                elif line == "下载失败的股票:":
                    current_section = "failed"
                elif line == "未下载的股票:":
                    current_section = "not_downloaded"
                elif line == "无需下载的股票:":
                    current_section = "no_download_needed"
                elif line.startswith("-") or line.startswith("=") or not line:
                    continue
                elif line.startswith(("总计股票:", "下载成功:", "下载失败:", "未下载:", "无需下载:", "最后更新时间:")):
                    continue
                elif current_section and line and "." in line:
                    # 解析股票代码，处理带注释的格式
                    # 例如: "a00.DF",      # 豆一主力连续合约
                    from utils.text_parser import parse_stock_code_input
                    parsed_codes = parse_stock_code_input(line)
                    result[current_section].extend(parsed_codes)
        
        except Exception as e:
            logger.error(LogTarget.FILE, f"读取历史结果失败: {e}", exc_info=True)
        
        return result
    
    def _write_results_file(self, successful: List[str], failed: List[str], 
                          not_downloaded: List[str], no_download_needed: List[str],
                          period: Optional[str], start_time: Optional[str], end_time: Optional[str],
                          original_header: str, original_timestamp: str):
        """写入结果文件"""
        # 确保目录存在
        os.makedirs(self.data_dir, exist_ok=True)
        
        with open(self.result_file, 'w', encoding='utf-8') as f:
            # 写入标题
            if original_header:
                f.write(f"{original_header}\n")
            else:
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                f.write(f"股票下载结果 - {timestamp}\n")
            f.write("="*50 + "\n\n")
            
            # 添加更新时间
            update_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            f.write(f"最后更新时间: {update_time}\n\n")
            
            # 添加周期和日期信息
            if period:
                f.write(f"周期: {period}\n")
            if start_time:
                f.write(f"开始日期: {start_time}\n")
            if end_time:
                f.write(f"结束日期: {end_time}\n")
            f.write("\n")
            
            # 写入统计信息
            total = len(successful) + len(failed) + len(not_downloaded) + len(no_download_needed)
            f.write(f"总计股票: {total}\n")
            f.write(f"下载成功: {len(successful)}\n")
            f.write(f"下载失败: {len(failed)}\n")
            f.write(f"未下载: {len(not_downloaded)}\n")
            f.write(f"无需下载: {len(no_download_needed)}\n\n")
            
            # 写入各部分详情
            sections = [
                ("下载成功的股票:", successful),
                ("下载失败的股票:", failed),
                ("未下载的股票:", not_downloaded),
                ("无需下载的股票:", no_download_needed)
            ]
            
            for title, symbols in sections:
                f.write(f"{title}\n")
                f.write("-"*30 + "\n")
                for symbol in sorted(symbols):
                    f.write(f"{symbol}\n")
                f.write("\n")

    def _write_results_file_multi_period(self, result_data: Dict[str, Any], current_period: str,
                                        start_time: Optional[str] = None, end_time: Optional[str] = None):
        """写入简化的多周期结果文件"""
        # 确保目录存在
        os.makedirs(self.data_dir, exist_ok=True)

        with open(self.result_file, 'w', encoding='utf-8') as f:
            # 写入标题
            if result_data["header"]:
                f.write(f"{result_data['header']}\n")
            else:
                timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                f.write(f"股票下载结果 - {timestamp}\n")
            f.write("="*40 + "\n\n")

            # 直接写入各周期的结果
            for period, period_data in result_data["periods"].items():
                period_display = self._get_period_display_name(period)
                f.write(f"========== {period_display}数据下载结果 ==========\n")

                # 写入各部分详情
                sections = [
                    ("下载成功的股票:", period_data["successful"]),
                    ("下载失败的股票:", period_data["failed"]),
                    ("未下载的股票:", period_data["not_downloaded"]),
                    ("无需下载的股票:", period_data["no_download_needed"])
                ]

                for title, symbols in sections:
                    f.write(f"{title}\n")
                    for symbol in sorted(symbols):
                        f.write(f"{symbol}\n")
                    f.write("\n")

                # 写入统计信息
                total = len(period_data["successful"]) + len(period_data["failed"]) + len(period_data["not_downloaded"]) + len(period_data["no_download_needed"])
                f.write(f"总计股票: {total}\n")
                f.write(f"下载成功: {len(period_data['successful'])}\n")
                f.write(f"下载失败: {len(period_data['failed'])}\n")
                f.write(f"未下载: {len(period_data['not_downloaded'])}\n")
                f.write(f"无需下载: {len(period_data['no_download_needed'])}\n")

                # 添加更新时间
                update_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                f.write(f"最后更新时间: {update_time}\n\n")

    def log_download_results(self, result: DownloadResult):
        """记录下载结果日志"""
        successful_symbols = result.successful_symbols
        no_download_needed = result.no_download_needed
        failed_symbols = result.failed_symbols
        success = result.success
        
        if success:
            if len(no_download_needed) > 0 and len(successful_symbols) == 0:
                logger.info(LogTarget.FILE, "数据操作成功：本地数据已是最新，无需下载")
            elif len(no_download_needed) > 0 and len(successful_symbols) > 0:
                logger.info(LogTarget.FILE, "数据操作成功：部分数据下载成功，部分使用本地最新数据")
            else:
                logger.info(LogTarget.FILE, "数据下载成功")
        else:
            logger.warning(LogTarget.FILE, "数据下载失败或部分失败"+"\n"*3)


def get_result_manager(data_dir: Optional[str] = None, result_file: Optional[str] = None) -> ResultManager:
    """
    获取结果管理器实例

    Args:
        data_dir: 数据目录
        result_file: 结果文件路径

    Returns:
        结果管理器实例
    """
    return ResultManager(data_dir, result_file)