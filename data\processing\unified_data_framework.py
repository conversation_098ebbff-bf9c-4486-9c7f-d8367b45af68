#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一高性能数据处理框架

基于DuckDB构建的统一数据处理框架，提供：
- 统一的数据处理接口
- 高性能的向量化操作
- 智能的查询优化
- 内存和性能监控

核心特性：
- 使用DuckDB作为计算引擎，性能优于pandas
- 支持SQL和DataFrame两种操作方式
- 自动查询优化和缓存
- 统一的错误处理和日志记录

作者: AI Assistant
创建时间: 2025-07-15
版本: 1.0.0
"""

import time
import functools
from typing import Dict, List, Optional, Any, Union, Callable
import pandas as pd
import numpy as np
from pathlib import Path

# DuckDB用于高性能数据处理（硬依赖）
import duckdb

from data.storage.vectorized_reader import get_vectorized_reader
from utils.logger import get_unified_logger

logger = get_unified_logger(__name__)


class UnifiedDataFramework:
    """
    统一高性能数据处理框架
    
    提供统一的数据处理接口，支持多种数据源和处理方式
    """
    
    def __init__(self, enable_optimization: bool = True):
        """
        初始化统一数据处理框架
        
        Args:
            enable_optimization: 是否启用查询优化
        """
        self.enable_optimization = enable_optimization
        self._performance_stats = {
            'total_queries': 0,
            'total_time': 0.0,
            'cache_hits': 0,
            'optimization_hits': 0
        }
        
        # 初始化DuckDB连接（硬依赖）
        try:
            self._conn = duckdb.connect()
            logger.info("统一数据处理框架初始化成功（DuckDB引擎）")

            # 配置DuckDB优化选项（使用兼容的配置）
            try:
                self._conn.execute("SET disabled_optimizers=''")  # 启用所有优化器
            except:
                pass  # 如果不支持则忽略

        except Exception as e:
            logger.error(f"DuckDB初始化失败: {e}")
            raise RuntimeError(f"DuckDB是系统必需依赖，初始化失败: {e}")
        
        # 查询缓存
        self._query_cache = {}
        self._optimization_cache = {}
        
        # 获取向量化读取器
        self.vectorized_reader = get_vectorized_reader()
    
    def _performance_monitor(self, func):
        """
        性能监控装饰器
        """
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()

            # 更新统计信息
            self._performance_stats['total_queries'] += 1

            # 执行函数
            result = func(*args, **kwargs)

            # 记录执行时间
            end_time = time.time()
            execution_time = end_time - start_time
            self._performance_stats['total_time'] += execution_time

            # 记录性能日志
            logger.debug(f"统一数据框架: {func.__name__} 耗时 {execution_time:.6f} 秒")

            return result
        return wrapper
    def load_data(self, data_root: str, symbol: str, period: str,
                  start_time: Optional[str] = None,
                  end_time: Optional[str] = None,
                  columns: Optional[List[str]] = None) -> Optional[pd.DataFrame]:
        """
        统一的数据加载接口（使用DuckDB向量化读取）

        Args:
            data_root: 数据根目录
            symbol: 股票代码
            period: 数据周期
            start_time: 开始时间
            end_time: 结束时间
            columns: 要读取的列

        Returns:
            pd.DataFrame: 加载的数据

        Raises:
            RuntimeError: 当数据加载失败时
        """
        logger.debug(f"统一数据加载: {symbol} {period}")

        try:
            return self.vectorized_reader.read_partitioned_data_vectorized(
                data_root, symbol, period, start_time, end_time, columns
            )
        except Exception as e:
            logger.error(f"数据加载失败: {e}")
            raise RuntimeError(f"数据加载失败: {e}") from e
    
    def execute_sql(self, sql: str, data: Optional[pd.DataFrame] = None) -> Optional[pd.DataFrame]:
        """
        执行SQL查询

        Args:
            sql: SQL查询语句
            data: 可选的DataFrame数据

        Returns:
            pd.DataFrame: 查询结果

        Raises:
            RuntimeError: 当SQL执行失败时
        """
        if not self._conn:
            raise RuntimeError("DuckDB连接未初始化")
        
        try:
            logger.debug(f"执行SQL查询: {sql[:100]}...")

            # 如果提供了数据，先注册为临时表
            if data is not None:
                self._conn.register('temp_data', data)
                logger.debug(f"注册临时表 temp_data，行数: {len(data)}")

            # 执行查询
            result = self._conn.execute(sql).df()

            logger.debug(f"SQL查询完成，结果行数: {len(result) if result is not None else 0}")
            return result

        except Exception as e:
            error_msg = f"SQL查询执行失败: {e}"
            logger.error(error_msg)
            logger.error(f"失败的SQL: {sql}")
            raise RuntimeError(error_msg) from e
    
    def aggregate_data(self, data: pd.DataFrame,
                      group_by: List[str],
                      agg_funcs: Dict[str, Union[str, List[str]]]) -> Optional[pd.DataFrame]:
        """
        数据聚合操作（使用DuckDB）

        Args:
            data: 输入数据
            group_by: 分组列
            agg_funcs: 聚合函数字典

        Returns:
            pd.DataFrame: 聚合结果

        Raises:
            RuntimeError: 当聚合操作失败时
        """
        logger.debug(f"数据聚合: 分组列={group_by}")

        try:
            # 构建SQL聚合查询
            group_cols = ', '.join(group_by)
            agg_exprs = []

            for col, funcs in agg_funcs.items():
                if isinstance(funcs, str):
                    funcs = [funcs]
                for func in funcs:
                    agg_exprs.append(f"{func}({col}) as {col}_{func}")

            agg_sql = ', '.join(agg_exprs)
            sql = f"SELECT {group_cols}, {agg_sql} FROM temp_data GROUP BY {group_cols}"

            return self.execute_sql(sql, data)

        except Exception as e:
            error_msg = f"数据聚合失败: {e}"
            logger.error(error_msg)
            raise RuntimeError(error_msg) from e
    
    def filter_data(self, data: pd.DataFrame,
                   conditions: Dict[str, Any]) -> Optional[pd.DataFrame]:
        """
        数据过滤操作（使用DuckDB）

        Args:
            data: 输入数据
            conditions: 过滤条件字典

        Returns:
            pd.DataFrame: 过滤结果

        Raises:
            RuntimeError: 当过滤操作失败时
        """
        logger.debug(f"数据过滤: 条件={conditions}")

        try:
            # 构建SQL过滤查询
            where_clauses = []
            for col, value in conditions.items():
                if isinstance(value, (list, tuple)):
                    value_str = ', '.join([f"'{v}'" if isinstance(v, str) else str(v) for v in value])
                    where_clauses.append(f"{col} IN ({value_str})")
                elif isinstance(value, str):
                    where_clauses.append(f"{col} = '{value}'")
                else:
                    where_clauses.append(f"{col} = {value}")

            where_sql = ' AND '.join(where_clauses)
            sql = f"SELECT * FROM temp_data WHERE {where_sql}"

            return self.execute_sql(sql, data)

        except Exception as e:
            error_msg = f"数据过滤失败: {e}"
            logger.error(error_msg)
            raise RuntimeError(error_msg) from e
    
    def join_data(self, left: pd.DataFrame, right: pd.DataFrame,
                 on: Union[str, List[str]],
                 how: str = 'inner') -> Optional[pd.DataFrame]:
        """
        数据连接操作（使用DuckDB）

        Args:
            left: 左表数据
            right: 右表数据
            on: 连接键
            how: 连接方式

        Returns:
            pd.DataFrame: 连接结果

        Raises:
            RuntimeError: 当连接操作失败时
        """
        logger.debug(f"数据连接: 连接键={on}, 方式={how}")

        try:
            # 注册临时表
            self._conn.register('left_table', left)
            self._conn.register('right_table', right)

            # 构建SQL连接查询
            if isinstance(on, str):
                join_condition = f"left_table.{on} = right_table.{on}"
            else:
                join_conditions = [f"left_table.{col} = right_table.{col}" for col in on]
                join_condition = ' AND '.join(join_conditions)

            sql = f"""
            SELECT * FROM left_table
            {how.upper()} JOIN right_table
            ON {join_condition}
            """

            result = self._conn.execute(sql).df()
            logger.debug(f"DuckDB连接完成，结果行数: {len(result)}")
            return result

        except Exception as e:
            error_msg = f"数据连接失败: {e}"
            logger.error(error_msg)
            raise RuntimeError(error_msg) from e
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息
        
        Returns:
            dict: 性能统计信息
        """
        stats = self._performance_stats.copy()
        if stats['total_queries'] > 0:
            stats['avg_time_per_query'] = stats['total_time'] / stats['total_queries']
            stats['cache_hit_rate'] = stats['cache_hits'] / stats['total_queries']
            stats['optimization_hit_rate'] = stats['optimization_hits'] / stats['total_queries']
        else:
            stats['avg_time_per_query'] = 0.0
            stats['cache_hit_rate'] = 0.0
            stats['optimization_hit_rate'] = 0.0
        
        return stats
    
    def clear_cache(self):
        """清空缓存"""
        self._query_cache.clear()
        self._optimization_cache.clear()
        logger.debug("统一数据框架缓存已清空")
    
    def __del__(self):
        """析构函数，关闭DuckDB连接"""
        if hasattr(self, '_conn') and self._conn:
            try:
                self._conn.close()
            except:
                pass


# 全局实例
_global_framework = None

def get_unified_framework() -> UnifiedDataFramework:
    """
    获取全局统一数据处理框架实例
    
    Returns:
        UnifiedDataFramework: 统一数据处理框架实例
    """
    global _global_framework
    if _global_framework is None:
        _global_framework = UnifiedDataFramework()
    return _global_framework
