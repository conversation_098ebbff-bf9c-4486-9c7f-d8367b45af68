#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
合成结果管理模块

负责处理数据合成结果的保存、读取、合并等操作
提供与下载结果管理器一致的即时保存机制
"""

import os
import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from config.settings import DATA_ROOT
from utils.logger import get_unified_logger, LogTarget

logger = get_unified_logger(__name__, enhanced=True)


@dataclass
class SynthesisResult:
    """合成结果数据类"""
    success: bool = True
    successful_symbols: List[str] = None
    failed_symbols: List[str] = None
    not_synthesized_symbols: List[str] = None
    no_synthesis_needed: List[str] = None
    data: Dict[str, Any] = None
    save_paths: Dict[str, str] = None
    logged_data_details: Dict[str, Any] = None
    failed_reasons: Dict[str, str] = None
    
    def __post_init__(self):
        """初始化默认值"""
        if self.successful_symbols is None:
            self.successful_symbols = []
        if self.failed_symbols is None:
            self.failed_symbols = []
        if self.not_synthesized_symbols is None:
            self.not_synthesized_symbols = []
        if self.no_synthesis_needed is None:
            self.no_synthesis_needed = []
        if self.data is None:
            self.data = {}
        if self.save_paths is None:
            self.save_paths = {}
        if self.logged_data_details is None:
            self.logged_data_details = {}
        if self.failed_reasons is None:
            self.failed_reasons = {}


class SynthesisResultManager:
    """合成结果管理器"""
    
    def __init__(self, result_file: str = None, data_root: str = None):
        """
        初始化合成结果管理器
        
        Args:
            result_file: 结果文件路径
            data_root: 数据根目录
        """
        self.data_root = data_root or DATA_ROOT
        self.result_file = result_file or os.path.join(self.data_root, "synthesis_results.txt")
        
    def get_period_display_name(self, period: str) -> str:
        """获取周期的显示名称"""
        period_names = {
            "1d": "日线",
            "1h": "1小时",
            "30m": "30分钟", 
            "15m": "15分钟",
            "5m": "5分钟",
            "1m": "1分钟",
            "tick": "tick"
        }
        return period_names.get(period, period)

    def save_synthesis_results(self, successful_symbols: List[str], failed_symbols: List[str],
                              not_synthesized_symbols: List[str], config_name: Optional[str] = None,
                              start_time: Optional[str] = None, end_time: Optional[str] = None,
                              no_synthesis_needed_symbols: Optional[List[str]] = None,
                              failed_reasons: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        保存合成结果到文件
        
        Args:
            successful_symbols: 成功的股票列表
            failed_symbols: 失败的股票列表
            not_synthesized_symbols: 未合成的股票列表
            config_name: 配置名称（如"tick→1m合成"）
            start_time: 开始时间
            end_time: 结束时间
            no_synthesis_needed_symbols: 无需合成的股票列表
            failed_reasons: 失败原因字典
            
        Returns:
            包含保存结果信息的字典
        """
        if not config_name:
            logger.warning(LogTarget.FILE, "未指定配置名称，使用默认配置")
            config_name = "unknown"
            
        # 读取之前的结果
        previous_result = self._read_previous_results_multi_config()
        
        # 处理无需合成的股票和失败原因
        no_synthesis_needed_symbols = no_synthesis_needed_symbols or []
        failed_reasons = failed_reasons or {}
        
        # 获取当前配置的之前结果
        config_key = config_name
        if config_key not in previous_result["configs"]:
            previous_result["configs"][config_key] = {
                "successful": [],
                "failed": [],
                "not_synthesized": [],
                "no_synthesis_needed": [],
                "failed_reasons": {}
            }
            
        config_previous = previous_result["configs"][config_key]
        
        # 合并当前配置的结果
        combined_successful = list(set(config_previous["successful"] + successful_symbols))
        combined_failed = list(set(config_previous["failed"] + failed_symbols))
        combined_no_synthesis_needed = list(set(config_previous["no_synthesis_needed"] + no_synthesis_needed_symbols))

        # 合并失败原因
        combined_failed_reasons = {**config_previous.get("failed_reasons", {}), **failed_reasons}

        # 对于未合成的股票，需要正确处理状态转移
        # 1. 先合并所有未合成的股票
        all_not_synthesized = list(set(config_previous["not_synthesized"] + not_synthesized_symbols))

        # 2. 从未合成列表中移除已经成功或失败的股票
        combined_not_synthesized = []
        for stock in all_not_synthesized:
            # 如果股票在本次成功或失败列表中，则从未合成中移除
            if stock not in successful_symbols and stock not in failed_symbols:
                # 如果股票不在历史成功或失败列表中，则保留在未合成中
                if stock not in config_previous["successful"] and stock not in config_previous["failed"]:
                    combined_not_synthesized.append(stock)

        # 3. 从成功列表中移除本次失败的股票（状态转移）
        combined_successful = [s for s in combined_successful if s not in failed_symbols]

        # 4. 从失败列表中移除本次成功的股票（状态转移）
        combined_failed = [s for s in combined_failed if s not in successful_symbols]
        
        # 更新当前配置的结果
        previous_result["configs"][config_key] = {
            "successful": combined_successful,
            "failed": combined_failed,
            "not_synthesized": combined_not_synthesized,
            "no_synthesis_needed": combined_no_synthesis_needed,
            "failed_reasons": combined_failed_reasons
        }
        
        # 写入文件
        self._write_results_file_multi_config(previous_result, config_name, start_time, end_time)
        
        logger.debug(LogTarget.FILE, f"合成结果已保存到: {self.result_file} (配置: {config_name})")
        
        # 计算新增数量
        new_successful = len([s for s in successful_symbols if s not in config_previous["successful"]])
        new_failed = len([s for s in failed_symbols if s not in config_previous["failed"]])
        
        return {
            "result_file": self.result_file,
            "config_name": config_name,
            "new_successful": new_successful,
            "new_failed": new_failed,
            "total_successful": len(combined_successful),
            "total_failed": len(combined_failed),
            "total_not_synthesized": len(combined_not_synthesized),
            "total_no_synthesis_needed": len(combined_no_synthesis_needed)
        }

    def _read_previous_results_multi_config(self) -> Dict[str, Any]:
        """读取之前的多配置合成结果"""
        if not os.path.exists(self.result_file):
            return {"configs": {}}
            
        try:
            with open(self.result_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 解析多配置结果文件
            configs = {}
            current_config = None
            current_section = None
            
            for line in content.split('\n'):
                line = line.strip()
                
                # 检测配置分段
                if line.startswith('==========') and line.endswith('=========='):
                    config_name = line.replace('=', '').strip()
                    if config_name:  # 移除"合成结果"的检查，任何配置名称都接受
                        current_config = config_name
                        configs[current_config] = {
                            "successful": [],
                            "failed": [],
                            "not_synthesized": [],
                            "no_synthesis_needed": [],
                            "failed_reasons": {}
                        }
                        current_section = None
                        continue
                
                if current_config is None:
                    continue
                    
                # 检测各个部分
                if line == "合成成功的股票:":
                    current_section = "successful"
                elif line == "合成失败的股票:":
                    current_section = "failed"
                elif line == "未合成的股票:":
                    current_section = "not_synthesized"
                elif line == "无需合成的股票:":
                    current_section = "no_synthesis_needed"
                elif line.startswith("总计股票:") or line.startswith("合成成功:") or line.startswith("最后更新时间:"):
                    current_section = None
                elif current_section and line and not line.startswith("="):
                    # 处理失败原因（格式：股票代码: 原因）
                    if current_section == "failed" and ": " in line:
                        symbol, reason = line.split(": ", 1)
                        configs[current_config]["failed_reasons"][symbol.strip()] = reason.strip()
                        configs[current_config][current_section].append(symbol.strip())
                    elif current_section and line:
                        configs[current_config][current_section].append(line)
                        
            return {"configs": configs}
            
        except Exception as e:
            logger.error(LogTarget.FILE, f"读取合成结果文件失败: {e}")
            return {"configs": {}}

    def _write_results_file_multi_config(self, result_data: Dict[str, Any], 
                                        current_config: str, start_time: str, end_time: str):
        """写入多配置合成结果文件"""
        try:
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            with open(self.result_file, 'w', encoding='utf-8') as f:
                f.write(f"批量数据合成结果 - {timestamp}\n")
                f.write("="*50 + "\n\n")
                
                # 写入每个配置的结果
                for config_name, config_data in result_data["configs"].items():
                    f.write(f"========== {config_name} ==========\n")
                    
                    # 写入各部分
                    sections = [
                        ("合成成功的股票:", config_data["successful"]),
                        ("合成失败的股票:", config_data["failed"]),
                        ("未合成的股票:", config_data["not_synthesized"]),
                        ("无需合成的股票:", config_data["no_synthesis_needed"])
                    ]
                    
                    for title, stock_list in sections:
                        f.write(f"{title}\n")
                        if title == "合成失败的股票:" and config_data.get("failed_reasons"):
                            # 失败的股票显示失败原因
                            for stock in stock_list:
                                reason = config_data["failed_reasons"].get(stock, "未知原因")
                                f.write(f"{stock}: {reason}\n")
                        else:
                            for stock in stock_list:
                                f.write(f"{stock}\n")
                        f.write("\n")
                    
                    # 写入统计信息
                    total = len(config_data["successful"]) + len(config_data["failed"]) + len(config_data["not_synthesized"]) + len(config_data["no_synthesis_needed"])
                    f.write(f"总计股票: {total}\n")
                    f.write(f"合成成功: {len(config_data['successful'])}\n")
                    f.write(f"合成失败: {len(config_data['failed'])}\n")
                    f.write(f"未合成: {len(config_data['not_synthesized'])}\n")
                    f.write(f"无需合成: {len(config_data['no_synthesis_needed'])}\n")
                    f.write(f"最后更新时间: {timestamp}\n\n")
                    
        except Exception as e:
            logger.error(LogTarget.FILE, f"写入合成结果文件失败: {e}")
            raise

    def get_unsynthesized_stocks_from_file(self, config_name: Optional[str] = None) -> List[str]:
        """
        从结果文件中读取未合成的股票列表
        
        Args:
            config_name: 配置名称
            
        Returns:
            未合成的股票列表
        """
        result_data = self._read_previous_results_multi_config()
        
        if not config_name:
            # 如果没有指定配置，返回所有配置的未合成股票
            all_unsynthesized = []
            for config_data in result_data["configs"].values():
                all_unsynthesized.extend(config_data["not_synthesized"])
            return list(set(all_unsynthesized))
        
        if config_name in result_data["configs"]:
            return result_data["configs"][config_name]["not_synthesized"]
        
        return []
