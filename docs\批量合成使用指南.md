# 批量合成使用指南

## 📋 概述

批量合成功能已从交互式界面迁移到高效的批量脚本模式，遵循"一个功能一个实现"的架构原则。新的批量合成脚本采用**智能配置生成机制**，支持任意周期组合，提供了更高效、更灵活的数据合成方案。

## 🤖 智能配置系统

### 核心特性
- **任意周期支持**: 支持秒(30s)、分钟(3m)、小时(2h)、天(1d)等任意有效周期
- **自动源周期推荐**: 系统自动为每个目标周期推荐最优的源周期
- **自动配置名称**: 根据源周期和目标周期自动生成友好的中文名称
- **周期格式验证**: 自动验证周期格式有效性，过滤无效配置
- **复权数据支持**: 支持原始数据、前复权、后复权三种数据类型的周期合成

## 🚀 快速开始

### 基本使用
```bash
# 直接运行批量合成脚本
python data/批量合成历史数据.py
```

### 准备工作
1. **创建股票列表文件**（可选）
   ```bash
   # 在数据目录下创建 stock_list.txt
   echo "000001.SZ" > data/stock_list.txt
   echo "600000.SH" >> data/stock_list.txt
   echo "000002.SZ" >> data/stock_list.txt
   ```

2. **支持的股票代码格式**
   系统支持多种股票代码格式，包括带注释的代码：
   ```text
   # 标准格式
   000001.SZ
   600000.SH

   # 带注释格式（推荐用于期货合约）
   000001.SZ    # 平安银行
   "A00.DF",    # 豆一主力连续合约
   'pg00.DF'    # 液化气连续合约

   # 混合格式
   rb00.DF      # 螺纹钢主力连续合约
   "cu00.DF"    # 沪铜主力连续合约
   ```

   **注意**: 系统会自动去除注释内容和引号，只保留纯净的股票代码。

3. **如果没有股票列表文件**
   - 脚本会自动使用默认股票列表 `['000001.SZ']`
   - 系统会提示创建自定义股票列表文件

## 📊 功能特性

### 支持的周期格式
智能配置系统支持以下周期格式：

| 类型 | 格式 | 示例 | 说明 |
|------|------|------|------|
| **秒级** | Ns | 30s, 60s | 秒级周期，用于高频数据 |
| **分钟级** | Nm | 1m, 3m, 5m, 15m, 30m | 分钟级周期，最常用 |
| **小时级** | Nh | 1h, 2h, 4h, 6h | 小时级周期，日内分析 |
| **日级** | Nd | 1d, 2d | 日级周期，中长期分析 |
| **周级** | Nw | 1w, 2w | 周级周期，趋势分析 |
| **月级** | NM | 1M, 3M | 月级周期，长期分析 |

### 自动源周期推荐规则
- **1分钟周期**: 自动使用 `tick` 数据作为源
- **其他周期**: 自动使用 `1m` 数据作为源（分层合成策略）
- **小于1分钟**: 自动使用 `tick` 数据作为源

### 默认配置示例
脚本默认生成以下配置：

| 自动生成配置 | 源周期 | 目标周期 | 配置名称 |
|-------------|--------|----------|----------|
| ✅ | tick | 1m | tick→1分钟 |
| ✅ | 1m | 3m | 1分钟→3分钟 |
| ✅ | 1m | 5m | 1分钟→5分钟 |
| ✅ | 1m | 15m | 1分钟→15分钟 |
| ✅ | 1m | 30m | 1分钟→30分钟 |
| ✅ | 1m | 1h | 1分钟→1小时 |
| ✅ | 1m | 2h | 1分钟→2小时 |
| ✅ | 1m | 4h | 1分钟→4小时 |

### 智能特性
- **增量更新检测**: 自动检测已有数据，只处理新增部分
- **进度跟踪**: 实时显示合成进度和统计信息
- **错误处理**: 完善的错误处理和重试机制
- **性能监控**: 详细的耗时统计和性能报告
- **延时控制**: 配置间延时，避免系统负载过高

## 📈 复权数据支持

### 复权类型配置
批量合成脚本现已支持复权数据处理，可在 `data/批量合成历史数据.py` 文件中配置：

```python
# ==================== 复权配置 ====================

# 🎯 复权类型配置：选择数据复权方式
#
# 支持的复权类型：
# - "none": 原始数据（不进行复权处理）
# - "front": 前复权（向前调整价格，保持最新价格不变）
# - "back": 后复权（向后调整价格，保持历史价格不变）
#
# 推荐设置：
# - 技术分析：使用 "front" 前复权
# - 历史回测：使用 "front" 前复权
# - 原始数据分析：使用 "none" 不复权
dividend_type = "none"  # 默认使用原始数据
```

### 复权数据特点
| 复权类型 | 特点 | 适用场景 | 价格连续性 |
|----------|------|----------|------------|
| **none** | 原始价格数据 | 原始数据分析、除权除息研究 | 除权日有跳空 |
| **front** | 前复权价格 | 技术分析、策略回测 | 价格连续，最新价格真实 |
| **back** | 后复权价格 | 历史价格分析 | 价格连续，历史价格真实 |

### 复权数据流程
1. **数据读取**: 从分区文件读取原始价格数据
2. **复权计算**: 根据复权因子计算调整后价格
3. **周期合成**: 对复权后数据进行周期合成
4. **数据保存**: 保存复权后的合成数据

## 🔧 自定义配置

### 简单配置方法（推荐）
只需修改 `data/批量合成历史数据.py` 文件中的 `target_periods` 列表和复权配置：

```python
# 🎯 用户配置区域：只需修改此处的目标周期列表
target_periods = [
    "1m",    # 系统自动选择: tick → 1分钟
    "3m",    # 系统自动选择: 1m → 3分钟
    "5m",    # 系统自动选择: 1m → 5分钟
    "15m",   # 系统自动选择: 1m → 15分钟
    "30m",   # 系统自动选择: 1m → 30分钟
    "1h",    # 系统自动选择: 1m → 1小时
    "2h",    # 系统自动选择: 1m → 2小时
    "4h",    # 系统自动选择: 1m → 4小时
    "6h",    # 系统自动选择: 1m → 6小时
    "1d",    # 系统自动选择: 1m → 1天
]
```

### 支持的自定义周期示例
```python
target_periods = [
    "30s",   # 30秒周期（使用tick数据）
    "2m",    # 2分钟周期
    "7m",    # 7分钟周期
    "45m",   # 45分钟周期
    "3h",    # 3小时周期
    "8h",    # 8小时周期
    "2d",    # 2天周期
    "3w",    # 3周周期
    "6M",    # 6月周期
]
```

### 高级配置方法
如需更精细的控制，可以直接使用配置生成函数：

```python
from data.批量合成历史数据 import generate_synthesis_configs

# 生成自定义配置
custom_configs = generate_synthesis_configs(
    target_periods=["2m", "7m", "3h"],
    start_date="20240101",  # 指定开始日期
    end_date="20241231",    # 指定结束日期
    show_data=False,        # 不显示数据预览
    display_rows=10         # 预览行数
)
```

### 修改延时设置
```python
delay_between_configs = 3  # 配置间延时（秒）
```

## 📈 输出示例

### 智能配置生成输出
```
🤖 智能配置生成中...
📋 目标周期列表: ['1m', '3m', '5m', '15m', '30m', '1h', '2h', '4h']
✅ 配置生成完成，共生成 8 个合成配置:
   1. tick→1分钟
   2. 1分钟→3分钟
   3. 1分钟→5分钟
   4. 1分钟→15分钟
   5. 1分钟→30分钟
   6. 1分钟→1小时
   7. 1分钟→2小时
   8. 1分钟→4小时
```

### 运行过程输出
```
🚀 开始批量数据合成，共 8 个配置
📋 统一结果文件: D:\data\synthesis_results.txt
📋 从文件读取到 1 只股票

============================================================
📈 [1/8] 开始合成 tick→1分钟 数据...
⏰ 时间范围: 最早可用 ~ 今天
📊 数据预览: 是
📁 结果文件: synthesis_results.txt (配置: tick→1m)
============================================================

✅ tick→1分钟数据合成完成
📊 合成统计: 总计 1 只股票, 成功 1 只, 失败 0 只
⏱️  配置耗时: 19.14秒
📈 总体进度: 1/8 配置已完成

⏳ 等待3秒后继续合成下一个配置...
```

### 最终统计报告
```
🎉 ============================================================
🎉 批量数据合成完成！
🎉 ============================================================

📊 总体统计:
   ⏱️  总耗时: 62.90秒 (1.0分钟)
   📈 完成配置: 8/8
   📊 处理股票: 8 只
   ✅ 成功合成: 8 只
   ❌ 失败合成: 0 只
   📈 成功率: 100.0%

📋 各配置详细结果:
   ✅ tick→1分钟: 19.14秒, 1只股票
   ✅ 1分钟→3分钟: 4.57秒, 1只股票
   ✅ 1分钟→5分钟: 3.73秒, 1只股票
   ✅ 1分钟→15分钟: 3.03秒, 1只股票
   ✅ 1分钟→30分钟: 2.89秒, 1只股票
   ✅ 1分钟→1小时: 2.87秒, 1只股票
   ✅ 1分钟→2小时: 2.88秒, 1只股票
   ✅ 1分钟→4小时: 2.78秒, 1只股票

📅 各配置时间范围:
   - tick→1分钟: 最早可用 ~ 今天
   - 1分钟→3分钟: 最早可用 ~ 今天
   - 1分钟→5分钟: 最早可用 ~ 今天
   - 1分钟→15分钟: 最早可用 ~ 今天
   - 1分钟→30分钟: 最早可用 ~ 今天
   - 1分钟→1小时: 最早可用 ~ 今天
   - 1分钟→2小时: 最早可用 ~ 今天
   - 1分钟→4小时: 最早可用 ~ 今天
```

## 🔍 故障排除

### 常见问题

1. **股票列表文件不存在**
   ```
   ❌ 股票列表文件不存在: D:\quant\data\stock_list.txt
   📋 使用默认股票列表: ['000001.SZ']
   💡 提示: 可创建 stock_list.txt 文件来自定义股票列表
   ```
   **解决方案**: 创建股票列表文件或使用默认配置

2. **合成失败**
   ```
   ❌ tick→1分钟数据合成失败
      失败原因: 源数据不存在
   ```
   **解决方案**: 确保已下载相应的源数据

3. **部分股票失败**
   ```
   ❌ 失败的股票:
      - 000002.SZ: 数据格式错误
      - 600001.SH: 文件读取失败
   ```
   **解决方案**: 检查失败股票的数据文件

## 📚 与交互式合成的对比

| 特性 | 交互式合成 | 批量脚本合成 |
|------|------------|--------------|
| **效率** | 低（需要逐步输入） | 高（一次性配置） |
| **批量处理** | 不支持 | 完全支持 |
| **进度跟踪** | 基础 | 详细统计 |
| **错误处理** | 简单 | 完善 |
| **自动化** | 不支持 | 完全自动化 |
| **配置灵活性** | 有限 | 高度可配置 |

## 🎯 最佳实践

### 1. 分层合成策略
- **tick → 1m**: 优先使用tick数据合成1分钟K线
- **1m → 更大周期**: 使用1分钟K线合成更大周期
- 避免tick数据直接合成大周期（效率低）

### 2. 时间范围设置
- **空字符串**: 使用系统自动检测的时间范围
- **具体日期**: 使用YYYYMMDD格式，如"20240101"
- **增量更新**: 系统自动检测，无需手动设置

### 3. 性能优化
- 合理设置配置间延时，避免系统负载过高
- 监控内存使用，大批量处理时考虑分批执行
- 定期清理日志文件，保持系统性能

## 🔗 相关文档

- [架构维护指南](架构维护指南.md) - 了解"一个功能一个实现"原则
- [增量更新功能文档](../utils/data_processor/INCREMENTAL_UPDATE.md) - 了解增量更新机制
- [项目结构规范](../PROJECT_STRUCTURE.md) - 了解项目组织结构

---

**注意**: 交互式合成功能已被移除，请统一使用批量脚本进行数据合成操作。
