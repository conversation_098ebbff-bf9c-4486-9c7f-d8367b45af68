#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
时间验证功能模块

提供日期时间格式的验证和处理功能
"""

from utils.time_formatter.parsing import parse_multi_format_date
import os
import sys
from datetime import datetime, timedelta
from typing import Optional, Tuple

# 将项目根目录添加到Python路径
root_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, root_path)

# 导入解析函数


def validate_date_format(date_str: str) -> bool:
    """
    验证日期格式是否正确

    Args:
        date_str: 日期字符串，格式应为YYYYMMDD或YYYYMMDD HHMMSS

    Returns:
        bool: 日期格式是否有效
    """
    try:
        # 验证日期格式
        if len(date_str) == 8:
            # 尝试转换为日期格式来验证
            datetime.strptime(date_str, '%Y%m%d')
            return True
        elif len(date_str) == 15:
            # 带时分秒的格式
            datetime.strptime(date_str, '%Y%m%d %H%M%S')
            return True
        else:
            # 尝试使用parse_multi_format_date解析
            parse_multi_format_date(date_str)
            return True

    except ValueError:
        return False


def is_trading_day(date_str: str) -> bool:
    """
    判断是否为交易日

    Args:
        date_str: 日期字符串，格式为YYYYMMDD

    Returns:
        bool: 是否为交易日
    """
    try:
        # 先转换为datetime
        date_obj = datetime.strptime(date_str, '%Y%m%d')

        # 周末判断
        if date_obj.weekday() >= 5:  # 5=周六, 6=周日
            return False

        # 此处可以添加节假日判断逻辑
        # TODO: 可以接入交易日历API，或维护一个节假日列表

        return True
    except Exception as e:
        # 解析失败时，记录日志并默认当作交易日处理
        print(f"交易日判断异常: {e}")
        return True  # 默认当作交易日处理


def adjust_to_trading_day(date_str: str, direction: str = 'forward') -> str:
    """
    调整日期到最近的交易日

    Args:
        date_str: 日期字符串，格式为YYYYMMDD
        direction: 调整方向，'forward'向后调整, 'backward'向前调整

    Returns:
        str: 调整后的日期字符串
    """
    # 检查日期字符串是否为空
    if not date_str:
        return date_str
        
    try:
        date_obj = datetime.strptime(date_str, '%Y%m%d')
        max_attempts = 10  # 最多尝试调整10天

        for _ in range(max_attempts):
            current_date_str = date_obj.strftime('%Y%m%d')
            if is_trading_day(current_date_str):
                return current_date_str

            if direction == 'forward':
                date_obj += timedelta(days=1)
            else:
                date_obj -= timedelta(days=1)

        # 如果尝试多次仍找不到交易日，返回原日期
        print(f"无法找到靠近 {date_str} 的交易日")
        return date_str
    except Exception as e:
        print(f"交易日调整异常: {e}")
        return date_str


def get_start_end_date(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    days_lookback: int = 30
) -> Tuple[datetime, datetime]:
    """
    获取开始和结束日期，处理默认值

    Args:
        start_date: 开始日期字符串，格式为YYYYMMDD
        end_date: 结束日期字符串，格式为YYYYMMDD
        days_lookback: 如果start_date为None，从end_date向前推days_lookback天

    Returns:
        (开始日期, 结束日期)的元组，均为datetime对象
    """
    import pytz

    # 处理结束日期
    if end_date is None or end_date == "":
        end_dt = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    else:
        end_dt = datetime.strptime(end_date, "%Y%m%d")

    # 处理开始日期
    if start_date is None or start_date == "":
        start_dt = end_dt - timedelta(days=days_lookback)
    else:
        start_dt = datetime.strptime(start_date, "%Y%m%d")

    # 设置时区
    tz = pytz.timezone("Asia/Shanghai")
    start_dt = tz.localize(start_dt)
    end_dt = tz.localize(end_dt)

    return start_dt, end_dt


def get_default_period_dates(period: str) -> Tuple[str, str]:
    """
    根据数据周期获取默认的开始和结束日期

    Args:
        period: 数据周期，如 "tick", "1m", "5m", "15m", "30m", "1h", "1d"

    Returns:
        (default_start_date, default_end_date) 元组，格式为YYYYMMDD的字符串
    """
    today = datetime.now()
    default_end_date = today.strftime("%Y%m%d")

    # 所有周期均使用统一的起始日期：""空日期
    default_start_date = ""

    return default_start_date, default_end_date
