---
description: 
globs: "*"
alwaysApply: true
---
# AI助手命令调用规则

本规则文档为AI助手提供了关于如何调用General Modules项目中各模块命令行工具的详细指南，包括命令行接口、参数选择和错误处理策略等。

## 一般原则

在处理用户请求时，AI助手应遵循以下原则：

1. **首选命令行工具**：优先使用模块的命令行工具而非调用Python代码。
2. **选择最简洁路径**：使用能够实现用户需求的最直接命令。
3. **参数精确性**：提供精确而完整的参数，避免产生交互式提示。
4. **错误处理**：监控命令执行结果，遇到错误时尝试备选方案或提供明确解释。
5. **权限最小化**：只请求执行任务所必需的系统权限。
6. **解释意图**：在执行命令前简要说明将要执行的操作及其预期结果。

## 统一可安装包

**General Modules** 项目提供了简洁、标准化的命令行工具。AI助手在调用项目功能时应使用这些统一的命令行工具。

### 安装方法

```bash
# 在项目根目录下安装
cd /path/to/General_modules
pip install .

# 或从特定路径安装
pip install /path/to/General_modules
```

### 统一命令行工具

安装后，以下命令将全局可用：

- **`genmod-format`** - VSCode格式化工具
- **`genmod-deps`** - 依赖管理工具
- **`genmod-crawl`** - Web爬虫工具
- **`genmod-cache`** - 缓存管理工具

### 统一选项

所有命令都支持以下通用选项：

- `--help` / `-h` - 显示帮助信息（包含所有可用的子命令和选项）
- `--version` - 显示版本信息
- `--verbose` / `-v` - 启用详细输出
- `--non-interactive` - 启用非交互式模式（不提示确认，AI助手调用时应始终使用此选项）
- `--output-format` - 指定输出格式，可选 `text`（默认）或 `json`（推荐AI调用时使用）

### 使用JSON输出格式

为了便于AI助手解析命令输出，建议在调用命令时始终使用 `--output-format json` 选项。JSON输出将包含以下标准字段：

- `status` - 状态（"success", "error", "warning"）
- `command` 或 `action_taken` - 执行的命令或操作
- `message` - 人类可读的消息
- `details` - 包含详细信息的对象（如有）

### 命令行工具详细说明

#### 1. genmod-format (VSCode格式化工具)

**基本用法**：
```bash
genmod-format [action] [options]
```

**可用的actions**：
- `format` - 仅格式化代码
- `fix` - 仅修复linter错误
- `all` - 既格式化代码又修复linter错误（默认行为）

**常用选项**：
- `--file, -f` - 指定要处理的单个文件
- `--dir, -d` - 指定要处理的目录
- `--project-root, -p` - 指定项目根目录
- `--non-interactive` - 不提示确认（推荐AI使用）
- `--output-format` - 输出格式（text或json）
- `--verbose, -v` - 显示详细输出

**示例命令**：
```bash
# 格式化指定文件
genmod-format format --file /path/to/file.py --non-interactive

# 修复目录中的代码问题并使用JSON输出
genmod-format fix --dir /path/to/project --non-interactive --output-format json

# 同时格式化和修复，显示详细输出
genmod-format all --dir /path/to/project --non-interactive --verbose
```

#### 2. genmod-deps (依赖管理工具)

**基本用法**：
```bash
genmod-deps [command] [options]
```

**主要命令**：
- `check` - 检查并安装/更新依赖
- `optimize` - 创建精简conda环境
- `missing` - 检查项目中缺失的依赖
- `activate` - 激活conda环境
- `list` - 列出requirements.txt中的依赖

**常用选项**：
- `--requirements, -r` - 指定requirements.txt文件路径（用于check和list命令）
- `--project-dir, -p` - 指定项目目录路径（用于missing命令）
- `--env-name, -e` - 指定conda环境名称（用于optimize和activate命令）
- `--no-install` - 仅检查不安装（用于check命令）
- `--non-interactive` - 不提示确认（推荐AI使用）
- `--output-format` - 输出格式（text或json）
- `--verbose, -v` - 显示详细输出

**示例命令**：
```bash
# 检查并安装依赖，使用JSON输出
genmod-deps check --non-interactive --output-format json

# 使用自定义requirements文件
genmod-deps check --requirements custom_requirements.txt --non-interactive

# 检查缺失的依赖
genmod-deps missing --project-dir /path/to/project --output-format json

# 创建精简conda环境
genmod-deps optimize --env-name myenv --non-interactive

# 列出当前依赖
genmod-deps list --output-format json
```

#### 3. genmod-crawl (Web爬虫工具)

**基本用法**：
```bash
genmod-crawl [command] [options]
```

**主要命令**：
- `crawl` - 爬取指定网站
- `discover` - 发现网站链接结构
- `interactive` - 启动交互式模式

**常用选项**：
- `--url` - 网站URL（crawl和discover命令必需）
- `--browser` - 浏览器类型（"playwright"或"selenium"）
- `--headless` - 是否使用无头模式（默认true）
- `--output-dir` - 输出目录（crawl命令，默认为"./output"）
- `--menu-selector` - 菜单元素的CSS选择器
- `--link-selector` - 链接元素的CSS选择器（默认为"a"）
- `--pagination-selector` - 分页元素的CSS选择器
- `--timeout` - 页面加载超时时间（秒）（默认为60）
- `--delay` - 页面间爬取延迟（秒）（默认为1）
- `--non-interactive` - 不提示确认（推荐AI使用）
- `--output-format` - 输出格式（text或json）

**示例命令**：
```bash
# 爬取网站，使用JSON输出
genmod-crawl crawl --url https://example.com --output-dir ./output --non-interactive --output-format json

# 使用Playwright引擎爬取
genmod-crawl crawl --url https://example.com --browser playwright --non-interactive

# 分析网站结构
genmod-crawl discover --url https://example.com --non-interactive

# 自定义选择器
genmod-crawl crawl --url https://example.com \
  --menu-selector ".main-menu li" \
  --link-selector ".content a" \
  --pagination-selector ".pagination a.next" \
  --non-interactive
```

#### 4. genmod-cache (缓存管理工具)

**基本用法**：
```bash
genmod-cache [command] [options]
```

**主要命令**：
- `quick-setup` - 一键设置（设置缓存+清理+环境变量）
- `clean` - 清理项目中的缓存目录
- `status` - 显示当前缓存配置状态
- `list-caches` - 列出支持的缓存类型和配置
- `monitor` - 监控项目目录中的缓存文件夹
- `system-vars` - 显示环境变量设置指南
- `setup-system-vars` - 设置系统环境变量
- `setup` - 设置所有缓存到外部目录
- `export-vars` - 导出环境变量到文件

**常用选项**：
- `--cache-dir` - 缓存根目录路径
- `--project-dir` - 项目根目录路径
- `--force` - 强制清理（clean命令）
- `--non-interactive` - 不提示确认（推荐AI使用）
- `--output-format` - 输出格式（text或json）
- `--verbose, -v` - 显示详细输出

**示例命令**：
```bash
# 一键设置缓存
genmod-cache quick-setup --non-interactive

# 清理项目缓存
genmod-cache clean --project-dir /path/to/project --force --non-interactive

# 查看缓存状态，JSON输出
genmod-cache status --output-format json

# 列出支持的缓存类型
genmod-cache list-caches --output-format json
```

## AI助手决策指南

### 命令选择优先级

统一命令行工具是推荐的使用方式，应始终优先使用 `genmod-*` 命令。

### JSON输出优先

AI助手应始终在调用命令时指定 `--output-format json`（如果支持），以便更可靠地解析命令输出和错误信息。

### 何时使用命令行工具

AI助手应在以下情况主动使用模块的命令行工具：

1. **识别任务匹配**：当用户的问题、任务或需求与某个模块的功能相关时，即使用户没有明确请求使用该模块。
2. **自动化任务**：当AI识别到用户需求可以通过某个模块的命令行工具高效完成时。
3. **标准化操作**：当用户描述的操作有明确的标准化流程，可以通过命令行工具执行时。
4. **批处理**：当用户需要处理多个文件或大量数据时，应主动推荐和使用适当的命令行工具。
5. **系统集成**：当用户需要多个功能协同工作，可以通过命令行工具实现自动化集成时。

### 命令选择决策树

当需要选择合适的命令时，AI助手应：

1. **识别需求领域**：分析用户请求，确定属于哪个功能领域(代码格式化、依赖管理、网络爬虫、缓存管理等)。
2. **确定操作类型**：
   - 是简单查询还是复杂操作？
   - 是否需要交互？
   - 是否处理大量数据？
3. **自主选择最佳工具**：
   - 对于代码格式化需求，使用 `genmod-format` 相关命令
   - 对于依赖管理需求，使用 `genmod-deps` 相关命令
   - 对于网站内容获取需求，使用 `genmod-crawl` 相关命令
   - 对于缓存和环境变量管理需求，使用 `genmod-cache` 相关命令
4. **参数优化**：选择最能满足用户需求的参数组合，提供完整准确的命令。始终添加 `--non-interactive` 和 `--output-format json`（如果支持）。

### 错误处理策略

当命令执行失败时，AI助手应：

1. **分析错误**：根据退出码和错误消息分析问题。
2. **重试策略**：
   - 如果是简单错误（如文件路径问题）→ 修正参数并重试
   - 如果是环境问题（如依赖缺失）→ 先解决环境问题再重试
   - 如果是权限问题 → 建议用户以适当权限运行
3. **反馈与解释**：向用户解释错误原因和采取的措施。

### 示例情境与推荐命令

1. **用户需要格式化项目中的所有Python文件**：
   ```bash
   genmod-format all --dir /path/to/project --non-interactive --output-format json
   ```

2. **用户需要检查并安装项目依赖**：
   ```bash
   genmod-deps check --non-interactive --output-format json
   ```

3. **用户想要爬取特定网站并保存**：
   ```bash
   genmod-crawl crawl --url https://example.com --output-dir /path/to/output --non-interactive --output-format json
   ```

4. **用户想要清理项目中的缓存文件**：
   ```bash
   genmod-cache clean --project-dir /path/to/project --force --non-interactive --output-format json
   ```

## 总结

通过遵循本规则文档，AI助手能够:
1. 有效地选择和调用合适的命令行工具，使用统一可安装包提供的 `genmod-*` 命令
2. 提供精确而完整的参数，使用非交互式模式和JSON输出
3. 处理可能出现的错误，并提供明确解释
4. 为用户提供最佳的自动化体验

在实际应用中，AI助手应优先考虑用户的具体需求和上下文，灵活调整命令参数和策略，确保命令行工具的使用既有效又符合项目标准。

