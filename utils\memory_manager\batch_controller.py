"""
批处理控制模块 - 提供数据分批处理功能
"""

import os
import time
import threading
import queue
import psutil
from typing import Dict, List, Any, Callable, Optional, Tuple, Iterator, TypeVar, Generic, Union

# 导入正确的logger模块
from ..logger import get_unified_logger, LogTarget

# 获取logger实例
logger = get_unified_logger("batch_controller")

# 定义泛型类型变量
T = TypeVar('T')  # 输入数据类型
R = TypeVar('R')  # 结果数据类型


class BatchProgress:
    """批处理进度跟踪类"""
    
    def __init__(self, total_items: int):
        """
        初始化进度跟踪器
        
        Args:
            total_items: 总项目数
        """
        self.total_items = total_items
        self.processed_items = 0
        self.failed_items = 0
        self.start_time = time.time()
        self.end_time: Optional[float] = None
        self.is_completed = False
        self.is_paused = False
        self.current_batch = 0
        self.total_batches = 0
        self.last_update_time = time.time()
        self.error_messages: List[str] = []
    
    def update(self, processed: int = 1, failed: int = 0, error_msg: Optional[str] = None) -> None:
        """
        更新进度
        
        Args:
            processed: 新处理的项目数
            failed: 处理失败的项目数
            error_msg: 错误消息
        """
        self.processed_items += processed
        self.failed_items += failed
        self.last_update_time = time.time()
        
        if error_msg:
            self.error_messages.append(error_msg)
    
    def complete(self) -> None:
        """标记处理完成"""
        self.is_completed = True
        self.end_time = time.time()
    
    def pause(self) -> None:
        """暂停处理"""
        self.is_paused = True
    
    def resume(self) -> None:
        """恢复处理"""
        self.is_paused = False
    
    def get_progress(self) -> Dict:
        """
        获取进度信息
        
        Returns:
            进度信息字典
        """
        current_time = time.time()
        elapsed = current_time - self.start_time
        
        # 计算完成百分比
        percent_complete = (self.processed_items / self.total_items * 100) if self.total_items > 0 else 0
        
        # 计算处理速度(项/秒)
        if elapsed > 0:
            items_per_second = self.processed_items / elapsed
        else:
            items_per_second = 0
        
        # 估计剩余时间
        if items_per_second > 0 and not self.is_completed:
            remaining_items = self.total_items - self.processed_items
            estimated_seconds = remaining_items / items_per_second
        else:
            estimated_seconds = 0
        
        return {
            'total_items': self.total_items,
            'processed_items': self.processed_items,
            'failed_items': self.failed_items,
            'percent_complete': percent_complete,
            'elapsed_seconds': elapsed,
            'items_per_second': items_per_second,
            'estimated_seconds_remaining': estimated_seconds,
            'is_completed': self.is_completed,
            'is_paused': self.is_paused,
            'current_batch': self.current_batch,
            'total_batches': self.total_batches,
            'error_count': len(self.error_messages),
            'recent_errors': self.error_messages[-5:] if self.error_messages else []
        }


class BatchResult(Generic[R]):
    """批处理结果类"""
    
    def __init__(self):
        """初始化批处理结果"""
        self.results: List[R] = []
        self.errors: List[Tuple[Any, Exception]] = []
        self.start_time = time.time()
        self.end_time: Optional[float] = None
        self.total_time = 0.0
        self.batch_times: List[float] = []
    
    def add_result(self, result: R) -> None:
        """
        添加处理结果
        
        Args:
            result: 处理结果
        """
        self.results.append(result)
    
    def add_error(self, item: Any, error: Exception) -> None:
        """
        添加处理错误
        
        Args:
            item: 处理项
            error: 错误信息
        """
        self.errors.append((item, error))
    
    def add_batch_time(self, seconds: float) -> None:
        """
        添加批次处理时间
        
        Args:
            seconds: 处理时间(秒)
        """
        self.batch_times.append(seconds)
    
    def complete(self) -> None:
        """完成处理"""
        self.end_time = time.time()
        self.total_time = self.end_time - self.start_time
    
    def get_summary(self) -> Dict:
        """
        获取处理摘要
        
        Returns:
            摘要信息字典
        """
        return {
            'success_count': len(self.results),
            'error_count': len(self.errors),
            'total_count': len(self.results) + len(self.errors),
            'total_time': self.total_time,
            'average_batch_time': sum(self.batch_times) / len(self.batch_times) if self.batch_times else 0,
            'batch_count': len(self.batch_times)
        }


class BatchController(Generic[T, R]):
    """
    批处理控制器类
    
    提供数据分批处理功能，支持内存监控和自适应批次大小
    """
    
    def __init__(
        self,
        process_func: Callable[[List[T]], List[R]],
        batch_size: int = 100,
        max_workers: int = 1,
        memory_threshold_percent: float = 80.0,
        pause_threshold_percent: float = 90.0
    ):
        """
        初始化批处理控制器
        
        Args:
            process_func: 处理函数，接收数据列表，返回结果列表
            batch_size: 批次大小，默认100
            max_workers: 最大工作线程数，默认1
            memory_threshold_percent: 内存阈值百分比，超过此值将减小批次大小，默认80%
            pause_threshold_percent: 暂停阈值百分比，超过此值将暂停处理，默认90%
        """
        # 处理函数
        self._process_func = process_func
        
        # 配置参数
        self._batch_size = batch_size
        self._initial_batch_size = batch_size
        self._max_workers = max_workers
        self._memory_threshold = memory_threshold_percent
        self._pause_threshold = pause_threshold_percent
        
        # 进度跟踪
        self._progress: Optional[BatchProgress] = None
        
        # 处理状态
        self._is_processing = False
        self._should_stop = False
        self._is_paused = False
        
        # 工作线程
        self._workers: List[threading.Thread] = []
        
        # 任务队列
        self._task_queue: queue.Queue = queue.Queue()
        
        # 结果
        self._result = BatchResult[R]()
        
        # 保存点
        self._checkpoint_data: Dict = {}
        
        # 处理回调
        self._on_batch_complete: Optional[Callable[[int, List[R]], None]] = None
        self._on_all_complete: Optional[Callable[[BatchResult[R]], None]] = None
        self._on_error: Optional[Callable[[Any, Exception], None]] = None
        
        logger.debug(LogTarget.FILE, "批处理控制器初始化完成")
    
    def process_data(self, data: List[T]) -> BatchResult[R]:
        """
        处理数据
        
        Args:
            data: 要处理的数据列表
            
        Returns:
            处理结果
        """
        if self._is_processing:
            logger.warning(LogTarget.FILE, "已有处理任务在进行中")
            return self._result
        
        # 重置状态
        self._reset()
        
        # 初始化进度跟踪
        self._progress = BatchProgress(len(data))
        
        # 计算批次数
        total_batches = (len(data) + self._batch_size - 1) // self._batch_size
        self._progress.total_batches = total_batches
        
        logger.info(
            LogTarget.FILE,
            f"开始批处理 {len(data)} 个项目，分为 {total_batches} 个批次，"
            f"每批次 {self._batch_size} 个项目"
        )
        
        # 设置处理状态
        self._is_processing = True
        self._should_stop = False
        self._is_paused = False
        
        # 创建批次
        for i in range(0, len(data), self._batch_size):
            batch = data[i:i + self._batch_size]
            batch_num = i // self._batch_size + 1
            self._task_queue.put((batch_num, batch))
        
        # 启动工作线程
        worker_count = min(self._max_workers, total_batches)
        for i in range(worker_count):
            worker = threading.Thread(
                target=self._worker_thread,
                name=f"BatchWorker-{i+1}",
                daemon=True
            )
            self._workers.append(worker)
            worker.start()
        
        # 等待所有工作线程完成
        for worker in self._workers:
            worker.join()
        
        # 完成处理
        self._progress.complete()
        self._result.complete()
        self._is_processing = False
        
        # 调用完成回调
        if self._on_all_complete:
            try:
                self._on_all_complete(self._result)
            except Exception as e:
                logger.error(LogTarget.FILE, f"执行完成回调出错: {e}")
        
        # 记录处理结果
        summary = self._result.get_summary()
        logger.info(
            LogTarget.FILE,
            f"批处理完成: 成功 {summary['success_count']} 项，"
            f"失败 {summary['error_count']} 项，"
            f"总耗时 {summary['total_time']:.2f} 秒"
        )
        
        return self._result
    
    def process_iterator(self, data_iter: Iterator[T], total_items: Optional[int] = None) -> BatchResult[R]:
        """
        处理迭代器数据
        
        Args:
            data_iter: 数据迭代器
            total_items: 总项目数，如果未提供则无法计算进度百分比
            
        Returns:
            处理结果
        """
        if self._is_processing:
            logger.warning(LogTarget.FILE, "已有处理任务在进行中")
            return self._result
        
        # 重置状态
        self._reset()
        
        # 初始化进度跟踪
        self._progress = BatchProgress(total_items or -1)
        
        logger.info(
            LogTarget.FILE,
            f"开始批处理迭代器数据，批次大小 {self._batch_size}"
        )
        
        # 设置处理状态
        self._is_processing = True
        self._should_stop = False
        self._is_paused = False
        
        # 创建批次并处理
        batch: List[T] = []
        batch_num = 1
        
        for item in data_iter:
            if self._should_stop:
                break
            
            # 等待如果暂停
            while self._is_paused and not self._should_stop:
                time.sleep(0.1)
            
            batch.append(item)
            
            # 如果达到批次大小，处理批次
            if len(batch) >= self._batch_size:
                self._process_batch(batch_num, batch)
                batch = []
                batch_num += 1
                self._progress.current_batch = batch_num
        
        # 处理剩余项目
        if batch and not self._should_stop:
            self._process_batch(batch_num, batch)
        
        # 完成处理
        self._progress.complete()
        self._result.complete()
        self._is_processing = False
        
        # 调用完成回调
        if self._on_all_complete:
            try:
                self._on_all_complete(self._result)
            except Exception as e:
                logger.error(LogTarget.FILE, f"执行完成回调出错: {e}")
        
        # 记录处理结果
        summary = self._result.get_summary()
        logger.info(
            LogTarget.FILE,
            f"批处理完成: 成功 {summary['success_count']} 项，"
            f"失败 {summary['error_count']} 项，"
            f"总耗时 {summary['total_time']:.2f} 秒"
        )
        
        return self._result
    
    def _worker_thread(self) -> None:
        """工作线程函数"""
        while not self._should_stop:
            try:
                # 获取任务，设置超时以便检查停止标志
                try:
                    batch_num, batch = self._task_queue.get(timeout=0.1)
                except queue.Empty:
                    # 队列为空，任务完成
                    break
                
                # 等待如果暂停
                while self._is_paused and not self._should_stop:
                    time.sleep(0.1)
                
                if self._should_stop:
                    break
                
                # 处理批次
                self._process_batch(batch_num, batch)
                
                # 标记任务完成
                self._task_queue.task_done()
                
            except Exception as e:
                logger.error(LogTarget.FILE, f"工作线程出错: {e}")
    
    def _process_batch(self, batch_num: int, batch: List[T]) -> None:
        """
        处理单个批次
        
        Args:
            batch_num: 批次编号
            batch: 批次数据
        """
        # 检查内存使用
        self._check_memory_usage()
        
        # 如果需要停止或暂停，跳过处理
        if self._should_stop or self._is_paused:
            return
        
        # 更新进度
        if self._progress:
            self._progress.current_batch = batch_num
        
        # 记录开始时间
        start_time = time.time()
        
        try:
            # 调用处理函数
            results = self._process_func(batch)
            
            # 记录结果
            for result in results:
                self._result.add_result(result)
            
            # 更新进度
            if self._progress:
                self._progress.update(processed=len(batch))
            
            # 记录批次时间
            batch_time = time.time() - start_time
            self._result.add_batch_time(batch_time)
            
            # 调用批次完成回调
            if self._on_batch_complete:
                try:
                    self._on_batch_complete(batch_num, results)
                except Exception as e:
                    logger.error(LogTarget.FILE, f"执行批次完成回调出错: {e}")
            
            logger.debug(
                LogTarget.FILE,
                f"批次 {batch_num} 处理完成: {len(batch)} 项，耗时 {batch_time:.2f} 秒"
            )
            
        except Exception as e:
            # 记录错误
            for item in batch:
                self._result.add_error(item, e)
                
                # 调用错误回调
                if self._on_error:
                    try:
                        self._on_error(item, e)
                    except Exception as callback_error:
                        logger.error(
                            LogTarget.FILE,
                            f"执行错误回调出错: {callback_error}"
                        )
            
            # 更新进度
            if self._progress:
                self._progress.update(
                    failed=len(batch),
                    error_msg=f"批次 {batch_num} 处理出错: {str(e)}"
                )
            
            logger.error(
                LogTarget.FILE,
                f"批次 {batch_num} 处理出错: {e}"
            )
    
    def _check_memory_usage(self) -> None:
        """检查内存使用情况并调整批次大小"""
        try:
            # 获取当前内存使用率
            memory_percent = psutil.virtual_memory().percent
            
            # 如果超过暂停阈值，暂停处理
            if memory_percent > self._pause_threshold:
                if not self._is_paused:
                    self._is_paused = True
                    logger.warning(
                        LogTarget.FILE,
                        f"内存使用率 ({memory_percent}%) 超过暂停阈值 "
                        f"({self._pause_threshold}%)，暂停处理"
                    )
                    
                    # 触发垃圾回收
                    import gc
                    gc.collect()
                    
                    # 等待内存释放
                    time.sleep(1.0)
                    
                    # 重新检查内存使用率
                    memory_percent = psutil.virtual_memory().percent
                    if memory_percent < self._pause_threshold:
                        self._is_paused = False
                        logger.info(
                            LogTarget.FILE,
                            f"内存使用率降至 {memory_percent}%，恢复处理"
                        )
            
            # 如果超过内存阈值，减小批次大小
            elif memory_percent > self._memory_threshold:
                # 减小批次大小，但不小于初始大小的1/4
                new_batch_size = max(self._batch_size // 2, self._initial_batch_size // 4)
                
                if new_batch_size < self._batch_size:
                    self._batch_size = new_batch_size
                    logger.warning(
                        LogTarget.FILE,
                        f"内存使用率 ({memory_percent}%) 超过阈值 ({self._memory_threshold}%)，"
                        f"减小批次大小至 {self._batch_size}"
                    )
            
            # 如果内存使用率较低，可以适当增加批次大小
            elif memory_percent < self._memory_threshold * 0.7 and self._batch_size < self._initial_batch_size:
                # 增加批次大小，但不超过初始大小
                new_batch_size = min(self._batch_size * 2, self._initial_batch_size)
                
                if new_batch_size > self._batch_size:
                    self._batch_size = new_batch_size
                    logger.info(
                        LogTarget.FILE,
                        f"内存使用率 ({memory_percent}%) 较低，增加批次大小至 {self._batch_size}"
                    )
        
        except Exception as e:
            logger.error(LogTarget.FILE, f"检查内存使用出错: {e}")
    
    def _reset(self) -> None:
        """重置状态"""
        self._result = BatchResult[R]()
        self._progress = None
        self._workers = []
        self._task_queue = queue.Queue()
        self._is_processing = False
        self._should_stop = False
        self._is_paused = False
        self._batch_size = self._initial_batch_size
    
    def stop(self) -> None:
        """停止处理"""
        if self._is_processing:
            self._should_stop = True
            logger.info(LogTarget.FILE, "正在停止批处理...")
    
    def pause(self) -> None:
        """暂停处理"""
        if self._is_processing and not self._is_paused:
            self._is_paused = True
            if self._progress:
                self._progress.pause()
            logger.info(LogTarget.FILE, "批处理已暂停")
    
    def resume(self) -> None:
        """恢复处理"""
        if self._is_processing and self._is_paused:
            self._is_paused = False
            if self._progress:
                self._progress.resume()
            logger.info(LogTarget.FILE, "批处理已恢复")
    
    def get_progress(self) -> Optional[Dict]:
        """
        获取当前进度
        
        Returns:
            进度信息字典，如果未在处理则返回None
        """
        if not self._progress:
            return None
        
        return self._progress.get_progress()
    
    def set_batch_complete_callback(self, callback: Callable[[int, List[R]], None]) -> None:
        """
        设置批次完成回调
        
        Args:
            callback: 回调函数，接收批次编号和结果列表
        """
        self._on_batch_complete = callback
    
    def set_all_complete_callback(self, callback: Callable[[BatchResult[R]], None]) -> None:
        """
        设置全部完成回调
        
        Args:
            callback: 回调函数，接收处理结果
        """
        self._on_all_complete = callback
    
    def set_error_callback(self, callback: Callable[[Any, Exception], None]) -> None:
        """
        设置错误回调
        
        Args:
            callback: 回调函数，接收处理项和异常
        """
        self._on_error = callback
    
    def create_checkpoint(self) -> Dict:
        """
        创建处理检查点
        
        Returns:
            检查点数据
        """
        if not self._is_processing or not self._progress:
            return {}
        
        checkpoint = {
            'processed_items': self._progress.processed_items,
            'failed_items': self._progress.failed_items,
            'current_batch': self._progress.current_batch,
            'is_paused': self._is_paused,
            'batch_size': self._batch_size,
            'timestamp': time.time()
        }
        
        self._checkpoint_data = checkpoint
        logger.info(
            LogTarget.FILE,
            f"创建检查点: 已处理 {checkpoint['processed_items']} 项，"
            f"当前批次 {checkpoint['current_batch']}"
        )
        
        return checkpoint
    
    def restore_checkpoint(self, checkpoint: Dict) -> bool:
        """
        恢复处理检查点
        
        Args:
            checkpoint: 检查点数据
            
        Returns:
            是否成功恢复
        """
        if self._is_processing:
            logger.warning(LogTarget.FILE, "无法恢复检查点，处理任务正在进行中")
            return False
        
        try:
            # 恢复批次大小
            if 'batch_size' in checkpoint:
                self._batch_size = checkpoint['batch_size']
            
            # 记录检查点
            self._checkpoint_data = checkpoint
            
            logger.info(
                LogTarget.FILE,
                f"已恢复检查点: 已处理 {checkpoint.get('processed_items', 0)} 项，"
                f"当前批次 {checkpoint.get('current_batch', 0)}"
            )
            
            return True
            
        except Exception as e:
            logger.error(LogTarget.FILE, f"恢复检查点出错: {e}")
            return False
    
    def get_last_checkpoint(self) -> Dict:
        """
        获取最近的检查点
        
        Returns:
            检查点数据
        """
        return self._checkpoint_data
    
    def set_batch_size(self, size: int) -> None:
        """
        设置批次大小
        
        Args:
            size: 批次大小
        """
        if size < 1:
            logger.warning(LogTarget.FILE, f"批次大小无效: {size}，使用默认值 1")
            size = 1
        
        self._batch_size = size
        self._initial_batch_size = size
        logger.debug(LogTarget.FILE, f"批次大小已设置为: {size}")
    
    def set_max_workers(self, count: int) -> None:
        """
        设置最大工作线程数
        
        Args:
            count: 线程数
        """
        if count < 1:
            logger.warning(LogTarget.FILE, f"工作线程数无效: {count}，使用默认值 1")
            count = 1
        
        self._max_workers = count
        logger.debug(LogTarget.FILE, f"最大工作线程数已设置为: {count}")
    
    def set_memory_thresholds(self, threshold_percent: float, pause_percent: float) -> None:
        """
        设置内存阈值
        
        Args:
            threshold_percent: 内存阈值百分比，超过此值将减小批次大小
            pause_percent: 暂停阈值百分比，超过此值将暂停处理
        """
        if threshold_percent < 10 or threshold_percent > 95:
            logger.warning(
                LogTarget.FILE,
                f"内存阈值百分比无效: {threshold_percent}，使用默认值 80"
            )
            threshold_percent = 80.0
        
        if pause_percent < threshold_percent or pause_percent > 99:
            logger.warning(
                LogTarget.FILE,
                f"暂停阈值百分比无效: {pause_percent}，使用默认值 {threshold_percent + 10}"
            )
            pause_percent = min(threshold_percent + 10, 95)
        
        self._memory_threshold = threshold_percent
        self._pause_threshold = pause_percent
        logger.debug(
            LogTarget.FILE,
            f"内存阈值已设置为: {threshold_percent}%，暂停阈值: {pause_percent}%"
        )
    
    @property
    def is_processing(self) -> bool:
        """是否正在处理"""
        return self._is_processing
    
    @property
    def is_paused(self) -> bool:
        """是否已暂停"""
        return self._is_paused
    
    @property
    def batch_size(self) -> int:
        """当前批次大小"""
        return self._batch_size
    
    @property
    def max_workers(self) -> int:
        """最大工作线程数"""
        return self._max_workers 