#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
日志系统示例模块

提供日志系统的使用示例和测试
"""

import os
import sys
import logging

# 将项目根目录添加到Python路径
root_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, root_path)

# 导入日志系统
from utils.logger import (  # noqa: E402
    get_unified_logger, LogTarget, setup_unified_logging
)


# 注意: 旧的setup_unified_logging_example函数已移除，请直接使用setup_unified_logging


def example_usage():
    """
    增强型日志系统使用示例
    """
    # 设置统一的日志系统
    setup_unified_logging(default_target=LogTarget.FILE)
    
    # 获取增强型日志记录器
    logger = get_unified_logger("example_module", enhanced=True)
    
    # 使用LogTarget枚举记录日志 (推荐方式)
    logger.info(LogTarget.FILE, "这条信息只记录到文件")
    logger.info(LogTarget.CONSOLE, "这条信息只显示在控制台")
    logger.info(LogTarget.BOTH, "这条信息同时记录到文件和显示在控制台")
    
    # 使用前置内容功能 (新功能)
    logger.info(LogTarget.FILE, "========================================", 
                "这条信息前面会有分隔线")
    logger.info(LogTarget.FILE, "\n\n\n", "这条信息前面会有多个换行符")
    logger.info(LogTarget.FILE, "===================数据下载===================", 
                "这是数据下载部分的日志")
    logger.info(LogTarget.BOTH, "\n========================================\n", 
                "这条信息在文件和控制台都会显示，并带有前置内容")
    
    # 简化语法 (不推荐，已弃用)
    # 以下代码仅为兼容旧代码，新代码请使用LogTarget枚举
    logger.info(1, "只记录到文件的信息")  # 不推荐，建议使用LogTarget.FILE
    logger.info(2, "只显示在控制台的信息")  # 不推荐，建议使用LogTarget.CONSOLE
    logger.info(3, "同时记录到文件和显示在控制台的信息")  # 不推荐，建议使用LogTarget.BOTH
    
    # 使用标准日志级别方法
    logger.debug(LogTarget.FILE, "这条调试信息只记录到文件")
    logger.warning(LogTarget.CONSOLE, "这条警告只显示在控制台")
    logger.error(LogTarget.BOTH, "这条错误同时记录到文件和显示在控制台")
    
    # 兼容旧API - 使用默认目标（只输出到文件）
    logger.debug("这条调试信息使用默认目标（只输出到文件）")
    logger.info("这条信息使用默认目标（只输出到文件）")
    logger.warning("这条警告使用默认目标（只输出到文件）")
    logger.error("这条错误使用默认目标（只输出到文件）")
    logger.critical("这条严重错误使用默认目标（只输出到文件）")


def test_logging_system():
    """
    测试日志配置系统

    此函数用于测试新的日志配置系统是否正常工作，
    会输出一些测试日志到不同的日志记录器，以验证配置有效性。
    """
    print("\n===== 测试日志系统 =====")
    
    # 初始化日志系统
    setup_unified_logging(
        default_target=LogTarget.FILE,
        use_table_formatter=True,  # 使用表格式Markdown格式化器
        module_width=30,  # 设置模块名显示宽度为30
        print_init_message=False  # 不打印初始化消息
    )

    # 获取各种日志记录器
    main_logger = get_unified_logger("data_main", enhanced=True)
    src_logger = get_unified_logger("data_source_manager", enhanced=True)
    cmd_logger = get_unified_logger("data_commands", enhanced=True)

    # 输出一些测试日志
    main_logger.info(LogTarget.FILE, "这是data_main的测试日志 - 只输出到文件")
    src_logger.info(LogTarget.CONSOLE, "这是data_source_manager的测试日志 - 只输出到控制台")
    cmd_logger.info(LogTarget.BOTH, "这是data_commands的测试日志 - 同时输出到文件和控制台")
    
    # 测试不同目标的日志
    main_logger.info(LogTarget.FILE, "这条日志只会记录到文件中")
    main_logger.info(LogTarget.CONSOLE, "这条日志只会显示在控制台")
    main_logger.info(LogTarget.BOTH, "这条日志同时记录到文件和显示在控制台")
    
    # 使用数字代码简化写法 (不推荐，保留仅为示例)
    main_logger.info(1, "使用数字代码1 - 只记录到文件 (不推荐)")
    main_logger.info(2, "使用数字代码2 - 只显示在控制台 (不推荐)")
    main_logger.info(3, "使用数字代码3 - 同时记录到文件和显示在控制台 (不推荐)")

    # 直接使用logging也应该输出到控制台
    logging.info("这是根日志记录器的测试消息")

    # 显示成功
    print("\n日志系统测试完成，请检查logs目录下的日志文件以及控制台输出")
    print("如果控制台只显示根日志器的消息和控制台目标日志，没有重复，则配置正确")


def test_enhanced_formatter():
    """
    测试增强型格式化器的功能
    
    这个示例展示了增强型格式化器的各种功能，包括：
    1. 颜色标识
    2. 层次缩进
    3. 操作分组
    4. 前置内容
    """
    # 清除之前的配置
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)
    
    print("\n" + "="*50)
    print("测试增强型格式化器 - 注意观察颜色和缩进效果")
    print("="*50 + "\n")
    
    # 使用增强型格式化器初始化日志系统
    setup_unified_logging(
        default_target=LogTarget.CONSOLE,  # 输出到控制台
        use_table_formatter=False,       # 不使用表格式Markdown格式化器
        use_colors=True,                 # 使用颜色
        use_icons=True,                  # 使用图标
        print_init_message=False           # 不打印初始化消息
    )
    
    # 获取不同模块的日志记录器
    main_logger = get_unified_logger("main_module")
    data_logger = get_unified_logger("data_source_manager")
    storage_logger = get_unified_logger("data.storage.parquet_storage")
    
    # 记录一系列日志，展示增强型格式化器的功能
    main_logger.info("应用程序启动")
    
    # 操作1：数据源管理器初始化
    data_logger.info(
        LogTarget.CONSOLE, 
        "=================================", 
        "开始初始化数据源管理器"
    )
    data_logger.info("配置数据源")  # 这里应该有缩进
    data_logger.info("注册数据提供者")  # 这里应该有缩进
    data_logger.debug("加载数据源配置文件")  # 这里应该有缩进和蓝色
    data_logger.info(
        LogTarget.CONSOLE, 
        "=================================", 
        "数据源管理器初始化完成"
    )
    
    # 操作2：下载数据
    data_logger.info(
        LogTarget.CONSOLE, 
        "=================================", 
        "开始下载股票数据"
    )
    data_logger.info("准备下载列表")  # 这里应该有缩进
    data_logger.info("下载 000001.SZ 的数据")  # 这里应该有缩进
    
    # 子操作：存储数据
    storage_logger.info(
        LogTarget.CONSOLE, 
        "---------------------------------", 
        "开始保存 000001.SZ 的数据"
    )
    storage_logger.debug("创建目录结构")  # 这里应该有缩进和蓝色
    storage_logger.info("写入数据文件")  # 这里应该有缩进
    storage_logger.info("更新索引")  # 这里应该有缩进
    storage_logger.info(
        LogTarget.CONSOLE, 
        "---------------------------------", 
        "数据保存完成"
    )
    
    # 继续主操作
    data_logger.info("下载 600000.SH 的数据")  # 这里应该有缩进
    data_logger.warning("600000.SH 数据不完整，使用部分数据")  # 这里应该有缩进和黄色
    data_logger.info(
        LogTarget.CONSOLE, 
        "=================================", 
        "数据下载完成"
    )
    
    # 错误示例
    try:
        raise ValueError("数据格式错误")
    except Exception as e:
        data_logger.error(f"处理数据时出错: {e}")  # 这里应该有红色
    
    main_logger.info("应用程序退出")
    
    print("\n" + "="*50)
    print("增强型格式化器测试结束")
    print("="*50 + "\n")


def test_prefix_content():
    """
    测试日志前置内容功能
    
    此函数展示了如何使用日志前置内容功能，这是添加分隔线和视觉组织的推荐方法。
    前置内容功能替代了旧的自动分隔线功能，提供更灵活的控制：
    1. 分隔线
    2. 标题分隔线
    3. 换行符
    4. 混合内容
    """
    # 清除之前的配置
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)
    
    print("\n" + "="*50)
    print("测试日志前置内容功能 - 推荐的日志分隔方式")
    print("="*50 + "\n")
    
    # 初始化日志系统
    setup_unified_logging(
        default_target=LogTarget.CONSOLE,  # 输出到控制台
        use_table_formatter=False,       # 不使用表格式Markdown格式化器
        print_init_message=False           # 不打印初始化消息
    )
    
    # 获取不同模块的日志记录器
    main_logger = get_unified_logger("main_module")
    data_logger = get_unified_logger("data_source_manager")
    storage_logger = get_unified_logger("data.storage.parquet_storage")
    
    # 创建常用的前置内容模式
    prefix_separator = "\n" * 3
    prefix_thin_separator = "-" * 80 + "\n"
    prefix_blank_lines = "\n" * 1
    
    # 创建带标题的分隔线函数
    def title_separator(title):
        line = "=" * 80
        start_pos = (len(line) - len(title)) // 2 - 1
        if start_pos < 0:
            start_pos = 0
        return line[:start_pos] + " " + title + " " + line[start_pos + len(title) + 2:]
    
    # 记录一系列日志，展示前置内容功能
    main_logger.info("应用程序启动")
    
    # 使用简单分隔线
    main_logger.info(LogTarget.CONSOLE, prefix_separator, "开始数据处理流程")
    
    # 使用带标题的分隔线
    data_logger.info(
        LogTarget.CONSOLE, 
        "\n" + title_separator("数据下载") + "\n", 
        "初始化数据源管理器"
    )
    data_logger.info("配置数据源")
    data_logger.info("下载 000001.SZ 的数据")
    data_logger.info("下载 600000.SH 的数据")
    
    # 使用换行符创建空白区域
    data_logger.info(LogTarget.CONSOLE, prefix_blank_lines, "数据下载完成")
    
    # 使用细分隔线
    storage_logger.info(LogTarget.CONSOLE, prefix_thin_separator, "开始保存数据")
    storage_logger.info("保存 000001.SZ 的数据")
    storage_logger.info("保存 600000.SH 的数据")
    storage_logger.info("数据保存完成")
    
    # 使用混合内容
    main_logger.info(
        LogTarget.CONSOLE, 
        f"\n{' 处理完成 ':=^80}\n", 
        "所有数据处理已完成"
    )
    
    print("\n" + "="*50)
    print("日志前置内容功能测试结束")
    print("="*50 + "\n")


def test_title_content():
    """测试标题和正文分离功能"""
    print("\n===== 测试标题和正文分离功能 =====")
    
    # 初始化日志系统
    setup_unified_logging(
        default_target=LogTarget.FILE,
        use_table_formatter=True,  # 使用表格式Markdown格式化器
        module_width=30,  # 设置模块名显示宽度为30
        print_init_message=False  # 不打印初始化消息
    )


def demonstrate_title_content_usage():
    """
    演示使用title和content参数
    """
    print("\n=== 演示使用title和content参数 ===")
    
    logger = get_unified_logger("title_demo", enhanced=True)
    
    # 示例1: 标题和内容都显示在控制台和文件中
    logger.info(
        title_target=LogTarget.BOTH, 
        title="【当前进度：标准化需要下载的股票代码】", 
        content_target=LogTarget.BOTH, 
        content="详细的数据内容...\n数据行1\n数据行2\n..."
    )
    
    # 示例2: 标题显示在控制台和文件中，但内容只显示在文件中
    logger.info(
        title_target=LogTarget.BOTH, 
        title="【当前进度：获取本地数据的最新时间点】", 
        content_target=LogTarget.FILE, 
        content="这些详细内容只会写入到日志文件中"
    )
    
    # 示例3: 只有标题，没有内容
    logger.info(
        title_target=LogTarget.BOTH, 
        title="【当前进度：解析最近数据日期】"
    )
    
    # 示例4: 使用警告级别
    logger.warning(
        title_target=LogTarget.BOTH, 
        title="【警告】数据可能不完整", 
        content_target=LogTarget.BOTH, 
        content="在处理过程中发现部分数据缺失"
    )
    
    # 示例5: 使用错误级别
    logger.error(
        title_target=LogTarget.BOTH, 
        title="【错误】下载失败", 
        content_target=LogTarget.BOTH, 
        content="无法连接到数据服务器"
    )
    
    print("在日志文件中查看详细输出")


if __name__ == "__main__":
    # 运行已实现的示例函数
    demonstrate_title_content_usage()
    
    # 默认只运行增强型格式化器测试
    # test_enhanced_formatter()
    
    # 如果需要测试基本日志功能，取消下面的注释
    # print("\n=== 测试基本日志功能 ===")
    # test_logging_system()
    
    # 如果需要测试前置内容功能，取消下面的注释
    print("\n=== 测试前置内容功能 ===")
    test_prefix_content() 