#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据提供者模块初始化文件

提供不同数据源的具体实现
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

# 导入XtQuant数据提供者
from data.source.providers.fetch_xtquant_data import XtQuantFetcher  # noqa: E402

# 版本信息
__version__ = "0.1.0"

# 导出接口
__all__ = [
    "XtQuantFetcher",  # 迅投数据提供者类
    "__version__",     # 版本信息
] 