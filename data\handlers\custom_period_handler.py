#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
自定义周期处理器模块

专门负责自定义周期数据的合成和处理逻辑
"""

import os
import pandas as pd
from datetime import datetime
from typing import Dict, List, Optional, Any

from config.settings import DATA_ROOT
from utils.logger import get_unified_logger, LogTarget
from utils.data_processor.period_handler import (
    get_synthesis_strategy, 
    synthesize_period_data,
    is_custom_period
)
from data.storage.path_manager import get_save_path
from utils.path_utils.file_operations import ensure_dir_exists
from .data_processor import DataProcessor
from .download_handler import DownloadHandler

logger = get_unified_logger(__name__, enhanced=True)


class CustomPeriodHandler:
    """自定义周期处理器"""
    
    def __init__(self, data_root: str = None, download_handler: DownloadHandler = None):
        """
        初始化自定义周期处理器
        
        Args:
            data_root: 数据根目录
            download_handler: 下载处理器实例
        """
        self.data_root = data_root or DATA_ROOT
        self.download_handler = download_handler
        self.data_processor = DataProcessor()
        
    def handle_custom_period_download(self,
                                    stock_list: List[str],
                                    period: str,
                                    start_time: str,
                                    end_time: str,
                                    incremental: bool = False,
                                    overlap_days: int = 1,
                                    force_update: bool = False,
                                    validate_data: bool = True,
                                    display_head_rows: int = 5,
                                    display_tail_rows: int = 5,
                                    show_data: bool = True,
                                    real_time_save: bool = True,
                                    result_file: Optional[str] = None,
                                    **kwargs) -> Dict[str, Any]:
        """
        处理自定义周期下载请求
        
        Args:
            stock_list: 股票代码列表
            period: 自定义周期
            start_time: 开始时间
            end_time: 结束时间
            incremental: 是否增量更新
            overlap_days: 重叠天数
            force_update: 是否强制更新
            validate_data: 是否验证数据
            display_head_rows: 显示头部行数
            display_tail_rows: 显示尾部行数
            show_data: 是否显示数据
            real_time_save: 是否实时保存
            result_file: 结果文件路径
            **kwargs: 其他参数
            
        Returns:
            包含处理结果的字典
        """
        logger.info(f"处理自定义周期 {period}: {len(stock_list)} 个股票")
        
        # 验证是否为自定义周期
        if not is_custom_period(period):
            logger.error(f"周期 {period} 不是自定义周期")
            return {"success": False, "message": f"周期 {period} 不是自定义周期"}
            
        # 初始化结果
        result = self._initialize_result()
        total_stocks = len(stock_list)
        
        # 检查下载处理器
        if not self.download_handler:
            logger.error("下载处理器未初始化")
            return {"success": False, "message": "下载处理器未初始化"}
            
        # 处理每个股票
        for i, symbol in enumerate(stock_list):
            logger.info(f"[{i+1}/{total_stocks}] 处理自定义周期: {symbol}")
            
            try:
                success = self._process_single_stock_custom_period(
                    symbol, period, start_time, end_time,
                    incremental, overlap_days, force_update, 
                    validate_data, result, **kwargs
                )
                
                if success:
                    logger.info(f"{symbol} 自定义周期处理成功")
                else:
                    logger.warning(f"{symbol} 自定义周期处理失败")
                    
            except Exception as e:
                logger.error(f"处理 {symbol} 自定义周期时出错: {e}")
                self._handle_error(symbol, str(e), result)
                
        # 汇总结果
        return self._finalize_result(result, total_stocks)
    
    def _initialize_result(self) -> Dict[str, Any]:
        """初始化结果字典"""
        return {
            "success": False,
            "successful_symbols": [],
            "failed_symbols": [],
            "data": {},
            "save_paths": {},
            "failed_reasons": {},
            "base_data_info": {},      # 基础数据信息
            "synthesized_data_info": {}  # 合成数据信息
        }
    
    def _process_single_stock_custom_period(self,
                                          symbol: str,
                                          period: str,
                                          start_time: str,
                                          end_time: str,
                                          incremental: bool,
                                          overlap_days: int,
                                          force_update: bool,
                                          validate_data: bool,
                                          result: Dict[str, Any],
                                          **kwargs) -> bool:
        """
        处理单个股票的自定义周期数据
        
        Args:
            symbol: 股票代码
            period: 自定义周期
            start_time: 开始时间
            end_time: 结束时间
            incremental: 是否增量更新
            overlap_days: 重叠天数
            force_update: 是否强制更新
            validate_data: 是否验证数据
            result: 结果字典
            **kwargs: 其他参数
            
        Returns:
            是否处理成功
        """
        try:
            # 获取合成策略
            strategy = get_synthesis_strategy(
                period=period,
                symbol=symbol,
                data_root=self.data_root,
                start_time=start_time,
                end_time=end_time
            )
            
            base_period = strategy["base_period"]
            
            # 判断是否需要下载基础数据
            if strategy["need_download"] or not strategy["local_data_sufficient"]:
                logger.info(f"{symbol} 需要下载 {base_period} 数据")
                
                # 下载基础周期数据
                base_data = self._download_base_period_data(
                    symbol, base_period, start_time, end_time,
                    incremental, overlap_days, force_update, validate_data, **kwargs
                )
                
                if base_data is None or base_data.empty:
                    self._handle_error(symbol, "基础周期数据下载失败", result)
                    return False
                    
            else:
                logger.info(f"{symbol} 使用本地 {base_period} 数据")
                
                # 读取本地基础数据
                base_data = pd.read_parquet(strategy["local_data_path"])
                
                if base_data.empty:
                    self._handle_error(symbol, "本地基础数据为空", result)
                    return False
                    
            # 合成自定义周期数据
            synthesized_data = self._synthesize_custom_period_data(
                base_data, period, symbol
            )
            
            if synthesized_data is None or synthesized_data.empty:
                self._handle_error(symbol, "周期合成失败", result)
                return False
                
            # 保存合成数据
            save_success = self._save_synthesized_data(
                symbol, period, synthesized_data, end_time
            )
            
            if not save_success:
                self._handle_error(symbol, "合成数据保存失败", result)
                return False
                
            # 更新结果
            self._update_success_result(
                symbol, base_period, period, base_data, synthesized_data, result
            )
            
            return True
            
        except Exception as e:
            logger.error(f"处理 {symbol} 自定义周期时出错: {e}")
            self._handle_error(symbol, f"处理异常: {str(e)}", result)
            return False
    
    def _download_base_period_data(self,
                                  symbol: str,
                                  base_period: str,
                                  start_time: str,
                                  end_time: str,
                                  incremental: bool,
                                  overlap_days: int,
                                  force_update: bool,
                                  validate_data: bool,
                                  **kwargs) -> Optional[pd.DataFrame]:
        """下载基础周期数据"""
        try:
            # 使用下载处理器下载基础数据
            base_result = self.download_handler.download_history_data(
                stock_list=[symbol],
                period=base_period,
                start_time=start_time,
                end_time=end_time,
                incremental=incremental,
                overlap_days=overlap_days,
                force_update=force_update,
                validate_data=validate_data,
                display_head_rows=0,  # 不显示数据
                display_tail_rows=0,
                show_data=False,
                **{k: v for k, v in kwargs.items() 
                   if k not in ['handle_custom_period']}  # 避免递归
            )
            
            # 检查下载结果
            if (base_result.get("success") and 
                symbol in base_result.get("data", {})):
                
                base_data = base_result["data"][symbol]
                logger.info(f"{symbol} 基础数据下载成功: {len(base_data)} 行")
                return base_data
            else:
                logger.error(f"{symbol} 基础数据下载失败")
                return None
                
        except Exception as e:
            logger.error(f"下载 {symbol} 基础数据时出错: {e}")
            return None
    
    def _synthesize_custom_period_data(self,
                                     base_data: pd.DataFrame,
                                     target_period: str,
                                     symbol: str) -> Optional[pd.DataFrame]:
        """合成自定义周期数据"""
        try:
            logger.info(f"{symbol} 开始合成 {target_period} 周期数据")
            
            # 调用周期合成函数
            synthesized_data = synthesize_period_data(base_data, target_period)
            
            if synthesized_data is not None and not synthesized_data.empty:
                logger.info(f"{symbol} 周期合成成功: {len(base_data)} -> {len(synthesized_data)} 行")
                return synthesized_data
            else:
                logger.error(f"{symbol} 周期合成返回空数据")
                return None
                
        except Exception as e:
            logger.error(f"{symbol} 周期合成失败: {e}")
            return None
    
    def _save_synthesized_data(self,
                              symbol: str,
                              period: str,
                              synthesized_data: pd.DataFrame,
                              end_time: str) -> bool:
        """保存合成数据"""
        try:
            # 提取时间戳用于路径生成
            timestamp = self.data_processor.extract_timestamp_from_data(
                synthesized_data, end_time
            )
            
            # 获取保存路径
            save_path = get_save_path(self.data_root, symbol, period, timestamp)
            ensure_dir_exists(os.path.dirname(save_path))
            
            # 使用ParquetStorage保存
            from data.storage.parquet_storage import ParquetStorage
            storage = ParquetStorage(base_dir=self.data_root)
            
            success = storage.save_data_by_partition_parallel(
                dataframe=synthesized_data,
                symbol=symbol,
                period=period
            )
            
            if success:
                logger.info(f"{symbol} 合成数据保存成功")
                return True
            else:
                logger.error(f"{symbol} 合成数据保存失败")
                return False
                
        except Exception as e:
            logger.error(f"保存 {symbol} 合成数据时出错: {e}")
            return False
    
    def _update_success_result(self,
                              symbol: str,
                              base_period: str,
                              target_period: str,
                              base_data: pd.DataFrame,
                              synthesized_data: pd.DataFrame,
                              result: Dict[str, Any]) -> None:
        """更新成功结果"""
        result["successful_symbols"].append(symbol)
        result["data"][symbol] = synthesized_data
        
        # 记录基础数据和合成数据信息
        result["base_data_info"][symbol] = {
            "period": base_period,
            "rows": len(base_data)
        }
        
        result["synthesized_data_info"][symbol] = {
            "period": target_period,
            "rows": len(synthesized_data)
        }
        
        logger.info(
            f"{symbol} 自定义周期处理完成: "
            f"{len(base_data)}行{base_period} -> {len(synthesized_data)}行{target_period}"
        )
    
    def _handle_error(self, symbol: str, reason: str, result: Dict[str, Any]) -> None:
        """处理错误"""
        if symbol not in result["failed_symbols"]:
            result["failed_symbols"].append(symbol)
        result["failed_reasons"][symbol] = reason
        logger.error(f"{symbol} 自定义周期处理失败: {reason}")
    
    def _finalize_result(self, result: Dict[str, Any], total_stocks: int) -> Dict[str, Any]:
        """完成结果汇总"""
        success_count = len(result["successful_symbols"])
        result["success"] = success_count > 0
        
        if success_count > 0:
            success_rate = success_count / total_stocks * 100
            logger.info(f"自定义周期处理完成: 成功 {success_count}/{total_stocks} ({success_rate:.1f}%)")
        else:
            logger.error("所有股票自定义周期处理失败")
            
        return result