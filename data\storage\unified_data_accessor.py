#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
统一数据访问器

基于VectorizedDataReader创建统一的数据访问接口，替代现有缓存系统。
支持所有数据类型：股票原始数据、复权数据、期货数据。

核心特性：
1. 统一的数据访问接口
2. 自动数据类型识别
3. 高效的内置缓存机制
4. 简洁的API设计
5. 向后兼容性

遵循核心指导思维：
- 删除重复实现，统一架构
- 避免复杂度，保持简洁
- 不使用后备方案，直接解决问题
"""

import pandas as pd
from typing import Optional, Dict, Any, List
from pathlib import Path
import re

from config.settings import DATA_ROOT
from data.storage.vectorized_reader import VectorizedDataReader
from utils.logger.manager import get_unified_logger

logger = get_unified_logger(__name__)


class UnifiedDataAccessor:
    """统一数据访问器
    
    基于VectorizedDataReader的统一数据访问接口，支持：
    - 股票原始数据和复权数据
    - 期货原始合约和连续合约数据
    - 自动数据类型识别
    - 高效的内置缓存机制
    """
    
    def __init__(self, enable_cache: bool = True, cache_size: int = 100):
        """初始化统一数据访问器
        
        Args:
            enable_cache: 是否启用缓存
            cache_size: 缓存大小限制
        """
        self.vectorized_reader = VectorizedDataReader(
            enable_cache=enable_cache, 
            cache_size=cache_size
        )
        self.data_root = DATA_ROOT
        
        logger.info("统一数据访问器初始化完成")
    
    def get_data(self, 
                 symbol: str,
                 period: str = "1d",
                 start_time: Optional[str] = None,
                 end_time: Optional[str] = None,
                 columns: Optional[List[str]] = None,
                 data_type: str = "raw",
                 adj_type: Optional[str] = None) -> Optional[pd.DataFrame]:
        """获取数据（主要接口）
        
        Args:
            symbol: 股票/期货代码，如"000001.SZ"、"IF00.IF"
            period: 数据周期，如"1d"、"1m"、"tick"
            start_time: 开始时间，如"20240101"
            end_time: 结束时间，如"20241231"
            columns: 要读取的列名列表
            data_type: 数据类型，"raw"（原始数据）或"adjusted"（复权数据）
            adj_type: 复权类型，"front"（前复权）或"back"（后复权），仅当data_type="adjusted"时有效
            
        Returns:
            pd.DataFrame: 读取的数据，如果失败返回None
        """
        try:
            logger.debug(f"统一数据访问器获取数据: {symbol} {period} {data_type} {adj_type}")
            
            # 自动识别数据类型和复权类型
            if data_type == "adjusted" and adj_type is None:
                logger.warning(f"复权数据类型未指定adj_type，默认使用front: {symbol}")
                adj_type = "front"
            
            # 转换为VectorizedDataReader的dividend_type参数
            dividend_type = self._convert_to_dividend_type(data_type, adj_type)
            
            # 使用VectorizedDataReader读取数据
            df = self.vectorized_reader.read_partitioned_data_vectorized(
                data_root=self.data_root,
                symbol=symbol,
                period=period,
                start_time=start_time,
                end_time=end_time,
                columns=columns,
                dividend_type=dividend_type
            )
            
            if df is not None and not df.empty:
                logger.debug(f"统一数据访问器成功获取数据: {symbol} {len(df)} 行")
            else:
                logger.warning(f"统一数据访问器未获取到数据: {symbol} {period}")
            
            return df
            
        except Exception as e:
            logger.error(f"统一数据访问器获取数据失败: {symbol} {period} - {e}")
            return None
    
    def get_stock_data(self,
                      symbol: str,
                      period: str = "1d",
                      start_time: Optional[str] = None,
                      end_time: Optional[str] = None,
                      columns: Optional[List[str]] = None,
                      dividend_type: str = "none") -> Optional[pd.DataFrame]:
        """获取股票数据（便捷接口）
        
        Args:
            symbol: 股票代码，如"000001.SZ"
            period: 数据周期
            start_time: 开始时间
            end_time: 结束时间
            columns: 要读取的列
            dividend_type: 复权类型，"none"（原始）、"front"（前复权）、"back"（后复权）
            
        Returns:
            pd.DataFrame: 股票数据
        """
        # 验证是否为股票代码
        if not self._is_stock_symbol(symbol):
            logger.warning(f"非股票代码，建议使用get_data接口: {symbol}")
        
        # 转换为统一接口参数
        data_type, adj_type = self._convert_from_dividend_type(dividend_type)
        
        return self.get_data(
            symbol=symbol,
            period=period,
            start_time=start_time,
            end_time=end_time,
            columns=columns,
            data_type=data_type,
            adj_type=adj_type
        )
    
    def get_futures_data(self,
                        symbol: str,
                        period: str = "1d",
                        start_time: Optional[str] = None,
                        end_time: Optional[str] = None,
                        columns: Optional[List[str]] = None) -> Optional[pd.DataFrame]:
        """获取期货数据（便捷接口）
        
        Args:
            symbol: 期货代码，如"IF00.IF"、"rb2403.SF"
            period: 数据周期
            start_time: 开始时间
            end_time: 结束时间
            columns: 要读取的列
            
        Returns:
            pd.DataFrame: 期货数据
        """
        # 验证是否为期货代码
        if not self._is_futures_symbol(symbol):
            logger.warning(f"非期货代码，建议使用get_data接口: {symbol}")
        
        return self.get_data(
            symbol=symbol,
            period=period,
            start_time=start_time,
            end_time=end_time,
            columns=columns,
            data_type="raw",
            adj_type=None
        )
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return self.vectorized_reader.get_performance_stats()
    
    def clear_cache(self):
        """清理缓存"""
        if hasattr(self.vectorized_reader, '_cache') and self.vectorized_reader._cache:
            self.vectorized_reader._cache.clear()
            logger.info("统一数据访问器缓存已清理")
    
    def _convert_to_dividend_type(self, data_type: str, adj_type: Optional[str]) -> str:
        """转换为VectorizedDataReader的dividend_type参数"""
        if data_type == "raw":
            return "none"
        elif data_type == "adjusted":
            if adj_type == "front":
                return "front"
            elif adj_type == "back":
                return "back"
            else:
                logger.warning(f"未知的复权类型: {adj_type}，默认使用front")
                return "front"
        else:
            logger.warning(f"未知的数据类型: {data_type}，默认使用none")
            return "none"
    
    def _convert_from_dividend_type(self, dividend_type: str) -> tuple:
        """从dividend_type转换为data_type和adj_type"""
        if dividend_type == "none":
            return "raw", None
        elif dividend_type == "front":
            return "adjusted", "front"
        elif dividend_type == "back":
            return "adjusted", "back"
        else:
            logger.warning(f"未知的dividend_type: {dividend_type}，默认使用raw")
            return "raw", None
    
    def _is_stock_symbol(self, symbol: str) -> bool:
        """判断是否为股票代码"""
        # 股票代码格式：6位数字.SH/SZ
        pattern = r'^\d{6}\.(SH|SZ)$'
        return bool(re.match(pattern, symbol))
    
    def _is_futures_symbol(self, symbol: str) -> bool:
        """判断是否为期货代码"""
        # 期货代码格式：字母+数字.交易所代码
        pattern = r'^[a-zA-Z]+\d*\.(IF|SF|DF|ZF|INE)$'
        return bool(re.match(pattern, symbol))


# 创建全局实例
unified_data_accessor = UnifiedDataAccessor()

# 便捷函数
def get_data(symbol: str, period: str = "1d", **kwargs) -> Optional[pd.DataFrame]:
    """便捷函数：获取数据"""
    return unified_data_accessor.get_data(symbol, period, **kwargs)

def get_stock_data(symbol: str, period: str = "1d", **kwargs) -> Optional[pd.DataFrame]:
    """便捷函数：获取股票数据"""
    return unified_data_accessor.get_stock_data(symbol, period, **kwargs)

def get_futures_data(symbol: str, period: str = "1d", **kwargs) -> Optional[pd.DataFrame]:
    """便捷函数：获取期货数据"""
    return unified_data_accessor.get_futures_data(symbol, period, **kwargs)


__all__ = [
    'UnifiedDataAccessor',
    'unified_data_accessor',
    'get_data',
    'get_stock_data', 
    'get_futures_data'
]
