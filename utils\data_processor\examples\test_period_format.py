#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试周期转换函数的时间格式保持功能

验证修改后的周期转换函数是否能正确保持原始数据的时间格式。
特别关注:
1. time列是否为毫秒时间戳格式
2. 索引是否为YYYYMMDDHHMMSS格式的字符串
"""

from utils.data_processor.period_converter import convert_kline_period
import os
import sys
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union, Any
from datetime import datetime, timedelta

# 将项目根目录添加到Python路径
root_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.insert(0, root_path)

# 导入日志模块
from utils.logger import get_unified_logger

# 设置日志记录器
logger = get_unified_logger("test_period_format")

# 导入周期转换函数


def create_test_data(start_time=None, periods=100, freq='1min'):
    """
    创建模拟测试数据，格式与实际数据一致

    Args:
        start_time: 开始时间，如果为None则使用当前时间
        periods: 数据点数量
        freq: 数据频率

    Returns:
        pd.DataFrame: 模拟数据
    """
    if start_time is None:
        start_time = datetime.now().replace(hour=9, minute=30, second=0, microsecond=0)

    # 创建时间索引
    date_range = pd.date_range(start=start_time, periods=periods, freq=freq)

    # 生成随机价格数据
    base_price = 100.0
    price_data = np.random.normal(0, 1, periods).cumsum() + base_price

    # 创建DataFrame
    df = pd.DataFrame({
        'open': price_data,
        'high': price_data + np.random.rand(periods),
        'low': price_data - np.random.rand(periods),
        'close': price_data + np.random.normal(0, 0.5, periods),
        'volume': np.random.randint(1000, 10000, periods),
    }, index=date_range)

    # 添加毫秒时间戳格式的time列
    df['time'] = df.index.astype(np.int64) // 10**6

    # 将索引转换为YYYYMMDDHHMMSS格式的字符串
    df.index = df.index.strftime('%Y%m%d%H%M%S')

    return df


def test_period_conversion():
    """测试周期转换功能"""
    # 创建模拟1分钟数据
    start_time = datetime.now().replace(hour=9, minute=30, second=0, microsecond=0)
    df_1m = create_test_data(start_time, periods=120, freq='1min')

    # 打印原始数据信息
    logger.info("原始数据信息:")
    logger.info(f"数据形状: {df_1m.shape}")
    logger.info(f"索引类型: {type(df_1m.index)}")
    logger.info(f"索引示例: {df_1m.index[:5].tolist()}")
    logger.info(f"时间列类型: {type(df_1m['time'].iloc[0])}")
    logger.info(f"时间列示例: {df_1m['time'].iloc[:5].tolist()}")
    logger.info("\n")

    # 转换为3分钟数据
    logger.info("转换为3分钟数据...")
    df_3m = convert_kline_period(df_1m, '3m')

    # 打印转换后的数据信息
    logger.info("转换后的3分钟数据信息:")
    logger.info(f"数据形状: {df_3m.shape}")
    logger.info(f"索引类型: {type(df_3m.index)}")
    logger.info(f"索引示例: {df_3m.index[:5].tolist()}")
    logger.info(f"时间列类型: {type(df_3m['time'].iloc[0])}")
    logger.info(f"时间列示例: {df_3m['time'].iloc[:5].tolist()}")
    logger.info("\n")

    # 验证格式是否一致
    index_format_ok = all(len(idx) == 14 and idx.isdigit() for idx in df_3m.index)
    time_format_ok = all(isinstance(t, (int, np.int64))
                         and t > 1e12 for t in df_3m['time'])

    if index_format_ok and time_format_ok:
        logger.info("测试通过: 转换后的数据格式与原始数据一致")
        logger.info(f"- 索引格式正确: {index_format_ok}")
        logger.info(f"- 时间列格式正确: {time_format_ok}")
    else:
        logger.error("测试失败: 转换后的数据格式与原始数据不一致")
        logger.error(f"- 索引格式正确: {index_format_ok}")
        logger.error(f"- 时间列格式正确: {time_format_ok}")


if __name__ == "__main__":
    logger.info("开始测试周期转换格式保持功能")
    test_period_conversion()
    logger.info("测试完成")
