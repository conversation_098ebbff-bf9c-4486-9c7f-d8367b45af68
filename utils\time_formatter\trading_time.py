#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
交易时间判断模块

提供A股和期货的交易时间判断功能，支持休盘时间识别
"""

from datetime import datetime, time
from typing import Optional, List, Tuple
import pandas as pd
from functools import lru_cache
import functools
import time as time_module
from utils.logger import get_unified_logger

# 使用统一的日志系统
logger = get_unified_logger(__name__)

# 性能监控装饰器
def function_performance_monitor(func):
    """
    函数性能监控装饰器

    监控函数执行时间和调用次数
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time_module.time()

        # 执行函数
        result = func(*args, **kwargs)

        # 记录执行时间
        end_time = time_module.time()
        execution_time = end_time - start_time

        # 记录性能日志
        if len(args) > 0 and hasattr(args[0], '__len__'):
            batch_size = len(args[0])
            logger.debug(f"函数 {func.__name__} 执行完成: 数据量={batch_size}, 耗时={execution_time:.4f}秒")
        else:
            logger.debug(f"函数 {func.__name__} 执行完成: 耗时={execution_time:.4f}秒")

        return result

    return wrapper

# 集合竞价时段缓存
_auction_periods_cache = {}
_auction_boundary_cache = {}


class TradingTimeRules:
    """完整的交易时间规则配置类
    
    基于准确的交易时间规则：
    - A股：开盘集合竞价 9:15-9:30，连续竞价 9:30-11:30/13:00-14:57，收盘集合竞价 14:57-15:00
    - 期货日盘：集合竞价 8:55-9:00，连续竞价 9:00-11:30/13:30-15:00（10:15-10:30小节休息）
    - 期货夜盘：集合竞价 20:55-21:00，连续竞价按品种分类
    - 中金所：集合竞价 9:25-9:30，连续竞价 9:30-11:30/13:00-15:00
    """
    
    # ========== A股交易时间规则 ==========
    
    # A股集合竞价时间段
    STOCK_AUCTION_PERIODS = [
        (time(9, 15), time(9, 30)),    # 开盘集合竞价：9:15-9:30
        (time(14, 57), time(15, 0)),   # 收盘集合竞价：14:57-15:00
    ]
    
    # A股连续竞价时间段
    STOCK_TRADING_PERIODS = [
        (time(9, 30), time(11, 30)),   # 上午连续竞价：9:30-11:30
        (time(13, 0), time(14, 57)),   # 下午连续竞价：13:00-14:57
    ]
    
    # A股休盘时间段
    STOCK_BREAK_PERIODS = [
        (time(11, 30), time(13, 0)),   # 午休：11:30-13:00
    ]
    
    # A股集合竞价撮合结束时间点（精确时间）
    STOCK_AUCTION_END_TIMES = [
        time(9, 30, 0),                # 开盘集合竞价结束：9:30:00
        time(15, 0, 0),                # 收盘集合竞价结束：15:00:00
    ]
    
    # ========== 期货日盘交易时间规则 ==========
    
    # 期货日盘集合竞价时间段
    FUTURES_DAY_AUCTION_PERIODS = [
        (time(8, 55), time(9, 0)),     # 集合竞价：8:55-9:00（含申报8:55-8:59和撮合8:59-9:00）
    ]
    
    # 期货日盘连续竞价时间段
    FUTURES_DAY_TRADING_PERIODS = [
        (time(9, 0), time(10, 15)),    # 上午第一节：9:00-10:15
        (time(10, 30), time(11, 30)),  # 上午第二节：10:30-11:30
        (time(13, 30), time(15, 0)),   # 下午交易：13:30-15:00
    ]
    
    # 期货日盘休盘时间段
    FUTURES_DAY_BREAK_PERIODS = [
        (time(10, 15), time(10, 30)),  # 小节休息：10:15-10:30
        (time(11, 30), time(13, 30)),  # 午休：11:30-13:30
    ]
    
    # 期货日盘集合竞价撮合结束时间点（精确时间）
    FUTURES_DAY_AUCTION_END_TIMES = [
        time(9, 0, 0),                 # 日盘开盘集合竞价结束：9:00:00
    ]
    
    # ========== 期货夜盘交易时间规则 ==========
    
    # 期货夜盘集合竞价时间段
    FUTURES_NIGHT_AUCTION_PERIODS = [
        (time(20, 55), time(21, 0)),   # 夜盘集合竞价：20:55-21:00（含申报20:55-20:59和撮合20:59-21:00）
    ]
    
    # 期货夜盘连续竞价时间段（按品种分类）
    FUTURES_NIGHT_TRADING_PERIODS = {
        'most': [                      # 大部分品种：21:00-23:00（橡胶、螺纹钢、热轧卷板、沥青等）
            (time(21, 0), time(23, 0)),
        ],
        'metals': [                    # 有色金属：21:00-01:00（铜、铝、镍、铅、锌、锡、不锈钢）
            (time(21, 0), time(23, 59, 59)),
            (time(0, 0), time(1, 0)),
        ],
        'precious': [                  # 贵金属：21:00-02:30（黄金、白银、原油）
            (time(21, 0), time(23, 59, 59)),
            (time(0, 0), time(2, 30)),
        ]
    }
    
    # 期货夜盘休盘时间段（按品种分类）
    # 覆盖夜盘结束后到日盘开始前的完整时间段
    FUTURES_NIGHT_BREAK_PERIODS = {
        'most': [                      # 大部分品种：23:00收盘后到次日8:59
            (time(23, 0), time(23, 59, 59)),    # 当日23:00-23:59:59
            (time(0, 0), time(8, 59, 59)),      # 次日00:00-08:59:59
        ],
        'metals': [                    # 有色金属：01:00收盘后到次日8:59
            (time(1, 0), time(8, 59, 59)),      # 01:00-08:59:59（跨日处理）
        ],
        'precious': [                  # 贵金属：02:30收盘后到次日8:59
            (time(2, 30), time(8, 59, 59)),     # 02:30-08:59:59（跨日处理）
        ],
        'day_only': []                 # 日盘品种无夜盘休盘时间
    }
    
    # 期货夜盘集合竞价撮合结束时间点（精确时间）
    FUTURES_NIGHT_AUCTION_END_TIMES = [
        time(21, 0, 0),                # 夜盘开盘集合竞价结束：21:00:00
    ]
    
    # ========== 中金所特殊规则 ==========
    
    # 中金所集合竞价时间段
    CFFEX_AUCTION_PERIODS = [
        (time(9, 25), time(9, 30)),    # 集合竞价：9:25-9:30（含申报9:25-9:29和撮合9:29-9:30）
    ]
    
    # 中金所连续竞价时间段
    CFFEX_TRADING_PERIODS = [
        (time(9, 30), time(11, 30)),   # 上午交易：9:30-11:30
        (time(13, 0), time(15, 0)),    # 下午交易：13:00-15:00（股指期货）
        (time(13, 0), time(15, 15)),   # 下午交易：13:00-15:15（国债期货）
    ]
    
    # 中金所休盘时间段
    CFFEX_BREAK_PERIODS = [
        (time(11, 30), time(13, 0)),   # 午休：11:30-13:00
    ]
    
    # 中金所集合竞价撮合结束时间点（精确时间）
    CFFEX_AUCTION_END_TIMES = [
        time(9, 30, 0),                # 开盘集合竞价结束：9:30:00
    ]


def is_valid_trading_time(dt: datetime, symbol_type: str = 'stock', 
                         futures_category: str = 'most') -> bool:
    """
    判断指定时间是否为有效交易时间
    
    Args:
        dt: 要判断的时间
        symbol_type: 品种类型，'stock'(A股) 或 'futures'(期货) 或 'cffex'(中金所)
        futures_category: 期货品种分类，'most'(大部分品种)、'metals'(有色金属)、'precious'(贵金属)
        
    Returns:
        bool: 是否为有效交易时间
    """
    if dt is None:
        return False
    
    # 提取时间部分
    current_time = dt.time()
    
    try:
        if symbol_type == 'stock':
            # A股交易时间判断
            return _is_time_in_periods(current_time, TradingTimeRules.STOCK_TRADING_PERIODS)
            
        elif symbol_type == 'cffex':
            # 中金所交易时间判断
            return _is_time_in_periods(current_time, TradingTimeRules.CFFEX_TRADING_PERIODS)
            
        elif symbol_type == 'futures':
            # 期货交易时间判断

            # 特殊处理：对于夜盘品种，9:00:00是休盘时间，不是交易时间
            if (futures_category in ['most', 'metals', 'precious'] and
                current_time == time(9, 0, 0)):
                return False

            # 先检查日盘时间
            if _is_time_in_periods(current_time, TradingTimeRules.FUTURES_DAY_TRADING_PERIODS):
                return True

            # 再检查夜盘时间
            night_periods = TradingTimeRules.FUTURES_NIGHT_TRADING_PERIODS.get(futures_category, [])
            return _is_time_in_periods(current_time, night_periods)
            
        else:
            logger.warning(f"未知的品种类型: {symbol_type}")
            return False
            
    except Exception as e:
        logger.error(f"判断交易时间时出错: {e}")
        return False


def _is_time_in_periods(current_time: time, periods: List[Tuple[time, time]]) -> bool:
    """
    判断时间是否在指定的时间段列表中
    
    Args:
        current_time: 当前时间
        periods: 时间段列表，每个元素为(开始时间, 结束时间)的元组
        
    Returns:
        bool: 是否在时间段内
    """
    for start_time, end_time in periods:
        if start_time <= current_time <= end_time:
            return True
    return False


def get_last_valid_trading_time(dt: datetime, symbol_type: str = 'stock',
                               futures_category: str = 'most') -> Optional[datetime]:
    """
    获取指定时间之前的最后一个有效交易时间
    
    Args:
        dt: 参考时间
        symbol_type: 品种类型
        futures_category: 期货品种分类
        
    Returns:
        Optional[datetime]: 最后一个有效交易时间，如果找不到则返回None
    """
    if dt is None:
        return None
    
    try:
        # 如果当前时间就是有效交易时间，直接返回
        if is_valid_trading_time(dt, symbol_type, futures_category):
            return dt
        
        # 获取对应的交易时间段
        if symbol_type == 'stock':
            periods = TradingTimeRules.STOCK_TRADING_PERIODS
        elif symbol_type == 'cffex':
            periods = TradingTimeRules.CFFEX_TRADING_PERIODS
        elif symbol_type == 'futures':
            periods = TradingTimeRules.FUTURES_DAY_TRADING_PERIODS
            night_periods = TradingTimeRules.FUTURES_NIGHT_TRADING_PERIODS.get(futures_category, [])
            periods = periods + night_periods
        else:
            return None
        
        current_time = dt.time()
        last_valid_time = None
        
        # 找到当前时间之前的最后一个交易时段的结束时间
        for start_time, end_time in periods:
            if current_time > end_time:
                # 创建同一天的结束时间
                candidate_time = dt.replace(hour=end_time.hour, 
                                          minute=end_time.minute, 
                                          second=end_time.second,
                                          microsecond=0)
                if last_valid_time is None or candidate_time > last_valid_time:
                    last_valid_time = candidate_time
        
        return last_valid_time
        
    except Exception as e:
        logger.error(f"获取最后有效交易时间时出错: {e}")
        return None


def detect_symbol_type(symbol: str) -> str:
    """
    根据股票代码检测品种类型
    
    Args:
        symbol: 股票代码，如 '000001.SZ', 'rb2501.SF'
        
    Returns:
        str: 品种类型 'stock', 'futures', 'cffex'
    """
    if symbol is None:
        return 'stock'  # 默认为股票
    
    symbol = symbol.upper()
    
    # 中金所代码判断
    if '.IF' in symbol:
        return 'cffex'
    
    # 期货代码判断
    futures_exchanges = ['.SF', '.DF', '.ZF', '.INE', '.GF']
    for exchange in futures_exchanges:
        if exchange in symbol:
            return 'futures'
    
    # 默认为股票
    return 'stock'


def detect_futures_category(symbol: str) -> str:
    """
    根据期货代码检测期货品种分类

    Args:
        symbol: 期货代码，如 'rb2501.SF', 'au2501.SF'

    Returns:
        str: 期货品种分类 'most', 'metals', 'precious', 'day_only'
    """
    if symbol is None:
        return 'most'

    symbol = symbol.lower()

    # 日盘品种（无夜盘交易）
    day_only_symbols = [
        'wr',    # 线材
        'simn',  # 锰硅
        'sf',    # 硅铁
        'fb',    # 纤维板
        'bb',    # 胶合板
        'ur',    # 尿素
        'wh',    # 强筋小麦
        'pm',    # 普通小麦
        'ri',    # 早籼稻
        'lr',    # 晚籼稻
        'jr',    # 粳稻
        'rs',    # 菜籽
        'rm',    # 菜籽粕
        'oi',    # 菜籽油
        'jd',    # 鸡蛋
        'ap',    # 苹果
        'cj',    # 红枣
        'pk',    # 花生
        'lh',    # 生猪
    ]
    for day_symbol in day_only_symbols:
        if symbol.startswith(day_symbol):
            return 'day_only'

    # 贵金属品种
    precious_metals = ['au', 'ag', 'sc']  # 黄金、白银、原油
    for metal in precious_metals:
        if symbol.startswith(metal):
            return 'precious'

    # 有色金属品种
    base_metals = ['cu', 'al', 'ni', 'pb', 'zn', 'sn', 'ss']  # 铜、铝、镍、铅、锌、锡、不锈钢
    for metal in base_metals:
        if symbol.startswith(metal):
            return 'metals'

    # 默认为大部分品种（有夜盘）
    return 'most'


def has_night_trading(symbol: str) -> bool:
    """
    判断期货品种是否有夜盘交易

    Args:
        symbol: 期货代码，如 'rb2501.SF', 'au2501.SF'

    Returns:
        bool: True表示有夜盘交易，False表示只有日盘交易
    """
    futures_category = detect_futures_category(symbol)
    return futures_category in ['most', 'metals', 'precious']


def is_auction_end_time(dt: datetime, symbol_type: str = 'stock',
                       futures_category: str = 'most') -> bool:
    """
    判断指定时间是否为集合竞价撮合结束的精确瞬间
    
    关键理解：
    - 只有集合竞价撮合结束的精确瞬间（如21:00:00.000）才返回True
    - 连续竞价时间（如21:00:00.500）应该返回False
    - 这个函数用于识别需要特殊处理集合竞价成交量的时间点
    
    精确判断逻辑：
    - 必须精确到毫秒级别：XX:XX:XX.000
    - 21:00:00.001及之后都是连续竞价时间，不是集合竞价结束时间
    
    Args:
        dt: 要判断的时间
        symbol_type: 品种类型，'stock'(A股) 或 'futures'(期货) 或 'cffex'(中金所)
        futures_category: 期货品种分类，'most'(大部分品种)、'metals'(有色金属)、'precious'(贵金属)
        
    Returns:
        bool: 是否为集合竞价撮合结束的精确瞬间
    """
    if dt is None:
        return False
    
    # 精确提取时、分、秒、微秒，精确到毫秒级判断
    current_time = dt.time()
    
    try:
        if symbol_type == 'stock':
            # A股集合竞价撮合结束精确时间点
            return current_time in TradingTimeRules.STOCK_AUCTION_END_TIMES
            
        elif symbol_type == 'cffex':
            # 中金所集合竞价撮合结束精确时间点
            return current_time in TradingTimeRules.CFFEX_AUCTION_END_TIMES
            
        elif symbol_type == 'futures':
            # 期货集合竞价撮合结束精确时间点
            all_futures_auction_end_times = (
                TradingTimeRules.FUTURES_DAY_AUCTION_END_TIMES + 
                TradingTimeRules.FUTURES_NIGHT_AUCTION_END_TIMES
            )
            return current_time in all_futures_auction_end_times
        
        return False
        
    except Exception as e:
        logger.error(f"判断集合竞价结束时间时出错: {e}")
        return False


def get_auction_end_times(symbol_type: str = 'stock', 
                         futures_category: str = 'most') -> List[time]:
    """
    获取指定品种的所有集合竞价撮合结束时间点
    
    Args:
        symbol_type: 品种类型，'stock'(A股) 或 'futures'(期货) 或 'cffex'(中金所)
        futures_category: 期货品种分类，'most'(大部分品种)、'metals'(有色金属)、'precious'(贵金属)
        
    Returns:
        List[time]: 集合竞价撮合结束时间点列表
    """
    try:
        if symbol_type == 'stock':
            # A股集合竞价撮合结束时间点
            return TradingTimeRules.STOCK_AUCTION_END_TIMES.copy()
            
        elif symbol_type == 'cffex':
            # 中金所集合竞价撮合结束时间点
            return TradingTimeRules.CFFEX_AUCTION_END_TIMES.copy()
            
        elif symbol_type == 'futures':
            # 期货集合竞价撮合结束时间点（日盘+夜盘）
            all_times = (
                TradingTimeRules.FUTURES_DAY_AUCTION_END_TIMES + 
                TradingTimeRules.FUTURES_NIGHT_AUCTION_END_TIMES
            )
            return all_times.copy()
        
        return []
        
    except Exception as e:
        logger.error(f"获取集合竞价结束时间时出错: {e}")
        return []


def is_break_time(dt: datetime, symbol_type: str = 'stock', 
                  futures_category: str = 'most') -> bool:
    """
    判断指定时间是否为休盘时间
    
    Args:
        dt: 要判断的时间
        symbol_type: 品种类型，'stock'(A股) 或 'futures'(期货) 或 'cffex'(中金所)
        futures_category: 期货品种分类，'most'(大部分品种)、'metals'(有色金属)、'precious'(贵金属)
        
    Returns:
        bool: 是否为休盘时间
    """
    if dt is None:
        return True
    
    # 提取时间部分
    current_time = dt.time()
    
    try:
        if symbol_type == 'stock':
            # A股休盘时间判断
            return _is_time_in_periods(current_time, TradingTimeRules.STOCK_BREAK_PERIODS)
            
        elif symbol_type == 'cffex':
            # 中金所休盘时间判断
            return _is_time_in_periods(current_time, TradingTimeRules.CFFEX_BREAK_PERIODS)
            
        elif symbol_type == 'futures':
            # 期货休盘时间判断 - 采用"非交易时间且非集合竞价时间即为休盘时间"的逻辑

            # 先检查是否为有效交易时间
            if is_valid_trading_time(dt, symbol_type, futures_category):
                return False

            # 再检查是否为集合竞价时间
            if is_auction_time(dt, symbol_type, futures_category):
                return False

            # 既不是交易时间也不是集合竞价时间，则为休盘时间
            return True
            
        else:
            logger.warning(f"未知的品种类型: {symbol_type}")
            return True
            
    except Exception as e:
        logger.error(f"判断休盘时间时出错: {e}")
        return True


def is_auction_time(dt: datetime, symbol_type: str = 'stock', 
                    futures_category: str = 'most') -> bool:
    """
    判断指定时间是否为集合竞价时间段
    
    Args:
        dt: 要判断的时间
        symbol_type: 品种类型，'stock'(A股) 或 'futures'(期货) 或 'cffex'(中金所)
        futures_category: 期货品种分类，'most'(大部分品种)、'metals'(有色金属)、'precious'(贵金属)
        
    Returns:
        bool: 是否为集合竞价时间段
    """
    if dt is None:
        return False
    
    # 提取时间部分
    current_time = dt.time()
    
    try:
        if symbol_type == 'stock':
            # A股集合竞价时间段判断
            return _is_time_in_periods(current_time, TradingTimeRules.STOCK_AUCTION_PERIODS)
            
        elif symbol_type == 'cffex':
            # 中金所集合竞价时间段判断
            return _is_time_in_periods(current_time, TradingTimeRules.CFFEX_AUCTION_PERIODS)
            
        elif symbol_type == 'futures':
            # 期货集合竞价时间段判断
            # 需要根据品种分类区分日盘和夜盘的集合竞价时间

            # 检查夜盘集合竞价时间 (20:55-21:00)
            if _is_time_in_periods(current_time, TradingTimeRules.FUTURES_NIGHT_AUCTION_PERIODS):
                return True

            # 对于只有日盘的品种，检查日盘集合竞价时间 (8:55-9:00)
            # 但是对于有夜盘的品种，8:55-9:00是连续竞价时间，不是集合竞价
            if futures_category in ['day_only']:  # 只有日盘的品种才在8:55-9:00有集合竞价
                return _is_time_in_periods(current_time, TradingTimeRules.FUTURES_DAY_AUCTION_PERIODS)

            # 对于有夜盘的品种（most, metals, precious），8:55-9:00是连续竞价时间
            return False
            
        else:
            logger.warning(f"未知的品种类型: {symbol_type}")
            return False
            
    except Exception as e:
        logger.error(f"判断集合竞价时间时出错: {e}")
        return False


def get_trading_session_info(dt: datetime, symbol_type: str = 'stock', 
                           futures_category: str = 'most') -> dict:
    """
    获取指定时间的交易时段详细信息
    
    Args:
        dt: 要判断的时间
        symbol_type: 品种类型
        futures_category: 期货品种分类
        
    Returns:
        dict: 交易时段信息，包含:
            - session_type: 'auction_end' | 'auction' | 'trading' | 'break' | 'closed'
            - is_auction_end: 是否为集合竞价撮合结束瞬间
            - description: 时段描述
    """
    if dt is None:
        return {
            'session_type': 'closed',
            'is_auction_end': False,
            'description': '无效时间'
        }
    
    try:
        # 判断是否为集合竞价撮合结束瞬间
        is_auction_end = is_auction_end_time(dt, symbol_type, futures_category)
        
        # 判断是否为集合竞价时间段
        is_auction = is_auction_time(dt, symbol_type, futures_category)
        
        # 判断是否为有效交易时间
        is_trading = is_valid_trading_time(dt, symbol_type, futures_category)
        
        # 判断是否为休盘时间
        is_break = is_break_time(dt, symbol_type, futures_category)
        
        if is_auction_end:
            return {
                'session_type': 'auction_end',
                'is_auction_end': True,
                'description': '集合竞价撮合结束瞬间'
            }
        elif is_auction:
            return {
                'session_type': 'auction',
                'is_auction_end': False,
                'description': '集合竞价时间段'
            }
        elif is_trading:
            return {
                'session_type': 'trading',
                'is_auction_end': False,
                'description': '连续竞价时间段'
            }
        elif is_break:
            return {
                'session_type': 'break',
                'is_auction_end': False,
                'description': '休盘时间段'
            }
        else:
            return {
                'session_type': 'closed',
                'is_auction_end': False,
                'description': '收盘时间段'
            }
            
    except Exception as e:
        logger.error(f"获取交易时段信息时出错: {e}")
        return {
            'session_type': 'unknown',
            'is_auction_end': False,
            'description': f'时段识别错误: {e}'
        }


@lru_cache(maxsize=32)
def get_all_auction_periods(symbol_type: str = 'stock', futures_category: str = 'most') -> List[tuple]:
    """
    获取指定品种的所有集合竞价时间段（带缓存优化）

    Args:
        symbol_type: 品种类型，'stock'(A股) 或 'futures'(期货) 或 'cffex'(中金所)
        futures_category: 期货品种分类，'most'(大部分品种)、'metals'(有色金属)、'precious'(贵金属)、'day_only'(仅日盘)

    Returns:
        List[tuple]: 集合竞价时间段列表，每个元素为(start_time, end_time)
    """
    try:
        # 使用缓存键
        cache_key = f"{symbol_type}_{futures_category}"

        if cache_key in _auction_periods_cache:
            return _auction_periods_cache[cache_key]

        result = []

        if symbol_type == 'stock':
            # A股集合竞价时间段：开盘9:15-9:30，收盘14:57-15:00
            result = TradingTimeRules.STOCK_AUCTION_PERIODS.copy()

        elif symbol_type == 'cffex':
            # 中金所集合竞价时间段：9:25-9:30
            result = TradingTimeRules.CFFEX_AUCTION_PERIODS.copy()

        elif symbol_type == 'futures':
            # 期货集合竞价时间段
            auction_periods = []

            # 夜盘集合竞价：20:55-21:00（所有有夜盘的品种）
            if futures_category in ['most', 'metals', 'precious']:
                auction_periods.extend(TradingTimeRules.FUTURES_NIGHT_AUCTION_PERIODS)

            # 日盘集合竞价：8:55-9:00（仅日盘品种）
            if futures_category == 'day_only':
                auction_periods.extend(TradingTimeRules.FUTURES_DAY_AUCTION_PERIODS)

            result = auction_periods

        # 缓存结果
        _auction_periods_cache[cache_key] = result
        return result

    except Exception as e:
        logger.error(f"获取集合竞价时间段时出错: {e}")
        return []


def is_auction_period_unified(dt: datetime, symbol_type: str = 'stock',
                             futures_category: str = 'most') -> bool:
    """
    统一的集合竞价时段判断函数

    支持所有品种的集合竞价时段识别：
    - A股：9:15-9:30（开盘），14:57-15:00（收盘）
    - 期货日盘：8:55-9:00（仅day_only品种）
    - 期货夜盘：20:55-21:00
    - 中金所：9:25-9:30

    Args:
        dt: 要判断的时间
        symbol_type: 品种类型，'stock'(A股) 或 'futures'(期货) 或 'cffex'(中金所)
        futures_category: 期货品种分类，'most'(大部分品种)、'metals'(有色金属)、'precious'(贵金属)、'day_only'(仅日盘)

    Returns:
        bool: 是否为集合竞价时段
    """
    if dt is None:
        return False

    try:
        # 获取该品种的所有集合竞价时间段
        auction_periods = get_all_auction_periods(symbol_type, futures_category)

        if not auction_periods:
            return False

        # 判断当前时间是否在任一集合竞价时间段内
        current_time = dt.time()
        return _is_time_in_periods(current_time, auction_periods)

    except Exception as e:
        logger.error(f"统一集合竞价时段判断时出错: {e}")
        return False


def get_auction_boundary_times(symbol_type: str = 'stock',
                              futures_category: str = 'most') -> List[time]:
    """
    获取指定品种的集合竞价边界时间点（用于休盘数据合并判断）

    边界时间是集合竞价结束的时间点，这些时间点的下一分钟数据可能需要向前合并

    Args:
        symbol_type: 品种类型，'stock'(A股) 或 'futures'(期货) 或 'cffex'(中金所)
        futures_category: 期货品种分类，'most'(大部分品种)、'metals'(有色金属)、'precious'(贵金属)、'day_only'(仅日盘)

    Returns:
        List[time]: 集合竞价边界时间点列表
    """
    try:
        auction_periods = get_all_auction_periods(symbol_type, futures_category)

        # 提取每个集合竞价时间段的结束时间作为边界时间
        boundary_times = []
        for start_time, end_time in auction_periods:
            boundary_times.append(end_time)

        return boundary_times

    except Exception as e:
        logger.error(f"获取集合竞价边界时间时出错: {e}")
        return []


def is_auction_boundary_time(dt: datetime, symbol_type: str = 'stock',
                            futures_category: str = 'most') -> bool:
    """
    判断指定时间是否为集合竞价边界时间点

    用于休盘数据合并时判断目标时间是否需要特殊处理

    Args:
        dt: 要判断的时间
        symbol_type: 品种类型，'stock'(A股) 或 'futures'(期货) 或 'cffex'(中金所)
        futures_category: 期货品种分类，'most'(大部分品种)、'metals'(有色金属)、'precious'(贵金属)、'day_only'(仅日盘)

    Returns:
        bool: 是否为集合竞价边界时间点
    """
    if dt is None:
        return False

    try:
        boundary_times = get_auction_boundary_times(symbol_type, futures_category)
        current_time = dt.time()

        return current_time in boundary_times

    except Exception as e:
        logger.error(f"判断集合竞价边界时间时出错: {e}")
        return False


@function_performance_monitor
def is_auction_period_batch(time_series: pd.Series, symbol_type: str = 'stock',
                           futures_category: str = 'most') -> pd.Series:
    """
    向量化批量判断集合竞价时段

    Args:
        time_series: 时间序列（pandas Series，包含datetime对象）
        symbol_type: 品种类型，'stock'(A股) 或 'futures'(期货) 或 'cffex'(中金所)
        futures_category: 期货品种分类，'most'(大部分品种)、'metals'(有色金属)、'precious'(贵金属)、'day_only'(仅日盘)

    Returns:
        pd.Series: 布尔序列，True表示对应时间为集合竞价时段
    """
    try:
        import pandas as pd
        import numpy as np

        if time_series.empty:
            return pd.Series([], dtype=bool)

        # 获取该品种的所有集合竞价时间段
        auction_periods = get_all_auction_periods(symbol_type, futures_category)

        if not auction_periods:
            return pd.Series([False] * len(time_series), index=time_series.index)

        # 提取时间部分进行向量化比较
        time_only = time_series.dt.time

        # 初始化结果数组
        result = np.zeros(len(time_series), dtype=bool)

        # 对每个集合竞价时间段进行向量化判断
        for start_time, end_time in auction_periods:
            # 向量化时间范围判断
            in_period = (time_only >= start_time) & (time_only < end_time)
            result |= in_period.values

        return pd.Series(result, index=time_series.index)

    except Exception as e:
        logger.error(f"向量化集合竞价时段判断时出错: {e}")
        return pd.Series([False] * len(time_series), index=time_series.index)


@function_performance_monitor
def is_auction_boundary_time_batch(time_series: pd.Series, symbol_type: str = 'stock',
                                  futures_category: str = 'most') -> pd.Series:
    """
    向量化批量判断集合竞价边界时间点

    Args:
        time_series: 时间序列（pandas Series，包含datetime对象）
        symbol_type: 品种类型，'stock'(A股) 或 'futures'(期货) 或 'cffex'(中金所)
        futures_category: 期货品种分类，'most'(大部分品种)、'metals'(有色金属)、'precious'(贵金属)、'day_only'(仅日盘)

    Returns:
        pd.Series: 布尔序列，True表示对应时间为集合竞价边界时间点
    """
    try:
        import pandas as pd
        import numpy as np

        if time_series.empty:
            return pd.Series([], dtype=bool)

        # 获取该品种的集合竞价边界时间点
        boundary_times = get_auction_boundary_times(symbol_type, futures_category)

        if not boundary_times:
            return pd.Series([False] * len(time_series), index=time_series.index)

        # 提取时间部分进行向量化比较
        time_only = time_series.dt.time

        # 向量化判断是否在边界时间点列表中
        result = time_only.isin(boundary_times)

        return pd.Series(result.values, index=time_series.index)

    except Exception as e:
        logger.error(f"向量化集合竞价边界时间判断时出错: {e}")
        return pd.Series([False] * len(time_series), index=time_series.index)
