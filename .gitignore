# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE specific files
.idea/
.vscode/
*.swp
*.swo

# OS specific files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
logs/
*.log
data/raw/
data/processed/
data/external/
*.parquet
*.csv
*.xlsx
*.h5
!requirements.txt

# Credentials and sensitive information
credentials.json
secrets.yaml
*.key
*.pem

# Temporary files
temp/
tmp/
*.bak
*.tmp
*.backup
*.backup_*
__pycache__

# Project specific results and reports
*_results/
*_reports/
backtest_results/
test_reports/
pdf_test/

# Download and processing results
download_results*.txt
*_fix_report.txt
time_processing_fix_report.txt

# Test temporary data
tests/temp_data/
tests/*/temp/
tests/temp/

# Temporary directories
docs/temp/

# Temporary files by type
temp_*.py
temp_*.md
*_plan.md
debug_*.py

# Development tools (temporary)
*_fixer.py
*_checker.py
*_tool.py

# Fix scripts (temporary)
fix_*.py
repair_*.py

# Demo scripts (temporary)
demo_*.py
show_*.py

# Dependency lock files (optional)
uv.lock
