---
description:
globs:
alwaysApply: true
---
# 交互沟通规则

## 1. 需求理解与确认流程

### 1.1 需求分析
- 收到用户需求后，首先进行完整的需求分析
- 列出对需求的具体理解要点
- 说明准备采取的行动步骤
- 明确指出可能的影响和风险

### 1.2 确认流程
- 在执行任何操作前，先向用户展示完整的执行计划
- 等待用户确认或调整
- 只有在得到用户明确许可后才执行操作
- 在auto-select模式下，模型使用选择上，问答模式下，默认使用gpt-4o-mini or cursor-small，只有生成代码才用Premium models

### 1.3 执行反馈
- 执行操作时提供清晰的进度反馈
- 操作完成后总结执行结果
- 如遇问题及时说明并请求指导
- 结果直接输出完整解决方案

## 2. 回复格式规范

### 2.1 需求分析回复格式
```
我理解您的需求是：
1. [具体需求点1]
2. [具体需求点2]
...

我计划执行以下操作：
1. [具体操作步骤1]
2. [具体操作步骤2]
...

可能的影响：
- [影响1]
- [影响2]
...

请确认以上理解和计划是否符合您的预期？
```

### 2.2 执行确认格式
- 等待用户明确的"同意"或"确认"后再执行
- 如用户提出调整，需重新展示修改后的计划

### 2.3 执行报告格式
```
执行结果：
1. [步骤1结果]
2. [步骤2结果]
...

是否需要进行其他调整？
```

## 3. 特殊情况处理

### 3.1 需求不明确
- 主动提出澄清问题
- 列出需要用户确认的具体点

### 3.2 多个可选方案
- 列出所有可行方案
- 说明各方案的优缺点
- 请用户选择或给出建议

### 3.3 风险提示
- 如发现潜在风险，必须在执行前提醒
- 提供规避风险的建议方案


---
description:
globs:
alwaysApply: true
---
# 量化交易系统综合规则

## 开发环境与依赖库版本更新规范
- 本项目在conda环境中开发，请检查一遍当前开发环境是否为conda，没有则创建对应的conda环境
- 每周检查一次项目依赖库requirements文件对应python3.12.9版本是否有最新版本有则更新requirements到最新版本，并确认现有依赖库是否需要更新


## 1. 系统架构规范
### 1.1 模块化设计
- 交易执行模块与策略模块必须解耦
- 数据处理模块必须独立封装
- 风控模块必须可独立配置和运行
- 按功能模块分目录存放代码
- 统一使用相对引用路径
- 配置文件集中管理

## 2. 实时交易规范
### 2.1 性能要求
- API响应时间不得超过10ms
- 订单延迟不超过100ms
- 内存使用需实时监控，不超过预设阈值

### 2.2 风控系统
- 实现多层风控体系（订单前风控、实时持仓风控、资金规模风控）
- 异常交易自动中断机制
- 关键操作必须记录详细日志

### 2.3 数据处理标准
- 实时行情必须进行异常值过滤
- 所有时间必须统一使用UTC时间戳
- 价格和数量必须使用Decimal类型
- 实现并行异步数据处理，提高处理效率
- 采用流式处理机制，边处理数据边释放资源

## 3. 回测系统规范
### 3.1 数据获取与管理
- 回测数据与实盘数据分离存储
- 历史数据必须包含清晰的数据源标注
- 数据预处理流程必须可重现
- 实现数据增量更新机制，避免重复处理
- 数据下载实现模块化管理，支持多个数据源获取数据

### 3.2 回测功能
- 支持多周期回测
- 提供完整的交易成本模型
- 回测结果必须包含标准化指标（夏普比率、最大回撤、年化收益、胜率统计）
- 回测过程必须保证结果准确性，同时优化内存使用

### 3.3 回测性能
- 严格控制回测速度指标
- 实时监控内存使用
- 优化数据加载效率
- 日线回测速度：<1分钟/10年数据
- 分钟线回测速度：<5分钟/1年数据
- tick回测速度：<5分钟/1年数据
- 支持100+并行回测
- 回测策略必须实现边处理数据边释放资源的流式机制

## 4. 策略开发规范
### 4.1 高频策略
- 信号生成延迟<1ms
- 必须考虑市场冲击
- 严格控制订单频率
- 实现队列位置估计

### 4.2 低频策略
- 完整的因子文档
- 因子有效性检验流程
- 严格的止损止盈机制

### 4.3 套利策略
- 价差计算标准化
- 考虑交易成本
- 设置最小套利空间
- 实现多腿订单管理

### 4.4 市场中性策略
- 严格控制Beta暴露
- 实现动态对冲
- 定期再平衡机制

## 5. 性能优化规范
### 5.1 数据加载优化
- 实现数据按需加载（仅加载所需时间段和字段）
- 支持数据分片加载
- 使用压缩存储格式（如Parquet）
- 建立数据索引加速查询
- 实现数据加载与处理的并行化

### 5.2 并行处理
- 实现数据并行加载器
- 设置最优线程池大小
- 因子计算并行处理
- 多品种并行回测
- GPU加速支持（适用于大规模矩阵运算）
- 实现异步IO操作，减少数据读取等待时间

### 5.3 内存管理
- 动态内存控制（设置使用上限、监控机制）
- 数据生命周期管理（及时释放无用数据）
- 实现数据缓存淘汰机制
- 内存峰值使用率<80%，稳定使用率<60%
- 确保无内存泄漏
- 回测过程中实现流式释放不再需要的历史数据

### 5.4 计算优化
- 流式处理机制（边处理边释放资源）
- 支持checkpoint机制
- 向量化运算
- 预计算常用指标
- 缓存中间结果
- 实现计算任务优先级队列，优先处理关键路径

### 5.5 资源管理
- 预分配内存池
- 循环利用对象
- 自动内存碎片整理
- 多级缓存机制
- LRU缓存淘汰策略
- 热点数据优先缓存

## 6. 代码质量规范
### 6.1 注释要求
- 策略逻辑必须有中文注释
- 关键参数必须说明含义
- 复杂算法必须附带文档

### 6.2 测试要求
- 单元测试覆盖率>80%
- 必须包含压力测试
- 模拟实盘测试不少于1个月

### 6.3 性能分析
- 关键函数必须进行性能分析
- 大数据操作必须使用异步处理
- 缓存机制合理使用
- 代码热点分析
- 内存泄漏检测
- IO瓶颈分析

## 7. 监控告警规范
### 7.1 系统监控
- CPU使用率预留部分保持系统运行流程
- 内存使用预留部分保持系统运行流程
- 网络延迟监控
- IO等待时间监控

### 7.2 交易监控
- 异常交易量告警
- 持仓超限告警
- 盈亏异常告警

## 8. 数据安全规范
### 8.1 数据存储
- 敏感数据加密存储
- 关键配置参数加密
- 定期数据备份机制
- 按照标准路径格式存储: <数据根目录>/<交易所>/<股票代码>/<周期>.parquet
```
例如:
D:\data\SZ\000001\tick.parquet
D:\data\SH\600000\1d.parquet
```

### 8.2 访问控制
- 严格的权限分级制度
- API访问认证机制
- 操作日志完整记录

## 9. 数据加载性能指标
### 9.1 速度目标
- 日线数据：<1秒/年数据
- 分钟线：<5秒/月数据
- tick数据：<10秒/日数据
---
description:
globs:
alwaysApply: true
---

# 迅投量化交易平台接口使用规则，此规则只在迅投xtquant接口有效

- xtquant接口数据下载默认使用**download_history_data2**
- 迅投字典链接：https://dict.thinktrader.net/dictionary/
- 迅投xtquant行情文档链接：@https://dict.thinktrader.net/nativeApi/xtdata.html
- 迅投xtquant交易文档链接：@https://dict.thinktrader.net/nativeApi/xttrader.html
