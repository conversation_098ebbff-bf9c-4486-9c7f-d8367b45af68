# Memory Dashboard Module
"""
内存监控仪表板 - 提供内存使用情况的可视化界面
"""

import time
import threading
from typing import Dict, List, Optional, Callable
import datetime

# 导入内存管理组件
from .memory_manager import MemoryManager

# 导入logger
from ..logger import get_unified_logger, LogTarget

# 获取logger实例
logger = get_unified_logger("memory_dashboard")


class MemoryDashboard:
    """
    内存监控仪表板类
    
    提供内存使用情况的可视化界面，支持实时监控和历史数据查看
    """
    
    def __init__(
        self,
        memory_manager: MemoryManager,
        update_interval: float = 1.0,
        history_size: int = 3600,  # 默认保存1小时的数据(按秒)
        auto_start: bool = False
    ):
        """
        初始化内存监控仪表板
        
        Args:
            memory_manager: 内存管理器实例
            update_interval: 更新间隔(秒)，默认1秒
            history_size: 历史数据大小，默认3600条(1小时，按秒)
            auto_start: 是否自动启动监控，默认False
        """
        # 内存管理器
        self._memory_manager = memory_manager
        
        # 配置
        self._update_interval = max(0.1, update_interval)  # 最小0.1秒
        self._history_size = max(10, history_size)  # 最小10条
        
        # 历史数据
        self._memory_history: List[Dict] = []
        self._process_history: List[Dict] = []
        self._timestamps: List[float] = []
        
        # 监控状态
        self._is_monitoring = False
        self._monitor_thread = None
        
        # 事件回调
        self._on_update: Optional[Callable[[Dict], None]] = None
        self._on_alert: Optional[Callable[[str, Dict], None]] = None
        
        # 自动启动
        if auto_start:
            self.start()
        
        logger.debug(LogTarget.FILE, "内存监控仪表板初始化完成")
    
    def start(self) -> None:
        """启动监控"""
        if self._is_monitoring:
            logger.warning(LogTarget.FILE, "监控仪表板已在运行中")
            return
        
        # 确保内存管理器已启动监控
        if not self._memory_manager.monitor._is_monitoring:
            self._memory_manager.start_monitoring()
        
        # 创建监控线程
        self._monitor_thread = threading.Thread(
            target=self._monitoring_thread,
            name="MemoryDashboardThread",
            daemon=True
        )
        
        # 启动线程
        self._is_monitoring = True
        self._monitor_thread.start()
        
        logger.info(LogTarget.FILE, "内存监控仪表板已启动")
    
    def stop(self) -> None:
        """停止监控"""
        if not self._is_monitoring:
            return
        
        # 停止监控
        self._is_monitoring = False
        
        # 等待监控线程结束
        if self._monitor_thread and self._monitor_thread.is_alive():
            self._monitor_thread.join(timeout=1.0)
        
        logger.info(LogTarget.FILE, "内存监控仪表板已停止")
    
    def _monitoring_thread(self) -> None:
        """监控线程函数"""
        while self._is_monitoring:
            try:
                # 获取当前内存状态
                status = self._memory_manager.get_memory_status()
                
                # 更新历史数据
                self._update_history(status)
                
                # 调用更新回调
                if self._on_update:
                    try:
                        self._on_update(status)
                    except Exception as e:
                        logger.error(LogTarget.FILE, f"执行更新回调出错: {e}")
                
                # 检查警报条件
                self._check_alerts(status)
                
                # 休眠一段时间
                time.sleep(self._update_interval)
                
            except Exception as e:
                logger.error(LogTarget.FILE, f"监控仪表板线程出错: {e}")
                time.sleep(1.0)  # 出错后短暂休眠
    
    def _update_history(self, status: Dict) -> None:
        """
        更新历史数据
        
        Args:
            status: 内存状态信息
        """
        # 添加时间戳
        timestamp = status.get('timestamp', time.time())
        self._timestamps.append(timestamp)
        
        # 添加内存数据
        self._memory_history.append(status['memory'])
        
        # 添加进程数据
        self._process_history.append(status['process'])
        
        # 限制历史数据大小
        if len(self._timestamps) > self._history_size:
            self._timestamps.pop(0)
            self._memory_history.pop(0)
            self._process_history.pop(0)
    
    def _check_alerts(self, status: Dict) -> None:
        """
        检查警报条件
        
        Args:
            status: 内存状态信息
        """
        # 获取内存使用率
        memory_percent = status['memory']['percent']
        
        # 获取阈值状态
        threshold_state = status['thresholds']['current_state']
        
        # 检查是否需要发出警报
        alert_message = None
        
        if threshold_state == 'critical':
            alert_message = f"内存使用率达到临界值: {memory_percent:.1f}%"
        elif threshold_state == 'warning':
            alert_message = f"内存使用率达到警告值: {memory_percent:.1f}%"
        
        # 发送警报
        if alert_message and self._on_alert:
            try:
                self._on_alert(alert_message, status)
            except Exception as e:
                logger.error(LogTarget.FILE, f"执行警报回调出错: {e}")
    
    def get_current_status(self) -> Dict:
        """
        获取当前内存状态
        
        Returns:
            内存状态信息
        """
        return self._memory_manager.get_memory_status()
    
    def get_history_data(
        self,
        start_time: Optional[float] = None,
        end_time: Optional[float] = None
    ) -> Dict:
        """
        获取历史数据
        
        Args:
            start_time: 开始时间戳，默认为最早的记录
            end_time: 结束时间戳，默认为最新的记录
            
        Returns:
            历史数据字典
        """
        if not self._timestamps:
            return {
                'timestamps': [],
                'memory': [],
                'process': []
            }
        
        # 设置默认时间范围
        if start_time is None:
            start_time = self._timestamps[0]
        
        if end_time is None:
            end_time = self._timestamps[-1]
        
        # 查找时间范围内的数据索引
        start_idx = 0
        end_idx = len(self._timestamps) - 1
        
        for i, ts in enumerate(self._timestamps):
            if ts >= start_time:
                start_idx = i
                break
        
        for i in range(len(self._timestamps) - 1, -1, -1):
            if self._timestamps[i] <= end_time:
                end_idx = i
                break
        
        # 提取数据
        timestamps = self._timestamps[start_idx:end_idx + 1]
        memory_data = self._memory_history[start_idx:end_idx + 1]
        process_data = self._process_history[start_idx:end_idx + 1]
        
        return {
            'timestamps': timestamps,
            'memory': memory_data,
            'process': process_data
        }
    
    def get_summary(self) -> Dict:
        """
        获取内存使用摘要
        
        Returns:
            内存使用摘要字典
        """
        if not self._memory_history:
            return {
                'current': None,
                'min': None,
                'max': None,
                'avg': None,
                'start': None,
                'end': None,
                'duration': 0
            }
        
        # 计算内存使用率统计
        memory_percents = [m['percent'] for m in self._memory_history]
        
        # 获取当前状态
        current = self._memory_history[-1]['percent']
        
        # 计算最小、最大、平均值
        min_value = min(memory_percents)
        max_value = max(memory_percents)
        avg_value = sum(memory_percents) / len(memory_percents)
        
        # 计算时间范围
        start_time = self._timestamps[0]
        end_time = self._timestamps[-1]
        duration = end_time - start_time
        
        return {
            'current': current,
            'min': min_value,
            'max': max_value,
            'avg': avg_value,
            'start': start_time,
            'end': end_time,
            'duration': duration
        }
    
    def register_update_callback(self, callback: Callable[[Dict], None]) -> None:
        """
        注册更新回调函数
        
        Args:
            callback: 回调函数，接收内存状态字典
        """
        self._on_update = callback
    
    def register_alert_callback(self, callback: Callable[[str, Dict], None]) -> None:
        """
        注册警报回调函数
        
        Args:
            callback: 回调函数，接收警报消息和内存状态字典
        """
        self._on_alert = callback
    
    def set_update_interval(self, seconds: float) -> None:
        """
        设置更新间隔
        
        Args:
            seconds: 更新间隔(秒)
        """
        if seconds < 0.1:
            logger.warning(LogTarget.FILE, f"更新间隔无效: {seconds}，使用默认值 0.1")
            seconds = 0.1
        
        self._update_interval = seconds
        logger.debug(LogTarget.FILE, f"更新间隔已设置为: {seconds}秒")
    
    def set_history_size(self, size: int) -> None:
        """
        设置历史数据大小
        
        Args:
            size: 历史数据大小
        """
        if size < 10:
            logger.warning(LogTarget.FILE, f"历史数据大小无效: {size}，使用默认值 10")
            size = 10
        
        # 更新历史数据大小
        self._history_size = size
        
        # 裁剪现有数据
        if len(self._timestamps) > size:
            self._timestamps = self._timestamps[-size:]
            self._memory_history = self._memory_history[-size:]
            self._process_history = self._process_history[-size:]
        
        logger.debug(LogTarget.FILE, f"历史数据大小已设置为: {size}")
    
    def generate_report(self, include_history: bool = True) -> Dict:
        """
        生成内存使用报告
        
        Args:
            include_history: 是否包含历史数据，默认True
            
        Returns:
            报告字典
        """
        # 获取当前状态
        current_status = self.get_current_status()
        
        # 获取摘要
        summary = self.get_summary()
        
        # 格式化时间
        if isinstance(summary['start'], datetime.datetime):
            start_time_str = summary['start'].strftime('%Y-%m-%d %H:%M:%S')
        else:
            start_time_str = datetime.datetime.fromtimestamp(
                summary['start']
            ).strftime('%Y-%m-%d %H:%M:%S') if summary['start'] else 'N/A'
        
        if isinstance(summary['end'], datetime.datetime):
            end_time_str = summary['end'].strftime('%Y-%m-%d %H:%M:%S')
        else:
            end_time_str = datetime.datetime.fromtimestamp(
                summary['end']
            ).strftime('%Y-%m-%d %H:%M:%S') if summary['end'] else 'N/A'
        
        # 创建报告
        report = {
            'timestamp': time.time(),
            'date': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'current_status': current_status,
            'summary': {
                'current_percent': summary['current'],
                'min_percent': summary['min'],
                'max_percent': summary['max'],
                'avg_percent': summary['avg'],
                'start_time': start_time_str,
                'end_time': end_time_str,
                'duration_seconds': summary['duration'],
                'duration_formatted': self._format_duration(summary['duration'])
            },
            'thresholds': {
                'warning': current_status['thresholds']['warning'],
                'critical': current_status['thresholds']['critical'],
                'current_state': current_status['thresholds']['current_state']
            }
        }
        
        # 添加历史数据
        if include_history:
            history_data = self.get_history_data()
            
            # 简化历史数据，最多包含100个点
            if len(history_data['timestamps']) > 100:
                step = len(history_data['timestamps']) // 100
                
                report['history'] = {
                    'timestamps': history_data['timestamps'][::step],
                    'memory_percent': [m['percent'] for m in history_data['memory']][::step],
                    'process_memory_mb': [
                        p['rss'] / (1024 * 1024) 
                        for p in history_data['process']
                    ][::step]
                }
            else:
                report['history'] = {
                    'timestamps': history_data['timestamps'],
                    'memory_percent': [m['percent'] for m in history_data['memory']],
                    'process_memory_mb': [
                        p['rss'] / (1024 * 1024) 
                        for p in history_data['process']
                    ]
                }
        
        return report
    
    def _format_duration(self, seconds_or_delta) -> str:
        """
        格式化持续时间
        
        Args:
            seconds_or_delta: 持续时间(秒或timedelta对象)
            
        Returns:
            格式化后的持续时间字符串
        """
        # 处理timedelta对象
        if isinstance(seconds_or_delta, datetime.timedelta):
            seconds = seconds_or_delta.total_seconds()
        else:
            seconds = seconds_or_delta
            
        if seconds < 60:
            return f"{seconds:.1f}秒"
        elif seconds < 3600:
            minutes = seconds / 60
            return f"{minutes:.1f}分钟"
        else:
            hours = seconds / 3600
            return f"{hours:.1f}小时"
    
    def print_status(self) -> None:
        """打印当前内存状态"""
        status = self.get_current_status()
        summary = self.get_summary()
        
        print("\n===== 内存监控状态 =====")
        print(f"当前时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"系统内存: {status['memory']['total'] / (1024**3):.2f}GB 总计")
        print(f"内存使用: {status['memory']['used'] / (1024**3):.2f}GB "
              f"({status['memory']['percent']:.1f}%)")
        print(f"可用内存: {status['memory']['available'] / (1024**3):.2f}GB "
              f"({100 - status['memory']['percent']:.1f}%)")
        print(f"进程内存: {status['process']['rss'] / (1024**3):.2f}GB")
        print(f"阈值状态: {status['thresholds']['current_state']}")
        
        if isinstance(summary['duration'], datetime.timedelta) and summary['duration'].total_seconds() > 0:
            print("\n===== 监控统计 =====")
            print(f"监控时长: {self._format_duration(summary['duration'])}")
            print(f"最大使用率: {summary['max']:.1f}%")
            print(f"最小使用率: {summary['min']:.1f}%")
            print(f"平均使用率: {summary['avg']:.1f}%")
        
        print("\n===== 阈值设置 =====")
        print(f"警告阈值: {status['thresholds']['warning']}%")
        print(f"临界阈值: {status['thresholds']['critical']}%")
        print("=====================\n")
    
    def __enter__(self):
        """上下文管理器入口"""
        self.start()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器退出"""
        self.stop()
        return False  # 不抑制异常