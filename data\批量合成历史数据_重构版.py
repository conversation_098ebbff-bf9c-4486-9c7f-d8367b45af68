#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
批量合成历史数据模块 - 重构版

基于新的数据处理管道架构重构的批量合成脚本。
使用数据流程编排器实现复权和周期转换的完全分离。

重构特性：
- 使用DataFlowOrchestrator进行流程编排
- 复权和周期转换功能完全分离
- 基于独立管道的模块化架构
- 保持原有的配置接口和用户体验
- 遵循单一职责原则和DRY原则

使用方式：
1. 配置股票列表、目标周期、复权类型
2. 系统自动使用管道编排器执行完整流程
3. 支持复权 + 周期转换的组合处理
4. 支持仅复权或仅周期转换的独立处理

作者: AI Assistant
创建时间: 2025-08-05
版本: 2.0.0 (重构版)
"""

import os
import sys
import datetime
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from data.processing.data_flow_orchestrator import create_data_flow_orchestrator
from utils.logger import get_unified_logger, LogTarget
from config.settings import DATA_ROOT
from utils.text_parser import parse_stock_code_input
from typing import List, Dict, Any

logger = get_unified_logger(__name__)


def get_optimal_source_period(target_period: str) -> str:
    """
    为目标周期推荐最优源周期
    
    Args:
        target_period: 目标周期，如 '3m', '2h', '1d' 等
    
    Returns:
        str: 推荐的源周期
    """
    # 特殊处理1分钟周期，强制使用tick作为源周期
    if target_period == '1m':
        return 'tick'
    
    # 其他周期使用1分钟作为源周期
    return '1m'


def generate_synthesis_configs(target_periods: List[str],
                             start_date: str = "",
                             end_date: str = "",
                             show_data: bool = True,
                             dividend_type: str = "none") -> List[Dict[str, Any]]:
    """
    根据目标周期列表自动生成合成配置
    
    Args:
        target_periods: 目标周期列表
        start_date: 开始日期
        end_date: 结束日期
        show_data: 是否显示数据
        dividend_type: 复权类型
    
    Returns:
        List[Dict]: 生成的合成配置列表
    """
    configs = []
    
    for target_period in target_periods:
        source_period = get_optimal_source_period(target_period)
        
        config = {
            "source_period": source_period,
            "target_period": target_period,
            "start_date": start_date,
            "end_date": end_date,
            "show_data": show_data,
            "dividend_type": dividend_type
        }
        
        configs.append(config)
        logger.debug(f"生成配置: {source_period} -> {target_period} ({dividend_type})")
    
    return configs


def read_stock_list_from_file(stock_list_file: str) -> List[str]:
    """从文件读取股票列表"""
    try:
        if not os.path.exists(stock_list_file):
            logger.warning(f"股票列表文件不存在: {stock_list_file}")
            return []
        
        with open(stock_list_file, 'r', encoding='utf-8') as f:
            stock_lines = f.readlines()
        
        stocks = []
        for line in stock_lines:
            line = line.strip()
            if line and not line.startswith('#'):
                parsed_codes = parse_stock_code_input(line)
                stocks.extend(parsed_codes)
        
        logger.info(f"从文件读取到 {len(stocks)} 只股票")
        return stocks
        
    except Exception as e:
        logger.error(f"读取股票列表文件失败: {e}")
        return []


def main():
    """主函数"""
    # ==================== 股票列表配置 ====================
    
    # 尝试从文件读取股票列表
    stock_list_file = os.path.join(DATA_ROOT, "stock_list.txt")
    stock_list = read_stock_list_from_file(stock_list_file)
    
    # 如果文件不存在或为空，使用默认列表
    if not stock_list:
        stock_list = ['000001.SZ']  # 默认使用平安银行作为示例
        print(f"📋 使用默认股票列表: {stock_list}")
        print(f"💡 提示: 可创建 {stock_list_file} 文件来自定义股票列表")
    
    # ==================== 多周期合成配置 ====================
    
    # 🎯 用户配置区域：只需修改此处的目标周期列表
    target_periods = [
        "1m",    # tick → 1分钟
        # "3m",    # 1m → 3分钟
        # "5m",    # 1m → 5分钟
        # "15m",   # 1m → 15分钟
        # "30m",   # 1m → 30分钟
        # "1h",    # 1m → 1小时
    ]
    
    # ==================== 复权配置 ====================
    
    # 🎯 复权类型配置：选择数据复权方式
    # - "none": 原始数据（不进行复权处理）
    # - "front": 前复权（向前调整价格，保持最新价格不变）
    # - "back": 后复权（向后调整价格，保持历史价格不变）
    dividend_type = "front"  # 默认使用前复权数据
    
    # ==================== 时间范围配置 ====================
    
    start_date = "20250715145600"
    end_date = "20250716093300"
    
    # ==================== 处理模式配置 ====================
    
    # 处理模式选择：
    # - "full": 完整流程（复权 + 周期转换）
    # - "adjustment_only": 仅复权处理
    # - "synthesis_only": 仅周期转换（使用原始数据）
    processing_mode = "full"
    
    # ==================== 开始批量处理 ====================
    
    print(f"\n🚀 开始批量数据处理 - 重构版")
    print(f"📈 处理模式: {processing_mode}")
    print(f"📋 股票数量: {len(stock_list)}")
    print(f"📊 目标周期: {target_periods}")
    print(f"🔧 复权类型: {dividend_type}")
    print(f"⏰ 时间范围: {start_date} ~ {end_date}")
    
    # 创建数据流程编排器
    orchestrator = create_data_flow_orchestrator(enable_parallel=True)
    
    # 生成处理配置
    synthesis_configs = generate_synthesis_configs(
        target_periods=target_periods,
        start_date=start_date,
        end_date=end_date,
        show_data=True,
        dividend_type=dividend_type
    )
    
    print(f"✅ 配置生成完成，共 {len(synthesis_configs)} 个处理配置")
    
    # 开始批量处理
    total_configs = len(synthesis_configs)
    total_start_time = time.time()
    
    overall_stats = {
        "total_configs": total_configs,
        "successful_configs": 0,
        "failed_configs": 0,
        "total_symbols": len(stock_list),
        "processing_mode": processing_mode
    }
    
    for i, config in enumerate(synthesis_configs, 1):
        source_period = config["source_period"]
        target_period = config["target_period"]
        config_dividend_type = config["dividend_type"]
        
        print(f"\n{'='*60}")
        print(f"📈 [{i}/{total_configs}] 处理配置: {source_period} -> {target_period} ({config_dividend_type})")
        print(f"📊 股票数量: {len(stock_list)}")
        print(f"⏰ 时间范围: {start_date} ~ {end_date}")
        print(f"{'='*60}")
        
        config_start_time = time.time()
        
        try:
            # 根据处理模式选择执行方式
            if processing_mode == "full":
                # 完整流程：复权 + 周期转换
                result = orchestrator.execute_full_pipeline(
                    symbols=stock_list,
                    source_period=source_period,
                    target_period=target_period,
                    dividend_type=config_dividend_type,
                    data_root=DATA_ROOT,
                    start_time=start_date,
                    end_time=end_date,
                    show_data=True,
                    validate_results=True
                )
            elif processing_mode == "adjustment_only":
                # 仅复权处理
                result = orchestrator.execute_adjustment_only(
                    symbols=stock_list,
                    period=target_period,  # 对目标周期数据进行复权
                    dividend_type=config_dividend_type,
                    data_root=DATA_ROOT,
                    start_time=start_date,
                    end_time=end_date
                )
            elif processing_mode == "synthesis_only":
                # 仅周期转换（使用原始数据）
                result = orchestrator.execute_synthesis_only(
                    symbols=stock_list,
                    source_period=source_period,
                    target_period=target_period,
                    data_root=DATA_ROOT,
                    start_time=start_date,
                    end_time=end_date
                )
            else:
                raise ValueError(f"不支持的处理模式: {processing_mode}")
            
            # 处理结果统计
            config_end_time = time.time()
            config_duration = config_end_time - config_start_time
            
            if result.success:
                overall_stats["successful_configs"] += 1
                print(f"✅ 配置处理成功，耗时: {config_duration:.2f}秒")
                logger.info(f"配置处理成功: {source_period} -> {target_period} ({config_dividend_type})")
            else:
                overall_stats["failed_configs"] += 1
                print(f"❌ 配置处理失败: {result.error_message}")
                logger.error(f"配置处理失败: {source_period} -> {target_period} - {result.error_message}")
            
            print(f"📈 总体进度: {i}/{total_configs} 配置已完成")
            
        except Exception as e:
            overall_stats["failed_configs"] += 1
            print(f"❌ 配置处理异常: {e}")
            logger.error(f"配置处理异常: {source_period} -> {target_period} - {e}")
        
        # 配置间延时
        if i < total_configs:
            print(f"⏳ 等待3秒后继续处理下一个配置...")
            time.sleep(3)
    
    # 处理完成总结
    total_end_time = time.time()
    total_duration = total_end_time - total_start_time
    
    print(f"\n🎉 {'='*60}")
    print(f"🎉 批量数据处理完成！")
    print(f"🎉 {'='*60}")
    
    print(f"\n📊 处理统计:")
    print(f"   ⏱️  总耗时: {total_duration:.2f}秒 ({total_duration/60:.1f}分钟)")
    print(f"   📈 处理模式: {processing_mode}")
    print(f"   📊 处理配置: {overall_stats['total_configs']}")
    print(f"   ✅ 成功配置: {overall_stats['successful_configs']}")
    print(f"   ❌ 失败配置: {overall_stats['failed_configs']}")
    print(f"   📋 处理股票: {overall_stats['total_symbols']} 只")
    
    if overall_stats['total_configs'] > 0:
        success_rate = (overall_stats['successful_configs'] / overall_stats['total_configs']) * 100
        print(f"   📈 成功率: {success_rate:.1f}%")
    
    # 显示流程统计信息
    flow_stats = orchestrator.get_flow_statistics()
    print(f"\n📋 流程统计:")
    print(f"   🔄 总流程数: {flow_stats['total_flows']}")
    print(f"   ✅ 成功流程: {flow_stats['successful_flows']}")
    print(f"   ❌ 失败流程: {flow_stats['failed_flows']}")
    
    print(f"\n🎉 {'='*60}")
    print(f"🎉 批量处理任务全部完成！")
    print(f"📋 详细日志请查看日志文件")
    print(f"🎉 {'='*60}")


if __name__ == "__main__":
    main()
