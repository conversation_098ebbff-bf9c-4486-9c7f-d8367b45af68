#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
工具模块

包含各种实用工具功能，如数据处理、日志、路径工具等
"""

# 导入需要暴露的函数和类
from .data_display.formatting import (
    get_display_width,
    format_column,
    dataframe_to_text_table,
    write_dataframe_to_file,
    print_progress_bar,
    create_console_table,
    truncate_text,
    print_boxed_text,
    format_number,
    format_percentage,
    print_dict_as_table,
    animate_text,
    highlight_text,
    create_text_box,
    format_float_smart
)
from .logger import get_unified_logger as get_logger
from .logger.decorators import log_execution_time
from .time_formatter.conversion import (
    timestamp_to_datetime,
    datetime_to_timestamp
)
from .time_formatter.formatting import (
    format_datetime,
    format_time_index,
    get_date_format_for_period,
    format_datetime_with_period
)
from .time_formatter.parsing import parse_datetime

# 导入内存管理模块
from .memory_manager import (
    MemoryMonitor,
    ThresholdManager,
    ResourceOptimizer,
    <PERSON><PERSON><PERSON>ontroller,
    MemoryManager,
    MemoryDashboard
)

# 项目根目录添加到Python路径的代码可以在各子模块中实现，避免过早导入

__version__ = "0.1.0"

# 定义公开接口
__all__ = [
    # data_display 模块
    "get_display_width",
    "format_column",
    "dataframe_to_text_table",
    "write_dataframe_to_file",
    "print_progress_bar",
    "create_console_table",
    "truncate_text",
    "print_boxed_text",
    "format_number",
    "format_percentage",
    "print_dict_as_table",
    "animate_text",
    "highlight_text",
    "create_text_box",
    "format_float_smart",
    # logger 模块
    "get_logger",
    "log_execution_time",
    # time_formatter 模块
    "timestamp_to_datetime",
    "datetime_to_timestamp",
    "format_datetime",
    "parse_datetime",
    "format_time_index",
    "get_date_format_for_period",
    "format_datetime_with_period",
    # memory_manager 模块
    "MemoryMonitor",
    "ThresholdManager",
    "ResourceOptimizer",
    "BatchController",
    "MemoryManager",
    "MemoryDashboard"
]
