#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
工厂模式实现工具模块

提供通用的工厂模式实现，支持对象的注册、创建和管理，
适用于策略、数据源、算法等多种类型对象的统一创建接口。
"""

import os
import sys
import importlib
import inspect
from typing import Any, Callable, Dict, List, Optional, Type, TypeVar, Union, Set, Generic

# 将项目根目录添加到Python路径
root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, root_path)


# 定义泛型类型变量，用于表示工厂创建的对象类型
T = TypeVar('T')


class Factory(Generic[T]):
    """
    通用工厂类

    支持对象类的注册、创建和管理，可用于创建策略、数据源、算法等。
    使用泛型参数T指定工厂创建的对象类型。
    """
    
    def __init__(self, base_type: Optional[Type[T]] = None):
        """
        初始化工厂

        Args:
            base_type: 基础类型，所有注册的类必须是该类型的子类
        """
        self._registry: Dict[str, Type[T]] = {}
        self._aliases: Dict[str, str] = {}  # 别名映射到实际名称
        self._descriptions: Dict[str, str] = {}  # 类名到描述的映射
        self._base_type = base_type
    
    def register(self, cls: Type[T], name: Optional[str] = None, 
                aliases: Optional[List[str]] = None, 
                description: Optional[str] = None) -> Type[T]:
        """
        注册一个类到工厂

        Args:
            cls: 要注册的类
            name: 注册名称，默认为类名
            aliases: 别名列表
            description: 类描述

        Returns:
            注册的类，用于装饰器使用

        Raises:
            TypeError: 如果cls不是base_type的子类
            ValueError: 如果name已被注册
        """
        # 验证类型
        if self._base_type and not issubclass(cls, self._base_type):
            raise TypeError(f"类 {cls.__name__} 不是 {self._base_type.__name__} 的子类")
        
        # 使用类名作为默认名称
        if name is None:
            name = cls.__name__
        
        # 检查名称是否已注册
        if name in self._registry:
            raise ValueError(f"名称 '{name}' 已被注册")
        
        # 注册类
        self._registry[name] = cls
        
        # 注册别名
        if aliases:
            for alias in aliases:
                if alias in self._aliases or alias in self._registry:
                    raise ValueError(f"别名 '{alias}' 已被注册")
                self._aliases[alias] = name
        
        # 存储描述
        if description:
            self._descriptions[name] = description
        elif cls.__doc__:
            # 使用类文档字符串的第一行作为描述
            self._descriptions[name] = cls.__doc__.split('\n')[0].strip()
        else:
            self._descriptions[name] = f"{name} - 无描述"
        
        return cls
    
    def unregister(self, name: str) -> None:
        """
        从工厂中注销一个类

        Args:
            name: 类的注册名称或别名

        Raises:
            KeyError: 如果name未注册
        """
        # 检查是否是别名
        if name in self._aliases:
            actual_name = self._aliases.pop(name)
            # 检查是否有其他别名指向相同的类
            other_aliases = [alias for alias, n in self._aliases.items() if n == actual_name]
            if not other_aliases:
                self._registry.pop(actual_name, None)
                self._descriptions.pop(actual_name, None)
        elif name in self._registry:
            # 移除所有指向这个类的别名
            aliases_to_remove = [alias for alias, n in self._aliases.items() if n == name]
            for alias in aliases_to_remove:
                self._aliases.pop(alias)
            self._registry.pop(name)
            self._descriptions.pop(name, None)
        else:
            raise KeyError(f"名称 '{name}' 未注册")
    
    def create(self, name: str, *args, **kwargs) -> T:
        """
        创建对象实例

        Args:
            name: 类的注册名称或别名
            *args: 传递给类构造函数的位置参数
            **kwargs: 传递给类构造函数的关键字参数

        Returns:
            创建的对象实例

        Raises:
            KeyError: 如果name未注册
        """
        # 如果是别名，获取实际名称
        if name in self._aliases:
            name = self._aliases[name]
        
        # 检查名称是否注册
        if name not in self._registry:
            raise KeyError(f"名称 '{name}' 未注册")
        
        # 创建实例
        cls = self._registry[name]
        return cls(*args, **kwargs)
    
    def list_registered(self) -> List[str]:
        """
        获取所有注册的类名称列表

        Returns:
            注册的类名称列表
        """
        return list(self._registry.keys())
    
    def list_with_descriptions(self) -> Dict[str, str]:
        """
        获取所有注册的类及其描述

        Returns:
            类名到描述的字典
        """
        return self._descriptions.copy()
    
    def get_aliases(self, name: str) -> List[str]:
        """
        获取类的所有别名

        Args:
            name: 类的注册名称

        Returns:
            别名列表

        Raises:
            KeyError: 如果name未注册
        """
        if name not in self._registry:
            raise KeyError(f"名称 '{name}' 未注册")
        
        return [alias for alias, n in self._aliases.items() if n == name]
    
    def is_registered(self, name: str) -> bool:
        """
        检查名称是否已注册

        Args:
            name: 类的名称或别名

        Returns:
            如果名称已注册，则为True
        """
        return name in self._registry or name in self._aliases
    
    def get_class(self, name: str) -> Type[T]:
        """
        获取注册的类

        Args:
            name: 类的注册名称或别名

        Returns:
            注册的类

        Raises:
            KeyError: 如果name未注册
        """
        # 如果是别名，获取实际名称
        if name in self._aliases:
            name = self._aliases[name]
        
        # 检查名称是否注册
        if name not in self._registry:
            raise KeyError(f"名称 '{name}' 未注册")
        
        return self._registry[name]
    
    def as_decorator(self, name: Optional[str] = None, 
                    aliases: Optional[List[str]] = None,
                    description: Optional[str] = None) -> Callable[[Type[T]], Type[T]]:
        """
        返回一个装饰器函数，用于注册类

        Args:
            name: 注册名称，默认为类名
            aliases: 别名列表
            description: 类描述

        Returns:
            装饰器函数
        """
        def decorator(cls: Type[T]) -> Type[T]:
            return self.register(cls, name, aliases, description)
        return decorator


class AutoDiscoveringFactory(Factory[T]):
    """
    支持自动发现的工厂类

    能够自动从指定目录中发现并注册类。
    """
    
    def __init__(self, base_type: Type[T], base_package: str = "", 
                 module_pattern: str = "*.py", recursive: bool = True):
        """
        初始化自动发现工厂

        Args:
            base_type: 基础类型，所有注册的类必须是该类型的子类
            base_package: 基础包名，用于发现模块
            module_pattern: 模块文件名模式
            recursive: 是否递归搜索子目录
        """
        super().__init__(base_type)
        self._base_package = base_package
        self._module_pattern = module_pattern
        self._recursive = recursive
        self._discovered = False
    
    def discover(self) -> None:
        """
        发现并注册符合条件的类
        """
        if self._discovered:
            return
        
        import glob
        import importlib.util
        
        # 确定基础目录
        if self._base_package:
            try:
                module = importlib.import_module(self._base_package)
                base_dir = os.path.dirname(module.__file__)
            except (ImportError, AttributeError):
                # 如果无法导入模块，使用相对于项目根目录的路径
                base_dir = os.path.join(root_path, *self._base_package.split('.'))
        else:
            base_dir = root_path
        
        # 构建搜索模式
        pattern = os.path.join(base_dir, self._module_pattern)
        
        # 扫描文件
        for file_path in glob.glob(pattern, recursive=self._recursive):
            if os.path.isfile(file_path) and file_path.endswith('.py') and not os.path.basename(file_path).startswith('_'):
                self._load_module_from_file(file_path, base_dir)
        
        self._discovered = True
    
    def _load_module_from_file(self, file_path: str, base_dir: str) -> None:
        """
        从文件加载模块并注册符合条件的类

        Args:
            file_path: 模块文件路径
            base_dir: 基础目录
        """
        # 计算模块名
        rel_path = os.path.relpath(file_path, base_dir)
        module_name = os.path.splitext(rel_path)[0].replace(os.path.sep, '.')
        
        if self._base_package:
            module_name = f"{self._base_package}.{module_name}"
        
        try:
            # 尝试导入模块
            module = importlib.import_module(module_name)
            
            # 遍历模块中的所有对象
            for name, obj in inspect.getmembers(module):
                # 检查是否是类、是否是基类的子类、是否定义在当前模块中
                if (inspect.isclass(obj) and issubclass(obj, self._base_type) and 
                    obj.__module__ == module.__name__ and obj is not self._base_type):
                    # 自动注册
                    if not self.is_registered(name):
                        self.register(obj)
        except (ImportError, AttributeError) as e:
            print(f"加载模块 {module_name} 时出错: {str(e)}")
    
    def create(self, name: str, *args, **kwargs) -> T:
        """
        创建对象实例，如果尚未发现类，则先进行发现

        Args:
            name: 类的注册名称或别名
            *args: 传递给类构造函数的位置参数
            **kwargs: 传递给类构造函数的关键字参数

        Returns:
            创建的对象实例
        """
        if not self._discovered:
            self.discover()
        return super().create(name, *args, **kwargs)
    
    def list_registered(self) -> List[str]:
        """
        获取所有注册的类名称列表，如果尚未发现类，则先进行发现

        Returns:
            注册的类名称列表
        """
        if not self._discovered:
            self.discover()
        return super().list_registered()


# 单例工厂类型变量
S = TypeVar('S')


class SingletonFactory(Factory[S]):
    """
    单例工厂类
    
    创建单例对象的工厂，确保对于每个注册的类只创建一个实例。
    """
    
    def __init__(self, base_type: Optional[Type[S]] = None):
        """
        初始化单例工厂

        Args:
            base_type: 基础类型，所有注册的类必须是该类型的子类
        """
        super().__init__(base_type)
        self._instances: Dict[str, S] = {}
    
    def create(self, name: str, *args, **kwargs) -> S:
        """
        创建或获取对象实例

        如果对象实例已经存在，则返回现有实例，否则创建新实例

        Args:
            name: 类的注册名称或别名
            *args: 传递给类构造函数的位置参数(仅在首次创建时使用)
            **kwargs: 传递给类构造函数的关键字参数(仅在首次创建时使用)

        Returns:
            单例对象实例
        """
        # 如果是别名，获取实际名称
        if name in self._aliases:
            name = self._aliases[name]
        
        # 检查是否已有实例
        if name in self._instances:
            return self._instances[name]
        
        # 创建新实例
        instance = super().create(name, *args, **kwargs)
        self._instances[name] = instance
        return instance
    
    def reset_instance(self, name: str) -> None:
        """
        重置特定名称的单例实例

        Args:
            name: 类的注册名称或别名

        Raises:
            KeyError: 如果name未注册
        """
        # 如果是别名，获取实际名称
        if name in self._aliases:
            name = self._aliases[name]
        
        # 检查名称是否注册
        if name not in self._registry:
            raise KeyError(f"名称 '{name}' 未注册")
        
        # 删除实例
        if name in self._instances:
            del self._instances[name]
    
    def reset_all(self) -> None:
        """
        重置所有单例实例
        """
        self._instances.clear()


class StrategyFactory(Factory[T]):
    """
    策略工厂类
    
    专用于创建各种策略对象，支持按类别组织策略。
    """
    
    def __init__(self, base_type: Type[T]):
        """
        初始化策略工厂

        Args:
            base_type: 策略基类类型
        """
        super().__init__(base_type)
        self._categories: Dict[str, Set[str]] = {}  # 类别到策略名称的映射
    
    def register(self, cls: Type[T], name: Optional[str] = None, 
                aliases: Optional[List[str]] = None, 
                description: Optional[str] = None,
                category: Optional[str] = "default") -> Type[T]:
        """
        注册一个策略类

        Args:
            cls: 要注册的策略类
            name: 注册名称，默认为类名
            aliases: 别名列表
            description: 策略描述
            category: 策略类别

        Returns:
            注册的策略类
        """
        if category is None:
            category = "default"
        
        # 注册类
        registered_cls = super().register(cls, name, aliases, description)
        
        # 记录类别
        actual_name = name if name is not None else cls.__name__
        if category not in self._categories:
            self._categories[category] = set()
        self._categories[category].add(actual_name)
        
        return registered_cls
    
    def unregister(self, name: str) -> None:
        """
        注销一个策略类

        Args:
            name: 策略类的注册名称或别名
        """
        # 查找实际名称
        actual_name = name
        if name in self._aliases:
            actual_name = self._aliases[name]
        
        # 从类别中删除
        for category, strategies in self._categories.items():
            if actual_name in strategies:
                strategies.remove(actual_name)
                if not strategies:  # 如果类别为空，删除类别
                    del self._categories[category]
                break
        
        # 调用父类方法完成注销
        super().unregister(name)
    
    def get_categories(self) -> List[str]:
        """
        获取所有策略类别

        Returns:
            策略类别列表
        """
        return list(self._categories.keys())
    
    def get_strategies_by_category(self, category: str) -> List[str]:
        """
        获取指定类别的所有策略

        Args:
            category: 策略类别

        Returns:
            该类别下的策略名称列表
        """
        if category not in self._categories:
            return []
        return list(self._categories[category])
    
    def as_decorator(self, name: Optional[str] = None, 
                     aliases: Optional[List[str]] = None,
                     description: Optional[str] = None,
                     category: str = "default") -> Callable[[Type[T]], Type[T]]:
        """
        返回一个装饰器函数，用于注册策略类

        Args:
            name: 注册名称，默认为类名
            aliases: 别名列表
            description: 策略描述
            category: 策略类别

        Returns:
            装饰器函数
        """
        def decorator(cls: Type[T]) -> Type[T]:
            return self.register(cls, name, aliases, description, category)
        return decorator


def create_strategy_factory(base_type: Type[T]) -> StrategyFactory[T]:
    """
    创建策略工厂

    Args:
        base_type: 策略基类类型

    Returns:
        策略工厂实例
    """
    return StrategyFactory(base_type)


def create_singleton_factory(base_type: Optional[Type[S]] = None) -> SingletonFactory[S]:
    """
    创建单例工厂

    Args:
        base_type: 基础类型

    Returns:
        单例工厂实例
    """
    return SingletonFactory(base_type)


def create_auto_discovering_factory(base_type: Type[T], base_package: str = "") -> AutoDiscoveringFactory[T]:
    """
    创建自动发现工厂

    Args:
        base_type: 基础类型
        base_package: 基础包名

    Returns:
        自动发现工厂实例
    """
    return AutoDiscoveringFactory(base_type, base_package)


# 用于示例的策略基类
class ExampleStrategy:
    """示例策略基类"""
    
    def execute(self, *args, **kwargs):
        raise NotImplementedError("子类必须实现execute方法")


if __name__ == "__main__":
    # 测试工厂模式实现
    print("=== 工厂模式测试 ===\n")
    
    # 测试基本工厂
    print("1. 基本工厂测试:")
    factory = Factory(ExampleStrategy)
    
    # 定义两个测试策略类
    @factory.as_decorator(aliases=["strategy_a"], description="测试策略A")
    class TestStrategyA(ExampleStrategy):
        """测试策略A的文档"""
        def execute(self, value):
            return f"策略A处理: {value}"
    
    @factory.as_decorator(name="StrategyB", description="测试策略B")
    class TestStrategyB(ExampleStrategy):
        def execute(self, value):
            return f"策略B处理: {value*2}"
    
    # 查看注册的策略
    print(f"  已注册的策略: {factory.list_registered()}")
    print(f"  策略描述: {factory.list_with_descriptions()}")
    
    # 创建并测试策略
    strategy_a = factory.create("TestStrategyA")
    strategy_a_alias = factory.create("strategy_a")
    strategy_b = factory.create("StrategyB")
    
    print(f"  策略A结果: {strategy_a.execute(5)}")
    print(f"  策略A别名结果: {strategy_a_alias.execute(5)}")
    print(f"  策略B结果: {strategy_b.execute(5)}")
    print()
    
    # 测试策略工厂
    print("2. 策略工厂测试:")
    strategy_factory = create_strategy_factory(ExampleStrategy)
    
    @strategy_factory.as_decorator(category="数值处理")
    class AddStrategy(ExampleStrategy):
        """加法策略"""
        def execute(self, a, b):
            return a + b
    
    @strategy_factory.as_decorator(category="数值处理")
    class MultiplyStrategy(ExampleStrategy):
        """乘法策略"""
        def execute(self, a, b):
            return a * b
    
    @strategy_factory.as_decorator(category="文本处理")
    class ConcatStrategy(ExampleStrategy):
        """字符串连接策略"""
        def execute(self, a, b):
            return str(a) + str(b)
    
    # 查看类别和策略
    print(f"  策略类别: {strategy_factory.get_categories()}")
    for category in strategy_factory.get_categories():
        strategies = strategy_factory.get_strategies_by_category(category)
        print(f"  {category}类别的策略: {strategies}")
    
    # 创建并测试策略
    add_strategy = strategy_factory.create("AddStrategy")
    mul_strategy = strategy_factory.create("MultiplyStrategy")
    concat_strategy = strategy_factory.create("ConcatStrategy")
    
    print(f"  加法结果: {add_strategy.execute(3, 4)}")
    print(f"  乘法结果: {mul_strategy.execute(3, 4)}")
    print(f"  连接结果: {concat_strategy.execute('hello', 'world')}")
    print()
    
    # 测试单例工厂
    print("3. 单例工厂测试:")
    singleton_factory = create_singleton_factory(ExampleStrategy)
    
    @singleton_factory.as_decorator()
    class CounterStrategy(ExampleStrategy):
        """计数器策略"""
        def __init__(self):
            self.count = 0
        
        def execute(self):
            self.count += 1
            return self.count
    
    # 创建多个实例并验证单例性
    counter1 = singleton_factory.create("CounterStrategy")
    counter2 = singleton_factory.create("CounterStrategy")
    
    print(f"  计数器1第一次: {counter1.execute()}")
    print(f"  计数器2第一次: {counter2.execute()}")  # 应该是2而不是1
    print(f"  计数器1第二次: {counter1.execute()}")  # 应该是3
    
    # 重置单例
    print("  重置单例...")
    singleton_factory.reset_instance("CounterStrategy")
    counter3 = singleton_factory.create("CounterStrategy")
    print(f"  计数器3第一次: {counter3.execute()}")  # 应该是1
    print()
    
    # 测试自动发现（由于没有实际目录结构，仅演示API）
    print("4. 自动发现工厂API演示:")
    auto_factory = create_auto_discovering_factory(ExampleStrategy, "strategy")
    print(f"  自动发现API可用: {auto_factory.__class__.__name__}")
    print("  注意：由于没有实际的目录结构，无法实际演示自动发现功能")
    print()
    
    print("所有测试完成!") 