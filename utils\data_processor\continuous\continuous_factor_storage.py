#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
期货连续化因子存储管理器

负责期货连续化因子的存储、查询和管理，包括：
1. 主力合约切换记录存储
2. 连续化因子计算和存储
3. 连续化因子查询和缓存
4. 数据完整性验证

连续化因子数据结构：
- switch_date: 切换日期
- old_contract: 旧主力合约代码
- new_contract: 新主力合约代码
- old_price: 旧合约切换时价格
- new_price: 新合约切换时价格
- ratio_factor: 比例调整因子
- diff_factor: 差值调整因子
"""

import os
import pandas as pd
from pathlib import Path
from typing import Optional, Dict, Any, List
from datetime import datetime, date

from config.settings import DATA_ROOT
from utils.logger.manager import get_unified_logger
from utils.smart_time_converter import smart_to_datetime

logger = get_unified_logger(__name__)


class ContinuousFactorStorage:
    """期货连续化因子存储管理器"""
    
    def __init__(self, data_root: Optional[str] = None):
        """初始化连续化因子存储管理器
        
        Args:
            data_root: 数据根目录，默认使用配置中的DATA_ROOT
        """
        self.data_root = Path(data_root or DATA_ROOT)
        logger.info(f"连续化因子存储管理器初始化，数据根目录: {self.data_root}")
    
    def _get_factor_file_path(self, symbol: str) -> Path:
        """获取连续化因子文件路径
        
        Args:
            symbol: 期货品种代码，如rb00.SF
            
        Returns:
            连续化因子文件路径
        """
        # 解析交易所和品种代码
        if '.' not in symbol:
            raise ValueError(f"期货代码格式错误: {symbol}，应包含交易所后缀")
        
        variety, exchange = symbol.split('.')
        variety = variety.replace('00', '')  # 移除00后缀，获取品种代码
        
        # 构建文件路径: raw/SF/rb00/continuous_factors.parquet
        factor_file = self.data_root / "raw" / exchange / f"{variety}00" / "continuous_factors.parquet"
        return factor_file
    
    def save_continuous_factors(self, symbol: str, factors_data: pd.DataFrame) -> bool:
        """保存连续化因子数据
        
        Args:
            symbol: 期货品种代码
            factors_data: 连续化因子数据
            
        Returns:
            保存是否成功
        """
        try:
            factor_file = self._get_factor_file_path(symbol)
            
            # 确保目录存在
            factor_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 数据验证
            required_columns = ['switch_date', 'old_contract', 'new_contract', 
                              'old_price', 'new_price', 'ratio_factor', 'diff_factor']
            missing_columns = [col for col in required_columns if col not in factors_data.columns]
            if missing_columns:
                raise ValueError(f"连续化因子数据缺少必要列: {missing_columns}")
            
            # 保存为Parquet格式
            factors_data.to_parquet(factor_file, index=True)
            logger.info(f"连续化因子数据已保存: {factor_file}, 记录数: {len(factors_data)}")
            return True
            
        except Exception as e:
            logger.error(f"保存连续化因子数据失败: {e}")
            return False
    
    def query_continuous_factors(
        self, 
        symbol: str,
        start_date: Optional[Any] = None,
        end_date: Optional[Any] = None
    ) -> pd.DataFrame:
        """查询连续化因子数据
        
        Args:
            symbol: 期货品种代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            连续化因子数据
        """
        try:
            factor_file = self._get_factor_file_path(symbol)
            
            if not factor_file.exists():
                logger.warning(f"连续化因子文件不存在: {factor_file}")
                return pd.DataFrame()
            
            # 读取数据
            factors_data = pd.read_parquet(factor_file)
            
            if factors_data.empty:
                logger.warning(f"连续化因子数据为空: {symbol}")
                return factors_data
            
            # 时间过滤
            if start_date is not None or end_date is not None:
                factors_data = self._filter_by_date_range(factors_data, start_date, end_date)
            
            logger.debug(f"查询连续化因子数据: {symbol}, 记录数: {len(factors_data)}")
            return factors_data
            
        except Exception as e:
            logger.error(f"查询连续化因子数据失败: {e}")
            return pd.DataFrame()
    
    def _filter_by_date_range(
        self, 
        data: pd.DataFrame, 
        start_date: Optional[Any], 
        end_date: Optional[Any]
    ) -> pd.DataFrame:
        """按日期范围过滤数据
        
        Args:
            data: 原始数据
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            过滤后的数据
        """
        try:
            if 'switch_date' not in data.columns:
                logger.warning("数据中缺少switch_date列，无法进行日期过滤")
                return data
            
            filtered_data = data.copy()
            
            # 开始日期过滤
            if start_date is not None:
                start_date = smart_to_datetime(start_date)
                if start_date:
                    filtered_data = filtered_data[filtered_data['switch_date'] >= start_date]
            
            # 结束日期过滤
            if end_date is not None:
                end_date = smart_to_datetime(end_date)
                if end_date:
                    filtered_data = filtered_data[filtered_data['switch_date'] <= end_date]
            
            return filtered_data
            
        except Exception as e:
            logger.error(f"日期范围过滤失败: {e}")
            return data
    
    def get_latest_switch_info(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取最新的主力合约切换信息
        
        Args:
            symbol: 期货品种代码
            
        Returns:
            最新切换信息字典，如果没有则返回None
        """
        try:
            factors_data = self.query_continuous_factors(symbol)
            
            if factors_data.empty:
                return None
            
            # 获取最新记录
            latest_record = factors_data.iloc[-1]
            
            return {
                'switch_date': latest_record['switch_date'],
                'old_contract': latest_record['old_contract'],
                'new_contract': latest_record['new_contract'],
                'old_price': latest_record['old_price'],
                'new_price': latest_record['new_price'],
                'ratio_factor': latest_record['ratio_factor'],
                'diff_factor': latest_record['diff_factor']
            }
            
        except Exception as e:
            logger.error(f"获取最新切换信息失败: {e}")
            return None
    
    def update_continuous_factors(self, symbol: str, new_switch_record: Dict[str, Any]) -> bool:
        """更新连续化因子数据（添加新的切换记录）
        
        Args:
            symbol: 期货品种代码
            new_switch_record: 新的切换记录
            
        Returns:
            更新是否成功
        """
        try:
            # 获取现有数据
            existing_data = self.query_continuous_factors(symbol)
            
            # 创建新记录DataFrame
            new_record_df = pd.DataFrame([new_switch_record])
            
            # 合并数据
            if existing_data.empty:
                updated_data = new_record_df
            else:
                updated_data = pd.concat([existing_data, new_record_df], ignore_index=True)
            
            # 按切换日期排序
            updated_data = updated_data.sort_values('switch_date')
            
            # 保存更新后的数据
            return self.save_continuous_factors(symbol, updated_data)
            
        except Exception as e:
            logger.error(f"更新连续化因子数据失败: {e}")
            return False
