#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
表格显示功能模块

提供DataFrame和表格数据的格式化与显示功能
"""

import pandas as pd
from typing import Any, List, Optional, Callable


def format_float_smart(value: float, precision: int = 1) -> str:
    """
    智能格式化浮点数，保留必要的小数位数
    
    Args:
        value: 要格式化的浮点数
        precision: 最大精度（小数位数）
        
    Returns:
        str: 格式化后的字符串
    """
    # 处理 None 和 NaN 值
    if value is None:
        return ""
    
    # 检查值是否为NaN
    try:
        if pd.isna(value):
            return ""
    except Exception:  # 明确指定异常类型
        if isinstance(value, float) and value != value:  # NaN不等于自身
            return ""
    
    # 处理整数
    if isinstance(value, int) or (isinstance(value, float) and value.is_integer()):
        return f"{int(value)}"
    
    # 处理零值
    if abs(value) < 1e-10:
        return "0"
    
    # 根据数值范围确定小数位数
    if abs(value) < 0.01:
        return f"{value:.6f}"
    elif abs(value) < 0.1:
        return f"{value:.4f}"
    elif abs(value) < 1:
        return f"{value:.3f}"
    elif abs(value) < 100:
        return f"{value:.2f}"
    elif abs(value) < 10000:
        return f"{value:.1f}"
    else:
        return f"{int(value)}"


def dataframe_to_text_table(
    df: pd.DataFrame,
    title: Optional[str] = None,
    max_rows: Optional[int] = None,
    max_cols: Optional[int] = None,
    include_index: bool = True,
    float_precision: int = 1,
    smart_float: bool = True,
    format_time_columns: bool = True,
    format_integer: bool = True,
    list_format: str = 'full',
    raw_display: bool = False
) -> str:
    """
    将DataFrame转换为格式化的文本表格
    
    Args:
        df: 要显示的DataFrame
        title: 表格标题
        max_rows: 最大显示行数
        max_cols: 最大显示列数
        include_index: 是否包含索引列
        float_precision: 浮点数精度
        smart_float: 是否智能格式化浮点数
        format_time_columns: 是否自动识别和格式化时间列
        format_integer: 是否以千位分隔符格式化整数列
        list_format: 列表显示方式
        raw_display: 是否完全保持原始数据显示
        
    Returns:
        str: 格式化后的文本表格
    """
    # 处理空DataFrame
    if df is None or df.empty:
        result = "空表格"
        if title:
            result = f"--- {title} ---\n{result}"
        return result
    
    # 如果raw_display=True，直接使用DataFrame的原始字符串表示
    if raw_display:
        with pd.option_context('display.max_columns', None, 'display.width', None):
            result = str(df)
            if title:
                result = f"--- {title} ---\n{result}"
            return result
    
    # 创建DataFrame的副本，避免修改原始数据
    display_df = df.copy()
    
    # 截取显示行数和列数
    if max_rows is not None and len(display_df) > max_rows:
        half_rows = max_rows // 2
        if half_rows > 0:
            # 显示前半和后半的行
            display_df = pd.concat([
                display_df.head(half_rows),
                pd.DataFrame({'...': ['...'] * 1}, index=[half_rows]),
                display_df.tail(half_rows)
            ])
        else:
            display_df = display_df.head(max_rows)
    
    # 处理列表类型数据
    for col in display_df.columns:
        # 检查该列的值是否包含列表类型
        contains_list = False
        for val in display_df[col].values:
            if isinstance(val, (list, tuple)):
                contains_list = True
                break
        
        if contains_list:
            if list_format == 'compact':
                # 使用更紧凑的格式显示列表
                display_df[col] = display_df[col].apply(
                    lambda x: '[' + ','.join(str(i) for i in x) + ']' 
                    if isinstance(x, (list, tuple)) else x
                )
            elif list_format == 'truncate':
                # 只显示列表的前几个元素
                display_df[col] = display_df[col].apply(
                    lambda x: '[' + ', '.join(str(i) for i in x[:3]) + 
                    (', ...]' if len(x) > 3 else ']') 
                    if isinstance(x, (list, tuple)) else x
                )
            elif list_format == 'length':
                # 只显示列表的长度
                display_df[col] = display_df[col].apply(
                    lambda x: f'[len={len(x)}]' 
                    if isinstance(x, (list, tuple)) else x
                )
    
    # 设置pandas显示选项
    def get_float_format() -> Optional[Callable]:
        """获取浮点数格式化函数"""
        if not smart_float:
            return lambda x: f"{x:.{float_precision}f}"
        return lambda x: format_float_smart(x, precision=float_precision)
    
    with pd.option_context(
        'display.max_columns', None if max_cols is None else max_cols,
        'display.width', None,
        'display.float_format', None if raw_display else get_float_format(),
        'display.max_rows', None
    ):
        # 生成表格字符串
        result = str(display_df)
        if title:
            result = f"--- {title} ---\n{result}"
        
        return result


def write_dataframe_to_file(
    df: pd.DataFrame,
    file_path: str,
    title: Optional[str] = None,
    mode: str = 'w',
    append_time: bool = False,
    **kwargs
) -> None:
    """
    将DataFrame写入文件
    
    Args:
        df: 要写入的DataFrame
        file_path: 文件路径
        title: 表格标题
        mode: 文件打开模式，'w'表示覆盖，'a'表示追加
        append_time: 是否在文件名后添加时间戳
        **kwargs: 传递给dataframe_to_text_table的其他参数
    """
    import os
    from datetime import datetime
    
    # 如果需要添加时间戳
    if append_time:
        name, ext = os.path.splitext(file_path)
        time_str = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_path = f"{name}_{time_str}{ext}"
    
    # 确保目录存在
    os.makedirs(os.path.dirname(os.path.abspath(file_path)), exist_ok=True)
    
    # 转换为文本表格
    table_text = dataframe_to_text_table(df, title=title, **kwargs)
    
    # 写入文件
    with open(file_path, mode, encoding='utf-8') as f:
        f.write(table_text + '\n')


def create_console_table(
    headers: List[str],
    rows: List[List[Any]],
    title: Optional[str] = None,
    column_align: Optional[List[str]] = None
) -> str:
    """
    创建控制台友好的文本表格
    
    Args:
        headers: 表头列表
        rows: 数据行列表
        title: 表格标题
        column_align: 列对齐方式列表，可选值为'<'(左对齐)、'>'(右对齐)、'^'(居中)
        
    Returns:
        格式化后的文本表格
    """
    # 创建临时DataFrame
    df = pd.DataFrame(rows, columns=headers)
    
    # 使用dataframe_to_text_table生成表格
    return dataframe_to_text_table(df, title=title)


def print_summary_statistics(df: pd.DataFrame, include_percentiles: bool = True) -> None:
    """
    打印DataFrame的统计摘要
    
    Args:
        df: 要统计的DataFrame
        include_percentiles: 是否包含百分位数
    """
    # 计算基本统计量
    if include_percentiles:
        percentiles = [0.1, 0.25, 0.5, 0.75, 0.9]
    else:
        percentiles = [0.25, 0.5, 0.75]
    
    stats = df.describe(include='all', percentiles=percentiles)
    
    # 转换为文本表格并打印
    print(dataframe_to_text_table(stats, include_index=True))