#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
日志装饰器功能模块

提供用于记录函数执行时间和状态的装饰器
"""

from utils.logger.manager import get_unified_logger
from utils.logger.config import LOG_LEVELS, LogTarget
import os
import sys
import time
import logging
from functools import wraps
from typing import Any, Callable, Optional

# 将项目根目录添加到Python路径
root_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, root_path)


def log_execution_time(logger: Optional[logging.Logger] = None, level: str = "info") -> Callable:
    """
    装饰器：记录函数执行时间

    Args:
        logger: 日志记录器，如果为None，将创建一个名为'performance'的记录器
        level: 日志级别，默认为info

    Returns:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            # 使用指定的logger或创建一个专门用于性能记录的logger
            perf_logger = logger or get_unified_logger('performance', enhanced=True)

            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()

            elapsed_time = end_time - start_time
            log_message = f"函数 {func.__name__} 执行时间: {elapsed_time:.4f} 秒"

            log_level = LOG_LEVELS.get(level.lower(), logging.INFO)

            if log_level == logging.DEBUG:
                perf_logger.debug(LogTarget.FILE, log_message)
            elif log_level == logging.INFO:
                perf_logger.info(LogTarget.FILE, log_message)
            elif log_level == logging.WARNING:
                perf_logger.warning(LogTarget.FILE, log_message)
            elif log_level == logging.ERROR:
                perf_logger.error(LogTarget.FILE, log_message)
            elif log_level == logging.CRITICAL:
                perf_logger.critical(LogTarget.FILE, log_message)

            return result
        return wrapper
    return decorator


def log_function_calls(logger: Optional[logging.Logger] = None, level: str = "debug") -> Callable:
    """
    装饰器：记录函数调用及其参数

    Args:
        logger: 日志记录器，如果为None，将创建一个名为'function_calls'的记录器
        level: 日志级别，默认为debug

    Returns:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            # 使用指定的logger或创建一个专门用于记录函数调用的logger
            call_logger = logger or get_unified_logger('function_calls', enhanced=True)

            # 格式化参数列表
            args_repr = [repr(arg) for arg in args]
            kwargs_repr = [f"{k}={repr(v)}" for k, v in kwargs.items()]
            signature = ", ".join(args_repr + kwargs_repr)

            # 记录函数调用
            log_message = f"调用 {func.__name__}({signature})"

            log_level = LOG_LEVELS.get(level.lower(), logging.DEBUG)

            if log_level == logging.DEBUG:
                call_logger.debug(LogTarget.FILE, log_message)
            elif log_level == logging.INFO:
                call_logger.info(LogTarget.FILE, log_message)
            elif log_level == logging.WARNING:
                call_logger.warning(LogTarget.FILE, log_message)
            elif log_level == logging.ERROR:
                call_logger.error(LogTarget.FILE, log_message)
            elif log_level == logging.CRITICAL:
                call_logger.critical(LogTarget.FILE, log_message)

            # 执行函数
            try:
                result = func(*args, **kwargs)

                # 记录返回值 (可能会很长，所以只在DEBUG级别记录)
                if log_level == logging.DEBUG:
                    result_repr = repr(result)
                    if len(result_repr) > 100:
                        result_repr = result_repr[:100] + "..."
                    call_logger.debug(
                        LogTarget.FILE, 
                        f"函数 {func.__name__} 返回: {result_repr}"
                    )

                return result
            except Exception as e:
                # 记录异常
                call_logger.error(
                    LogTarget.FILE, 
                    f"函数 {func.__name__} 执行失败: {str(e)}", 
                    exc_info=True
                )
                raise

        return wrapper
    return decorator


def log_exceptions(logger: Optional[logging.Logger] = None, level: str = "error",
                   reraise: bool = True) -> Callable:
    """
    装饰器：记录函数执行过程中的异常

    Args:
        logger: 日志记录器，如果为None，将创建一个名为'exceptions'的记录器
        level: 日志级别，默认为error
        reraise: 是否重新抛出异常，如果为False则捕获异常并返回None

    Returns:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            # 使用指定的logger或创建一个专门用于记录异常的logger
            exc_logger = logger or get_unified_logger('exceptions', enhanced=True)

            try:
                return func(*args, **kwargs)
            except Exception as e:
                # 构建日志消息
                log_message = f"函数 {func.__name__} 执行发生异常: {type(e).__name__}: {str(e)}"

                log_level = LOG_LEVELS.get(level.lower(), logging.ERROR)

                if log_level == logging.DEBUG:
                    exc_logger.debug(LogTarget.FILE, log_message, exc_info=True)
                elif log_level == logging.INFO:
                    exc_logger.info(LogTarget.FILE, log_message, exc_info=True)
                elif log_level == logging.WARNING:
                    exc_logger.warning(LogTarget.FILE, log_message, exc_info=True)
                elif log_level == logging.ERROR:
                    exc_logger.error(LogTarget.FILE, log_message, exc_info=True)
                elif log_level == logging.CRITICAL:
                    exc_logger.critical(LogTarget.FILE, log_message, exc_info=True)

                # 根据reraise参数决定是否重新抛出异常
                if reraise:
                    raise
                return None

        return wrapper
    return decorator
