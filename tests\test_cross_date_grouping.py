#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
跨日期数据分组测试

测试修复后的智能时间戳处理器跨日期数据分组功能。
"""

import pandas as pd
import numpy as np
from datetime import datetime
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.smart_timestamp_processor import get_smart_timestamp_processor


def test_cross_date_grouping():
    """测试跨日期数据分组功能"""
    print("=== 测试跨日期数据分组功能 ===")
    
    # 创建跨日期测试数据（7月15日和7月16日）
    test_data = pd.DataFrame({
        'time': [
            1752562262000,  # 2025-07-15 14:51:02
            1752562265000,  # 2025-07-15 14:51:05
            1752562268000,  # 2025-07-15 14:51:08
            1752629698000,  # 2025-07-16 09:34:58
            1752629701000,  # 2025-07-16 09:35:01
            1752629704000   # 2025-07-16 09:35:04
        ],
        'lastPrice': [13.53, 13.54, 13.55, 13.45, 13.46, 13.47],
        'volume': [100, 200, 300, 400, 500, 600]
    })
    
    print(f"测试数据行数: {len(test_data)}")
    print(f"时间范围: {test_data['time'].min()} - {test_data['time'].max()}")
    
    # 转换时间戳为可读格式验证
    start_time = pd.to_datetime(test_data['time'].min(), unit='ms')
    end_time = pd.to_datetime(test_data['time'].max(), unit='ms')
    print(f"可读时间范围: {start_time} - {end_time}")
    
    # 测试智能时间戳处理器分组
    processor = get_smart_timestamp_processor()
    grouped_data = processor.analyze_cross_date_data(test_data)
    
    print(f"\n分组结果:")
    for date_str, group_data in grouped_data.items():
        print(f"日期 {date_str}: {len(group_data)} 行数据")
        
        # 验证分组数据的时间范围
        group_start = pd.to_datetime(group_data['time'].min(), unit='ms')
        group_end = pd.to_datetime(group_data['time'].max(), unit='ms')
        print(f"  时间范围: {group_start} - {group_end}")
    
    # 验证分组结果
    expected_groups = 2  # 应该有2个分组（7月15日和7月16日）
    if len(grouped_data) == expected_groups:
        print("✅ 跨日期分组测试通过")
        
        # 验证每个分组的数据
        if "20250715" in grouped_data and "20250716" in grouped_data:
            group_715 = grouped_data["20250715"]
            group_716 = grouped_data["20250716"]
            
            print(f"7月15日数据: {len(group_715)} 行")
            print(f"7月16日数据: {len(group_716)} 行")
            
            # 验证数据完整性
            total_rows = len(group_715) + len(group_716)
            if total_rows == len(test_data):
                print("✅ 数据完整性验证通过")
                return True
            else:
                print(f"❌ 数据完整性验证失败，原始: {len(test_data)}, 分组后: {total_rows}")
                return False
        else:
            print("❌ 分组日期不正确")
            return False
    else:
        print(f"❌ 分组数量不正确，期望: {expected_groups}, 实际: {len(grouped_data)}")
        return False


def test_single_date_data():
    """测试单日期数据分组"""
    print("\n=== 测试单日期数据分组 ===")
    
    # 创建单日期测试数据
    test_data = pd.DataFrame({
        'time': [1752562262000, 1752562265000, 1752562268000],  # 都是7月15日
        'lastPrice': [13.53, 13.54, 13.55],
        'volume': [100, 200, 300]
    })
    
    processor = get_smart_timestamp_processor()
    grouped_data = processor.analyze_cross_date_data(test_data)
    
    print(f"分组结果: {list(grouped_data.keys())}")
    
    if len(grouped_data) == 1 and "20250715" in grouped_data:
        print("✅ 单日期数据分组测试通过")
        return True
    else:
        print("❌ 单日期数据分组测试失败")
        return False


def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===")
    
    processor = get_smart_timestamp_processor()
    
    # 测试空数据
    empty_data = pd.DataFrame()
    grouped_empty = processor.analyze_cross_date_data(empty_data)
    print(f"空数据分组结果: {list(grouped_empty.keys())}")
    
    # 测试无时间列数据
    no_time_data = pd.DataFrame({
        'price': [13.53, 13.54],
        'volume': [100, 200]
    })
    grouped_no_time = processor.analyze_cross_date_data(no_time_data)
    print(f"无时间列数据分组结果: {list(grouped_no_time.keys())}")
    
    if len(grouped_empty) >= 0 and len(grouped_no_time) >= 0:
        print("✅ 边界情况测试通过")
        return True
    else:
        print("❌ 边界情况测试失败")
        return False


def run_all_tests():
    """运行所有测试"""
    print("开始跨日期数据分组测试...")
    
    tests = [
        test_cross_date_grouping,
        test_single_date_data,
        test_edge_cases
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试执行失败: {e}")
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！跨日期分组功能修复成功！")
        return True
    else:
        print("⚠️ 部分测试失败")
        return False


if __name__ == "__main__":
    run_all_tests()
