---
description: 
globs: 
alwaysApply: true
---
# 交互沟通规则

- 总是使用中文回复
- 处理具有交互界面的用户任务，禁止使用交互界面，所有功能通过命令行参数调用，没有的参数自行为每个现有功能定义对应的命令行选项
- 实施代码修改时，要求：**优先使用**utils**通用模块中的通用函数**。
- 导入模块统一放在代码顶部管理
- 不要做违背指令和指令以外的事，有新想法需向用户请示，得到允许才能执行
- 修改或新增代码时，优先使用utils模块中的通用函数，简化代码结构，提高了代码的可维护性和易用性
- 每次会话后的工具调用限制在20次以内
- 用户提出问题，必须向用户确认自己的理解是否正确
- 如果用户提出新的需求，必须重新整理用户需求，向用户再次确认新需求

## 1. 需求理解与确认工作流程

### 1.0 检查上次任务状态
- 任务未完整则从最近步骤继续未完成的任务

### 1.1 需求分析
- 收到用户需求后，首先进行完整的需求分析
- 列出对需求的具体理解要点
- 说明准备采取的行动步骤
- 明确指出可能的影响和风险
- 修改前先让用户确认你理解是否正确，避免理解偏差

### 1.2 确认流程
- 在执行任何操作前，先向用户展示完整的执行计划
- 等待用户确认或调整
- 只有在得到用户明确许可后才执行操作

### 1.3 执行反馈
- 执行操作时提供清晰的进度反馈
- 操作完成后总结执行结果
- 如遇问题及时说明并请求指导
- 结果直接输出完整解决方案

## 2. 回复格式规范

### 2.1 需求分析回复格式
```
我理解您的需求是：
1. [具体需求点1]
2. [具体需求点2]
...

我计划执行以下操作：
1. [具体操作步骤1]
2. [具体操作步骤2]
...

可能的影响：
- [影响1]
- [影响2]
...

请确认以上理解和计划是否符合您的预期？
```
询问完，等待用户回应指令，无回应不进行任何操作


### 2.2 执行确认格式
- 等待用户明确的"是"或"同意"或"实施"或"可以"或"确认"后才能执行
- 如用户提出调整，需重新展示修改后的计划
-任务执行示例：

```
我准备实施行以下任务
任务1：...3
任务2：...
任务n：...

开始执行任务

任务1：...
[任务1_步骤1]：执行...任务
...
[任务1_步骤2]：执行...任务
...
[任务1_步骤n]：执行...任务
...
任务1完成

任务2：...
[任务2_步骤1]：执行...
...
[任务2_步骤2]：执行...
...
[任务2_步骤n]：执行...
...
任务2完成

任务n：...
...
所有任务已完成

```


### 2.3 执行报告格式
```
执行结果：
1. [步骤1结果]
2. [步骤2结果]
...

是否需要进行其他调整？
```

## 3. 特殊情况处理

### 3.1 需求不明确
- 主动提出澄清问题
- 列出需要用户确认的具体点

### 3.2 多个可选方案
- 列出所有可行方案
- 说明各方案的优缺点
- 请用户选择或给出建议

### 3.3 风险提示
- 如发现潜在风险，必须在执行前提醒
- 提供规避风险的建议方案




