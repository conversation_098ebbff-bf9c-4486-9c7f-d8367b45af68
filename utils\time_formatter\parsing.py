#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
时间解析功能模块

提供从各种格式的字符串解析为日期时间对象的功能
"""

import os
import sys
from datetime import datetime
from typing import Optional
import pandas as pd
import pytz

# 将项目根目录添加到Python路径
root_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, root_path)


def parse_datetime(
    date_str: str, 
    format_str: Optional[str] = None, 
    tz: str = 'Asia/Shanghai'
) -> datetime:
    """
    解析时间字符串为datetime对象
    
    Args:
        date_str: 时间字符串
        format_str: 格式字符串，如果为None则自动推断
        tz: 时区名称
        
    Returns:
        解析后的datetime对象
        
    Raises:
        ValueError: 无法解析时间字符串
    """
    if format_str:
        try:
            dt = datetime.strptime(date_str, format_str)
        except ValueError:
            raise ValueError(f"无法使用格式'{format_str}'解析时间字符串'{date_str}'")
    else:
        # 尝试自动推断格式
        for fmt in [
            '%Y%m%d', '%Y-%m-%d', '%Y/%m/%d',
            '%Y%m%d%H%M%S', '%Y-%m-%d %H:%M:%S', '%Y/%m/%d %H:%M:%S'
        ]:
            try:
                dt = datetime.strptime(date_str, fmt)
                break
            except ValueError:
                continue
        else:
            # 尝试使用pandas解析
            try:
                dt = pd.Timestamp(date_str).to_pydatetime()
            except:
                raise ValueError(f"无法自动推断时间字符串'{date_str}'的格式")
    
    # 设置时区
    timezone = pytz.timezone(tz)
    return timezone.localize(dt) if dt.tzinfo is None else dt.astimezone(timezone)


def parse_multi_format_date(date_str: str) -> datetime:
    """
    解析日期字符串，支持多种格式，比parse_datetime更灵活
    
    Args:
        date_str: 日期字符串，格式可以是 'YYYYMMDD' 或 'YYYY-MM-DD' 
                 或 'YYYYMMDD HH:MM:SS' 等
        
    Returns:
        datetime: 解析后的日期时间对象
        
    Raises:
        ValueError: 无法解析日期字符串
    """
    date_str = str(date_str).strip()
    
    # 尝试不同的日期格式
    formats = [
        '%Y%m%d',           # YYYYMMDD
        '%Y-%m-%d',         # YYYY-MM-DD
        '%Y/%m/%d',         # YYYY/MM/DD
        '%Y%m%d %H:%M:%S',  # YYYYMMDD HH:MM:SS
        '%Y-%m-%d %H:%M:%S', # YYYY-MM-DD HH:MM:SS
        '%Y/%m/%d %H:%M:%S', # YYYY/MM/DD HH:MM:SS
        '%Y%m%d%H%M%S',      # YYYYMMDDHHMMSS
        '%Y%m%d %H%M%S'      # YYYYMMDD HHMMSS
    ]
    
    for fmt in formats:
        try:
            return datetime.strptime(date_str, fmt)
        except ValueError:
            continue
    
    # 如果所有格式都失败，尝试pandas解析
    try:
        return pd.Timestamp(date_str).to_pydatetime()
    except:
        # 如果所有方法都失败，抛出异常
        raise ValueError(f"无法解析日期字符串: {date_str}")