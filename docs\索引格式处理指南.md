# 索引格式处理指南

## 概述

本指南介绍项目中统一的索引格式处理标准，确保所有数据操作都保持索引格式一致性。

## 索引格式标准

### 标准格式
- **时间戳格式**: YYYYMMDDHHMMSS (14位数字字符串)
- **示例**: '20250619090000', '20250619090100', '20250619090200'
- **支持类型**: 字符串索引、DatetimeIndex

### 禁止格式
- **数字索引**: 0, 1, 2, 3, ... (RangeIndex)
- **原因**: 破坏时间序列数据的时间关联性

## 核心工具

### IndexManager
统一索引处理管理器，提供以下功能：

#### 1. 索引格式验证
```python
from utils.data_processor.index_manager import IndexManager

# 验证索引格式
is_valid = IndexManager.validate_index_format(df)
print(f"索引格式是否正确: {is_valid}")
```

#### 2. 索引格式修复
```python
# 自动修复索引格式
fixed_df = IndexManager.ensure_proper_index(df, time_column='time')
```

#### 3. 安全数据合并
```python
# 安全合并，确保索引格式正确
merged_df = IndexManager.safe_concat([df1, df2, df3])
```

#### 4. 索引信息查看
```python
# 获取详细索引信息
info = IndexManager.get_index_info(df)
print(info)

# 记录索引信息到日志
IndexManager.log_index_info(df, "数据处理后")
```

### IndexValidator
索引格式验证器，提供详细的验证报告：

```python
from utils.data_processor.index_manager import IndexValidator

# 全面验证
report = IndexValidator.comprehensive_validation(df)
print(f"验证结果: {report['is_valid']}")
print(f"问题列表: {report['issues']}")
print(f"建议操作: {report['recommendations']}")
```

## 使用规范

### 数据合并操作

#### ✅ 正确做法
```python
# 使用IndexManager.safe_concat()
from utils.data_processor.index_manager import IndexManager

# 合并多个DataFrame
dfs = [df1, df2, df3]
result_df = IndexManager.safe_concat(dfs)

# 验证结果
if IndexManager.validate_index_format(result_df):
    print("合并成功，索引格式正确")
else:
    print("合并后索引格式有问题")
```

#### ❌ 错误做法
```python
# 禁止使用ignore_index=True
result_df = pd.concat([df1, df2], ignore_index=True)  # 会破坏索引！

# 禁止使用reset_index()
result_df = df.reset_index()  # 会将索引变成数字序列！
```

### 数据读取验证

```python
from data.storage.vectorized_reader import read_partitioned_data_vectorized
from utils.data_processor.index_manager import IndexManager

# 读取数据
df = read_partitioned_data_vectorized(data_root, symbol, period)

# 验证索引格式
if not IndexManager.validate_index_format(df):
    print("警告：读取的数据索引格式不正确！")
    df = IndexManager.ensure_proper_index(df)
```

### 数据处理流程

```python
# 完整的数据处理流程示例
def process_data_with_index_protection(data_sources):
    """
    带索引保护的数据处理流程
    """
    from utils.data_processor.index_manager import IndexManager
    
    # 1. 读取所有数据源
    dfs = []
    for source in data_sources:
        df = read_data_from_source(source)
        
        # 验证每个数据源的索引格式
        if not IndexManager.validate_index_format(df):
            print(f"修复数据源 {source} 的索引格式")
            df = IndexManager.ensure_proper_index(df)
        
        dfs.append(df)
    
    # 2. 安全合并所有数据
    merged_df = IndexManager.safe_concat(dfs)
    
    # 3. 验证合并结果
    if merged_df is None:
        raise ValueError("数据合并失败")
    
    if not IndexManager.validate_index_format(merged_df):
        raise ValueError("合并后索引格式不正确")
    
    # 4. 记录处理结果
    IndexManager.log_index_info(merged_df, "数据处理完成")
    
    return merged_df
```

## 故障排除

### 常见问题

#### 1. 索引变成数字序列
**症状**: 索引显示为 0, 1, 2, 3, ...
**原因**: 使用了 `ignore_index=True` 或 `reset_index()`
**解决方案**:
```python
# 如果有time列，使用time列重建索引
df = IndexManager.ensure_proper_index(df, time_column='time')

# 如果没有time列，需要从其他来源获取时间信息
```

#### 2. 合并后数据索引混乱
**症状**: 合并后索引顺序不正确或有重复
**原因**: 没有使用统一的合并方法
**解决方案**:
```python
# 使用安全合并
merged_df = IndexManager.safe_concat([df1, df2])

# 如果仍有问题，检查原始数据
for i, df in enumerate([df1, df2]):
    print(f"DataFrame {i} 索引信息:")
    IndexManager.log_index_info(df, f"df{i}")
```

#### 3. 索引格式验证失败
**症状**: `validate_index_format()` 返回 False
**原因**: 索引不符合标准格式
**解决方案**:
```python
# 获取详细验证报告
from utils.data_processor.index_manager import IndexValidator
report = IndexValidator.comprehensive_validation(df)

print("问题:", report['issues'])
print("建议:", report['recommendations'])

# 根据建议进行修复
```

### 调试工具

#### 索引信息查看
```python
# 查看索引详细信息
info = IndexManager.get_index_info(df)
for key, value in info.items():
    print(f"{key}: {value}")
```

#### 性能监控
```python
import time

# 监控合并操作性能
start_time = time.time()
merged_df = IndexManager.safe_concat(dfs)
end_time = time.time()

print(f"合并耗时: {end_time - start_time:.6f} 秒")
print(f"合并后数据行数: {len(merged_df)}")
```

## 测试验证

### 运行索引格式测试
```bash
# 运行单元测试
python -m pytest tests/test_index_manager.py -v

# 运行集成测试
python -m pytest tests/test_index_integration.py -v
```

### 自定义测试
```python
def test_my_data_processing():
    """测试自定义数据处理流程的索引格式"""
    # 处理数据
    result_df = my_data_processing_function()
    
    # 验证索引格式
    assert IndexManager.validate_index_format(result_df), "索引格式不正确"
    
    # 验证数据完整性
    report = IndexValidator.comprehensive_validation(result_df)
    assert report['is_valid'], f"数据验证失败: {report['issues']}"
```

## 最佳实践总结

1. **始终使用 IndexManager.safe_concat() 进行数据合并**
2. **在数据处理的关键节点验证索引格式**
3. **使用 IndexManager.log_index_info() 记录索引状态**
4. **禁止使用 ignore_index=True 和 reset_index()**
5. **定期运行索引格式测试确保系统稳定性**
6. **遇到问题时使用 IndexValidator 获取详细诊断信息**
