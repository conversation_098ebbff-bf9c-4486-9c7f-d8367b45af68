#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
日志格式化功能模块

提供各种日志格式化器，包括详细格式化器、简单格式化器和彩色格式化器等
"""

import os
import sys
import logging
import random
import string
import inspect
from typing import Dict, Optional
from utils.logger.config import (
    LogTarget
)

# 将项目根目录添加到Python路径
root_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, root_path)

# 日志级别对应的颜色代码
LOG_COLORS = {
    'DEBUG': '\033[94m',     # 蓝色
    'INFO': '\033[92m',      # 绿色
    'WARNING': '\033[93m',   # 黄色
    'ERROR': '\033[91m',     # 红色
    'CRITICAL': '\033[1;91m'  # 加粗红色
}

# 颜色重置代码
COLOR_RESET = '\033[0m'

# 模块名称对应的颜色代码
MODULE_COLORS = {
    'data_source_manager': '\033[96m',  # 青色
    'data_commands': '\033[95m',        # 紫色
    'xtquant_data': '\033[94m',         # 蓝色
    'data.storage': '\033[93m',         # 黄色
    'root': '\033[97m',                 # 白色
    'main_module': '\033[97m',          # 白色
}

# 文本样式代码
BOLD = '\033[1m'
UNDERLINE = '\033[4m'
ITALIC = '\033[3m'  # 部分终端支持

# 默认日志格式（定义在这里，避免与导入的冲突）
DEFAULT_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
DETAILED_FORMAT = (
    '%(asctime)s [%(levelname)s] [%(name)s:%(lineno)d] '
    '[%(pathname)s:%(funcName)s()] %(message)s'
)


def _format_fixed_width_text(text: str, width: int) -> str:
    """
    格式化文本，右对齐并在必要时从左侧截断
    
    Args:
        text: 原始文本
        width: 固定宽度
        
    Returns:
        str: 格式化后的文本
    """
    if len(text) <= width:
        # 如果文本长度小于等于设定宽度，右对齐
        return text.rjust(width)
    else:
        # 如果文本长度大于设定宽度，从左侧截断并添加省略号
        return '...' + text[-(width-3):]


def _format_line_number(line_no: int, width: int = 5) -> str:
    """
    格式化行号，右对齐并使用固定宽度
    
    Args:
        line_no: 行号
        width: 固定宽度，默认为5
        
    Returns:
        str: 格式化后的行号字符串
    """
    return str(line_no).rjust(width)


def _format_level_name(level_name: str, width: int = 10) -> str:
    """
    格式化日志级别，右对齐并使用固定宽度
    
    Args:
        level_name: 日志级别名称
        width: 固定宽度，默认为10
        
    Returns:
        str: 格式化后的日志级别字符串
    """
    return level_name.rjust(width)


def get_caller_function_name():
    """
    获取调用日志函数的真实函数名和行号
    
    通过检查堆栈跟踪，跳过日志系统自身的调用帧，找到实际调用日志方法的用户代码函数。
    
    Returns:
        tuple: (调用者的函数名, 行号)，如果无法确定则返回("", 0)
    """
    try:
        # 获取完整的堆栈跟踪
        stack = inspect.stack()
        
        # 日志系统的模块名列表，用于识别需要跳过的帧
        logger_modules = ['logging', 'utils.logger']
        
        # 查找第一个不属于日志系统的帧
        for frame_info in stack:
            module = inspect.getmodule(frame_info.frame)
            if module is None:
                continue
                
            module_name = module.__name__
            # 如果模块名不在日志系统模块列表中，且不是当前模块，则认为是用户代码
            is_not_logger = not any(
                module_name.startswith(logger_mod) for logger_mod in logger_modules
            )
            if is_not_logger and module_name != __name__:
                func_name = frame_info.function
                line_no = frame_info.lineno
                # 如果是模块级别调用（如<module>），返回空字符串和行号
                if func_name == "<module>":
                    return "", line_no
                return func_name, line_no
        
        return "", 0
    except Exception:
        return "", 0
    finally:
        # 确保引用被清理，避免循环引用
        del stack


class TableMarkdownFormatter(logging.Formatter):
    """
    表格式Markdown日志格式化器
    
    提供以下功能：
    1. 使用表格式布局，各字段用竖线(|)分隔
    2. 模块名设置固定宽度，右对齐，超长时从左侧截断并添加省略号
    3. 显示函数名，格式为【...函数名 】，超长时从左侧截断
    """
    
    def __init__(
        self, 
        fmt: Optional[str] = None, 
        datefmt: Optional[str] = None,
        task_id: Optional[str] = None,
        use_colors: bool = True,
        module_width: int = 30,
        function_width: int = 30,
        level_colors: Optional[Dict[str, str]] = None,
        module_colors: Optional[Dict[str, str]] = None
    ):
        """
        初始化表格式Markdown日志格式化器
        
        Args:
            fmt: 日志格式字符串，默认使用带有任务ID的格式
            datefmt: 日期时间格式字符串，默认为ISO格式
            task_id: 任务ID，如果为None则自动生成
            use_colors: 是否使用彩色输出，默认为True
            module_width: 模块名显示的固定宽度，默认为30
            function_width: 函数名显示的固定宽度，默认为30
            level_colors: 日志级别对应的颜色代码字典，默认使用LOG_COLORS
            module_colors: 模块名称对应的颜色代码字典，默认使用MODULE_COLORS
        """
        if fmt is None:
            fmt = '【%(task_id)s】%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        self._fmt_with_task_id = fmt
        # 创建一个不需要task_id的备用格式
        self._fmt_without_task_id = (
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        # 初始化父类，但使用不需要task_id的格式
        super().__init__(fmt=self._fmt_without_task_id, datefmt=datefmt)
        
        # 如果没有提供任务ID，生成一个随机的6位字母数字混合ID
        self.task_id = task_id or self._generate_task_id()
        
        # 配置选项
        self.use_colors = use_colors
        self.module_width = module_width
        self.function_width = function_width
        
        # 颜色配置
        self.level_colors = level_colors or LOG_COLORS
        self.module_colors = module_colors or MODULE_COLORS
    
    def _generate_task_id(self, length: int = 6) -> str:
        """
        生成一个随机的字母数字混合ID
        
        Args:
            length: ID长度，默认为6
            
        Returns:
            生成的ID字符串
        """
        # 生成随机字符串，包含大写字母和数字
        chars = string.ascii_uppercase + string.digits
        return ''.join(random.choice(chars) for _ in range(length))
    
    def _format_module_name(self, name: str) -> str:
        """
        格式化模块名，右对齐并在必要时从左侧截断
        
        Args:
            name: 完整模块名
            
        Returns:
            str: 格式化后的模块名
        """
        return _format_fixed_width_text(name, self.module_width)
    
    def _apply_color(self, text: str, color_code: str) -> str:
        """
        为文本应用颜色
        
        Args:
            text: 原始文本
            color_code: 颜色代码
            
        Returns:
            str: 应用颜色后的文本
        """
        if self.use_colors and color_code:
            return f"{color_code}{text}{COLOR_RESET}"
        return text
    
    def _get_module_color(self, name: str) -> str:
        """
        获取模块对应的颜色代码
        
        Args:
            name: 模块名
            
        Returns:
            str: 颜色代码
        """
        # 先尝试完整匹配
        if name in self.module_colors:
            return self.module_colors[name]
        
        # 尝试匹配前缀
        for prefix, color in self.module_colors.items():
            if name.startswith(prefix):
                return color
        
        # 默认返回空（无颜色）
        return ''
    
    def format(self, record: logging.LogRecord) -> str:
        """
        格式化日志记录
        
        Args:
            record: 日志记录对象
            
        Returns:
            格式化后的日志字符串
        """
        # 如果记录中没有task_id属性，添加它
        if not hasattr(record, 'task_id'):
            record.task_id = self.task_id
        
        # 获取真实的调用函数名和行号
        caller_func_name, line_no = get_caller_function_name()
        
        # 检查是否使用新的参数格式（title和content）
        has_title = hasattr(record, 'title')
        has_content = hasattr(record, 'content')
        
        if has_title or has_content:
            # 获取标题和内容，以及它们的目标
            title = getattr(record, 'title', None)
            content = getattr(record, 'content', None)
            title_target = getattr(record, 'title_target', None)
            content_target = getattr(record, 'content_target', None)
            
            # 获取当前处理器类型（文件或控制台）
            # 这个属性需要在过滤器中设置
            current_handler_type = getattr(record, 'current_handler_type', None)
            
            # 检查是否应该显示标题和内容
            show_title = False
            show_content = False
            
            if current_handler_type is not None:
                # 根据当前处理器类型和目标设置决定是否显示标题和内容
                if title_target is not None:
                    if (title_target == LogTarget.BOTH or 
                        (title_target == LogTarget.FILE and 
                         current_handler_type == LogTarget.FILE) or
                        (title_target == LogTarget.CONSOLE and 
                         current_handler_type == LogTarget.CONSOLE)):
                        show_title = True
                
                if content_target is not None:
                    if (content_target == LogTarget.BOTH or 
                        (content_target == LogTarget.FILE and 
                         current_handler_type == LogTarget.FILE) or
                        (content_target == LogTarget.CONSOLE and 
                         current_handler_type == LogTarget.CONSOLE)):
                        show_content = True
            else:
                # 如果没有处理器类型信息，默认都显示
                show_title = title is not None
                show_content = content is not None
            
            # 构建最终消息
            final_msg = ""
            title_text = None
            
            # 提取标题（如果需要显示）
            if show_title and title:
                title_text = title
            
            # 添加内容到最终消息（如果需要显示）
            if show_content and content:
                final_msg = content
            
            # 如果没有任何内容要显示，返回空字符串
            if not final_msg and not title_text:
                return ""
            
            # 如果只有标题没有内容，创建一个空内容
            if title_text and not final_msg:
                final_msg = ""
            
            # 创建一个临时记录对象，仅包含内容消息
            temp_record = logging.makeLogRecord(record.__dict__.copy())
            temp_record.msg = final_msg
            temp_record.args = ()
            
            # 获取原始消息
            message = temp_record.getMessage()
            
            # 格式化模块名
            module_name = self._format_module_name(temp_record.name)
            
            # 如果有真实调用函数名，添加到模块名后面，并包含行号
            if caller_func_name:
                formatted_func_name = _format_fixed_width_text(
                    caller_func_name, self.function_width
                )
                formatted_line_no = _format_line_number(line_no)
                module_name = f"{module_name}【{formatted_func_name}】({formatted_line_no})"
            else:
                # 如果没有函数名，仍然保留固定宽度的空白区域
                empty_func_name = " ".rjust(self.function_width)
                formatted_line_no = _format_line_number(0)
                module_name = (
                    f"{module_name}【{empty_func_name}】({formatted_line_no})"
                )
            
            # 应用颜色（如果启用）
            if self.use_colors:
                # 为模块名称添加颜色
                module_color = self._get_module_color(temp_record.name)
                if module_color:
                    colored_module = self._apply_color(module_name, module_color)
                else:
                    colored_module = module_name
            else:
                colored_module = module_name
            
            # 构建表格式日志
            timestamp = self.formatTime(temp_record, self.datefmt)
            task_id_part = (
                f"【{temp_record.task_id}】" 
                if hasattr(temp_record, 'task_id') else ""
            )
            
            # 格式化日志级别
            formatted_level = _format_level_name(temp_record.levelname)
            
            # 最终的表格式日志（分行以避免行过长）
            result = (
                f"{task_id_part}{timestamp} | {colored_module}| "
                f"{formatted_level} | {message}"
            )
            
            # 如果有标题，将标题添加到结果的前面
            if title_text:
                # 如果内容为空，使用一个空格作为消息内容，而不是完全空的消息
                if not final_msg:
                    # 创建一个空记录以获取日志头部
                    empty_record = logging.makeLogRecord(record.__dict__.copy())
                    # 使用一个空格作为消息，而不是空字符串，确保格式正确
                    empty_record.msg = " "
                    empty_record.args = ()
                    
                    # 获取日志头部（时间戳、模块名等）
                    header_only = super().format(empty_record)
                    
                    # 移除可能存在的换行符和额外空格
                    header_only = header_only.strip()
                    
                    # 直接将标题与头部连接
                    result = f"{title_text}{header_only}"
                else:
                    # 添加标题到结果的前面（不添加换行符）
                    result = f"{title_text}{result}"
            
            return result
        
        # 使用旧的参数格式
        # 尝试使用带task_id的格式
        if not hasattr(record, 'task_id'):
            record.task_id = self.task_id
        
        # 获取原始消息
        message = record.getMessage()
        
        # 格式化模块名
        module_name = self._format_module_name(record.name)
        
        # 如果有真实调用函数名，添加到模块名后面，并包含行号
        if caller_func_name:
            formatted_func_name = _format_fixed_width_text(
                caller_func_name, self.function_width
            )
            formatted_line_no = _format_line_number(line_no)
            module_name = f"{module_name}【{formatted_func_name}】({formatted_line_no})"
        else:
            # 如果没有函数名，仍然保留固定宽度的空白区域
            empty_func_name = " ".rjust(self.function_width)
            formatted_line_no = _format_line_number(0)
            module_name = (
                f"{module_name}【{empty_func_name}】({formatted_line_no})"
            )
        
        # 应用颜色（如果启用）
        if self.use_colors:
            # 为模块名称添加颜色
            module_color = self._get_module_color(record.name)
            if module_color:
                colored_module = self._apply_color(module_name, module_color)
            else:
                colored_module = module_name
        else:
            colored_module = module_name
        
        # 构建表格式日志
        timestamp = self.formatTime(record, self.datefmt)
        task_id_part = (
            f"【{record.task_id}】" 
            if hasattr(record, 'task_id') else ""
        )
        
        # 格式化日志级别
        formatted_level = _format_level_name(record.levelname)
        
        # 最终的表格式日志
        result = (
            f"{task_id_part}{timestamp} | {colored_module}| "
            f"{formatted_level} | {message}"
        )
        
        return result


class EnhancedFormatter(logging.Formatter):
    """
    增强型日志格式化器
    
    提供以下功能：
    1. 支持彩色输出（可配置）
    2. 支持图标（可配置）
    3. 支持任务ID
    4. 支持标题和内容分离
    5. 支持不同目标（文件/控制台）的输出控制
    6. 支持操作状态图标（开始/结束/失败）
    7. 支持重要消息样式增强
    """
    
    def __init__(
        self, 
        fmt: Optional[str] = None, 
        datefmt: Optional[str] = None,
        task_id: Optional[str] = None,
        use_colors: bool = True,
        use_icons: bool = True,
        module_width: int = 30,
        function_width: int = 30,
        level_colors: Optional[Dict[str, str]] = None,
        module_colors: Optional[Dict[str, str]] = None,
    ):
        """
        初始化增强型日志格式化器
        
        Args:
            fmt: 日志格式字符串，默认使用带有任务ID的格式
            datefmt: 日期时间格式字符串，默认为ISO格式
            task_id: 任务ID，如果为None则自动生成
            use_colors: 是否使用彩色输出，默认为True
            use_icons: 是否使用图标，默认为True
            module_width: 模块名显示的固定宽度，默认为30
            function_width: 函数名显示的固定宽度，默认为30
            level_colors: 日志级别对应的颜色代码字典，默认使用LOG_COLORS
            module_colors: 模块名称对应的颜色代码字典，默认使用MODULE_COLORS
        """
        if fmt is None:
            fmt = '【%(task_id)s】%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        self._fmt_with_task_id = fmt
        # 创建一个不需要task_id的备用格式
        self._fmt_without_task_id = (
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        # 初始化父类，但使用不需要task_id的格式
        super().__init__(fmt=self._fmt_without_task_id, datefmt=datefmt)
        
        # 如果没有提供任务ID，生成一个随机的6位字母数字混合ID
        self.task_id = task_id or self._generate_task_id()
        
        # 增强功能开关
        self.use_colors = use_colors
        self.use_icons = use_icons
        
        # 颜色配置
        self.level_colors = level_colors or LOG_COLORS
        self.module_colors = module_colors or MODULE_COLORS
        
        # 图标映射
        self.level_icons = {
            'DEBUG': '🔍 ',
            'INFO': 'ℹ️ ',
            'WARNING': '⚠️ ',
            'ERROR': '❌ ',
            'CRITICAL': '🔥 ',
        }
        
        # 操作类型图标
        self.operation_icons = {
            'start': '▶️ ',
            'end': '✅ ',
            'fail': '❌ ',
        }
        
        # 显示宽度配置
        self.module_width = module_width
        self.function_width = function_width

    def _generate_task_id(self, length: int = 6) -> str:
        """
        生成一个随机的字母数字混合ID
        
        Args:
            length: ID长度，默认为6
            
        Returns:
            生成的ID字符串
        """
        # 生成随机字符串，包含大写字母和数字
        chars = string.ascii_uppercase + string.digits
        return ''.join(random.choice(chars) for _ in range(length))
    
    def _is_operation_start(self, message: str) -> bool:
        """
        判断日志消息是否表示操作开始
        
        Args:
            message: 日志消息
            
        Returns:
            bool: 是否是操作开始
        """
        # 操作开始关键词
        start_keywords = ['开始', '准备', '初始化', '启动']
        return any(keyword in message for keyword in start_keywords)
    
    def _is_operation_end(self, message: str) -> bool:
        """
        判断日志消息是否表示操作结束
        
        Args:
            message: 日志消息
            
        Returns:
            bool: 是否是操作结束
        """
        # 操作结束关键词
        end_keywords = ['完成', '结束', '成功', '失败']
        return any(keyword in message for keyword in end_keywords)
    
    def _is_operation_fail(self, message: str) -> bool:
        """
        判断日志消息是否表示操作失败
        
        Args:
            message: 日志消息
            
        Returns:
            bool: 是否是操作失败
        """
        # 操作失败关键词
        fail_keywords = ['失败', '错误', '异常', '错误', 'error', 'exception', 'failed']
        return any(keyword in message.lower() for keyword in fail_keywords)
    
    def _get_module_base(self, name: str) -> str:
        """
        获取模块的基础名称（第一部分）
        
        Args:
            name: 完整模块名
            
        Returns:
            str: 模块基础名称
        """
        # 如果模块名包含点，取第一部分
        if '.' in name:
            return name.split('.')[0]
        return name
    
    def _apply_color(self, text: str, color_code: str) -> str:
        """
        为文本应用颜色
        
        Args:
            text: 原始文本
            color_code: 颜色代码
            
        Returns:
            str: 应用颜色后的文本
        """
        if self.use_colors and color_code:
            return f"{color_code}{text}{COLOR_RESET}"
        return text
    
    def _apply_style(self, text: str, style_code: str) -> str:
        """
        为文本应用样式
        
        Args:
            text: 原始文本
            style_code: 样式代码
            
        Returns:
            str: 应用样式后的文本
        """
        if self.use_colors and style_code:
            return f"{style_code}{text}{COLOR_RESET}"
        return text
    
    def _get_level_icon(self, level: str) -> str:
        """
        获取日志级别对应的图标
        
        Args:
            level: 日志级别
            
        Returns:
            str: 图标字符串
        """
        if not self.use_icons:
            return ''
        
        return self.level_icons.get(level, '')
    
    def _get_operation_icon(self, message: str) -> str:
        """
        根据消息内容获取操作图标
        
        Args:
            message: 日志消息
            
        Returns:
            str: 操作图标
        """
        if not self.use_icons:
            return ''
        
        # 根据消息内容判断操作类型
        if self._is_operation_start(message):
            return self.operation_icons.get('start', '')
        elif self._is_operation_fail(message):
            return self.operation_icons.get('fail', '')
        elif self._is_operation_end(message):
            return self.operation_icons.get('end', '')
        
        return ''
    
    def _format_module_name(self, name: str) -> str:
        """
        格式化模块名，右对齐并在必要时从左侧截断
        
        Args:
            name: 完整模块名
            
        Returns:
            str: 格式化后的模块名
        """
        return _format_fixed_width_text(name, self.module_width)
    
    def _get_module_color(self, name: str) -> str:
        """
        获取模块对应的颜色代码
        
        Args:
            name: 模块名
            
        Returns:
            str: 颜色代码
        """
        # 先尝试完整匹配
        if name in self.module_colors:
            return self.module_colors[name]
        
        # 尝试匹配前缀
        for prefix, color in self.module_colors.items():
            if name.startswith(prefix):
                return color
        
        # 默认返回空（无颜色）
        return ''
    
    def format(self, record: logging.LogRecord) -> str:
        """
        格式化日志记录
        
        Args:
            record: 日志记录对象
            
        Returns:
            格式化后的日志字符串
        """
        # 如果记录中没有task_id属性，添加它
        if not hasattr(record, 'task_id'):
            record.task_id = self.task_id
        
        # 获取真实的调用函数名和行号
        caller_func_name, line_no = get_caller_function_name()
        
        # 临时更改格式为带task_id的格式
        old_fmt = self._style._fmt
        self._style._fmt = self._fmt_with_task_id
        
        try:
            # 检查是否使用新的参数格式（title和content）
            has_title = hasattr(record, 'title')
            has_content = hasattr(record, 'content')
            
            if has_title or has_content:
                # 获取标题和内容，以及它们的目标
                title = getattr(record, 'title', None)
                content = getattr(record, 'content', None)
                title_target = getattr(record, 'title_target', None)
                content_target = getattr(record, 'content_target', None)
                
                # 获取当前处理器类型（文件或控制台）
                # 这个属性需要在过滤器中设置
                current_handler_type = getattr(record, 'current_handler_type', None)
                
                # 检查是否应该显示标题和内容
                show_title = False
                show_content = False
                
                if current_handler_type is not None:
                    # 根据当前处理器类型和目标设置决定是否显示标题和内容
                    if title_target is not None:
                        if (title_target == LogTarget.BOTH or 
                            (title_target == LogTarget.FILE and 
                             current_handler_type == LogTarget.FILE) or
                            (title_target == LogTarget.CONSOLE and 
                             current_handler_type == LogTarget.CONSOLE)):
                            show_title = True
                    
                    if content_target is not None:
                        if (content_target == LogTarget.BOTH or 
                            (content_target == LogTarget.FILE and 
                             current_handler_type == LogTarget.FILE) or
                            (content_target == LogTarget.CONSOLE and 
                             current_handler_type == LogTarget.CONSOLE)):
                            show_content = True
                else:
                    # 如果没有处理器类型信息，默认都显示
                    show_title = title is not None
                    show_content = content is not None
                
                # 构建最终消息
                final_msg = ""
                title_text = None
                
                # 提取标题（如果需要显示）
                if show_title and title:
                    # 添加操作图标
                    if self.use_icons:
                        operation_icon = self._get_operation_icon(title)
                        if operation_icon:
                            title = f"{operation_icon}{title}"
                    
                    # 保存标题以便后面使用
                    title_text = title
                
                # 添加内容到最终消息（如果需要显示）
                if show_content and content:
                    # 添加操作图标（仅当没有标题时）
                    if self.use_icons and not (show_title and title):
                        operation_icon = self._get_operation_icon(content)
                        if operation_icon:
                            content = f"{operation_icon}{content}"
                    
                    final_msg = content
                
                # 如果没有任何内容要显示，返回空字符串
                if not final_msg and not title_text:
                    return ""
                
                # 如果只有标题没有内容，创建一个空内容
                if title_text and not final_msg:
                    final_msg = ""
                
                # 创建一个临时记录对象，仅包含内容消息
                temp_record = logging.makeLogRecord(record.__dict__.copy())
                temp_record.msg = final_msg
                temp_record.args = ()
                
                # 获取原始消息
                message = temp_record.getMessage()
                
                # 格式化模块名
                module_name = self._format_module_name(temp_record.name)
                
                # 如果有真实调用函数名，添加到模块名后面，并包含行号
                if caller_func_name:
                    formatted_func_name = _format_fixed_width_text(
                        caller_func_name, self.function_width
                    )
                    formatted_line_no = _format_line_number(line_no)
                    module_name = (
                        f"{module_name}【{formatted_func_name}】({formatted_line_no})"
                    )
                else:
                    # 如果没有函数名，仍然保留固定宽度的空白区域
                    empty_func_name = " ".rjust(self.function_width)
                    formatted_line_no = _format_line_number(0)
                    module_name = (
                        f"{module_name}【{empty_func_name}】({formatted_line_no})"
                    )
                
                # 应用颜色（如果启用）
                if self.use_colors:
                    # 为日志级别添加颜色
                    if temp_record.levelname in self.level_colors:
                        level_color = self.level_colors[temp_record.levelname]
                        formatted_level = _format_level_name(temp_record.levelname)
                        colored_level = self._apply_color(formatted_level, level_color)
                    else:
                        formatted_level = _format_level_name(temp_record.levelname)
                        colored_level = formatted_level
                    
                    # 为模块名称添加颜色
                    module_color = self._get_module_color(temp_record.name)
                    if module_color:
                        colored_module = self._apply_color(module_name, module_color)
                    else:
                        colored_module = module_name
                else:
                    formatted_level = _format_level_name(temp_record.levelname)
                    colored_level = formatted_level
                    colored_module = module_name
                
                # 构建表格式日志
                timestamp = self.formatTime(temp_record, self.datefmt)
                task_id_part = (
                    f"【{temp_record.task_id}】" 
                    if hasattr(temp_record, 'task_id') else ""
                )
                
                # 最终的表格式日志（分行以避免行过长）
                result = (
                    f"{task_id_part}{timestamp} | {colored_module}| "
                    f"{colored_level} | {message}"
                )
                
                # 如果有标题，将标题添加到结果的前面
                if title_text:
                    # 如果内容为空，使用一个空格作为消息内容，而不是完全空的消息
                    if not final_msg:
                        # 创建一个空记录以获取日志头部
                        empty_record = logging.makeLogRecord(record.__dict__.copy())
                        # 使用一个空格作为消息，而不是空字符串，确保格式正确
                        empty_record.msg = " "
                        empty_record.args = ()
                        
                        # 获取日志头部（时间戳、模块名等）
                        header_only = super().format(empty_record)
                        
                        # 移除可能存在的换行符和额外空格
                        header_only = header_only.strip()
                        
                        # 直接将标题与头部连接
                        result = f"{title_text}{header_only}"
                    else:
                        # 添加标题到结果的前面（不添加换行符）
                        result = f"{title_text}{result}"
                
                return result
            
            # 使用旧的参数格式
            # 尝试使用带task_id的格式
            result = super().format(record)
            
            # 如果有真实调用函数名，添加到结果中
            if caller_func_name:
                # 在模块名后面添加函数名和行号
                formatted_func_name = _format_fixed_width_text(
                    caller_func_name, self.function_width
                )
                result = result.replace(
                    f"- {record.name} -", 
                    f"- {record.name}【{formatted_func_name}】({line_no}) -"
                )
            
            # 应用颜色
            if self.use_colors:
                # 为日志级别添加颜色
                if record.levelname in self.level_colors:
                    level_color = self.level_colors[record.levelname]
                    formatted_level = _format_level_name(record.levelname)
                    colored_level = self._apply_color(formatted_level, level_color)
                    result = result.replace(
                        f"- {record.levelname} -", 
                        f"- {colored_level} -"
                    )
                
                # 为模块名称添加颜色
                module_base = self._get_module_base(record.name)
                if module_base in self.module_colors:
                    module_color = self.module_colors[module_base]
                    # 考虑到模块名可能已经被修改（添加了函数名和行号）
                    if caller_func_name:
                        formatted_func_name = _format_fixed_width_text(
                            caller_func_name, self.function_width
                        )
                        module_with_func = (
                            f"{record.name}【{formatted_func_name}】({line_no})"
                        )
                        result = result.replace(
                            f"- {module_with_func} -", 
                            f"- {self._apply_color(module_with_func, module_color)} -"
                        )
                    else:
                        result = result.replace(
                            f"- {record.name} -", 
                            f"- {self._apply_color(record.name, module_color)} -"
                        )
            
            # 添加图标
            if self.use_icons:
                # 获取日志级别图标
                level_icon = self._get_level_icon(record.levelname)
                
                # 获取操作图标
                message = record.getMessage()
                operation_icon = self._get_operation_icon(message)
                
                # 组合图标
                icon = level_icon or operation_icon
                
                if icon:
                    # 修复过长的行
                    msg_part = f"- {record.getMessage()}"
                    icon_msg = f"- {icon}{record.getMessage()}"
                    result = result.replace(msg_part, icon_msg)
            
            # 应用前置内容
            if hasattr(record, 'prefix') and record.prefix:
                result = f"{record.prefix}{result}"
            
            # 为重要消息添加样式
            if "警告" in record.getMessage() or "注意" in record.getMessage():
                msg = record.getMessage()
                warning_style = BOLD + LOG_COLORS['WARNING']
                styled_msg = self._apply_style(msg, warning_style)
                result = result.replace(msg, styled_msg)
            
            # 为错误消息添加样式
            if "错误" in record.getMessage() or "失败" in record.getMessage():
                msg = record.getMessage()
                error_style = BOLD + LOG_COLORS['ERROR']
                styled_msg = self._apply_style(msg, error_style)
                result = result.replace(msg, styled_msg)
            
            return result
        except Exception:
            # 如果失败，回退到不带task_id的格式
            self._style._fmt = self._fmt_without_task_id
            result = super().format(record)
            return result
        finally:
            # 恢复原始格式
            self._style._fmt = old_fmt


def get_formatter(
    formatter_type: str = 'default', 
    use_colors: bool = True,
    datefmt: Optional[str] = None, 
    task_id: Optional[str] = None,
    use_icons: bool = True,
    module_width: int = 30,  # 模块名显示的固定宽度
    function_width: int = 30,  # 函数名显示的固定宽度
) -> logging.Formatter:
    """
    获取指定类型的日志格式化器
    
    Args:
        formatter_type: 格式化器类型，可以是'default'、'enhanced'或'table_markdown'
        use_colors: 是否使用颜色
        datefmt: 日期时间格式
        task_id: 任务ID
        use_icons: 是否使用图标
        module_width: 模块名显示的固定宽度，默认为30
        function_width: 函数名显示的固定宽度，默认为30
        
    Returns:
        logging.Formatter: 指定类型的日志格式化器
    """
    if formatter_type == 'default':
        return logging.Formatter(fmt=DEFAULT_FORMAT, datefmt=datefmt)
    elif formatter_type == 'enhanced':
        return EnhancedFormatter(
            datefmt=datefmt,
            task_id=task_id,
            use_colors=use_colors,
            use_icons=use_icons,
            module_width=module_width,
            function_width=function_width,
            level_colors=LOG_COLORS,
            module_colors=MODULE_COLORS
        )
    elif formatter_type == 'table_markdown':
        return TableMarkdownFormatter(
            datefmt=datefmt,
            task_id=task_id,
            use_colors=use_colors,
            module_width=module_width,
            function_width=function_width
        )
    else:
        # 默认使用增强型格式化器
        return EnhancedFormatter(
            datefmt=datefmt,
            task_id=task_id,
            use_colors=use_colors,
            use_icons=use_icons,
            module_width=module_width,
            function_width=function_width,
            level_colors=LOG_COLORS,
            module_colors=MODULE_COLORS
        )
