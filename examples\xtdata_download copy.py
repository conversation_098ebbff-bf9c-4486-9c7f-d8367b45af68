import time
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from xtquant import xtdata as xt_data
xt_data.enable_hello = False

# 导入日志模块
from utils.logger import get_unified_logger, LogTarget

# 初始化日志记录器
logger = get_unified_logger(__name__, enhanced=True)

# 创建一个下载状态跟踪器
class DownloadTracker:
    """
    用于跟踪异步下载进度的类
    """
    def __init__(self):
        self.completed = False    # 下载是否完成
        self.progress = 0         # 下载进度百分比
        self.error = None         # 错误信息（如果有）
        self.finished = 0         # 已完成的单位数
        self.total = 0            # 总单位数
    
    def reset(self):
        """重置下载状态"""
        self.completed = False
        self.progress = 0
        self.error = None
        self.finished = 0
        self.total = 0

# 全局下载状态跟踪器
download_tracker = DownloadTracker()

def download_data(field_list, period, start_time, end_time, stock_codes):
    """
    下载历史数据

    Args:
        field_list: 字段列表
        period: 周期
        start_time: 开始时间
        end_time: 结束时间
        stock_codes: 股票代码列表

    Returns:
        下载的数据结果
    """
    logger.info(f"开始下载数据 - 周期: {period}, 时间范围: {start_time} ~ {end_time}, 股票数量: {len(stock_codes)}")

    for i, stock_code in enumerate(stock_codes, 1):
        logger.info(f"正在下载第 {i}/{len(stock_codes)} 只股票: {stock_code}")

        try:
            xt_data.download_history_data(
                stock_code=stock_code,
                period=period,
                start_time=start_time,
                end_time=end_time
            )
            logger.debug(f"股票 {stock_code} 下载请求已发送")
        except Exception as e:
            logger.error(f"股票 {stock_code} 下载失败: {e}")
            continue

    logger.info("开始获取本地数据")
    try:
        result = xt_data.get_local_data(
            field_list=field_list,
            stock_list=stock_codes,
            period=period,
            start_time=start_time,
            end_time=end_time,
            dividend_type="front"
        )
        logger.info(f"本地数据获取完成，结果类型: {type(result)}")
        return result
    except Exception as e:
        logger.error(f"获取本地数据失败: {e}")
        return None


def on_xt_download_progress(data):
    """
    处理下载进度回调

    参数:
        data: 回调数据，格式示例:
              {'finished': 10, 'total': 334, 'stockcode': '', 'message': '600845.SH'}
    """
    logger.debug(f"下载进度回调: {data}")

    # 更新下载状态
    if 'finished' in data and 'total' in data:
        download_tracker.finished = data['finished']
        download_tracker.total = data['total']

        # 计算进度百分比
        if download_tracker.total > 0:
            download_tracker.progress = int(download_tracker.finished / download_tracker.total * 100)

        # 检查是否下载完成
        if download_tracker.finished >= download_tracker.total:
            download_tracker.completed = True
            logger.info(f"下载完成! 总共下载了 {download_tracker.finished}/{download_tracker.total} 单位")
            print(f"[回调] 下载完成! 总共下载了 {download_tracker.finished}/{download_tracker.total} 单位")


def download_data2(field_list, period, start_time, end_time, stock_codes):
    """
    使用异步方式下载历史数据，并等待下载完成后返回数据

    参数:
        field_list: 需要获取的字段列表
        period: 周期，如'1d'表示日线，'tick'表示逐笔
        start_time: 开始时间
        end_time: 结束时间
        stock_codes: 股票代码列表

    返回:
        包含历史数据的字典，如果下载失败则返回None
    """
    logger.info(f"开始异步下载数据 - 周期: {period}, 时间范围: {start_time} ~ {end_time}, 股票数量: {len(stock_codes)}")

    # 重置下载状态
    download_tracker.reset()
    logger.debug("下载状态跟踪器已重置")

    # 开始异步下载
    try:
        xt_data.download_history_data2(
            stock_list=stock_codes,
            period=period,
            start_time=start_time,
            end_time=end_time,
            incrementally=True,
            callback=on_xt_download_progress
        )
        logger.info("异步下载请求已发送，等待下载完成...")
    except Exception as e:
        logger.error(f"发送异步下载请求失败: {e}")
        return None

    # 等待下载完成或出错
    logger.info("等待下载完成...")
    print("等待下载完成...")
    timeout = 300  # 设置超时时间为300秒（5分钟）
    start_wait_time = time.time()
    last_progress_time = time.time()

    while not download_tracker.completed:
        time.sleep(0.5)  # 每0.5秒检查一次状态

        # 每5秒打印一次当前进度
        current_time = time.time()
        if current_time - last_progress_time >= 5:
            if download_tracker.total > 0:
                progress_msg = f"当前进度: {download_tracker.finished}/{download_tracker.total} ({download_tracker.progress}%)"
                logger.info(progress_msg)
                print(progress_msg)
            last_progress_time = current_time

        # 检查是否超时
        if current_time - start_wait_time > timeout:
            timeout_msg = f"下载超时（{timeout}秒）"
            logger.error(timeout_msg)
            print(timeout_msg)
            return None

    # 下载完成后获取数据
    elapsed_time = time.time() - start_wait_time
    completion_msg = f"下载完成，正在获取本地数据... (耗时: {elapsed_time:.2f}秒)"
    logger.info(completion_msg)
    print(completion_msg)

    # 开始计时获取本地数据
    data_fetch_start = time.time()
    try:
        result = xt_data.get_local_data(
            field_list=field_list,
            stock_list=stock_codes,
            period=period,
            start_time=start_time,
            end_time=end_time,
            dividend_type="front"
        )
        data_fetch_time = time.time() - data_fetch_start
        logger.info(f"本地数据获取完成，结果类型: {type(result)}, 获取耗时: {data_fetch_time:.2f}秒")

        # 记录总体性能统计
        total_time = time.time() - start_wait_time + data_fetch_time
        logger.info(f"download_data2 总耗时: {total_time:.2f}秒 (下载: {elapsed_time:.2f}秒, 获取: {data_fetch_time:.2f}秒)")

        return result
    except Exception as e:
        data_fetch_time = time.time() - data_fetch_start
        logger.error(f"获取本地数据失败: {e}, 尝试耗时: {data_fetch_time:.2f}秒")
        return None


if __name__ == "__main__":
    
    import pandas as pd
    pd.set_option('display.max_columns', None)  # 显示所有列
    pd.set_option('display.width', None)  # 设置宽度为无限制
    pd.set_option('display.max_rows', None)  # 显示所有行
    
    logger.info("=" * 60)
    logger.info("开始执行 xtdata 数据下载脚本")
    logger.info("=" * 60)

    # 记录脚本开始时间
    script_start_time = time.time()

    field_list = [
    ]

    period = "tick"
    logger.info(f"请求周期: {period}")
    print(f"  - 请求周期: {period}")

    start_time = "20250715145700"
    end_time = "20250716093100"
    logger.info(f"时间范围: {start_time} ~ {end_time}")
    print(f"  - 开始时间: {start_time}")
    print(f"  - 结束时间: {end_time}")

    stock_codes = [
        "600000.SH",    # 螺纹钢
        #"600027.SH",    # 平安银行
    ]
    logger.info(f"股票代码: {stock_codes}")
    print(f"  - 股票代码: {stock_codes}")

    # 使用download_data2下载数据 - 添加性能计时
    logger.info("开始使用 download_data2 方法下载数据")
    method2_start_time = time.time()

    result_data22 = download_data2(field_list, period, start_time, end_time, stock_codes)

    method2_total_time = time.time() - method2_start_time
    logger.info(f"download_data2 结果类型: {type(result_data22)}")
    logger.info(f"  - download_data2 执行完成，耗时: {method2_total_time:.2f}秒")

    df = pd.DataFrame(result_data22["600000.SH"])
    #logger.info(f"1m数据:\n{df.head(1000)}")
    logger.info(f"1m数据:\n{df.loc['20250715145700':'20250716093100'] }")

    #logger.info(f"tick数据:\n{df.loc['20250701000000':'20250701090030'] }")
    #logger.info(f"tick数据:\n{df.loc['20250701101430':'20250701103030'] }")
    #logger.info(f"tick数据:\n{df.loc['20250701112930':'20250701133030'] }")
    #logger.info(f"tick数据:\n{df.loc['20250721000000':'20250721240000'] }")
    #logger.info(f"tick数据:\n{df.loc['20250721000000':'20250702090200'] }")


    # 使用download_data下载数据
    #logger.info("开始使用 download_data 方法下载数据")
    #result_data = download_data(field_list, period, start_time, end_time, stock_codes)
    #logger.info(f"download_data 结果类型: {type(result_data)}")
    #logger.info(f"  - result_download_data: {result_data}\n")

    # 计算脚本总执行时间
    script_total_time = time.time() - script_start_time

    logger.info("=" * 60)
    logger.info("xtdata 数据下载脚本执行完成")
    logger.info(f"脚本总执行时间: {script_total_time:.2f}秒")
    logger.info("=" * 60)

    print(f"\n🎯 性能统计:")
    print(f"  - 脚本总执行时间: {script_total_time:.2f}秒")
    print(f"  - 数据下载方法: download_data2")
    print(f"  - 处理股票数量: {len(stock_codes)}")
    print(f"  - 数据周期: {period}")
    print(f"  - 时间范围: {start_time} ~ {end_time}")