#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
股票代码相关工具模块

提供股票代码的标准化、验证、解析和转换功能
"""

import os
import sys
import re
from typing import Dict, List, Optional, Tuple, Union, Set

# 将项目根目录添加到Python路径
root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, root_path)

# 定义常用市场代码
MARKET_SH = "SH"  # 上海证券交易所
MARKET_SZ = "SZ"  # 深圳证券交易所
MARKET_BJ = "BJ"  # 北京证券交易所
MARKET_HK = "HK"  # 香港交易所
MARKET_SF = "SF"  # 上期所
MARKET_DF = "DF"  # 大商所
MARKET_ZF = "ZF"  # 郑商所
MARKET_CF = "CF"  # 中金所
MARKET_GF = "GF"  # 广期所

# 所有支持的市场列表
SUPPORTED_MARKETS = [MARKET_SH, MARKET_SZ, MARKET_BJ, MARKET_HK, 
                    MARKET_SF, MARKET_DF, MARKET_ZF, MARKET_CF, MARKET_GF]

# 市场代码到中文名称的映射
MARKET_NAME_MAP = {
    MARKET_SH: "上海证券交易所",
    MARKET_SZ: "深圳证券交易所",
    MARKET_BJ: "北京证券交易所",
    MARKET_HK: "香港交易所",
    MARKET_SF: "上期所",
    MARKET_DF: "大商所",
    MARKET_ZF: "郑商所",
    MARKET_CF: "中金所",
    MARKET_GF: "广期所"
}

# 股票代码前缀到市场的映射
STOCK_PREFIX_MARKET_MAP = {
    "6": MARKET_SH,  # 600开头、601开头等为上交所
    "900": MARKET_SH,  # 900开头为上交所B股
    "5": MARKET_SH,  # 500开头为上交所基金

    "0": MARKET_SZ,  # 000开头、001开头为深交所
    "2": MARKET_SZ,  # 200开头为深交所B股
    "1": MARKET_SZ,  # 100开头、101开头创业板
    "3": MARKET_SZ,  # 300开头为创业板
    "200": MARKET_SZ,  # 200开头为深交所B股
    "201": MARKET_SZ,  # 201开头为深交所债券
    "238": MARKET_SZ,  # 特定的深交所债券

    "4": MARKET_BJ,  # 北证所股票
    "8": MARKET_BJ,  # 北证所股票
    "430": MARKET_BJ,  # 北证所股票
    "83": MARKET_BJ,  # 北证所股票
    "87": MARKET_BJ,  # 北证所股票
}

# 期货交易所代码后缀映射
FUTURES_SUFFIX_MARKET_MAP = {
    "SF": MARKET_SF,  # 上期所
    "DF": MARKET_DF,  # 大商所
    "ZF": MARKET_ZF,  # 郑商所
    "CF": MARKET_CF,  # 中金所
    "GF": MARKET_GF,  # 广期所
}


def standardize_stock_codes(stock_codes: List[str]) -> List[str]:
    """
    标准化股票代码，确保所有代码都有市场后缀
    
    Args:
        stock_codes: 股票代码列表，如 ['600000', '000001', '600000.SH']
        
    Returns:
        List[str]: 标准化后的股票代码列表，如 ['600000.SH', '000001.SZ', '600000.SH']
    """
    standardized_codes = []
    
    for code in stock_codes:
        code = code.strip().upper()
        
        # 已经有市场后缀
        if "." in code:
            code_part, market_part = code.split(".", 1)
            # 确保市场代码是大写的
            standardized_codes.append(f"{code_part}.{market_part.upper()}")
            continue
        
        # 根据代码前缀判断市场
        market = None
        for prefix, mkt in STOCK_PREFIX_MARKET_MAP.items():
            if code.startswith(prefix):
                market = mkt
                break
        
        if market:
            standardized_codes.append(f"{code}.{market}")
        else:
            # 如果无法判断，保持原样
            standardized_codes.append(code)
    
    return standardized_codes


def parse_stock_code(stock_code: str) -> Tuple[str, str]:
    """
    解析股票代码获取代码和市场
    
    Args:
        stock_code: 完整股票代码，如 '600000.SH'
        
    Returns:
        Tuple[str, str]: (代码, 市场)，如 ('600000', 'SH')
        
    Raises:
        ValueError: 无效的股票代码格式
    """
    parts = stock_code.split(".")
    if len(parts) != 2:
        raise ValueError(f"无效的股票代码格式: {stock_code}，应为'code.market'格式")
    
    code_part, market_part = parts
    return code_part, market_part.upper()


def validate_stock_code(stock_code: str) -> bool:
    """
    验证股票代码格式是否有效
    
    Args:
        stock_code: 股票代码，如 '600000.SH'
        
    Returns:
        bool: 股票代码格式是否有效
    """
    # 检查基本格式
    pattern = r'^[A-Za-z0-9]{1,10}\.[A-Za-z]{1,5}$'
    if not re.match(pattern, stock_code):
        return False
    
    # 解析代码和市场
    try:
        code, market = parse_stock_code(stock_code)
    except ValueError:
        return False
    
    # 验证市场代码
    if market not in SUPPORTED_MARKETS:
        return False
    
    return True


def get_market_name(market_code: str) -> str:
    """
    获取市场代码对应的中文名称
    
    Args:
        market_code: 市场代码，如 'SH'
        
    Returns:
        str: 市场中文名称，如 '上海证券交易所'
    """
    return MARKET_NAME_MAP.get(market_code.upper(), f"未知市场({market_code})")


def guess_market(code: str) -> str:
    """
    根据股票代码猜测可能的市场
    
    Args:
        code: 股票代码，如 '600000'
        
    Returns:
        str: 猜测的市场代码，如无法猜测则返回空字符串
    """
    code = code.strip()
    
    # 根据前缀猜测市场
    for prefix, market in STOCK_PREFIX_MARKET_MAP.items():
        if code.startswith(prefix):
            return market
    
    # 无法确定
    return ""


def split_stock_list(stock_str: str, delimiters: str = ',;\n\t ') -> List[str]:
    """
    拆分股票代码字符串为列表
    
    Args:
        stock_str: 股票代码字符串，如 '600000.SH, 000001.SZ'
        delimiters: 分隔符，默认为逗号、分号、换行、制表符和空格
        
    Returns:
        List[str]: 标准化后的股票代码列表
    """
    if not stock_str:
        return []
    
    # 使用正则表达式根据多种分隔符分割
    pattern = f'[{delimiters}]+'
    codes = re.split(pattern, stock_str)
    
    # 过滤空字符串并标准化股票代码
    valid_codes = [code for code in codes if code.strip()]
    return standardize_stock_codes(valid_codes)


def is_index_code(stock_code: str) -> bool:
    """
    判断是否为指数代码
    
    Args:
        stock_code: 股票代码，如 '000001.SH'
        
    Returns:
        bool: 是否为指数代码
    """
    try:
        code, market = parse_stock_code(stock_code)
        
        # 上证指数
        if market == MARKET_SH and code in ['000001', '000002', '000003', '000008', '000010', '000016']:
            return True
        
        # 深证指数
        if market == MARKET_SZ and code in ['399001', '399002', '399003', '399004', '399005', '399006']:
            return True
            
        return False
    except ValueError:
        return False


def is_etf_code(stock_code: str) -> bool:
    """
    判断是否为ETF代码
    
    Args:
        stock_code: 股票代码，如 '510300.SH'
        
    Returns:
        bool: 是否为ETF代码
    """
    try:
        code, market = parse_stock_code(stock_code)
        
        # ETF通常为5开头的代码
        if market == MARKET_SH and code.startswith('5'):
            return True
        
        # 深交所ETF通常为15开头的代码
        if market == MARKET_SZ and code.startswith('15'):
            return True
            
        return False
    except ValueError:
        return False


def is_futures_code(stock_code: str) -> bool:
    """
    判断是否为期货代码
    
    Args:
        stock_code: 股票代码，如 'rb2210.SF'
        
    Returns:
        bool: 是否为期货代码
    """
    try:
        code, market = parse_stock_code(stock_code)
        
        # 检查是否为期货市场
        if market in [MARKET_SF, MARKET_DF, MARKET_ZF, MARKET_CF, MARKET_GF]:
            return True
            
        return False
    except ValueError:
        return False 