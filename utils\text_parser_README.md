# 文本解析工具使用指南

## 📋 概述

`utils/text_parser.py` 提供了专业的文本解析工具，特别针对金融工具代码（股票、期货等）的解析进行了优化。

## 🔧 主要功能

### 1. 股票代码解析 - `parse_stock_code_input()`

**推荐使用的专业股票代码解析函数**，能够处理复杂的输入格式：

#### 支持的格式
- **标准代码**: `600000.SH`, `000001.SZ`
- **带注释的代码**: `"A00.DF",      # 豆一主力连续合约`
- **带引号的代码**: `'000001.SZ'` 或 `"600000.SH"`
- **混合格式**: `sh000001, '600000.SH' # 上证指数`
- **表格行数据**: `601866.SH 中远海发    SH     --   0.01     1`
- **多行输入**: 每行一个代码

#### 自动处理功能
- ✅ 删除Python风格的注释（#后面的内容）
- ✅ 删除引号和括号
- ✅ 处理常见分隔符（逗号、空格等）
- ✅ 严格过滤，只保留符合"代码.交易所"格式的股票代码
- ✅ 保持原始大小写格式（期货合约需要）

#### 使用示例
```python
from utils.text_parser import parse_stock_code_input

# 处理带注释的股票代码
text = '''
000001.SZ    # 平安银行
"A00.DF",    # 豆一主力连续合约
'pg00.DF'    # 液化气连续合约
'''

codes = parse_stock_code_input(text)
print(codes)  # ['000001.SZ', 'A00.DF', 'pg00.DF']
```

### 2. 通用金融工具解析 - `parse_instrument_input()`

通用的金融工具代码解析函数，内部会调用 `parse_stock_code_input()` 进行专业解析。

### 3. 通用文本项目解析 - `parse_text_items()`

用于解析一般的文本项目列表，不会添加市场后缀。

## 🚨 重要提醒

### 统一使用专业解析工具

**强烈建议**在所有需要解析股票代码的地方使用 `parse_stock_code_input()` 函数，而不是简单的字符串处理：

#### ❌ 不推荐的简单解析方式
```python
# 这种方式无法处理带注释的代码
stocks = []
for line in stock_lines:
    line = line.strip()
    if line and not line.startswith('#') and '.' in line:
        stocks.append(line)  # 可能包含注释内容
```

#### ✅ 推荐的专业解析方式
```python
from utils.text_parser import parse_stock_code_input

stocks = []
for line in stock_lines:
    line = line.strip()
    if line and not line.startswith('#'):
        parsed_codes = parse_stock_code_input(line)
        stocks.extend(parsed_codes)
```

## 📊 解析效果对比

### 输入示例
```text
"A00.DF",      # 豆一主力连续合约
'pg00.DF',     # 液化气连续(2506)         DCE    --   1.0      20
000001.SZ      # 平安银行
```

### 简单解析结果（有问题）
```python
['"A00.DF",      # 豆一主力连续合约', "'pg00.DF',     # 液化气连续(2506)         DCE    --   1.0      20", '000001.SZ      # 平安银行']
```

### 专业解析结果（正确）
```python
['A00.DF', 'pg00.DF', '000001.SZ']
```

## 🔧 其他工具函数

- `parse_key_value_text()`: 解析键值对文本
- `parse_number_range()`: 解析数字范围
- `parse_date_range()`: 解析日期范围

## 📝 最佳实践

1. **统一使用**: 在整个项目中统一使用 `parse_stock_code_input()` 解析股票代码
2. **避免重复**: 不要在不同模块中重复实现简单的解析逻辑
3. **测试验证**: 使用带注释的测试数据验证解析效果
4. **保持格式**: 特别注意期货合约代码的大小写格式要求
