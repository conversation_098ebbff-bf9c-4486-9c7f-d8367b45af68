<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{{ strategy_name }} - 回测报告</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <style>
        body { font-family: 'Arial', sans-serif; margin: 20px; }
        .report-header { text-align: center; margin-bottom: 30px; }
        .performance-metrics { margin-bottom: 30px; }
        .monthly-returns { margin-bottom: 30px; }
        .parameters { margin-bottom: 30px; }
        .charts { margin-bottom: 30px; }
        .trades-table { margin-bottom: 30px; }
        img { max-width: 100%; height: auto; margin-bottom: 20px; }
        .positive { color: green; }
        .negative { color: red; }
    </style>
</head>
<body>
    <div class="container">
        <div class="report-header">
            <h1>{{ strategy_name }} - 回测报告</h1>
            <p>生成时间: {{ report_time }}</p>
        </div>
        
        <div class="performance-metrics">
            <h2>性能指标</h2>
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>收益指标</th>
                                <th>值</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>初始资金</td>
                                <td>¥{{ stats.initial_equity|format_money }}</td>
                            </tr>
                            <tr>
                                <td>最终资金</td>
                                <td>¥{{ stats.final_equity|format_money }}</td>
                            </tr>
                            <tr>
                                <td>总收益</td>
                                <td class="{{ 'positive' if stats.total_return > 0 else 'negative' }}">
                                    {{ stats.total_return|format_percent }}
                                </td>
                            </tr>
                            <tr>
                                <td>年化收益</td>
                                <td class="{{ 'positive' if stats.annual_return > 0 else 'negative' }}">
                                    {{ stats.annual_return|format_percent }}
                                </td>
                            </tr>
                            <tr>
                                <td>波动率</td>
                                <td>{{ stats.volatility|format_percent }}</td>
                            </tr>
                            <tr>
                                <td>最大回撤</td>
                                <td class="negative">{{ stats.max_drawdown|format_percent }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>风险指标</th>
                                <th>值</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>夏普比率</td>
                                <td class="{{ 'positive' if stats.sharpe_ratio > 0 else 'negative' }}">
                                    {{ stats.sharpe_ratio|format_number }}
                                </td>
                            </tr>
                            <tr>
                                <td>索提诺比率</td>
                                <td class="{{ 'positive' if stats.sortino_ratio > 0 else 'negative' }}">
                                    {{ stats.sortino_ratio|format_number }}
                                </td>
                            </tr>
                            <tr>
                                <td>卡尔马比率</td>
                                <td class="{{ 'positive' if stats.calmar_ratio > 0 else 'negative' }}">
                                    {{ stats.calmar_ratio|format_number }}
                                </td>
                            </tr>
                            {% if stats.win_rate is defined %}
                            <tr>
                                <td>胜率</td>
                                <td>{{ stats.win_rate|format_percent }}</td>
                            </tr>
                            <tr>
                                <td>盈亏比</td>
                                <td>{{ stats.profit_ratio|format_number }}</td>
                            </tr>
                            <tr>
                                <td>平均持仓时间</td>
                                <td>{{ stats.avg_holding_period|format_number }} 天</td>
                            </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="parameters">
            <h2>策略参数</h2>
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>参数</th>
                        <th>值</th>
                    </tr>
                </thead>
                <tbody>
                    {% for key, value in parameters.items() %}
                    <tr>
                        <td>{{ key }}</td>
                        <td>{{ value }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <div class="monthly-returns">
            <h2>月度收益</h2>
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>年份</th>
                        {% for month in monthly_returns.columns %}
                        <th>{{ month }}</th>
                        {% endfor %}
                        <th>年度</th>
                    </tr>
                </thead>
                <tbody>
                    {% for year, row in monthly_returns.iterrows() %}
                    <tr>
                        <td>{{ year }}</td>
                        {% for month in monthly_returns.columns %}
                        {% set value = row[month] %}
                        <td class="{{ 'positive' if value > 0 else 'negative' if value < 0 else '' }}">
                            {{ value|format_percent if not value is none else '-' }}
                        </td>
                        {% endfor %}
                        <td class="{{ 'positive' if row['年度'] > 0 else 'negative' if row['年度'] < 0 else '' }}">
                            {{ row['年度']|format_percent if not row['年度'] is none else '-' }}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <div class="charts">
            <h2>收益曲线</h2>
            <img src="equity_curve.png" alt="收益曲线">
            
            <h2>月度收益热图</h2>
            <img src="monthly_returns.png" alt="月度收益热图">
            
            <h2>回撤分布</h2>
            <img src="drawdown_distribution.png" alt="回撤分布">
        </div>
        
        {% if trades_html %}
        <div class="trades-table">
            <h2>交易记录</h2>
            {{ trades_html|safe }}
        </div>
        {% endif %}
    </div>
</body>
</html> 