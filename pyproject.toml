[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "quant"
version = "0.1.0"
description = "Quant Trading System"
requires-python = ">=3.12.9"
authors = [
    {name = "Quant Team"}
]

[project.scripts]
quant-data = "quant.main:data_cli"
quant-backtest = "quant.main:backtest_cli"
quant-strategy = "quant.main:strategy_cli"
quant-trade = "quant.main:trade_cli"
quant-risk = "quant.main:risk_cli"
quant-monitor = "quant.main:monitor_cli"
quant-config = "quant.main:config_cli"
quant-main = "quant.main:main_cli"

[tool.setuptools]
packages = ["quant"]
package-dir = {"quant" = "."}

[tool.black]
line-length = 88
target-version = ['py38']

[tool.isort]
profile = "black"
line-length = 88 