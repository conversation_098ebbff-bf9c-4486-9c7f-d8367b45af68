#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
性能分析器模块，用于分析回测结果并生成图表
"""

import os
import sys
from datetime import datetime
from typing import Dict, List, Optional, Union

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns

# 添加项目根目录到系统路径
root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, root_path)

from utils.logger import get_unified_logger  # noqa: E402


class PerformanceAnalyzer:
    """
    性能分析器，用于计算回测性能指标和绘制各类图表
    """
    
    def __init__(self, **kwargs):
        """
        初始化性能分析器
        
        Args:
            **kwargs: 参数字典，可包含：
                - output_dir: 输出目录
                - benchmark_symbol: 基准指数代码
                - risk_free_rate: 无风险利率
                - logger: 日志记录器
        """
        self.output_dir = kwargs.get("output_dir", "./output")
        self.benchmark_symbol = kwargs.get("benchmark_symbol", "000300.SH")  # 沪深300作为默认基准
        self.risk_free_rate = kwargs.get("risk_free_rate", 0.03)  # 默认无风险利率3%
        self.logger = kwargs.get("logger", get_unified_logger("performance_analyzer"))
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 设置图表风格
        sns.set_style("whitegrid")
        plt.rcParams["font.sans-serif"] = ["SimHei"]  # 用来正常显示中文标签
        plt.rcParams["axes.unicode_minus"] = False  # 用来正常显示负号
    
    def analyze(self, equity_curve: pd.DataFrame, trades: List[Dict] = None) -> Dict:
        """
        分析回测结果，计算各项性能指标
        
        Args:
            equity_curve: 权益曲线DataFrame
            trades: 交易记录列表
            
        Returns:
            包含各项性能指标的字典
        """
        if equity_curve.empty:
            self.logger.warning("权益曲线为空，无法计算性能指标")
            return {}
        
        try:
            # 确保索引是日期时间类型
            if not isinstance(equity_curve.index, pd.DatetimeIndex):
                equity_curve = equity_curve.set_index('date')
            
            # 计算各项指标
            total_return = self._calculate_total_return(equity_curve)
            annual_return = self._calculate_annual_return(equity_curve)
            sharpe_ratio = self._calculate_sharpe_ratio(equity_curve)
            sortino_ratio = self._calculate_sortino_ratio(equity_curve)
            max_drawdown, max_drawdown_duration = self._calculate_max_drawdown(equity_curve)
            win_rate, profit_ratio = self._calculate_trade_metrics(trades)
            
            # 汇总指标
            metrics = {
                "total_return": round(total_return * 100, 2),  # 转换为百分比
                "annual_return": round(annual_return * 100, 2),  # 转换为百分比
                "sharpe_ratio": round(sharpe_ratio, 2),
                "sortino_ratio": round(sortino_ratio, 2),
                "max_drawdown": round(max_drawdown * 100, 2),  # 转换为百分比
                "max_drawdown_duration": max_drawdown_duration,
                "win_rate": round(win_rate * 100, 2),  # 转换为百分比
                "profit_ratio": round(profit_ratio, 2),
                "avg_holding_period": self._calculate_avg_holding_period(trades)
            }
            
            self.logger.info(f"性能指标计算完成: {metrics}")
            return metrics
            
        except Exception as e:
            self.logger.error(f"计算性能指标失败: {e}")
            return {}
    
    def _calculate_total_return(self, equity_curve: pd.DataFrame) -> float:
        """计算总收益率"""
        if 'equity' not in equity_curve.columns:
            return 0.0
        
        initial_equity = equity_curve['equity'].iloc[0]
        final_equity = equity_curve['equity'].iloc[-1]
        
        if initial_equity == 0:
            return 0.0
        
        return (final_equity / initial_equity) - 1
    
    def _calculate_annual_return(self, equity_curve: pd.DataFrame) -> float:
        """计算年化收益率"""
        if 'equity' not in equity_curve.columns:
            return 0.0
        
        initial_equity = equity_curve['equity'].iloc[0]
        final_equity = equity_curve['equity'].iloc[-1]
        
        if initial_equity == 0:
            return 0.0
        
        # 计算交易日数
        start_date = equity_curve.index[0]
        end_date = equity_curve.index[-1]
        days = (end_date - start_date).days
        
        if days <= 0:
            return 0.0
        
        # 计算年化收益率
        years = days / 365.0
        return ((final_equity / initial_equity) ** (1 / years)) - 1
    
    def _calculate_sharpe_ratio(self, equity_curve: pd.DataFrame) -> float:
        """计算夏普比率"""
        if 'returns' not in equity_curve.columns:
            return 0.0
        
        # 计算超额收益
        excess_returns = equity_curve['returns'] - (self.risk_free_rate / 252)
        
        # 计算夏普比率
        if excess_returns.std() == 0:
            return 0.0
        
        sharpe = (excess_returns.mean() / excess_returns.std()) * np.sqrt(252)
        return sharpe
    
    def _calculate_sortino_ratio(self, equity_curve: pd.DataFrame) -> float:
        """计算索提诺比率"""
        if 'returns' not in equity_curve.columns:
            return 0.0
        
        # 计算超额收益
        excess_returns = equity_curve['returns'] - (self.risk_free_rate / 252)
        
        # 计算下行标准差
        downside_returns = excess_returns[excess_returns < 0]
        downside_std = downside_returns.std()
        
        # 计算索提诺比率
        if downside_std == 0 or np.isnan(downside_std):
            return 0.0
        
        sortino = (excess_returns.mean() / downside_std) * np.sqrt(252)
        return sortino
    
    def _calculate_max_drawdown(self, equity_curve: pd.DataFrame) -> tuple:
        """计算最大回撤和最大回撤持续时间"""
        if 'equity' not in equity_curve.columns:
            return 0.0, 0
        
        # 计算累计最大值
        equity = equity_curve['equity']
        cummax = equity.cummax()
        drawdown = (equity / cummax) - 1
        
        # 找到最大回撤
        max_drawdown = drawdown.min()
        
        # 计算最大回撤持续时间
        # 找到回撤开始和结束的时间点
        drawdown_begin = drawdown[drawdown == 0].index[-1]
        drawdown_end = drawdown[drawdown == max_drawdown].index[0]
        
        # 计算持续天数
        max_drawdown_duration = (drawdown_end - drawdown_begin).days
        
        return abs(max_drawdown), max_drawdown_duration
    
    def _calculate_trade_metrics(self, trades: List[Dict]) -> tuple:
        """计算交易相关指标：胜率和盈亏比"""
        if not trades:
            return 0.0, 0.0
        
        # 统计盈利和亏损交易
        profits = [trade['profit'] for trade in trades if trade.get('profit', 0) > 0]
        losses = [trade['profit'] for trade in trades if trade.get('profit', 0) <= 0]
        
        # 计算胜率
        win_rate = len(profits) / len(trades) if trades else 0
        
        # 计算盈亏比
        avg_profit = np.mean(profits) if profits else 0
        avg_loss = abs(np.mean(losses)) if losses else 1  # 避免除以0
        profit_ratio = avg_profit / avg_loss if avg_loss != 0 else 0
        
        return win_rate, profit_ratio
    
    def _calculate_avg_holding_period(self, trades: List[Dict]) -> float:
        """计算平均持仓周期（天）"""
        if not trades:
            return 0.0
        
        holding_periods = []
        for trade in trades:
            entry_date = trade.get('entry_date')
            exit_date = trade.get('exit_date')
            
            if not entry_date or not exit_date:
                continue
            
            # 尝试转换日期字符串为日期对象
            try:
                if isinstance(entry_date, str):
                    entry_date = datetime.strptime(entry_date, '%Y-%m-%d')
                if isinstance(exit_date, str):
                    exit_date = datetime.strptime(exit_date, '%Y-%m-%d')
                
                # 计算持仓天数
                holding_days = (exit_date - entry_date).days
                holding_periods.append(max(1, holding_days))  # 至少1天
            except Exception:
                continue
        
        # 计算平均持仓周期
        if not holding_periods:
            return 0.0
        
        return np.mean(holding_periods)
    
    def plot_equity_curve(
        self, equity_curve: pd.DataFrame, title: str = "策略收益曲线",
        benchmark: pd.DataFrame = None, save_path: str = None
    ) -> str:
        """
        绘制权益曲线图
        
        Args:
            equity_curve: 权益曲线DataFrame
            title: 图表标题
            benchmark: 基准指数DataFrame，可选
            save_path: 保存路径，如不提供则自动生成
            
        Returns:
            图表保存路径
        """
        if equity_curve.empty or 'equity' not in equity_curve.columns:
            self.logger.warning("权益曲线数据无效，无法绘制图表")
            return ""
        
        try:
            # 创建画布
            plt.figure(figsize=(12, 6))
            
            # 绘制策略权益曲线
            plt.plot(
                equity_curve.index,
                equity_curve['equity'] / equity_curve['equity'].iloc[0] - 1,
                label="策略收益", color='blue', linewidth=2
            )
            
            # 绘制基准曲线（如果有）
            if benchmark is not None and not benchmark.empty and 'close' in benchmark.columns:
                # 确保基准数据的索引与权益曲线一致
                benchmark = benchmark.reindex(equity_curve.index, method='ffill')
                plt.plot(
                    benchmark.index,
                    benchmark['close'] / benchmark['close'].iloc[0] - 1,
                    label="基准收益", color='gray', linewidth=1, linestyle='--'
                )
            
            # 设置图表格式
            plt.title(title, fontsize=14)
            plt.xlabel("日期", fontsize=12)
            plt.ylabel("收益率", fontsize=12)
            plt.grid(True, alpha=0.3)
            plt.legend(loc='best')
            
            # 添加最大回撤阴影
            self._add_drawdown_shade(equity_curve)
            
            # 保存图表
            if not save_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                save_path = os.path.join(self.output_dir, f"equity_curve_{timestamp}.png")
            
            plt.tight_layout()
            plt.savefig(save_path, dpi=100)
            plt.close()
            
            self.logger.info(f"权益曲线图表已保存: {save_path}")
            return save_path
            
        except Exception as e:
            self.logger.error(f"绘制权益曲线图表失败: {e}")
            plt.close()
            return ""
    
    def _add_drawdown_shade(self, equity_curve: pd.DataFrame):
        """在权益曲线图上添加回撤阴影"""
        if 'equity' not in equity_curve.columns:
            return
        
        # 计算回撤
        equity = equity_curve['equity']
        cummax = equity.cummax()
        drawdown = (equity / cummax) - 1
        
        # 找到回撤开始和结束的区间
        is_drawdown = drawdown < 0
        
        # 如果没有回撤，直接返回
        if not is_drawdown.any():
            return
        
        # 找到回撤的开始和结束点
        drawdown_start = np.where(np.diff(is_drawdown.astype(int)) == 1)[0]
        drawdown_end = np.where(np.diff(is_drawdown.astype(int)) == -1)[0]
        
        # 处理边界情况
        if len(drawdown_start) == 0 and len(drawdown_end) == 0:
            return
        
        # 如果回撤从一开始就存在
        if (len(drawdown_end) > 0 and 
                (len(drawdown_start) == 0 or drawdown_end[0] < drawdown_start[0])):
            drawdown_start = np.insert(drawdown_start, 0, 0)
        
        # 如果回撤一直持续到最后
        if (len(drawdown_start) > 0 and 
                (len(drawdown_end) == 0 or drawdown_start[-1] > drawdown_end[-1])):
            drawdown_end = np.append(drawdown_end, len(drawdown) - 1)
        
        # 绘制回撤阴影
        for i in range(min(len(drawdown_start), len(drawdown_end))):
            start_idx = drawdown_start[i]
            end_idx = drawdown_end[i]
            
            # 确保索引有效
            if start_idx >= len(equity_curve) or end_idx >= len(equity_curve):
                continue
            
            start_date = equity_curve.index[start_idx]
            end_date = equity_curve.index[end_idx]
            
            # 添加阴影
            plt.axvspan(start_date, end_date, color='red', alpha=0.2)
    
    def plot_monthly_returns(
        self, equity_curve: pd.DataFrame, title: str = "月度收益热图",
        save_path: str = None
    ) -> str:
        """
        绘制月度收益热图
        
        Args:
            equity_curve: 权益曲线DataFrame
            title: 图表标题
            save_path: 保存路径，如不提供则自动生成
            
        Returns:
            图表保存路径
        """
        if equity_curve.empty or 'returns' not in equity_curve.columns:
            self.logger.warning("收益率数据无效，无法绘制月度收益热图")
            return ""
        
        try:
            # 准备月度收益数据
            monthly_returns = self._prepare_monthly_returns(equity_curve)
            
            if monthly_returns.empty:
                self.logger.warning("月度收益数据为空，无法绘制热图")
                return ""
            
            # 创建画布
            plt.figure(figsize=(12, 8))
            
            # 绘制热图
            sns.heatmap(
                monthly_returns,
                annot=True,
                fmt=".2f",
                cmap=sns.diverging_palette(10, 220, as_cmap=True),
                center=0,
                cbar_kws={'label': '月度收益率（%）'}
            )
            
            # 设置图表格式
            plt.title(title, fontsize=14)
            plt.ylabel("年份", fontsize=12)
            plt.xlabel("月份", fontsize=12)
            
            # 保存图表
            if not save_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                save_path = os.path.join(self.output_dir, f"monthly_returns_{timestamp}.png")
            
            plt.tight_layout()
            plt.savefig(save_path, dpi=100)
            plt.close()
            
            self.logger.info(f"月度收益热图已保存: {save_path}")
            return save_path
            
        except Exception as e:
            self.logger.error(f"绘制月度收益热图失败: {e}")
            plt.close()
            return ""
    
    def _prepare_monthly_returns(self, equity_curve: pd.DataFrame) -> pd.DataFrame:
        """准备月度收益数据用于热图绘制"""
        # 确保有收益率数据
        if 'returns' not in equity_curve.columns:
            return pd.DataFrame()
        
        # 计算月度收益
        equity_curve = equity_curve.copy()
        equity_curve['year'] = equity_curve.index.year
        equity_curve['month'] = equity_curve.index.month
        
        monthly_returns = equity_curve.groupby(['year', 'month'])['returns'].sum() * 100
        
        # 转换为表格形式
        monthly_returns = monthly_returns.unstack(level=1)
        
        # 填充缺失值
        monthly_returns = monthly_returns.fillna(0)
        
        # 调整列顺序
        if not monthly_returns.empty:
            if set(monthly_returns.columns) != set(range(1, 13)):
                for month in range(1, 13):
                    if month not in monthly_returns.columns:
                        monthly_returns[month] = 0
                monthly_returns = monthly_returns.reindex(columns=range(1, 13))
        
        return monthly_returns
    
    def plot_drawdown_distribution(
        self, equity_curve: pd.DataFrame, title: str = "回撤分布",
        save_path: str = None
    ) -> str:
        """
        绘制回撤分布图
        
        Args:
            equity_curve: 权益曲线DataFrame
            title: 图表标题
            save_path: 保存路径，如不提供则自动生成
            
        Returns:
            图表保存路径
        """
        if equity_curve.empty or 'equity' not in equity_curve.columns:
            self.logger.warning("权益曲线数据无效，无法绘制回撤分布图")
            return ""
        
        try:
            # 计算回撤序列
            equity = equity_curve['equity']
            cummax = equity.cummax()
            drawdown_series = (equity / cummax) - 1
            
            # 创建画布
            plt.figure(figsize=(12, 6))
            
            # 绘制回撤分布图
            plt.plot(equity_curve.index, drawdown_series * 100, color='red', linewidth=1.5)
            plt.fill_between(
                equity_curve.index, drawdown_series * 100, 0,
                color='red', alpha=0.3
            )
            
            # 标记最大回撤
            max_drawdown_date = drawdown_series.idxmin()
            max_drawdown = drawdown_series.min() * 100
            
            plt.scatter(
                max_drawdown_date, max_drawdown,
                color='darkred', s=80, zorder=5
            )
            plt.annotate(
                f'最大回撤: {max_drawdown:.2f}%',
                xy=(max_drawdown_date, max_drawdown),
                xytext=(20, -30),
                textcoords='offset points',
                arrowprops=dict(arrowstyle='->', color='black'),
                bbox=dict(boxstyle='round,pad=0.5', facecolor='yellow', alpha=0.5)
            )
            
            # 设置图表格式
            plt.title(title, fontsize=14)
            plt.xlabel("日期", fontsize=12)
            plt.ylabel("回撤（%）", fontsize=12)
            plt.grid(True, alpha=0.3)
            plt.ylim(min(drawdown_series) * 110, 5)  # 设置y轴范围，留出标注空间
            
            # 保存图表
            if not save_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                save_path = os.path.join(self.output_dir, f"drawdown_distribution_{timestamp}.png")
            
            plt.tight_layout()
            plt.savefig(save_path, dpi=100)
            plt.close()
            
            self.logger.info(f"回撤分布图已保存: {save_path}")
            return save_path
            
        except Exception as e:
            self.logger.error(f"绘制回撤分布图失败: {e}")
            plt.close()
            return ""
    
    def plot_returns_distribution(
        self, equity_curve: pd.DataFrame, title: str = "收益分布",
        bins: int = 50, save_path: str = None
    ) -> str:
        """
        绘制收益分布图
        
        Args:
            equity_curve: 权益曲线DataFrame
            title: 图表标题
            bins: 直方图的桶数量
            save_path: 保存路径，如不提供则自动生成
            
        Returns:
            图表保存路径
        """
        if equity_curve.empty or 'returns' not in equity_curve.columns:
            self.logger.warning("收益率数据无效，无法绘制收益分布图")
            return ""
        
        try:
            # 提取收益率数据
            returns = equity_curve['returns'] * 100  # 转为百分比
            
            # 创建画布
            plt.figure(figsize=(12, 6))
            
            # 绘制分布图
            sns.histplot(
                returns,
                bins=bins,
                kde=True,
                color='blue',
                stat='density'
            )
            
            # 添加正态分布拟合曲线
            mu = returns.mean()
            sigma = returns.std()
            x = np.linspace(returns.min(), returns.max(), 100)
            y = 1 / (sigma * np.sqrt(2 * np.pi)) * np.exp(-(x - mu)**2 / (2 * sigma**2))
            plt.plot(x, y, 'r--', linewidth=2, label='正态分布拟合')
            
            # 标记均值和标准差
            plt.axvline(mu, color='green', linestyle='dashed', linewidth=2, label=f'均值: {mu:.2f}%')
            plt.axvline(mu + sigma, color='orange', linestyle='dashdot', linewidth=1)
            plt.axvline(mu - sigma, color='orange', linestyle='dashdot', linewidth=1, 
                       label=f'标准差: {sigma:.2f}%')
            
            # 设置图表格式
            plt.title(title, fontsize=14)
            plt.xlabel("日收益率（%）", fontsize=12)
            plt.ylabel("密度", fontsize=12)
            plt.grid(True, alpha=0.3)
            plt.legend()
            
            # 保存图表
            if not save_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                save_path = os.path.join(self.output_dir, f"returns_distribution_{timestamp}.png")
            
            plt.tight_layout()
            plt.savefig(save_path, dpi=100)
            plt.close()
            
            self.logger.info(f"收益分布图已保存: {save_path}")
            return save_path
            
        except Exception as e:
            self.logger.error(f"绘制收益分布图失败: {e}")
            plt.close()
            return ""
    
    def create_performance_report(
        self, equity_curve: pd.DataFrame, trades: List[Dict] = None,
        benchmark: pd.DataFrame = None, strategy_name: str = "策略回测",
        save_path: str = None
    ) -> str:
        """
        创建完整的性能分析报告
        
        Args:
            equity_curve: 权益曲线DataFrame
            trades: 交易记录列表
            benchmark: 基准指数DataFrame
            strategy_name: 策略名称
            save_path: 保存路径，如不提供则自动生成
            
        Returns:
            报告保存路径
        """
        if equity_curve.empty:
            self.logger.warning("权益曲线为空，无法创建性能分析报告")
            return ""
        
        try:
            # 计算性能指标
            performance = self.analyze(equity_curve, trades)
            
            # 创建报告目录
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            if not save_path:
                report_dir = os.path.join(
                    self.output_dir, f"{strategy_name.replace(' ', '_')}_{timestamp}"
                )
                os.makedirs(report_dir, exist_ok=True)
            else:
                report_dir = save_path
                os.makedirs(report_dir, exist_ok=True)
            
            # 绘制各类图表
            self.plot_equity_curve(
                equity_curve,
                title=f"{strategy_name}收益曲线",
                benchmark=benchmark,
                save_path=os.path.join(report_dir, "equity_curve.png")
            )
            
            self.plot_monthly_returns(
                equity_curve,
                title=f"{strategy_name}月度收益",
                save_path=os.path.join(report_dir, "monthly_returns.png")
            )
            
            self.plot_drawdown_distribution(
                equity_curve,
                title=f"{strategy_name}回撤分布",
                save_path=os.path.join(report_dir, "drawdown.png")
            )
            
            self.plot_returns_distribution(
                equity_curve,
                title=f"{strategy_name}收益分布",
                save_path=os.path.join(report_dir, "returns_distribution.png")
            )
            
            # 保存性能指标
            with open(os.path.join(report_dir, "performance.txt"), "w") as f:
                f.write(f"策略名称: {strategy_name}\n")
                f.write(f"回测时间: {timestamp}\n")
                f.write(f"回测区间: {equity_curve.index[0]} 至 {equity_curve.index[-1]}\n\n")
                f.write("性能指标:\n")
                for key, value in performance.items():
                    f.write(f"{key}: {value}\n")
            
            # 保存交易记录
            if trades:
                pd.DataFrame(trades).to_csv(os.path.join(report_dir, "trades.csv"), index=False)
            
            # 保存权益曲线
            equity_curve.to_csv(os.path.join(report_dir, "equity_curve.csv"))
            
            self.logger.info(f"性能分析报告已保存至: {report_dir}")
            return report_dir
            
        except Exception as e:
            self.logger.error(f"创建性能分析报告失败: {e}")
            return ""


if __name__ == "__main__":
    # 测试代码
    from backtest.engine import BacktestEngine
    from config.settings import setup_system
    from strategy.ma_cross_strategy import MACrossStrategy

    # 初始化系统
    setup_system()
    
    # 创建回测引擎
    backtest = BacktestEngine(
        strategy=MACrossStrategy,
        strategy_params={"short_period": 5, "long_period": 20},
        start_date="20230101",
        end_date="20231231",
        period="1d",
        symbols=["000001.SZ", "600000.SH"],
        initial_capital=100000,
        commission_rate=0.0003
    )
    
    # 运行回测
    results = backtest.run_backtest()
    
    # 创建性能分析器
    analyzer = PerformanceAnalyzer(output_dir="./output")
    
    # 分析结果
    performance = analyzer.analyze(
        results["equity_curve"],
        results["trades"]
    )
    
    # 生成报告
    report_path = analyzer.create_performance_report(
        results["equity_curve"],
        results["trades"],
        strategy_name="MA交叉策略"
    )
    
    print(f"报告已生成: {report_path}") 