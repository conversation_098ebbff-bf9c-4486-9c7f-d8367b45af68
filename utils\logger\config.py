#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
日志配置功能模块

提供日志系统配置相关功能
"""

import os
import sys
import logging
import platform
import warnings
from typing import Dict, List, Optional, Union, Any

# 将项目根目录添加到Python路径
# Get the directory of the current file (config.py within utils/logger)
current_file_dir = os.path.dirname(os.path.abspath(__file__))
# Assuming the original root_path derivation was correct for the project structure:
root_path = os.path.dirname(os.path.dirname(
    os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, root_path)

# 导入统一配置
from config.settings import LOG_DIR, LOG_LEVEL, LOG_FORMAT, DATE_FORMAT


# 日志输出目标枚举
class LogTarget:
    """
    日志输出目标枚举
    
    定义了日志输出的目标位置：文件、控制台或两者
    
    属性:
        FILE (1): 只输出到文件
        CONSOLE (2): 只输出到控制台
        BOTH (3): 同时输出到文件和控制台
    
    推荐用法:
        logger.info(LogTarget.FILE, "只输出到文件的消息")
        logger.info(LogTarget.CONSOLE, "只输出到控制台的消息")
        logger.info(LogTarget.BOTH, "同时输出到文件和控制台的消息")
    """
    FILE = 1      # 只记录到文件
    CONSOLE = 2   # 只显示在控制台
    BOTH = 3      # 同时记录到文件和显示在控制台
    
    @staticmethod
    def from_code(code: Union[int, str, Any]) -> int:
        """
        从数字代码或字符串获取目标类型
        
        Args:
            code: 目标代码（1/2/3或'file'/'console'/'both'）
            
        Returns:
            int: 标准化的目标代码
            
        注意:
            直接使用数字代码(1,2,3)的方式已弃用，建议使用LogTarget枚举
        """
        if isinstance(code, int) and 1 <= code <= 3:
            warnings.warn(
                "使用数字代码指定日志目标已弃用，建议使用LogTarget枚举",
                DeprecationWarning,
                stacklevel=2
            )
            return code
        elif isinstance(code, str):
            code_lower = code.lower()
            if code_lower == 'file':
                return LogTarget.FILE
            elif code_lower == 'console':
                return LogTarget.CONSOLE
            elif code_lower == 'both':
                return LogTarget.BOTH
        # 默认返回FILE
        return LogTarget.FILE


# 日志级别映射
LOG_LEVELS = {
    "debug": logging.DEBUG,
    "info": logging.INFO,
    "warning": logging.WARNING,
    "error": logging.ERROR,
    "critical": logging.CRITICAL
}

# 带任务ID的日志格式
TASK_ID_LOG_FORMAT = "【%(task_id)s】%(asctime)s - %(name)s - %(levelname)s - %(message)s"
# 默认日志格式
DEFAULT_LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
# 详细日志格式 (添加进程ID、线程信息和调用位置)
DETAILED_LOG_FORMAT = (
    "%(asctime)s - %(name)s - %(levelname)s - "
    "[%(process)d:%(thread)d] (%(filename)s:%(lineno)d) - %(message)s"
)
# 控制台日志格式
CONSOLE_LOG_FORMAT = DEFAULT_LOG_FORMAT
# 默认日期格式
DEFAULT_DATE_FORMAT = "%Y-%m-%d %H:%M:%S"

# 缓存已创建的日志器
_loggers: Dict[str, logging.Logger] = {}


def get_appropriate_encoding():
    """
    根据操作系统获取合适的日志文件编码
    
    在Windows系统上使用带BOM头的UTF-8编码(utf-8-sig)，
    在其他系统上使用标准UTF-8编码。这样可以确保在Windows下
    正确显示中文字符。
    """
    # Windows系统使用带BOM的UTF-8编码
    if platform.system() == 'Windows':
        return 'utf-8-sig'
    # 其他系统使用标准UTF-8编码
    return 'utf-8'


def get_log_file_path(filename: Optional[str] = None, logger_name: Optional[str] = None) -> str:
    """
    获取日志文件的路径

    Args:
        filename: 日志文件名，不指定则根据logger_name或当前日期生成
        logger_name: 日志记录器名称，用于生成默认文件名

    Returns:
        str: 日志文件的完整路径
    """
    # 获取项目日志目录
    logs_dir = os.path.join(root_path, "logs")

    # 确保日志目录存在
    if not os.path.exists(logs_dir):
        os.makedirs(logs_dir, exist_ok=True)

    # 如果指定了文件名
    if filename:
        # 如果文件名不包含路径，添加日志目录
        if os.path.dirname(filename) == '':
            return os.path.join(logs_dir, filename)
        # 如果已经是完整路径，直接返回
        return filename

    # 根据logger_name生成文件名
    if logger_name:
        return os.path.join(logs_dir, f"{logger_name}.log")

    # 默认使用日期命名
    from datetime import datetime
    date_str = datetime.now().strftime("%Y%m%d")
    return os.path.join(logs_dir, f"quant_{date_str}.log")


def list_log_files() -> List[str]:
    """
    列出日志目录中的所有日志文件

    Returns:
        List[str]: 日志文件路径列表
    """
    logs_dir = os.path.join(root_path, "logs")

    # 确保日志目录存在
    if not os.path.exists(logs_dir):
        return []

    # 获取所有日志文件
    log_files = []
    for file in os.listdir(logs_dir):
        if file.endswith(".log"):
            log_files.append(os.path.join(logs_dir, file))

    # 按修改时间降序排序
    log_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)

    return log_files


def get_all_loggers() -> Dict[str, logging.Logger]:
    """
    获取所有已创建的日志记录器

    Returns:
        Dict[str, logging.Logger]: 名称到日志记录器的映射
    """
    # 返回内部缓存的日志记录器和logging模块管理的所有日志记录器
    result = _loggers.copy()
    
    # 获取logging模块管理的所有日志记录器
    for name, logger in logging.Logger.manager.loggerDict.items():
        if isinstance(logger, logging.Logger):
            result[name] = logger
    
    return result
