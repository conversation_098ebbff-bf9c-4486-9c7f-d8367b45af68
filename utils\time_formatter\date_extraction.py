#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
统一日期提取模块

提供项目中所有日期/时间提取功能的统一实现，消除代码重复。

功能：
1. 从文件路径中提取日期信息
2. 从DataFrame中提取时间戳
3. 支持多种文件名格式和时间列格式
4. 统一的错误处理和日志记录

版本: v1.0
作者: Augment AI
日期: 2025-07-22
"""

import os
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Optional, Union, List, Tuple, Dict
from pathlib import Path

from utils.logger import get_unified_logger, LogTarget
from utils.smart_time_converter import smart_to_datetime, simple_ms_to_datetime, TimeConversionError

# 创建统一日志记录器
logger = get_unified_logger(__name__, enhanced=True)

# 日期提取结果缓存
_date_extraction_cache = {}

# 批量日志控制
_batch_log_stats = {
    'total_extractions': 0,
    'successful_extractions': 0,
    'date_range': {'earliest': None, 'latest': None},
    'last_log_time': 0,
    'log_interval': 100  # 每100次提取输出一次进度日志
}


def _update_batch_log_stats(date_str: Optional[str], operation_type: str = "directory_scan"):
    """
    更新批量日志统计信息

    Args:
        date_str: 提取到的日期字符串
        operation_type: 操作类型，用于区分不同的批量操作
    """
    global _batch_log_stats

    _batch_log_stats['total_extractions'] += 1

    if date_str:
        _batch_log_stats['successful_extractions'] += 1

        # 更新日期范围
        date_only = date_str[:8]  # 只取日期部分 YYYYMMDD
        if _batch_log_stats['date_range']['earliest'] is None or date_only < _batch_log_stats['date_range']['earliest']:
            _batch_log_stats['date_range']['earliest'] = date_only
        if _batch_log_stats['date_range']['latest'] is None or date_only > _batch_log_stats['date_range']['latest']:
            _batch_log_stats['date_range']['latest'] = date_only

    # 检查是否需要输出进度日志
    if _batch_log_stats['total_extractions'] % _batch_log_stats['log_interval'] == 0:
        earliest = _batch_log_stats['date_range']['earliest'] or "未知"
        latest = _batch_log_stats['date_range']['latest'] or "未知"
        success_rate = (_batch_log_stats['successful_extractions'] / _batch_log_stats['total_extractions']) * 100

        logger.debug(f"批量日期提取进度: 已处理 {_batch_log_stats['total_extractions']} 个目录, "
                    f"成功率 {success_rate:.1f}%, 日期范围: {earliest} ~ {latest}")


def _log_batch_summary(operation_name: str = "目录扫描"):
    """
    输出批量操作的汇总日志

    Args:
        operation_name: 操作名称
    """
    global _batch_log_stats

    if _batch_log_stats['total_extractions'] > 0:
        earliest = _batch_log_stats['date_range']['earliest'] or "未知"
        latest = _batch_log_stats['date_range']['latest'] or "未知"
        success_rate = (_batch_log_stats['successful_extractions'] / _batch_log_stats['total_extractions']) * 100

        logger.info(f"{operation_name}完成: 共处理 {_batch_log_stats['total_extractions']} 个目录, "
                   f"成功提取 {_batch_log_stats['successful_extractions']} 个日期 ({success_rate:.1f}%), "
                   f"日期范围: {earliest} ~ {latest}")

    # 重置统计信息
    _batch_log_stats.update({
        'total_extractions': 0,
        'successful_extractions': 0,
        'date_range': {'earliest': None, 'latest': None},
        'last_log_time': 0
    })


def extract_date_from_path(file_path: str) -> Optional[str]:
    """
    从文件路径中提取日期信息（带缓存优化）

    支持多种文件名格式：
    - YYYYMMDD.parquet (如: 20250722.parquet)
    - YYYY.parquet (如: 2025.parquet)
    - DD.parquet (如: 30.parquet，需要年/月/日目录结构)
    - 年/月/日目录结构 (如: 2025/07/22.parquet)

    Args:
        file_path: 文件路径

    Returns:
        Optional[str]: 格式为'YYYYMMDDHHMMSS'的时间戳，如果提取失败则返回None
    """
    if not file_path:
        return None

    # 检查缓存
    if file_path in _date_extraction_cache:
        return _date_extraction_cache[file_path]

    try:
        # 标准化路径
        file_path = os.path.normpath(file_path)
        
        # 获取文件名（不含扩展名）
        filename = os.path.basename(file_path)
        if filename.endswith('.parquet'):
            filename = os.path.splitext(filename)[0]
        
        # 方法1: 直接从文件名提取日期
        result = None
        if filename.isdigit():
            if len(filename) == 8:  # YYYYMMDD格式
                logger.debug(f"从文件名提取到YYYYMMDD格式日期: {filename}")
                result = f"{filename}000000"
            elif len(filename) == 6:  # YYYYMM格式
                logger.debug(f"从文件名提取到YYYYMM格式日期: {filename}")
                result = f"{filename}01000000"  # 使用月份的第一天
            elif len(filename) == 4:  # YYYY格式
                logger.debug(f"从文件名提取到YYYY格式日期: {filename}")
                result = f"{filename}0101000000"  # 使用年份的第一天
            elif len(filename) <= 2:  # DD格式，需要从目录结构提取年月
                result = _extract_date_from_directory_structure(file_path, filename)

        # 方法2: 从文件路径的目录结构中提取日期
        if result is None:
            result = _extract_date_from_path_parts(file_path)

        # 缓存结果
        _date_extraction_cache[file_path] = result
        return result
        
    except Exception as e:
        logger.debug(f"从文件路径提取日期失败: {file_path} - {e}")
        return None


def _extract_date_from_directory_structure(file_path: str, day_str: str) -> Optional[str]:
    """
    从年/月/日目录结构中提取日期（优化版：智能日志控制）

    Args:
        file_path: 文件路径
        day_str: 日期字符串

    Returns:
        Optional[str]: 格式为'YYYYMMDDHHMMSS'的时间戳
    """
    try:
        parent_dir = os.path.dirname(file_path)
        parent_name = os.path.basename(parent_dir)

        if parent_name.isdigit() and len(parent_name) <= 2:  # 月份
            # 检查上级目录，可能是年份
            upper_dir = os.path.dirname(parent_dir)
            upper_name = os.path.basename(upper_dir)

            if upper_name.isdigit() and len(upper_name) == 4:  # 年份
                # 这是年/月/日结构
                year = upper_name
                month = parent_name.zfill(2)
                day = day_str.zfill(2)
                result = f"{year}{month}{day}000000"

                # 更新批量日志统计（替代原来的单条日志）
                _update_batch_log_stats(result, "directory_structure_scan")

                return result

        # 更新统计（提取失败的情况）
        _update_batch_log_stats(None, "directory_structure_scan")
        return None

    except Exception as e:
        logger.debug(f"从目录结构提取日期失败: {file_path} - {e}")
        _update_batch_log_stats(None, "directory_structure_scan")
        return None


def _extract_date_from_path_parts(file_path: str) -> Optional[str]:
    """
    从文件路径的各个部分中提取日期信息
    
    Args:
        file_path: 文件路径
        
    Returns:
        Optional[str]: 格式为'YYYYMMDDHHMMSS'的时间戳
    """
    try:
        # 分割路径
        parts = file_path.split(os.sep)
        
        # 查找年/月/日结构
        for i in range(len(parts) - 2):
            if (parts[i].isdigit() and len(parts[i]) == 4 and  # 年份
                i + 2 < len(parts) and 
                parts[i+1].isdigit() and len(parts[i+1]) <= 2 and  # 月份
                parts[i+2].split('.')[0].isdigit() and len(parts[i+2].split('.')[0]) <= 2):  # 日期
                
                year = parts[i]
                month = parts[i+1].zfill(2)
                day = parts[i+2].split('.')[0].zfill(2)
                
                logger.debug(f"从路径中的年/月/日结构提取到日期: {year}/{month}/{day}")
                return f"{year}{month}{day}000000"
        
        return None
        
    except Exception as e:
        logger.debug(f"从路径部分提取日期失败: {file_path} - {e}")
        return None


def extract_timestamp_from_data(df: pd.DataFrame, default_time: Optional[str] = None) -> Optional[str]:
    """
    从DataFrame中提取时间戳
    
    统一实现，替代项目中所有重复的extract_timestamp_from_data函数。
    
    Args:
        df: 数据DataFrame
        default_time: 如果从数据中提取失败，使用的备选时间戳
        
    Returns:
        Optional[str]: 格式为'YYYYMMDD'或'YYYYMMDDHHMMSS'的时间戳，如果提取失败则返回None
    """
    if df is None or df.empty:
        return default_time[:8] if default_time else None
    
    try:
        # 方法1: 从DatetimeIndex提取
        if isinstance(df.index, pd.DatetimeIndex):
            last_timestamp = df.index[-1]
            timestamp_str = last_timestamp.strftime('%Y%m%d%H%M%S')
            logger.debug(f"从DatetimeIndex获取到时间戳: {timestamp_str}")
            return timestamp_str
        
        # 方法2: 从时间列提取
        time_columns = _get_time_column_names()
        
        for col_name in time_columns:
            if col_name in df.columns:
                try:
                    last_time = df[col_name].iloc[-1]
                    
                    # 处理不同类型的时间表示
                    if pd.api.types.is_datetime64_any_dtype(last_time):
                        timestamp_str = smart_to_datetime(last_time).strftime('%Y%m%d%H%M%S')
                        logger.debug(f"从{col_name}列获取到时间戳: {timestamp_str}")
                        return timestamp_str
                    elif isinstance(last_time, (int, float, np.integer, np.floating)):
                        # 处理数值时间戳
                        return _extract_timestamp_from_numeric(last_time, col_name)
                    elif isinstance(last_time, str):
                        # 处理字符串时间
                        return _extract_timestamp_from_string(last_time, col_name)
                        
                except Exception as e:
                    logger.debug(f"从{col_name}列提取时间戳失败: {e}")
                    continue
        
        # 方法3: 从字符串索引提取日期
        if len(df.index) > 0:
            last_index = str(df.index[-1])
            if len(last_index) >= 8 and last_index[:8].isdigit():
                logger.debug(f"从索引提取到日期: {last_index[:8]}")
                return last_index[:8]
        
        # 如果所有方法都失败，返回默认时间
        return default_time[:8] if default_time else None
        
    except Exception as e:
        logger.error(f"提取时间戳失败: {e}")
        return default_time[:8] if default_time else None


def _get_time_column_names() -> List[str]:
    """
    获取可能的时间列名列表，按优先级排序
    
    Returns:
        List[str]: 时间列名列表
    """
    # 标准时间列名
    time_columns = ['time', 'datetime', 'date', 'timestamp', 'Time', 'DateTime', 'Date', 'Timestamp']
    
    # tick数据特殊时间列名
    tick_time_columns = [
        'TradingDay', 'UpdateTime', 'ExchangeTime', 'Date', 'DateTime', 'timestamp',
        'trade_time', 'TradeTime', 'trade_datetime', 'TradeDateTime', 
        'quote_time', 'QuoteTime', 'tick_time', 'TickTime',
        'market_time', 'MarketTime', 'exchange_time', 'ExchangeTime'
    ]
    
    # 合并并去重，保持优先级
    return time_columns + [col for col in tick_time_columns if col not in time_columns]


def _extract_timestamp_from_numeric(time_value: Union[int, float, np.integer, np.floating], 
                                   col_name: str) -> Optional[str]:
    """
    从数值时间戳中提取时间
    
    Args:
        time_value: 数值时间戳
        col_name: 列名
        
    Returns:
        Optional[str]: 格式化的时间戳字符串
    """
    try:
        # 转换为Python原生类型
        time_value = int(time_value)
        time_str = str(time_value)
        
        # 检查是否是毫秒级时间戳
        if len(time_str) >= 13 or (time_value > 1500000000000 and time_value < 2000000000000):
            # 毫秒级时间戳
            dt = simple_ms_to_datetime(time_value)
            
            # 检查生成的时间戳是否合理 (1980年之后)
            if dt.year < 1980:
                logger.warning(f"从{col_name}列检测到不合理的时间戳，时间戳值: {time_value}")
                return None
            
            timestamp_str = dt.strftime('%Y%m%d%H%M%S')
            logger.debug(f"从{col_name}列的毫秒级时间戳({time_value})获取到时间戳: {timestamp_str}")
            return timestamp_str
        else:
            # 秒级时间戳或其他格式
            dt = smart_to_datetime(time_value, unit='s')
            timestamp_str = dt.strftime('%Y%m%d%H%M%S')
            logger.debug(f"从{col_name}列的秒级时间戳({time_value})获取到时间戳: {timestamp_str}")
            return timestamp_str
            
    except (TimeConversionError, Exception) as e:
        logger.debug(f"转换{col_name}列的数值时间戳({time_value})失败: {e}")
        return None


def _extract_timestamp_from_string(time_str: str, col_name: str) -> Optional[str]:
    """
    从字符串时间中提取时间戳
    
    Args:
        time_str: 时间字符串
        col_name: 列名
        
    Returns:
        Optional[str]: 格式化的时间戳字符串
    """
    try:
        # 检查是否是纯数字字符串（可能是时间戳）
        if time_str.isdigit():
            if len(time_str) >= 8:
                # 可能是YYYYMMDD或YYYYMMDDHHMMSS格式
                if len(time_str) == 8:
                    logger.debug(f"从{col_name}列提取到YYYYMMDD格式: {time_str}")
                    return f"{time_str}000000"
                elif len(time_str) >= 14:
                    logger.debug(f"从{col_name}列提取到完整时间戳: {time_str[:14]}")
                    return time_str[:14]
        
        # 尝试使用智能时间转换器解析
        dt = smart_to_datetime(time_str)
        timestamp_str = dt.strftime('%Y%m%d%H%M%S')
        logger.debug(f"从{col_name}列的字符串时间({time_str})获取到时间戳: {timestamp_str}")
        return timestamp_str
        
    except Exception as e:
        logger.debug(f"解析{col_name}列的字符串时间({time_str})失败: {e}")
        return None


def sort_files_by_date(file_paths: List[str]) -> List[str]:
    """
    根据文件路径中的日期信息对文件进行排序
    
    Args:
        file_paths: 文件路径列表
        
    Returns:
        List[str]: 按日期排序后的文件路径列表
    """
    if not file_paths:
        return []
    
    # 提取每个文件的日期信息
    file_dates = []
    for file_path in file_paths:
        date_str = extract_date_from_path(file_path)
        if date_str:
            file_dates.append((file_path, date_str))
        else:
            # 如果无法提取日期，使用文件修改时间作为备选
            try:
                mtime = os.path.getmtime(file_path)
                date_str = datetime.fromtimestamp(mtime).strftime('%Y%m%d%H%M%S')
                file_dates.append((file_path, date_str))
                logger.debug(f"使用文件修改时间作为排序依据: {file_path}")
            except Exception as e:
                logger.warning(f"无法获取文件时间信息: {file_path} - {e}")
                # 使用默认时间戳
                file_dates.append((file_path, '19700101000000'))
    
    # 按日期排序
    file_dates.sort(key=lambda x: x[1])
    
    return [file_path for file_path, _ in file_dates]


def get_latest_file_by_date(file_paths: List[str]) -> Optional[str]:
    """
    根据日期信息获取最新的文件
    
    Args:
        file_paths: 文件路径列表
        
    Returns:
        Optional[str]: 最新文件的路径，如果列表为空则返回None
    """
    if not file_paths:
        return None
    
    sorted_files = sort_files_by_date(file_paths)
    return sorted_files[-1] if sorted_files else None


def get_earliest_file_by_date(file_paths: List[str]) -> Optional[str]:
    """
    根据日期信息获取最早的文件

    Args:
        file_paths: 文件路径列表

    Returns:
        Optional[str]: 最早文件的路径，如果列表为空则返回None
    """
    if not file_paths:
        return None

    sorted_files = sort_files_by_date(file_paths)
    return sorted_files[0] if sorted_files else None


def extract_dates_from_paths_batch(file_paths: List[str]) -> Dict[str, Optional[str]]:
    """
    批量从文件路径中提取日期信息（性能优化版本，带汇总日志）

    Args:
        file_paths: 文件路径列表

    Returns:
        Dict[str, Optional[str]]: 文件路径到日期字符串的映射
    """
    if not file_paths:
        return {}

    logger.info(f"开始批量日期提取: 共 {len(file_paths)} 个文件路径")

    results = {}

    # 批量处理，减少重复的路径解析
    for file_path in file_paths:
        results[file_path] = extract_date_from_path(file_path)

    # 输出批量操作汇总日志
    _log_batch_summary("批量日期提取")

    return results


def clear_date_extraction_cache():
    """
    清理日期提取缓存

    在处理大量文件后调用此函数可以释放内存
    """
    global _date_extraction_cache
    _date_extraction_cache.clear()
    logger.debug("已清理日期提取缓存")


def start_batch_operation(operation_name: str = "批量目录扫描"):
    """
    开始批量操作，重置统计信息并输出开始日志

    Args:
        operation_name: 操作名称
    """
    global _batch_log_stats

    # 重置统计信息
    _batch_log_stats.update({
        'total_extractions': 0,
        'successful_extractions': 0,
        'date_range': {'earliest': None, 'latest': None},
        'last_log_time': 0
    })

    logger.info(f"开始{operation_name}...")


def end_batch_operation(operation_name: str = "批量目录扫描"):
    """
    结束批量操作，输出汇总日志

    Args:
        operation_name: 操作名称
    """
    _log_batch_summary(operation_name)


def get_cache_stats() -> Dict[str, int]:
    """
    获取缓存统计信息

    Returns:
        Dict[str, int]: 缓存统计信息
    """
    return {
        'cache_size': len(_date_extraction_cache),
        'cached_paths': len([k for k, v in _date_extraction_cache.items() if v is not None])
    }
