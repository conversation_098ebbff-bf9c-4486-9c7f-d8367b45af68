#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据清洗功能模块

提供数据清洗、重命名列、去重、类型转换等功能
"""

import os
import sys
import logging
import numpy as np
import pandas as pd
from typing import Any, Dict, List, Optional, Set, Tuple, Union, Callable

# 将项目根目录添加到Python路径
root_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, root_path)

# 导入日志模块
from utils.logger import get_unified_logger
from utils.smart_time_converter import smart_to_datetime

# 设置日志记录器
logger = get_unified_logger(__name__)


def convert_dtypes(df: pd.DataFrame, optimize_memory: bool = True) -> pd.DataFrame:
    """
    自动将DataFrame列转换为合适的数据类型以优化内存使用

    Args:
        df: 输入DataFrame
        optimize_memory: 是否尝试优化内存使用，默认为True

    Returns:
        转换后的DataFrame
    """
    if df.empty:
        logger.warning("输入的DataFrame为空")
        return df
    
    result = df.copy()
    
    # 跟踪内存使用变化
    initial_memory = result.memory_usage(deep=True).sum()
    
    # 对所有列尝试转换
    for col in result.columns:
        # 跳过已经是分类型的列
        if pd.api.types.is_categorical_dtype(result[col]):
            continue
            
        col_data = result[col]
        
        # 对于对象类型列（通常是字符串）
        if pd.api.types.is_object_dtype(col_data):
            # 尝试转换为数值类型
            try:
                num_data = pd.to_numeric(col_data, errors='coerce')
                # 如果没有太多缺失值，而且成功转换为数字，则保留该转换
                if num_data.isna().mean() < 0.5:  # 缺失值少于50%
                    result[col] = num_data
                    # 进一步转换为最优数值类型
                    if optimize_memory:
                        result[col] = _optimize_numeric_column(result[col])
                    continue
            except:
                pass
            
            # 尝试转换为日期时间类型
            try:
                date_data = smart_to_datetime(col_data, errors='coerce')
                # 如果没有太多缺失值，而且成功转换为日期，则保留该转换
                if date_data.isna().mean() < 0.5:  # 缺失值少于50%
                    result[col] = date_data
                    continue
            except:
                pass
            
            # 尝试转换为分类类型（对于重复值多的列）
            if optimize_memory and col_data.nunique() < len(col_data) * 0.5:  # 唯一值少于50%
                result[col] = col_data.astype('category')
                
        # 对于数值类型列，尝试优化内存使用
        elif optimize_memory and pd.api.types.is_numeric_dtype(col_data):
            result[col] = _optimize_numeric_column(col_data)
    
    # 记录优化结果
    if optimize_memory:
        final_memory = result.memory_usage(deep=True).sum()
        saved = initial_memory - final_memory
        if saved > 0:
            logger.info(f"数据类型转换节省了 {saved / 1024**2:.2f} MB 内存 "
                      f"({saved / initial_memory:.1%} 减少)")
    
    return result


def _optimize_numeric_column(col: pd.Series) -> pd.Series:
    """
    优化数值列的内存使用

    Args:
        col: 输入Series

    Returns:
        优化后的Series
    """
    # 对于整数列
    if pd.api.types.is_integer_dtype(col):
        # 检查值范围，选择合适的整数类型
        c_min, c_max = col.min(), col.max()
        
        # 是否可以用无符号整数
        if c_min >= 0:
            if c_max < 2**8:
                return col.astype(np.uint8)
            elif c_max < 2**16:
                return col.astype(np.uint16)
            elif c_max < 2**32:
                return col.astype(np.uint32)
            elif c_max < 2**64:
                return col.astype(np.uint64)
        # 有符号整数
        else:
            if c_min > -2**7 and c_max < 2**7:
                return col.astype(np.int8)
            elif c_min > -2**15 and c_max < 2**15:
                return col.astype(np.int16)
            elif c_min > -2**31 and c_max < 2**31:
                return col.astype(np.int32)
            elif c_min > -2**63 and c_max < 2**63:
                return col.astype(np.int64)
    
    # 对于浮点列
    elif pd.api.types.is_float_dtype(col):
        # 检查是否可以转换为整数（无小数部分且在整数范围内）
        if col.dropna().apply(lambda x: x.is_integer()).all():
            col_no_na = col.dropna()
            c_min, c_max = col_no_na.min(), col_no_na.max()
            
            # 决定使用什么整数类型
            if c_min >= 0:
                if c_max < 2**8:
                    return col.astype(pd.Int8Dtype())  # 使用可空整数类型
                elif c_max < 2**16:
                    return col.astype(pd.Int16Dtype())
                elif c_max < 2**32:
                    return col.astype(pd.Int32Dtype())
                elif c_max < 2**64:
                    return col.astype(pd.Int64Dtype())
            else:
                if c_min > -2**7 and c_max < 2**7:
                    return col.astype(pd.Int8Dtype())
                elif c_min > -2**15 and c_max < 2**15:
                    return col.astype(pd.Int16Dtype())
                elif c_min > -2**31 and c_max < 2**31:
                    return col.astype(pd.Int32Dtype())
                elif c_min > -2**63 and c_max < 2**63:
                    return col.astype(pd.Int64Dtype())
        
        # 尝试使用更小的浮点类型
        else:
            try:
                # 如果精度允许，尝试使用float32
                test_series = col.astype(np.float32)
                # 检查转换是否保留了原始值
                if (test_series.dropna() == col.dropna()).all():
                    return col.astype(np.float32)
            except:
                pass
    
    # 默认返回原始列
    return col


def drop_duplicates(df: pd.DataFrame, subset: Optional[List[str]] = None,
                  keep: str = 'first', inplace: bool = False) -> Optional[pd.DataFrame]:
    """
    删除重复行

    Args:
        df: 输入DataFrame
        subset: 用于识别重复行的列，如果为None则使用所有列
        keep: 保留哪个重复项，可选值: 'first' (保留第一个), 'last' (保留最后一个), False (删除所有重复项)
        inplace: 是否就地修改DataFrame，默认为False

    Returns:
        如果inplace=False，返回去重后的DataFrame；否则返回None
    """
    if df.empty:
        logger.warning("输入的DataFrame为空")
        return df if not inplace else None
    
    # 记录初始行数
    initial_rows = len(df)
    
    # 执行去重操作
    result = df.drop_duplicates(subset=subset, keep=keep, inplace=inplace)
    
    # 计算删除的行数
    removed_rows = initial_rows - (len(df) if inplace else len(result))
    
    # 记录去重结果
    if removed_rows > 0:
        subset_str = "所有列" if subset is None else f"列: {subset}"
        logger.info(f"已删除 {removed_rows} 行重复数据 (基于 {subset_str})")
    
    return result


def standardize_column_names(df: pd.DataFrame, mapping: Optional[Dict[str, str]] = None,
                           lower_case: bool = True, strip_spaces: bool = True,
                           replace_spaces: bool = False, inplace: bool = False) -> pd.DataFrame:
    """
    标准化列名

    Args:
        df: 输入DataFrame
        mapping: 列名映射字典，键为原始列名，值为新列名
        lower_case: 是否将列名转换为小写，默认为True
        strip_spaces: 是否去除列名首尾空格，默认为True
        replace_spaces: 是否将列名中的空格替换为下划线，默认为False
        inplace: 是否就地修改DataFrame，默认为False

    Returns:
        列名标准化后的DataFrame
    """
    if df.empty:
        logger.warning("输入的DataFrame为空")
        return df
    
    # 创建副本（如果不是就地操作）
    result = df if inplace else df.copy()
    
    # 获取当前列名列表
    columns = list(result.columns)
    
    # 应用映射
    if mapping is not None:
        for i, col in enumerate(columns):
            if col in mapping:
                columns[i] = mapping[col]
    
    # 应用其他标准化操作
    for i, col in enumerate(columns):
        # 转换为字符串
        col_str = str(col)
        
        # 去除空格
        if strip_spaces:
            col_str = col_str.strip()
        
        # 转换为小写
        if lower_case:
            col_str = col_str.lower()
        
        # 替换空格
        if replace_spaces:
            col_str = col_str.replace(' ', '_')
        
        columns[i] = col_str
    
    # 更新列名
    result.columns = columns
    
    return result


def fill_missing_values(df: pd.DataFrame, method: str = 'ffill', 
                       value: Optional[Any] = None, columns: Optional[List[str]] = None,
                       inplace: bool = False) -> pd.DataFrame:
    """
    填充缺失值

    Args:
        df: 输入DataFrame
        method: 填充方法，可选值: 'ffill' (前向填充), 'bfill' (后向填充), 'interpolate' (插值), 'value' (指定值)
        value: 当method='value'时使用的填充值
        columns: 要处理的列列表，如果为None则处理所有列
        inplace: 是否就地修改DataFrame，默认为False

    Returns:
        填充缺失值后的DataFrame
    """
    if df.empty:
        logger.warning("输入的DataFrame为空")
        return df
    
    # 创建副本（如果不是就地操作）
    result = df if inplace else df.copy()
    
    # 确定要处理的列
    target_columns = columns if columns is not None else result.columns
    
    # 检查目标列是否在DataFrame中
    missing_columns = [col for col in target_columns if col not in result.columns]
    if missing_columns:
        logger.warning(f"以下列不存在于DataFrame中：{missing_columns}")
        target_columns = [col for col in target_columns if col in result.columns]
    
    # 计算每列的初始缺失值数量
    na_counts_before = {col: result[col].isna().sum() for col in target_columns}
    total_na_before = sum(na_counts_before.values())
    
    # 根据方法填充缺失值
    if method == 'ffill':
        result[target_columns] = result[target_columns].fillna(method='ffill')
    elif method == 'bfill':
        result[target_columns] = result[target_columns].fillna(method='bfill')
    elif method == 'interpolate':
        # 对数值列进行插值
        for col in target_columns:
            if pd.api.types.is_numeric_dtype(result[col]):
                result[col] = result[col].interpolate()
            else:
                logger.warning(f"列 '{col}' 不是数值类型，无法进行插值")
    elif method == 'value':
        result[target_columns] = result[target_columns].fillna(value)
    else:
        logger.error(f"不支持的填充方法: {method}")
        return df if not inplace else result
    
    # 计算填充后的缺失值数量
    na_counts_after = {col: result[col].isna().sum() for col in target_columns}
    total_na_after = sum(na_counts_after.values())
    filled_count = total_na_before - total_na_after
    
    if filled_count > 0:
        logger.info(f"已填充 {filled_count} 个缺失值 (方法: {method})")
    
    return result