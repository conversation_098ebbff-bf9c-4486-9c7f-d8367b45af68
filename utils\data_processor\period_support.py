#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
周期支持判断策略上下文模块

提供数据源周期支持判断的统一接口，使用策略模式管理不同数据源对周期的支持情况。
"""

import logging
from typing import Dict, List, Optional, Type

from .period_converter import (
    DataSourcePeriodStrategy,
    XTQuantPeriodStrategy,
    validate_period_string
)

# 导入日志模块
from utils.logger import get_unified_logger

# 使用已有的日志系统
logger = get_unified_logger(__name__)


class PeriodSupportContext:
    """
    周期支持策略上下文类

    管理不同数据源的周期支持策略，提供统一的接口检查周期是否被支持。
    """

    # 数据源类型枚举
    DATA_SOURCE_XTQUANT = "xtquant"
    DATA_SOURCE_OTHER = "other"  # 未来可以添加其他数据源

    def __init__(self):
        """初始化周期支持上下文"""
        # 初始化策略字典
        self._strategies: Dict[str, DataSourcePeriodStrategy] = {}

        # 注册默认策略
        self.register_strategy(self.DATA_SOURCE_XTQUANT, XTQuantPeriodStrategy())

        # 设置默认数据源
        self._default_source = self.DATA_SOURCE_XTQUANT

    def register_strategy(self, source_name: str, strategy: DataSourcePeriodStrategy) -> None:
        """
        注册数据源周期策略

        Args:
            source_name (str): 数据源名称
            strategy (DataSourcePeriodStrategy): 周期策略实例
        """
        self._strategies[source_name] = strategy
        logger.debug(f"已注册数据源策略: {source_name}")

    def set_default_source(self, source_name: str) -> bool:
        """
        设置默认数据源

        Args:
            source_name (str): 数据源名称

        Returns:
            bool: 如果设置成功返回True，否则返回False
        """
        if source_name in self._strategies:
            self._default_source = source_name
            logger.debug(f"已设置默认数据源: {source_name}")
            return True
        logger.warning(f"无法设置默认数据源，未找到策略: {source_name}")
        return False

    def is_period_supported(self, period: str, source_name: Optional[str] = None) -> bool:
        """
        判断周期是否被指定数据源支持

        Args:
            period (str): 周期字符串，如 '3m', '1h', '2d' 等
            source_name (Optional[str]): 数据源名称，如果为None则使用默认数据源

        Returns:
            bool: 如果支持则返回True，否则返回False
        """
        # 首先验证周期格式
        if not validate_period_string(period):
            logger.warning(f"无效的周期格式: {period}")
            return False

        # 获取策略
        source = source_name or self._default_source
        strategy = self._strategies.get(source)

        if not strategy:
            logger.warning(f"未找到数据源策略: {source}")
            return False

        # 使用策略判断
        return strategy.is_period_supported(period)

    def get_supported_periods(self, source_name: Optional[str] = None) -> List[str]:
        """
        获取指定数据源支持的周期列表

        Args:
            source_name (Optional[str]): 数据源名称，如果为None则使用默认数据源

        Returns:
            List[str]: 支持的周期列表
        """
        # 获取策略
        source = source_name or self._default_source
        strategy = self._strategies.get(source)

        if not strategy:
            logger.warning(f"未找到数据源策略: {source}")
            return []

        # 使用策略获取支持的周期
        return strategy.get_supported_periods()

    def get_available_sources(self) -> List[str]:
        """
        获取所有可用的数据源列表

        Returns:
            List[str]: 可用的数据源名称列表
        """
        return list(self._strategies.keys())


# 创建全局单例实例
period_support_context = PeriodSupportContext()


# 以下是兼容性封装函数，保持与原有代码的兼容性

def is_period_supported(period: str, source_name: Optional[str] = None) -> bool:
    """
    判断周期是否被指定数据源支持（兼容性封装）

    Args:
        period (str): 周期字符串，如 '3m', '1h', '2d' 等
        source_name (Optional[str]): 数据源名称，如果为None则使用默认数据源

    Returns:
        bool: 如果支持则返回True，否则返回False
    """
    return period_support_context.is_period_supported(period, source_name)


def is_supported_by_xtquant(period: str) -> bool:
    """
    检查指定的周期是否被迅投API原生支持（兼容性封装）

    Args:
        period (str): 周期字符串，如 '3m', '1h', '2d' 等

    Returns:
        bool: 如果原生支持则返回True，否则返回False
    """
    return period_support_context.is_period_supported(
        period, PeriodSupportContext.DATA_SOURCE_XTQUANT
    )


def get_supported_periods(source_name: Optional[str] = None) -> List[str]:
    """
    获取指定数据源支持的周期列表（兼容性封装）

    Args:
        source_name (Optional[str]): 数据源名称，如果为None则使用默认数据源

    Returns:
        List[str]: 支持的周期列表
    """
    return period_support_context.get_supported_periods(source_name)
