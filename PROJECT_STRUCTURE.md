# 项目结构说明

## 📁 核心目录结构

```
quant/
├── 📂 data/                    # 数据处理核心模块
│   ├── core/                   # 核心数据处理逻辑
│   ├── handlers/               # 数据处理器
│   ├── processing/             # 数据处理流水线
│   ├── source/                 # 数据源接口
│   ├── storage/                # 数据存储模块
│   └── ui/                     # 数据处理UI
│
├── 📂 utils/                   # 工具函数库
│   ├── calendar/               # 交易日历工具
│   ├── data_display/           # 数据显示工具
│   ├── data_processor/         # 数据处理工具
│   ├── logger/                 # 日志系统
│   ├── memory_manager/         # 内存管理
│   ├── path_utils/             # 路径工具
│   └── time_formatter/         # 时间格式化工具
│
├── 📂 backtest/                # 回测引擎
│   ├── engine.py               # 回测引擎核心
│   ├── performance.py          # 性能分析
│   └── report_generator.py     # 报告生成
│
├── 📂 strategy/                # 策略模块
│   ├── ma_cross_strategy.py    # 双均线策略
│   └── strategy_template.py    # 策略模板
│
├── 📂 config/                  # 配置模块
│   ├── settings.py             # 系统设置
│   └── symbols.py              # 股票代码配置
│
├── 📂 tests/                   # 测试文件
│   ├── test_*.py               # 单元测试
│   ├── benchmark_*.py          # 性能测试
│   └── time_safety_demo.py     # 时间安全演示
│
├── 📂 examples/                # 示例代码
│   ├── xtdata_download.py      # 数据下载示例
│   └── 时间戳转换.py            # 时间转换示例
│
├── 📂 docs/                    # 项目文档
│   ├── context.md              # 上下文文档
│   ├── INCREMENTAL_UPDATE.md   # 增量更新文档
│   └── *.md                    # 其他技术文档
│
└── 📂 logs/                    # 日志文件（运行时生成）
```

## 🎯 核心功能模块

### 1. **数据处理模块** (`data/`)
- **核心功能**: 数据下载、存储、读取、合成
- **关键特性**: 增量更新、多层次交易日历、分层合成策略
- **主要文件**:
  - `data_main.py` - 数据处理主入口
  - `下载历史数据.py` - 历史数据下载
  - `storage/parquet_storage.py` - 数据存储
  - `storage/parquet_reader.py` - 数据读取

### 2. **工具函数库** (`utils/`)
- **核心功能**: 通用工具函数和系统组件
- **关键特性**: 时间处理安全、内存管理、日志系统
- **主要文件**:
  - `data_processor/period_handler.py` - 周期处理
  - `calendar/trading_calendar.py` - 交易日历
  - `time_processing_enforcer.py` - 时间安全保护
  - `smart_time_converter.py` - 智能时间转换

### 3. **回测引擎** (`backtest/`)
- **核心功能**: 策略回测和性能分析
- **主要文件**:
  - `engine.py` - 回测引擎
  - `performance.py` - 性能分析
  - `report_generator.py` - 报告生成

## 🧪 测试和示例

### 测试文件 (`tests/`)
- **单元测试**: `test_*.py`
- **性能测试**: `benchmark_*.py`
- **功能演示**: `time_safety_demo.py`

### 示例代码 (`examples/`)
- **数据下载**: `xtdata_download.py`
- **时间转换**: `时间戳转换.py`

## 📝 文档系统 (`docs/`)

### 核心文档
- `README.md` - 项目总览
- `context.md` - 项目上下文
- `INCREMENTAL_UPDATE.md` - 增量更新功能文档

### 技术文档
- 架构设计文档
- 性能优化报告
- 实施指南

## 🚫 忽略的文件和目录

### 自动生成的文件
- `logs/` - 运行时日志
- `*_results/` - 结果文件
- `*_reports/` - 报告文件
- `__pycache__/` - Python缓存

### 临时文件
- `*.backup*` - 备份文件
- `*.tmp` - 临时文件
- `temp/` - 临时目录

### 依赖文件
- `uv.lock` - 依赖锁文件（可重新生成）

## 🔧 开发规范

### 文件命名规范
- **核心模块**: 使用英文命名
- **示例文件**: 可使用中文命名
- **测试文件**: `test_` 前缀
- **基准测试**: `benchmark_` 前缀

### 目录组织原则
- **核心代码**: 放在对应的功能模块目录
- **测试代码**: 统一放在 `tests/` 目录
- **示例代码**: 统一放在 `examples/` 目录
- **临时文件**: 不提交到版本控制

## 🎉 项目特色

### 1. **增量更新架构**
- 智能检测已有数据
- 基于交易日历的重叠计算
- 90%+性能提升

### 2. **时间处理安全**
- 多层次时区保护机制
- 自动检测危险时间处理方法
- 零8小时时区偏移

### 3. **分层合成策略**
- tick → 1m → 更大周期
- 清晰的数据处理架构
- 高效的资源利用

### 4. **完整的测试体系**
- 单元测试覆盖
- 性能基准测试
- 功能演示脚本
