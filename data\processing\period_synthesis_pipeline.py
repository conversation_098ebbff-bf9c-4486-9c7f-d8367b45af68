#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
周期转换数据处理管道

基于DataPipeline架构的独立周期转换数据处理管道，专门负责周期转换的处理流程。
遵循单一职责原则，与复权功能完全分离，只处理原始数据的周期转换。

核心特性：
- 基于现有DataPipeline架构
- 专门处理周期转换合成
- 支持批量周期转换处理
- 链式操作和错误处理
- 进度跟踪和性能监控
- 完全移除复权功能集成

使用示例：
```python
from data.processing.period_synthesis_pipeline import PeriodSynthesisDataPipeline

# 单只股票周期转换
pipeline = PeriodSynthesisDataPipeline()
result = (pipeline
    .load_source_data(data_root, symbol, source_period)
    .synthesize_period(target_period)
    .save_synthesized_data()
    .execute())

# 批量周期转换
batch_result = (pipeline
    .load_batch_source_data(data_root, symbols, source_period)
    .synthesize_batch_period(target_period)
    .save_batch_synthesized_data()
    .execute())
```

作者: AI Assistant
创建时间: 2025-08-05
版本: 1.0.0
"""

import time
from typing import Dict, List, Optional, Any, Union
import pandas as pd
from dataclasses import dataclass
from enum import Enum

from data.processing.data_pipeline import DataPipeline, OperationType, PipelineOperation
from utils.data_processor.period_converter import convert_kline_period
from data.storage.vectorized_reader import read_partitioned_data_vectorized
from data.storage.unified_data_saver import save_data_unified, SaveStrategy
from utils.logger import get_unified_logger, LogTarget
from config.settings import DATA_ROOT

logger = get_unified_logger(__name__)


class PeriodSynthesisOperationType(Enum):
    """周期转换操作类型枚举"""
    LOAD_SOURCE = "load_source"
    LOAD_BATCH_SOURCE = "load_batch_source"
    SYNTHESIZE_PERIOD = "synthesize_period"
    SYNTHESIZE_BATCH_PERIOD = "synthesize_batch_period"
    SAVE_SYNTHESIZED = "save_synthesized"
    SAVE_BATCH_SYNTHESIZED = "save_batch_synthesized"
    VALIDATE_SYNTHESIS = "validate_synthesis"


class PeriodSynthesisDataPipeline(DataPipeline):
    """
    周期转换数据处理管道
    
    专门处理周期转换的独立管道，继承自DataPipeline
    完全移除复权功能，只处理原始数据的周期转换
    """
    
    def __init__(self, enable_parallel: bool = True):
        """
        初始化周期转换数据处理管道
        
        Args:
            enable_parallel: 是否启用并行处理
        """
        super().__init__(enable_parallel)
        self.synthesis_stats = {
            'total_symbols': 0,
            'successful_synthesis': 0,
            'failed_synthesis': 0,
            'total_synthesis_time': 0.0
        }
        logger.info("周期转换数据处理管道初始化完成（无复权功能）")
    
    def load_source_data(self, data_root: str, symbol: str, source_period: str,
                        start_time: Optional[str] = None,
                        end_time: Optional[str] = None,
                        columns: Optional[List[str]] = None) -> 'PeriodSynthesisDataPipeline':
        """
        加载源周期数据（原始数据，无复权处理）
        
        Args:
            data_root: 数据根目录
            symbol: 股票代码
            source_period: 源数据周期
            start_time: 开始时间
            end_time: 结束时间
            columns: 要读取的列
            
        Returns:
            PeriodSynthesisDataPipeline: 管道实例（支持链式调用）
        """
        operation = PipelineOperation(
            op_type=OperationType.LOAD,
            func=self._load_source_data_impl,
            args=(data_root, symbol, source_period),
            kwargs={
                'start_time': start_time,
                'end_time': end_time,
                'columns': columns
            },
            name=f"load_source_{symbol}_{source_period}"
        )
        self.operations.append(operation)
        logger.debug(f"添加源数据加载操作: {symbol} {source_period}")
        return self
    
    def load_batch_source_data(self, data_root: str, symbols: List[str], source_period: str,
                              start_time: Optional[str] = None,
                              end_time: Optional[str] = None,
                              columns: Optional[List[str]] = None) -> 'PeriodSynthesisDataPipeline':
        """
        批量加载源周期数据
        
        Args:
            data_root: 数据根目录
            symbols: 股票代码列表
            source_period: 源数据周期
            start_time: 开始时间
            end_time: 结束时间
            columns: 要读取的列
            
        Returns:
            PeriodSynthesisDataPipeline: 管道实例（支持链式调用）
        """
        operation = PipelineOperation(
            op_type=OperationType.LOAD,
            func=self._load_batch_source_data_impl,
            args=(data_root, symbols, source_period),
            kwargs={
                'start_time': start_time,
                'end_time': end_time,
                'columns': columns
            },
            name=f"load_batch_source_{len(symbols)}symbols_{source_period}"
        )
        self.operations.append(operation)
        logger.debug(f"添加批量源数据加载操作: {len(symbols)} 只股票 {source_period}")
        return self
    
    def synthesize_period(self, target_period: str) -> 'PeriodSynthesisDataPipeline':
        """
        执行周期转换
        
        Args:
            target_period: 目标周期
            
        Returns:
            PeriodSynthesisDataPipeline: 管道实例（支持链式调用）
        """
        operation = PipelineOperation(
            op_type=OperationType.TRANSFORM,
            func=self._synthesize_period_impl,
            args=(),
            kwargs={'target_period': target_period},
            name=f"synthesize_period_{target_period}"
        )
        self.operations.append(operation)
        logger.debug(f"添加周期转换操作: -> {target_period}")
        return self
    
    def synthesize_batch_period(self, target_period: str) -> 'PeriodSynthesisDataPipeline':
        """
        批量执行周期转换
        
        Args:
            target_period: 目标周期
            
        Returns:
            PeriodSynthesisDataPipeline: 管道实例（支持链式调用）
        """
        operation = PipelineOperation(
            op_type=OperationType.TRANSFORM,
            func=self._synthesize_batch_period_impl,
            args=(),
            kwargs={'target_period': target_period},
            name=f"synthesize_batch_period_{target_period}"
        )
        self.operations.append(operation)
        logger.debug(f"添加批量周期转换操作: -> {target_period}")
        return self
    
    def save_synthesized_data(self, data_root: Optional[str] = None,
                             strategy: SaveStrategy = SaveStrategy.MULTI_PARTITION,
                             parallel: bool = True) -> 'PeriodSynthesisDataPipeline':
        """
        保存转换后的数据
        
        Args:
            data_root: 数据根目录
            strategy: 保存策略
            parallel: 是否并行保存
            
        Returns:
            PeriodSynthesisDataPipeline: 管道实例（支持链式调用）
        """
        operation = PipelineOperation(
            op_type=OperationType.CUSTOM,
            func=self._save_synthesized_data_impl,
            args=(),
            kwargs={
                'data_root': data_root or DATA_ROOT,
                'strategy': strategy,
                'parallel': parallel
            },
            name="save_synthesized_data"
        )
        self.operations.append(operation)
        logger.debug("添加转换数据保存操作")
        return self
    
    def save_batch_synthesized_data(self, data_root: Optional[str] = None,
                                   strategy: SaveStrategy = SaveStrategy.MULTI_PARTITION,
                                   parallel: bool = True) -> 'PeriodSynthesisDataPipeline':
        """
        批量保存转换后的数据
        
        Args:
            data_root: 数据根目录
            strategy: 保存策略
            parallel: 是否并行保存
            
        Returns:
            PeriodSynthesisDataPipeline: 管道实例（支持链式调用）
        """
        operation = PipelineOperation(
            op_type=OperationType.CUSTOM,
            func=self._save_batch_synthesized_data_impl,
            args=(),
            kwargs={
                'data_root': data_root or DATA_ROOT,
                'strategy': strategy,
                'parallel': parallel
            },
            name="save_batch_synthesized_data"
        )
        self.operations.append(operation)
        logger.debug("添加批量转换数据保存操作")
        return self
    
    def validate_synthesis(self, tolerance: float = 1e-6) -> 'PeriodSynthesisDataPipeline':
        """
        验证周期转换数据质量
        
        Args:
            tolerance: 容差值
            
        Returns:
            PeriodSynthesisDataPipeline: 管道实例（支持链式调用）
        """
        operation = PipelineOperation(
            op_type=OperationType.CUSTOM,
            func=self._validate_synthesis_impl,
            args=(),
            kwargs={'tolerance': tolerance},
            name="validate_synthesis"
        )
        self.operations.append(operation)
        logger.debug("添加周期转换数据验证操作")
        return self

    # ==================== 内部实现方法 ====================

    def _load_source_data_impl(self, data_root: str, symbol: str, source_period: str,
                              start_time: Optional[str] = None,
                              end_time: Optional[str] = None,
                              columns: Optional[List[str]] = None) -> pd.DataFrame:
        """加载源周期数据的内部实现（只加载原始数据）"""
        try:
            logger.debug(f"加载源周期数据: {symbol} {source_period}")

            # 使用向量化读取器加载原始数据（强制不使用复权）
            df = read_partitioned_data_vectorized(
                data_root=data_root,
                symbol=symbol,
                period=source_period,
                start_time=start_time,
                end_time=end_time,
                columns=columns,
                dividend_type="none"  # 强制加载原始数据，不进行复权处理
            )

            if df is None or df.empty:
                raise ValueError(f"未读取到源周期数据: {symbol} {source_period}")

            logger.info(f"源周期数据加载成功: {symbol} {source_period} {len(df)} 行")
            return df

        except Exception as e:
            logger.error(f"源周期数据加载失败: {symbol} {source_period} - {e}")
            raise

    def _load_batch_source_data_impl(self, data_root: str, symbols: List[str], source_period: str,
                                    start_time: Optional[str] = None,
                                    end_time: Optional[str] = None,
                                    columns: Optional[List[str]] = None) -> Dict[str, pd.DataFrame]:
        """批量加载源周期数据的内部实现"""
        try:
            logger.info(f"批量加载源周期数据: {len(symbols)} 只股票 {source_period}")

            batch_data = {}
            successful_count = 0
            failed_count = 0

            for symbol in symbols:
                try:
                    df = self._load_source_data_impl(
                        data_root, symbol, source_period, start_time, end_time, columns
                    )
                    batch_data[symbol] = df
                    successful_count += 1

                except Exception as e:
                    logger.warning(f"跳过加载失败的股票: {symbol} - {e}")
                    failed_count += 1
                    continue

            logger.info(f"批量源周期数据加载完成: 成功 {successful_count} 只, 失败 {failed_count} 只")
            return batch_data

        except Exception as e:
            logger.error(f"批量源周期数据加载失败: {e}")
            raise

    def _synthesize_period_impl(self, data: pd.DataFrame, target_period: str) -> pd.DataFrame:
        """执行周期转换的内部实现"""
        try:
            if not hasattr(self, '_current_symbol') or not hasattr(self, '_current_source_period'):
                raise ValueError("缺少股票代码或源周期信息，无法进行周期转换")

            symbol = self._current_symbol
            source_period = self._current_source_period

            logger.debug(f"执行周期转换: {symbol} {source_period} -> {target_period}")

            # 使用周期转换器进行转换（不涉及复权）
            synthesized_data = convert_kline_period(
                base_data=data,
                target_period=target_period,
                source_period=source_period,
                symbol=symbol
            )

            if synthesized_data is None or synthesized_data.empty:
                raise ValueError(f"周期转换失败: {symbol} {source_period} -> {target_period}")

            logger.info(f"周期转换成功: {symbol} {source_period} -> {target_period} {len(synthesized_data)} 行")
            self.synthesis_stats['successful_synthesis'] += 1
            return synthesized_data

        except Exception as e:
            logger.error(f"周期转换失败: {e}")
            self.synthesis_stats['failed_synthesis'] += 1
            raise

    def _synthesize_batch_period_impl(self, batch_data: Dict[str, pd.DataFrame],
                                     target_period: str) -> Dict[str, pd.DataFrame]:
        """批量执行周期转换的内部实现"""
        try:
            if not hasattr(self, '_current_source_period'):
                raise ValueError("缺少源周期信息，无法进行批量周期转换")

            source_period = self._current_source_period
            logger.info(f"批量执行周期转换: {len(batch_data)} 只股票 {source_period} -> {target_period}")

            synthesized_batch_data = {}
            successful_count = 0
            failed_count = 0

            for symbol, data in batch_data.items():
                try:
                    if data is None or data.empty:
                        logger.warning(f"跳过空数据: {symbol}")
                        continue

                    # 临时设置当前股票信息
                    self._current_symbol = symbol

                    synthesized_data = self._synthesize_period_impl(data, target_period)
                    synthesized_batch_data[symbol] = synthesized_data
                    successful_count += 1

                except Exception as e:
                    logger.warning(f"跳过转换失败的股票: {symbol} - {e}")
                    failed_count += 1
                    continue

            logger.info(f"批量周期转换完成: 成功 {successful_count} 只, 失败 {failed_count} 只")
            return synthesized_batch_data

        except Exception as e:
            logger.error(f"批量周期转换失败: {e}")
            raise

    def _save_synthesized_data_impl(self, data: pd.DataFrame, data_root: str,
                                   strategy: SaveStrategy, parallel: bool) -> Dict[str, Any]:
        """保存转换数据的内部实现"""
        try:
            if not hasattr(self, '_current_symbol') or not hasattr(self, '_current_target_period'):
                raise ValueError("缺少股票代码或目标周期信息，无法保存转换数据")

            symbol = self._current_symbol
            target_period = self._current_target_period

            logger.debug(f"保存转换数据: {symbol} {target_period}")

            # 使用统一数据保存器保存转换数据（原始数据类型）
            save_result = save_data_unified(
                df=data,
                data_root=data_root,
                symbol=symbol,
                period=target_period,
                strategy=strategy,
                parallel=parallel,
                data_type="raw",  # 保存为原始数据类型
                adj_type=None     # 无复权类型
            )

            if save_result.success:
                logger.info(f"转换数据保存成功: {symbol} {target_period}")
                return {
                    "success": True,
                    "symbol": symbol,
                    "period": target_period,
                    "partitions": len(save_result.saved_partitions),
                    "strategy": save_result.strategy_used.value
                }
            else:
                logger.error(f"转换数据保存失败: {symbol} {target_period} - {save_result.error_message}")
                return {
                    "success": False,
                    "symbol": symbol,
                    "period": target_period,
                    "error": save_result.error_message
                }

        except Exception as e:
            logger.error(f"转换数据保存失败: {e}")
            raise

    def _save_batch_synthesized_data_impl(self, batch_data: Dict[str, pd.DataFrame],
                                         data_root: str, strategy: SaveStrategy,
                                         parallel: bool) -> Dict[str, Any]:
        """批量保存转换数据的内部实现"""
        try:
            if not hasattr(self, '_current_target_period'):
                raise ValueError("缺少目标周期信息，无法保存批量转换数据")

            target_period = self._current_target_period

            logger.info(f"批量保存转换数据: {len(batch_data)} 只股票 {target_period}")

            save_results = {}
            successful_count = 0
            failed_count = 0

            for symbol, data in batch_data.items():
                try:
                    if data is None or data.empty:
                        logger.warning(f"跳过空数据: {symbol}")
                        continue

                    # 临时设置当前股票信息
                    self._current_symbol = symbol

                    save_result = self._save_synthesized_data_impl(
                        data, data_root, strategy, parallel
                    )
                    save_results[symbol] = save_result

                    if save_result["success"]:
                        successful_count += 1
                    else:
                        failed_count += 1

                except Exception as e:
                    logger.error(f"保存转换数据失败: {symbol} - {e}")
                    save_results[symbol] = {
                        "success": False,
                        "symbol": symbol,
                        "error": str(e)
                    }
                    failed_count += 1

            logger.info(f"批量转换数据保存完成: 成功 {successful_count} 只, 失败 {failed_count} 只")
            return {
                "total_symbols": len(batch_data),
                "successful_saves": successful_count,
                "failed_saves": failed_count,
                "save_results": save_results
            }

        except Exception as e:
            logger.error(f"批量转换数据保存失败: {e}")
            raise

    def _validate_synthesis_impl(self, data: Union[pd.DataFrame, Dict[str, pd.DataFrame]],
                                tolerance: float = 1e-6) -> Dict[str, Any]:
        """验证周期转换数据质量的内部实现"""
        try:
            logger.debug("开始验证周期转换数据质量")

            if isinstance(data, pd.DataFrame):
                # 单只股票数据验证
                return self._validate_single_synthesis(data, tolerance)
            elif isinstance(data, dict):
                # 批量数据验证
                return self._validate_batch_synthesis(data, tolerance)
            else:
                raise ValueError(f"不支持的数据类型: {type(data)}")

        except Exception as e:
            logger.error(f"周期转换数据验证失败: {e}")
            raise

    def _validate_single_synthesis(self, data: pd.DataFrame, tolerance: float) -> Dict[str, Any]:
        """验证单只股票周期转换数据"""
        validation_result = {
            "valid": True,
            "issues": [],
            "statistics": {}
        }

        try:
            # 检查数据完整性
            if data.empty:
                validation_result["valid"] = False
                validation_result["issues"].append("转换数据为空")
                return validation_result

            # 检查必要的OHLCV列
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            missing_columns = [col for col in required_columns if col not in data.columns]
            if missing_columns:
                validation_result["issues"].append(f"缺少必要列: {missing_columns}")

            # 检查数据的时间连续性
            if hasattr(data.index, 'to_series'):
                time_diffs = data.index.to_series().diff().dropna()
                if len(time_diffs.unique()) > 2:  # 允许少量时间间隔差异
                    validation_result["issues"].append("时间间隔不规律")

            # 检查OHLCV数据的合理性
            price_columns = ['open', 'high', 'low', 'close']
            for col in price_columns:
                if col in data.columns:
                    if (data[col] <= 0).any():
                        validation_result["issues"].append(f"{col}列存在非正值")

                    if data[col].isnull().any():
                        validation_result["issues"].append(f"{col}列存在空值")

            # 检查成交量
            if 'volume' in data.columns:
                if (data['volume'] < 0).any():
                    validation_result["issues"].append("成交量存在负值")

            # 检查高开低收的逻辑关系
            if all(col in data.columns for col in price_columns):
                invalid_ohlc = (
                    (data['high'] < data['low']) |
                    (data['high'] < data['open']) |
                    (data['high'] < data['close']) |
                    (data['low'] > data['open']) |
                    (data['low'] > data['close'])
                )

                if invalid_ohlc.any():
                    validation_result["issues"].append(f"存在 {invalid_ohlc.sum()} 行OHLC逻辑错误")

            # 统计信息
            validation_result["statistics"] = {
                "total_rows": len(data),
                "date_range": f"{data.index[0]} ~ {data.index[-1]}",
                "columns": list(data.columns)
            }

            if validation_result["issues"]:
                validation_result["valid"] = False

            logger.debug(f"单只股票周期转换数据验证完成: {'通过' if validation_result['valid'] else '失败'}")
            return validation_result

        except Exception as e:
            logger.error(f"单只股票周期转换数据验证异常: {e}")
            validation_result["valid"] = False
            validation_result["issues"].append(f"验证异常: {e}")
            return validation_result

    def _validate_batch_synthesis(self, batch_data: Dict[str, pd.DataFrame],
                                 tolerance: float) -> Dict[str, Any]:
        """验证批量周期转换数据"""
        batch_validation_result = {
            "overall_valid": True,
            "total_symbols": len(batch_data),
            "valid_symbols": 0,
            "invalid_symbols": 0,
            "symbol_results": {}
        }

        try:
            for symbol, data in batch_data.items():
                symbol_result = self._validate_single_synthesis(data, tolerance)
                batch_validation_result["symbol_results"][symbol] = symbol_result

                if symbol_result["valid"]:
                    batch_validation_result["valid_symbols"] += 1
                else:
                    batch_validation_result["invalid_symbols"] += 1
                    batch_validation_result["overall_valid"] = False

            logger.info(f"批量周期转换数据验证完成: 有效 {batch_validation_result['valid_symbols']} 只, "
                       f"无效 {batch_validation_result['invalid_symbols']} 只")

            return batch_validation_result

        except Exception as e:
            logger.error(f"批量周期转换数据验证异常: {e}")
            batch_validation_result["overall_valid"] = False
            return batch_validation_result

    def get_synthesis_stats(self) -> Dict[str, Any]:
        """获取周期转换处理统计信息"""
        return {
            **self.synthesis_stats,
            **self._performance_stats
        }


def create_period_synthesis_pipeline(enable_parallel: bool = True) -> PeriodSynthesisDataPipeline:
    """
    创建周期转换数据处理管道的工厂函数

    Args:
        enable_parallel: 是否启用并行处理

    Returns:
        PeriodSynthesisDataPipeline: 周期转换数据处理管道实例
    """
    return PeriodSynthesisDataPipeline(enable_parallel=enable_parallel)
