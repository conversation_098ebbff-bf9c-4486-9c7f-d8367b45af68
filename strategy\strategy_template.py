#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
策略模板文件，定义了策略的基本接口和通用方法
所有自定义策略都应继承自这里的基类，并实现特定方法
"""

import abc
import logging
import os
import sys
from datetime import datetime
from typing import Dict, List, Optional, Union

import pandas as pd

# 添加项目根目录到系统路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.logger import get_unified_logger


class StrategyTemplate(abc.ABC):
    """
    策略基类，定义了策略的基本接口
    所有策略都应该继承这个类，并实现相应的方法
    """
    
    def __init__(self, **kwargs):
        """
        初始化策略
        
        Args:
            **kwargs: 策略参数
        """
        self.logger = kwargs.get("logger", get_unified_logger(self.__class__.__name__))
        self.name = kwargs.get("name", self.__class__.__name__)
        
        # 初始化性能统计
        self.performance_stats = {
            "total_trades": 0,
            "win_trades": 0,
            "loss_trades": 0,
            "total_profit": 0.0,
            "total_loss": 0.0,
        }
        
        # 调用子类的初始化方法
        self.init(**kwargs)
        
    def init(self, **kwargs):
        """
        初始化策略参数，由子类实现
        
        Args:
            **kwargs: 策略参数
        """
        pass
    
    @abc.abstractmethod
    def on_bar(self, context) -> dict:
        """
        K线数据事件回调
        由子类实现，处理每个K线数据
        
        Args:
            context: 策略上下文对象
            
        Returns:
            操作指令字典
        """
        pass
    
    def on_order(self, order) -> None:
        """
        订单状态变化回调
        
        Args:
            order: 订单对象
        """
        self.logger.info(f"订单状态变化: {order}")
    
    def on_trade(self, trade) -> None:
        """
        交易执行回调
        
        Args:
            trade: 交易对象
        """
        self.logger.info(f"交易执行: {trade}")
        
        # 更新性能统计
        self.performance_stats["total_trades"] += 1
        if trade.get("profit", 0) > 0:
            self.performance_stats["win_trades"] += 1
            self.performance_stats["total_profit"] += trade.get("profit", 0)
        else:
            self.performance_stats["loss_trades"] += 1
            self.performance_stats["total_loss"] += abs(trade.get("profit", 0))
    
    def on_position_change(self, position) -> None:
        """
        持仓变化回调
        
        Args:
            position: 持仓对象
        """
        self.logger.info(f"持仓变化: {position}")
    
    def on_account_change(self, account) -> None:
        """
        账户变化回调
        
        Args:
            account: 账户对象
        """
        self.logger.info(f"账户变化: {account}")
    
    def on_backtest_finished(self) -> Dict:
        """
        回测结束回调
        
        Returns:
            性能统计字典
        """
        win_rate = 0.0
        if self.performance_stats["total_trades"] > 0:
            win_rate = self.performance_stats["win_trades"] / self.performance_stats["total_trades"] * 100
            
        profit_factor = 0.0
        if self.performance_stats["total_loss"] > 0:
            profit_factor = self.performance_stats["total_profit"] / self.performance_stats["total_loss"]
            
        self.logger.info(f"回测结束，总交易次数: {self.performance_stats['total_trades']}")
        self.logger.info(f"盈利交易: {self.performance_stats['win_trades']}, "
                         f"亏损交易: {self.performance_stats['loss_trades']}")
        self.logger.info(f"胜率: {win_rate:.2f}%, 盈亏比: {profit_factor:.2f}")
        
        return self.performance_stats


class SimpleStrategyTemplate(StrategyTemplate):
    """
    简化版策略模板，适用于回测和实盘
    为简单策略提供了更方便的接口
    """
    
    def __init__(self, **kwargs):
        """
        初始化简化版策略
        
        Args:
            **kwargs: 策略参数
        """
        super().__init__(**kwargs)
        self.data = None  # 当前数据
        self.position = 0  # 当前持仓
        self.cash = kwargs.get("initial_cash", 100000.0)  # 初始资金
        
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算技术指标
        由子类实现，处理价格数据并计算所需指标
        
        Args:
            data: 原始价格数据
            
        Returns:
            添加了技术指标的DataFrame
        """
        return data
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        生成交易信号
        由子类实现，根据技术指标生成交易信号
        
        Args:
            data: 带有技术指标的DataFrame
            
        Returns:
            添加了交易信号的DataFrame
        """
        return data
    
    def on_bar(self, context) -> dict:
        """
        K线数据事件回调
        处理每个K线数据，计算指标，生成信号，执行交易
        
        Args:
            context: 策略上下文对象，至少包含：
                - data: 当前K线数据
                - symbol: 当前交易品种
                - position: 当前持仓信息
                - account: 当前账户信息
            
        Returns:
            操作指令字典
        """
        # 保存上下文数据
        self.data = context.get("data")
        
        # 获取当前持仓和账户信息
        self.position = context.get("position", 0)
        if "account" in context:
            self.cash = context.get("account", {}).get("cash", self.cash)
        
        # 计算指标
        data_with_indicators = self.calculate_indicators(self.data)
        
        # 生成信号
        data_with_signals = self.generate_signals(data_with_indicators)
        
        # 最新K线的信号
        current_signal = 0
        if not data_with_signals.empty:
            current_signal = data_with_signals["signal"].iloc[-1]
        
        # 生成操作指令
        orders = {}
        if current_signal > 0 and self.position <= 0:  # 买入信号
            orders = {
                "action": "买入",
                "symbol": context.get("symbol", ""),
                "quantity": self._calculate_position_size(context),
                "price": "market",  # 市价单
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            self.logger.info(f"生成买入信号: {orders}")
            
        elif current_signal < 0 and self.position > 0:  # 卖出信号
            orders = {
                "action": "卖出",
                "symbol": context.get("symbol", ""),
                "quantity": self.position,
                "price": "market",  # 市价单
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            self.logger.info(f"生成卖出信号: {orders}")
        
        return orders
    
    def _calculate_position_size(self, context) -> int:
        """
        计算仓位大小
        
        Args:
            context: 策略上下文对象
            
        Returns:
            仓位大小（股票数量）
        """
        # 默认使用所有可用资金的95%
        cash = self.cash
        price = 0
        
        if self.data is not None and not self.data.empty:
            price = self.data["close"].iloc[-1]
        
        if "account" in context:
            cash = context.get("account", {}).get("cash", cash)
        
        if price <= 0:
            return 0
        
        # 计算可买入股数（按100股为1手）
        shares = int((cash * 0.95) / (price * 100)) * 100
        return shares

if __name__ == "__main__":
    # 测试代码
    print("这是策略模板文件，请不要直接运行。") 