---
description: 
globs: "*"
alwaysApply: true
---
# 迅投量化交易平台接口使用规则，此规则只在迅投xtquant接口有效

- 迅投量化交易平台xtquant接口数据下载默认使用**download_history_data2**
- 迅投字典链接：https://dict.thinktrader.net/dictionary/
- 迅投xtquant行情文档链接：@https://dict.thinktrader.net/nativeApi/xtdata.html
- 迅投xtquant交易文档链接：@https://dict.thinktrader.net/nativeApi/xttrader.html
- 迅投xtquant实例示例链接：https://dict.thinktrader.net/nativeApi/code_examples.html
- 迅投xtquant数字字典链接：https://dict.thinktrader.net/dictionary/
- 迅投xtquant期货合约列表链接：https://dict.thinktrader.net/dictionary/future.html#%E6%9C%9F%E8%B4%A7%E5%88%97%E8%A1%A8
- 迅投xtquant下载：https://dict.thinktrader.net/nativeApi/download_xtquant.html 已支持python3.12版本

## xtquant调用规则
- 所有脚本导入xtquant包参考下方代码为统一标准

```python
from xtquant import xtdata as xt_data
xt_data.enable_hello = False

def download_data():
    xt_data.download_history_data2(
        stock_list=["600000.SH"],
        period="1d",
        start_time="20250101",
        end_time="20250131"
    )

    result = xt_data.get_local_data(
        field_list=["open","high","low","close"],
        stock_list=["600000.SH"],
        period="1d",
        start_time="20250101",
        end_time="20250131"
    )

    return result
```




