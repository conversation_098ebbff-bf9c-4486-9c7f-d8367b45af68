#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
时间格式化功能模块

提供日期时间对象和字符串之间的格式化转换功能
"""

import os
import sys
from datetime import datetime
from typing import Optional, Union
import pandas as pd
import re
from utils.smart_time_converter import smart_to_datetime

# 将项目根目录添加到Python路径
root_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, root_path)


def format_datetime(dt: datetime, format_str: str = '%Y-%m-%d %H:%M:%S') -> str:
    """
    格式化datetime对象为字符串
    
    Args:
        dt: datetime对象
        format_str: 格式字符串
        
    Returns:
        格式化后的时间字符串
    """
    return dt.strftime(format_str)


def format_time_index(df: pd.DataFrame, period: str) -> pd.DataFrame:
    """
    格式化DataFrame的时间索引，根据数据周期选择合适的格式
    
    Args:
        df: 输入的DataFrame，假设index为时间索引
        period: 数据周期，如'tick', '1m', '5m', '1d'等
        
    Returns:
        格式化后的DataFrame
    """
    # 创建副本，避免修改原始数据
    result = df.copy()
    
    # 确保索引是datetime类型
    if not isinstance(result.index, pd.DatetimeIndex):
        try:
            result.index = smart_to_datetime(result.index)
        except:
            # 如果转换失败，返回原始数据
            return df
    
    # 设置时区（如果没有）
    if result.index.tz is None:
        result.index = result.index.tz_localize('Asia/Shanghai')
    elif result.index.tz.zone != 'Asia/Shanghai':
        result.index = result.index.tz_convert('Asia/Shanghai')
    
    # 根据周期格式化时间索引
    time_format = get_date_format_for_period(period)
    
    if time_format:
        # 将索引转换为所需格式的字符串
        string_index = result.index.strftime(time_format)
        # 创建新的DataFrame，使用字符串作为索引
        result = pd.DataFrame(result.values, 
                             index=string_index, 
                             columns=result.columns)
    
    return result


def get_date_format_for_period(period: str) -> Optional[str]:
    """
    根据数据周期获取适合的日期格式
    
    Args:
        period: 数据周期，如'tick', '1m', '5m', '1d'等
        
    Returns:
        对应周期的日期格式字符串，如果没有匹配则返回None
    """
    # 标准化周期字符串
    period = period.lower().strip()
    
    # 定义周期到格式的映射
    period_formats = {
        'tick': '%Y-%m-%d %H:%M:%S.%f',
        '1s': '%Y-%m-%d %H:%M:%S',
        '1m': '%Y-%m-%d %H:%M',
        '5m': '%Y-%m-%d %H:%M',
        '15m': '%Y-%m-%d %H:%M',
        '30m': '%Y-%m-%d %H:%M',
        '60m': '%Y-%m-%d %H:%M',
        '1h': '%Y-%m-%d %H:%M',
        '1d': '%Y-%m-%d',
        '1w': '%Y-%m-%d',
        '1mon': '%Y-%m',
        'd': '%Y-%m-%d',
        'w': '%Y-%m-%d',
        'mon': '%Y-%m',
    }
    
    # 处理数字前缀周期格式，如1d、5m等
    match = re.match(r'(\d+)([a-z]+)', period)
    if match:
        number, unit = match.groups()
        key = f'1{unit}'
        return period_formats.get(key, None)
    
    # 直接查找完整匹配
    return period_formats.get(period, None)


def format_datetime_with_period(dt: datetime, period: str) -> str:
    """
    根据数据周期格式化日期时间
    
    Args:
        dt: 要格式化的datetime对象
        period: 数据周期
        
    Returns:
        格式化后的时间字符串
    """
    format_str = get_date_format_for_period(period)
    if format_str:
        return dt.strftime(format_str)
    else:
        # 默认格式
        return dt.strftime('%Y-%m-%d %H:%M:%S')


def format_time(timestamp: Union[int, float], format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """
    格式化时间戳为可读字符串
    
    Args:
        timestamp: Unix时间戳(秒)
        format_str: 格式化字符串
        
    Returns:
        格式化后的时间字符串
    """
    return datetime.fromtimestamp(timestamp).strftime(format_str)


def format_time_duration(seconds: float) -> str:
    """
    格式化时间持续时间为可读字符串
    
    Args:
        seconds: 秒数
        
    Returns:
        格式化后的持续时间字符串
    """
    # 处理极端情况
    if seconds < 0:
        return "无效时间"
    
    # 计算各个时间单位
    days, seconds = divmod(seconds, 86400)
    hours, seconds = divmod(seconds, 3600)
    minutes, seconds = divmod(seconds, 60)
    
    # 构建时间字符串
    parts = []
    if days > 0:
        parts.append(f"{int(days)}天")
    if hours > 0 or days > 0:
        parts.append(f"{int(hours)}小时")
    if minutes > 0 or hours > 0 or days > 0:
        parts.append(f"{int(minutes)}分钟")
    
    parts.append(f"{seconds:.2f}秒")
    
    return " ".join(parts)