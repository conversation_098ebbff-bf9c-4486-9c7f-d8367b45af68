#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据验证功能模块

提供数据质量检查、异常值检测和数据验证功能
"""

import os
import sys
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union, Any
import re

# 将项目根目录添加到Python路径
root_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, root_path)

# 导入日志模块
from utils.logger import get_unified_logger
from utils.smart_time_converter import smart_to_datetime

# 设置日志记录器
logger = get_unified_logger(__name__)


def detect_data_issues(df: pd.DataFrame) -> Dict[str, Any]:
    """
    检测数据集中的潜在问题
    
    检查缺失值、重复行、异常值等问题，并生成汇总报告
    
    Args:
        df: 输入DataFrame
        
    Returns:
        包含各种问题统计的字典
    """
    if df.empty:
        logger.warning("输入的DataFrame为空")
        return {"status": "empty_dataframe"}
    
    # 初始化结果字典
    issues = {
        "rows": len(df),
        "columns": len(df.columns),
        "missing_values": {},
        "duplicates": {
            "total": 0,
            "percentage": 0.0
        },
        "zero_variance": [],
        "high_correlation": [],
        "possible_outliers": {},
        "data_types": {}
    }
    
    # 检查缺失值
    missing_counts = df.isna().sum()
    missing_percentages = (missing_counts / len(df)) * 100
    
    for col, count in missing_counts.items():
        if count > 0:
            issues["missing_values"][col] = {
                "count": int(count),
                "percentage": float(missing_percentages[col])
            }
    
    # 检查重复行
    duplicate_count = df.duplicated().sum()
    issues["duplicates"]["total"] = int(duplicate_count)
    issues["duplicates"]["percentage"] = float((duplicate_count / len(df)) * 100)
    
    # 检查零方差列（常量列）
    for col in df.columns:
        if pd.api.types.is_numeric_dtype(df[col]):
            if df[col].nunique() <= 1:
                issues["zero_variance"].append(col)
    
    # 检查高相关列（仅对数值列）
    numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
    if len(numeric_cols) >= 2:  # 需要至少两列才能计算相关性
        try:
            corr_matrix = df[numeric_cols].corr().abs()
            
            # 获取高相关性的列对
            for i in range(len(numeric_cols)):
                for j in range(i+1, len(numeric_cols)):
                    col1, col2 = numeric_cols[i], numeric_cols[j]
                    if corr_matrix.loc[col1, col2] > 0.95:  # 设置较高的阈值
                        issues["high_correlation"].append({
                            "columns": [col1, col2],
                            "correlation": float(corr_matrix.loc[col1, col2])
                        })
        except Exception as e:
            logger.warning(f"计算相关性时出错: {str(e)}")
    
    # 使用IQR检测可能的异常值（仅对数值列）
    for col in numeric_cols:
        q1 = df[col].quantile(0.25)
        q3 = df[col].quantile(0.75)
        iqr = q3 - q1
        
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr
        
        outliers = df[(df[col] < lower_bound) | (df[col] > upper_bound)]
        outlier_count = len(outliers)
        
        if outlier_count > 0:
            issues["possible_outliers"][col] = {
                "count": int(outlier_count),
                "percentage": float((outlier_count / len(df)) * 100),
                "min": float(df[col].min()),
                "max": float(df[col].max()),
                "lower_bound": float(lower_bound),
                "upper_bound": float(upper_bound)
            }
    
    # 记录数据类型
    for col in df.columns:
        dtype = str(df[col].dtype)
        issues["data_types"][col] = {
            "type": dtype,
            "unique_values": int(df[col].nunique()),
            "memory_usage": int(df[col].memory_usage(deep=True))
        }
    
    # 添加内存使用情况
    issues["memory_usage"] = {
        "total": int(df.memory_usage(deep=True).sum()),
        "per_column": {col: int(usage) for col, usage in df.memory_usage(deep=True).items()}
    }
    
    return issues


def data_quality_score(df: pd.DataFrame, weights: Optional[Dict[str, float]] = None) -> Dict[str, Union[float, Dict]]:
    """
    计算数据质量得分
    
    综合考虑缺失值比例、重复数据比例、异常值比例等因素，给出一个0-100的得分
    
    Args:
        df: 输入DataFrame
        weights: 各项指标的权重，默认为None（使用均匀权重）
        
    Returns:
        包含总分和各项指标得分的字典
    """
    if df.empty:
        logger.warning("输入的DataFrame为空")
        return {"total_score": 0.0, "message": "DataFrame is empty"}
    
    # 默认权重
    default_weights = {
        "completeness": 0.3,  # 完整性（缺失值）
        "uniqueness": 0.2,    # 唯一性（重复值）
        "validity": 0.15,     # 有效性（数据类型）
        "consistency": 0.15,  # 一致性（异常值）
        "integrity": 0.2      # 完整性（特征间关系）
    }
    
    # 使用用户提供的权重或默认权重
    weights = weights or default_weights
    
    # 规范化权重，确保总和为1
    weight_sum = sum(weights.values())
    normalized_weights = {k: v / weight_sum for k, v in weights.items()}
    
    # 计算各项得分
    scores = {}
    
    # 1. 完整性得分（基于缺失值比例）
    missing_percentages = df.isna().mean() * 100
    avg_missing = missing_percentages.mean()
    scores["completeness"] = max(0, 100 - avg_missing)
    
    # 2. 唯一性得分（基于重复值比例）
    duplicate_percentage = df.duplicated().mean() * 100
    scores["uniqueness"] = max(0, 100 - duplicate_percentage)
    
    # 3. 有效性得分（基于数据类型的适当性）
    validity_score = 100.0
    
    # 检查日期列是否正确的日期类型
    for col in df.columns:
        col_lower = col.lower()
        if ('date' in col_lower or 'time' in col_lower) and not pd.api.types.is_datetime64_any_dtype(df[col]):
            # 尝试解析为日期，看是否成功
            try:
                smart_to_datetime(df[col], errors='raise')
            except:
                # 扣分
                validity_score -= 5
        
        # 检查数值列是否是数值类型
        if any(keyword in col_lower for keyword in ['price', 'amount', 'sum', 'total', 'count', 'number']):
            if not pd.api.types.is_numeric_dtype(df[col]):
                # 尝试解析为数值，看是否成功
                try:
                    pd.to_numeric(df[col], errors='raise')
                except:
                    # 扣分
                    validity_score -= 5
    
    scores["validity"] = max(0, validity_score)
    
    # 4. 一致性得分（基于异常值比例）
    consistency_score = 100.0
    
    # 检查数值列的异常值
    numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
    outlier_percentages = []
    
    for col in numeric_cols:
        q1 = df[col].quantile(0.25)
        q3 = df[col].quantile(0.75)
        iqr = q3 - q1
        
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr
        
        outlier_percentage = ((df[col] < lower_bound) | (df[col] > upper_bound)).mean() * 100
        outlier_percentages.append(outlier_percentage)
    
    if outlier_percentages:
        avg_outlier_percentage = sum(outlier_percentages) / len(outlier_percentages)
        consistency_score = max(0, 100 - avg_outlier_percentage * 2)  # 异常值扣分权重更高
    
    scores["consistency"] = consistency_score
    
    # 5. 完整性得分（基于特征间关系）
    integrity_score = 100.0
    
    # 检查高相关性特征
    if len(numeric_cols) >= 2:
        try:
            corr_matrix = df[numeric_cols].corr().abs()
            
            # 计算高相关性特征对的比例
            high_corr_pairs = 0
            total_pairs = 0
            
            for i in range(len(numeric_cols)):
                for j in range(i+1, len(numeric_cols)):
                    total_pairs += 1
                    if corr_matrix.iloc[i, j] > 0.95:
                        high_corr_pairs += 1
            
            if total_pairs > 0:
                high_corr_percentage = (high_corr_pairs / total_pairs) * 100
                integrity_score = max(0, 100 - high_corr_percentage * 2)  # 高相关性扣分权重更高
        except Exception as e:
            logger.warning(f"计算相关性时出错: {str(e)}")
    
    scores["integrity"] = integrity_score
    
    # 计算总分
    total_score = sum(score * normalized_weights[key] for key, score in scores.items())
    
    # 添加总分和权重信息
    result = {
        "total_score": round(total_score, 2),
        "scores": {k: round(v, 2) for k, v in scores.items()},
        "weights": normalized_weights
    }
    
    # 添加评级
    if total_score >= 90:
        result["rating"] = "Excellent"
    elif total_score >= 80:
        result["rating"] = "Very Good"
    elif total_score >= 70:
        result["rating"] = "Good"
    elif total_score >= 60:
        result["rating"] = "Fair"
    else:
        result["rating"] = "Poor"
    
    # 添加改进建议
    suggestions = []
    
    if scores["completeness"] < 90:
        suggestions.append("Consider handling missing values in the dataset.")
    
    if scores["uniqueness"] < 90:
        suggestions.append("Check and remove duplicate records.")
    
    if scores["validity"] < 90:
        suggestions.append("Review data types, especially for date and numeric columns.")
    
    if scores["consistency"] < 90:
        suggestions.append("Identify and handle outliers in numeric columns.")
    
    if scores["integrity"] < 90:
        suggestions.append("Review highly correlated features and consider dimensionality reduction.")
    
    result["suggestions"] = suggestions
    
    return result


def check_column_values(df: pd.DataFrame, column: str, 
                       valid_values: Optional[List] = None, 
                       pattern: Optional[str] = None,
                       value_range: Optional[Tuple[float, float]] = None) -> Dict[str, Any]:
    """
    检查列值是否满足特定条件
    
    Args:
        df: 输入DataFrame
        column: 要检查的列名
        valid_values: 有效值列表，如果提供，则检查列值是否在此列表中
        pattern: 正则表达式模式，如果提供，则检查列值是否匹配此模式
        value_range: 数值范围元组(min, max)，如果提供，则检查数值列是否在此范围内
        
    Returns:
        检查结果字典
    """
    if df.empty:
        logger.warning("输入的DataFrame为空")
        return {"status": "error", "message": "DataFrame is empty"}
    
    # 检查列是否存在
    if column not in df.columns:
        logger.error(f"列 '{column}' 不存在于DataFrame中")
        return {"status": "error", "message": f"Column '{column}' not found"}
    
    # 获取列数据
    col_data = df[column]
    
    # 初始化结果字典
    result = {
        "status": "success",
        "column": column,
        "total_values": len(col_data),
        "null_values": int(col_data.isna().sum()),
        "unique_values": int(col_data.nunique()),
        "value_counts": {},
        "invalid_values": [],
        "invalid_indices": []
    }
    
    # 添加值计数（仅对唯一值不太多的列）
    if result["unique_values"] <= 20:
        value_counts = col_data.value_counts().to_dict()
        result["value_counts"] = {str(k): int(v) for k, v in value_counts.items()}
    
    # 检查有效值列表
    if valid_values is not None:
        # 将有效值转换为集合以加快查找
        valid_set = set(valid_values)
        
        # 找出无效值
        non_null_mask = ~col_data.isna()  # 忽略空值
        invalid_mask = ~col_data.isin(valid_values) & non_null_mask
        
        if invalid_mask.any():
            invalid_indices = df.index[invalid_mask].tolist()
            invalid_values = col_data[invalid_mask].unique().tolist()
            
            result["invalid_values"] = [str(v) for v in invalid_values]
            result["invalid_indices"] = invalid_indices
            result["valid_values_check"] = {
                "status": "failed",
                "invalid_count": int(invalid_mask.sum()),
                "invalid_percentage": float((invalid_mask.sum() / non_null_mask.sum()) * 100)
            }
        else:
            result["valid_values_check"] = {
                "status": "passed",
                "invalid_count": 0,
                "invalid_percentage": 0.0
            }
    
    # 检查正则表达式模式
    if pattern is not None:
        try:
            regex = re.compile(pattern)
            
            # 对于对象类型列，检查每个值是否匹配模式
            if pd.api.types.is_object_dtype(col_data):
                # 忽略空值
                non_null_mask = ~col_data.isna()
                
                # 检查每个非空值是否匹配模式
                invalid_mask = non_null_mask.copy()
                for i, value in enumerate(col_data[non_null_mask]):
                    if regex.match(str(value)):
                        invalid_mask.iloc[non_null_mask.to_numpy().nonzero()[0][i]] = False
                
                if invalid_mask.any():
                    invalid_indices = df.index[invalid_mask].tolist()
                    invalid_values = col_data[invalid_mask].unique().tolist()
                    
                    result["invalid_values"].extend([str(v) for v in invalid_values])
                    result["invalid_indices"].extend(invalid_indices)
                    result["pattern_check"] = {
                        "status": "failed",
                        "pattern": pattern,
                        "invalid_count": int(invalid_mask.sum()),
                        "invalid_percentage": float((invalid_mask.sum() / non_null_mask.sum()) * 100)
                    }
                else:
                    result["pattern_check"] = {
                        "status": "passed",
                        "pattern": pattern,
                        "invalid_count": 0,
                        "invalid_percentage": 0.0
                    }
        except Exception as e:
            logger.error(f"正则表达式检查时出错: {str(e)}")
            result["pattern_check"] = {
                "status": "error",
                "message": str(e)
            }
    
    # 检查数值范围
    if value_range is not None:
        min_val, max_val = value_range
        
        # 检查列是否为数值类型
        if pd.api.types.is_numeric_dtype(col_data):
            # 忽略空值
            non_null_mask = ~col_data.isna()
            
            # 检查值是否在范围内
            invalid_mask = ((col_data < min_val) | (col_data > max_val)) & non_null_mask
            
            if invalid_mask.any():
                invalid_indices = df.index[invalid_mask].tolist()
                
                # 对于大数据集，仅记录少量无效值
                if len(invalid_indices) > 20:
                    invalid_values = col_data.iloc[invalid_indices[:20]].tolist()
                    result["invalid_values"].extend([str(v) for v in invalid_values])
                    result["invalid_values"].append("... and more")
                else:
                    invalid_values = col_data[invalid_mask].tolist()
                    result["invalid_values"].extend([str(v) for v in invalid_values])
                
                result["invalid_indices"].extend(invalid_indices)
                result["range_check"] = {
                    "status": "failed",
                    "range": [float(min_val), float(max_val)],
                    "invalid_count": int(invalid_mask.sum()),
                    "invalid_percentage": float((invalid_mask.sum() / non_null_mask.sum()) * 100),
                    "min_value": float(col_data[non_null_mask].min()),
                    "max_value": float(col_data[non_null_mask].max())
                }
            else:
                result["range_check"] = {
                    "status": "passed",
                    "range": [float(min_val), float(max_val)],
                    "invalid_count": 0,
                    "invalid_percentage": 0.0,
                    "min_value": float(col_data[non_null_mask].min()),
                    "max_value": float(col_data[non_null_mask].max())
                }
        else:
            logger.warning(f"列 '{column}' 不是数值类型，无法进行范围检查")
            result["range_check"] = {
                "status": "error",
                "message": "Column is not numeric type"
            }
    
    # 去重无效值和索引
    result["invalid_values"] = list(set(result["invalid_values"]))
    result["invalid_indices"] = list(set(result["invalid_indices"]))
    
    # 更新总体状态
    if result.get("valid_values_check", {}).get("status") == "failed" or \
       result.get("pattern_check", {}).get("status") == "failed" or \
       result.get("range_check", {}).get("status") == "failed":
        result["status"] = "failed"
    
    return result