#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
时间处理工具模块

提供时间戳转换、格式化和标准化相关功能
"""

# 从转换模块导入
from utils.time_formatter.conversion import (
    timestamp_to_datetime,
    datetime_to_timestamp,
    standardize_timestamp_series,
    format_date_to_int,
    format_int_to_date
)

# 从格式化模块导入
from utils.time_formatter.formatting import (
    format_datetime,
    format_time_index,
    get_date_format_for_period,
    format_datetime_with_period,
    format_time,
    format_time_duration
)

# 从解析模块导入
from utils.time_formatter.parsing import (
    parse_datetime,
    parse_multi_format_date
)

# 从验证模块导入
from utils.time_formatter.validation import (
    validate_date_format,
    is_trading_day,
    adjust_to_trading_day,
    get_start_end_date,
    get_default_period_dates
)

# 从日期提取模块导入
from utils.time_formatter.date_extraction import (
    extract_date_from_path,
    extract_timestamp_from_data,
    sort_files_by_date,
    get_latest_file_by_date,
    get_earliest_file_by_date,
    extract_dates_from_paths_batch,
    clear_date_extraction_cache,
    get_cache_stats
)

# 统一从极简工具导入（唯一实现）
from utils.time_utils import (
    # 核心转换函数
    simple_ms_to_datetime,
    simple_ms_to_datetime_list,
    fast_ms_to_datetime_index,
    simple_string_to_datetime_list,

    # 向后兼容别名
    ms_to_datetime,
    ms_to_datetime_index,
    s_to_datetime,
    s_to_datetime_index,

    # 扩展功能
    datetime_to_ms,
    datetime_to_s,
    format_datetime,
    verify_conversion,
    TimeConversionError,

    # 向后兼容别名（替代旧unified_converter）
    _convert_ms_timestamp_to_datetime,
    convert_ms_timestamp_to_datetimeindex,
    convert_s_timestamp_to_datetimeindex
)

# 从交易时间模块导入
from utils.time_formatter.trading_time import (
    is_valid_trading_time,
    get_last_valid_trading_time,
    detect_symbol_type,
    detect_futures_category,
    TradingTimeRules
)

# 导出所有接口
__all__ = [
    # 核心时间转换（推荐使用）
    'simple_ms_to_datetime',
    'simple_ms_to_datetime_list',
    'fast_ms_to_datetime_index',
    'simple_string_to_datetime_list',

    # 向后兼容别名
    'ms_to_datetime',
    'ms_to_datetime_index',
    's_to_datetime',
    's_to_datetime_index',

    # 扩展功能
    'datetime_to_ms',
    'datetime_to_s',
    'format_datetime',
    'verify_conversion',
    'TimeConversionError',

    # 向后兼容（替代旧unified_converter）
    '_convert_ms_timestamp_to_datetime',
    'convert_ms_timestamp_to_datetimeindex',
    'convert_s_timestamp_to_datetimeindex',

    # 传统转换相关
    'timestamp_to_datetime',
    'datetime_to_timestamp',
    'standardize_timestamp_series',
    'format_date_to_int',
    'format_int_to_date',

    # 格式化相关
    'format_time_index',
    'get_date_format_for_period',
    'format_datetime_with_period',
    'format_time',
    'format_time_duration',

    # 解析相关
    'parse_datetime',
    'parse_multi_format_date',

    # 验证相关
    'validate_date_format',
    'is_trading_day',
    'adjust_to_trading_day',
    'get_start_end_date',
    'get_default_period_dates',

    # 交易时间相关
    'is_valid_trading_time',
    'get_last_valid_trading_time',
    'detect_symbol_type',
    'detect_futures_category',
    'TradingTimeRules',

    # 日期提取相关
    'extract_date_from_path',
    'extract_timestamp_from_data',
    'sort_files_by_date',
    'get_latest_file_by_date',
    'get_earliest_file_by_date',
    'extract_dates_from_paths_batch',
    'clear_date_extraction_cache',
    'get_cache_stats'
]