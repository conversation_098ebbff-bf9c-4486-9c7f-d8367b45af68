#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据处理性能基准测试

提供全面的性能基准测试，包括：
- 向量化 vs 传统方法对比
- 不同数据量的性能测试
- 内存使用监控
- 性能回归检测

作者: AI Assistant
创建时间: 2025-07-15
版本: 1.0.0
"""

import time
import psutil
import os
from typing import Dict, List, Any, Optional
import pandas as pd
import numpy as np
from dataclasses import dataclass

from data.processing import (
    load_data,
    create_pipeline,
    get_performance_stats,
    clear_all_cache
)
from data.storage.parquet_reader import read_partitioned_data
from utils.logger import get_unified_logger

logger = get_unified_logger(__name__)


@dataclass
class BenchmarkResult:
    """基准测试结果"""
    method: str
    data_size: int
    execution_time: float
    memory_usage: float
    cpu_usage: float
    success: bool
    error_message: Optional[str] = None


class PerformanceBenchmark:
    """
    性能基准测试器
    """
    
    def __init__(self):
        """初始化性能基准测试器"""
        self.results = []
        self.process = psutil.Process(os.getpid())
    
    def _monitor_resources(self):
        """监控系统资源使用"""
        memory_info = self.process.memory_info()
        cpu_percent = self.process.cpu_percent()
        
        return {
            'memory_mb': memory_info.rss / 1024 / 1024,
            'cpu_percent': cpu_percent
        }
    
    def benchmark_data_loading(self, data_root: str, symbol: str, period: str,
                             start_time: str = None, end_time: str = None) -> List[BenchmarkResult]:
        """
        数据加载性能基准测试
        
        Args:
            data_root: 数据根目录
            symbol: 股票代码
            period: 数据周期
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            List[BenchmarkResult]: 基准测试结果列表
        """
        logger.info(f"开始数据加载性能基准测试: {symbol} {period}")
        
        results = []
        
        # 测试传统方法
        logger.info("测试传统数据加载方法...")
        try:
            # 清空缓存
            clear_all_cache()
            
            # 监控资源使用
            start_resources = self._monitor_resources()
            start_time_bench = time.time()
            
            # 执行传统方法
            df_traditional = read_partitioned_data(
                data_root, symbol, period, start_time, end_time
            )
            
            end_time_bench = time.time()
            end_resources = self._monitor_resources()
            
            # 记录结果
            if df_traditional is not None:
                result = BenchmarkResult(
                    method="traditional",
                    data_size=len(df_traditional),
                    execution_time=end_time_bench - start_time_bench,
                    memory_usage=end_resources['memory_mb'] - start_resources['memory_mb'],
                    cpu_usage=end_resources['cpu_percent'],
                    success=True
                )
                logger.info(f"传统方法完成: {result.data_size} 行, {result.execution_time:.6f} 秒")
            else:
                result = BenchmarkResult(
                    method="traditional",
                    data_size=0,
                    execution_time=end_time_bench - start_time_bench,
                    memory_usage=0,
                    cpu_usage=0,
                    success=False,
                    error_message="返回空数据"
                )
                logger.warning("传统方法返回空数据")
            
            results.append(result)
            
        except Exception as e:
            logger.error(f"传统方法测试失败: {e}")
            results.append(BenchmarkResult(
                method="traditional",
                data_size=0,
                execution_time=0,
                memory_usage=0,
                cpu_usage=0,
                success=False,
                error_message=str(e)
            ))
        
        # 测试向量化方法
        logger.info("测试向量化数据加载方法...")
        try:
            # 清空缓存
            clear_all_cache()
            
            # 监控资源使用
            start_resources = self._monitor_resources()
            start_time_bench = time.time()
            
            # 执行向量化方法
            df_vectorized = load_data(
                data_root, symbol, period, start_time, end_time, method='vectorized'
            )
            
            end_time_bench = time.time()
            end_resources = self._monitor_resources()
            
            # 记录结果
            if df_vectorized is not None:
                result = BenchmarkResult(
                    method="vectorized",
                    data_size=len(df_vectorized),
                    execution_time=end_time_bench - start_time_bench,
                    memory_usage=end_resources['memory_mb'] - start_resources['memory_mb'],
                    cpu_usage=end_resources['cpu_percent'],
                    success=True
                )
                logger.info(f"向量化方法完成: {result.data_size} 行, {result.execution_time:.6f} 秒")
            else:
                result = BenchmarkResult(
                    method="vectorized",
                    data_size=0,
                    execution_time=end_time_bench - start_time_bench,
                    memory_usage=0,
                    cpu_usage=0,
                    success=False,
                    error_message="返回空数据"
                )
                logger.warning("向量化方法返回空数据")
            
            results.append(result)
            
        except Exception as e:
            logger.error(f"向量化方法测试失败: {e}")
            results.append(BenchmarkResult(
                method="vectorized",
                data_size=0,
                execution_time=0,
                memory_usage=0,
                cpu_usage=0,
                success=False,
                error_message=str(e)
            ))
        
        # 保存结果
        self.results.extend(results)
        
        return results
    
    def benchmark_pipeline_processing(self, data_root: str, symbol: str, period: str) -> BenchmarkResult:
        """
        数据处理管道性能基准测试
        
        Args:
            data_root: 数据根目录
            symbol: 股票代码
            period: 数据周期
            
        Returns:
            BenchmarkResult: 基准测试结果
        """
        logger.info(f"开始数据处理管道性能基准测试: {symbol} {period}")
        
        try:
            # 清空缓存
            clear_all_cache()
            
            # 监控资源使用
            start_resources = self._monitor_resources()
            start_time_bench = time.time()
            
            # 创建并执行管道
            result_df = (create_pipeline()
                .load_data(data_root, symbol, period, method='vectorized')
                .filter({'price': lambda df: df['price'] > 0})  # 示例过滤
                .execute())
            
            end_time_bench = time.time()
            end_resources = self._monitor_resources()
            
            # 记录结果
            if result_df is not None:
                result = BenchmarkResult(
                    method="pipeline",
                    data_size=len(result_df),
                    execution_time=end_time_bench - start_time_bench,
                    memory_usage=end_resources['memory_mb'] - start_resources['memory_mb'],
                    cpu_usage=end_resources['cpu_percent'],
                    success=True
                )
                logger.info(f"管道处理完成: {result.data_size} 行, {result.execution_time:.6f} 秒")
            else:
                result = BenchmarkResult(
                    method="pipeline",
                    data_size=0,
                    execution_time=end_time_bench - start_time_bench,
                    memory_usage=0,
                    cpu_usage=0,
                    success=False,
                    error_message="管道返回空数据"
                )
                logger.warning("管道处理返回空数据")
            
            self.results.append(result)
            return result
            
        except Exception as e:
            logger.error(f"管道处理测试失败: {e}")
            result = BenchmarkResult(
                method="pipeline",
                data_size=0,
                execution_time=0,
                memory_usage=0,
                cpu_usage=0,
                success=False,
                error_message=str(e)
            )
            self.results.append(result)
            return result
    
    def generate_report(self) -> Dict[str, Any]:
        """
        生成性能基准测试报告
        
        Returns:
            dict: 测试报告
        """
        if not self.results:
            return {"error": "没有测试结果"}
        
        # 按方法分组结果
        method_results = {}
        for result in self.results:
            if result.method not in method_results:
                method_results[result.method] = []
            method_results[result.method].append(result)
        
        # 生成报告
        report = {
            "summary": {
                "total_tests": len(self.results),
                "successful_tests": sum(1 for r in self.results if r.success),
                "failed_tests": sum(1 for r in self.results if not r.success)
            },
            "methods": {}
        }
        
        for method, results in method_results.items():
            successful_results = [r for r in results if r.success]
            
            if successful_results:
                avg_time = sum(r.execution_time for r in successful_results) / len(successful_results)
                avg_memory = sum(r.memory_usage for r in successful_results) / len(successful_results)
                total_data = sum(r.data_size for r in successful_results)
                
                report["methods"][method] = {
                    "tests_count": len(results),
                    "success_count": len(successful_results),
                    "avg_execution_time": avg_time,
                    "avg_memory_usage": avg_memory,
                    "total_data_processed": total_data,
                    "throughput": total_data / avg_time if avg_time > 0 else 0
                }
            else:
                report["methods"][method] = {
                    "tests_count": len(results),
                    "success_count": 0,
                    "error": "所有测试都失败了"
                }
        
        # 计算性能提升
        if "traditional" in report["methods"] and "vectorized" in report["methods"]:
            traditional_time = report["methods"]["traditional"].get("avg_execution_time", 0)
            vectorized_time = report["methods"]["vectorized"].get("avg_execution_time", 0)
            
            if vectorized_time > 0:
                speedup = traditional_time / vectorized_time
                report["performance_improvement"] = {
                    "speedup": speedup,
                    "time_saved_percent": (1 - vectorized_time / traditional_time) * 100 if traditional_time > 0 else 0
                }
        
        return report
    
    def clear_results(self):
        """清空测试结果"""
        self.results.clear()
        logger.debug("基准测试结果已清空")


def run_comprehensive_benchmark(data_root: str, symbol: str, period: str) -> Dict[str, Any]:
    """
    运行综合性能基准测试
    
    Args:
        data_root: 数据根目录
        symbol: 股票代码
        period: 数据周期
        
    Returns:
        dict: 综合测试报告
    """
    logger.info("开始综合性能基准测试")
    
    benchmark = PerformanceBenchmark()
    
    # 数据加载测试
    loading_results = benchmark.benchmark_data_loading(data_root, symbol, period)
    
    # 管道处理测试
    pipeline_result = benchmark.benchmark_pipeline_processing(data_root, symbol, period)
    
    # 生成报告
    report = benchmark.generate_report()
    
    # 添加系统性能统计
    performance_stats = get_performance_stats()
    report["system_stats"] = performance_stats
    
    logger.info("综合性能基准测试完成")
    
    return report
