#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能时间戳处理器

专为数据驱动的分区路径管理设计，集成项目现有的时间处理基础设施。
解决时间戳处理分散化问题，提供统一的数据感知时间戳处理能力。

核心特性：
1. 数据驱动：自动从DataFrame中提取时间信息
2. 智能检测：支持多种时间格式和数据结构
3. 统一处理：集成smart_time_converter和extract_timestamp_from_data
4. 范围分析：支持跨日期数据的时间范围分析
5. 向后兼容：保持现有API兼容性

设计理念：
- 准确性优于便利性：确保时间戳提取的准确性
- 自动化优于手动：减少人为错误的可能性
- 统一优于分散：集中管理所有时间戳处理逻辑
- 数据驱动优于参数驱动：基于数据内容确定时间信息

版本: v1.0
作者: Augment AI
日期: 2025-08-05
"""

import pandas as pd
import numpy as np
from datetime import datetime, date
from typing import Optional, Union, List, Tuple, Dict, Any
from dataclasses import dataclass

from utils.logger import get_unified_logger, LogTarget
from utils.smart_time_converter import smart_to_datetime
from utils.time_formatter.date_extraction import extract_timestamp_from_data

# 创建统一日志记录器
logger = get_unified_logger(__name__, enhanced=True)


@dataclass
class TimeRangeInfo:
    """时间范围信息"""
    start_time: datetime
    end_time: datetime
    start_timestamp: str  # YYYYMMDDHHMMSS格式
    end_timestamp: str    # YYYYMMDDHHMMSS格式
    date_range: List[str]  # YYYYMMDD格式的日期列表
    span_days: int        # 跨越天数
    
    def get_primary_date(self) -> str:
        """获取主要日期（用于单一分区的情况）"""
        return self.start_timestamp[:8]
    
    def get_date_list(self) -> List[str]:
        """获取所有涉及的日期列表"""
        return self.date_range.copy()


class SmartTimestampProcessor:
    """
    智能时间戳处理器
    
    集成项目现有的时间处理基础设施，提供数据驱动的时间戳处理能力。
    """
    
    def __init__(self):
        """初始化智能时间戳处理器"""
        self._cache = {}  # 简单缓存，避免重复计算
        logger.debug(LogTarget.FILE, "智能时间戳处理器初始化完成")
    
    def extract_time_range_from_data(
        self, 
        df: pd.DataFrame, 
        fallback_timestamp: Optional[str] = None
    ) -> Optional[TimeRangeInfo]:
        """
        从DataFrame中提取时间范围信息
        
        Args:
            df: 数据DataFrame
            fallback_timestamp: 备选时间戳
            
        Returns:
            TimeRangeInfo: 时间范围信息，如果提取失败则返回None
        """
        if df is None or df.empty:
            logger.warning(LogTarget.FILE, "数据为空，无法提取时间范围")
            return self._create_fallback_time_range(fallback_timestamp)
        
        try:
            # 使用现有的统一时间提取模块
            timestamp_str = extract_timestamp_from_data(df, fallback_timestamp)
            
            if not timestamp_str:
                logger.warning(LogTarget.FILE, "无法从数据中提取时间戳")
                return self._create_fallback_time_range(fallback_timestamp)
            
            # 分析数据的时间范围
            time_range = self._analyze_data_time_range(df, timestamp_str)
            
            if time_range:
                logger.debug(
                    LogTarget.FILE, 
                    f"成功提取时间范围: {time_range.start_timestamp} - {time_range.end_timestamp}"
                )
                return time_range
            else:
                logger.warning(LogTarget.FILE, "时间范围分析失败")
                return self._create_fallback_time_range(fallback_timestamp)
                
        except Exception as e:
            logger.error(LogTarget.FILE, f"提取时间范围失败: {e}")
            return self._create_fallback_time_range(fallback_timestamp)
    
    def get_partition_timestamp(
        self,
        df: pd.DataFrame,
        period: str = "tick",
        fallback_timestamp: Optional[str] = None,
        force_single_partition: bool = False
    ) -> str:
        """
        获取用于分区的时间戳（修复版本）

        Args:
            df: 数据DataFrame
            period: 数据周期
            fallback_timestamp: 备选时间戳
            force_single_partition: 是否强制单分区（用于向后兼容）

        Returns:
            str: YYYYMMDD格式的时间戳

        Raises:
            ValueError: 当检测到跨日期数据但未强制单分区时抛出异常
        """
        try:
            time_range = self.extract_time_range_from_data(df, fallback_timestamp)

            if time_range:
                # 检查是否跨日期数据
                if time_range.span_days > 1:
                    if not force_single_partition:
                        # 跨日期数据不应该使用单一时间戳，应该使用多分区策略
                        raise ValueError(
                            f"检测到跨日期数据（跨越{time_range.span_days}天: "
                            f"{time_range.start_timestamp[:8]} - {time_range.end_timestamp[:8]}），"
                            f"不能使用单一分区时间戳。请使用多分区策略处理跨日期数据。"
                        )
                    else:
                        # 强制单分区时，记录警告并返回主要日期
                        logger.warning(LogTarget.FILE,
                                     f"强制单分区模式：跨日期数据（{time_range.span_days}天）"
                                     f"将全部保存到主要日期 {time_range.get_primary_date()}")
                        return time_range.get_primary_date()

                # 单日期数据，正常返回
                return time_range.get_primary_date()
            else:
                # 使用备选时间戳或当前日期
                if fallback_timestamp and len(fallback_timestamp) >= 8:
                    return fallback_timestamp[:8]
                else:
                    return datetime.now().strftime('%Y%m%d')

        except ValueError:
            # 重新抛出跨日期数据异常
            raise
        except Exception as e:
            logger.error(LogTarget.FILE, f"获取分区时间戳失败: {e}")
            return datetime.now().strftime('%Y%m%d')
    
    def analyze_cross_date_data(self, df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """
        分析跨日期数据，按日期分组
        
        Args:
            df: 数据DataFrame
            
        Returns:
            Dict[str, pd.DataFrame]: 按日期分组的数据字典
        """
        try:
            time_range = self.extract_time_range_from_data(df)
            
            if not time_range or time_range.span_days <= 1:
                # 单日数据或无法分析，返回原数据
                primary_date = time_range.get_primary_date() if time_range else datetime.now().strftime('%Y%m%d')
                return {primary_date: df}
            
            # 跨日期数据，需要按日期分组
            return self._group_data_by_date(df, time_range)
            
        except Exception as e:
            logger.error(LogTarget.FILE, f"分析跨日期数据失败: {e}")
            # 返回原数据作为单一分组
            fallback_date = datetime.now().strftime('%Y%m%d')
            return {fallback_date: df}
    
    def _analyze_data_time_range(self, df: pd.DataFrame, base_timestamp: str) -> Optional[TimeRangeInfo]:
        """分析数据的实际时间范围"""
        try:
            # 查找时间列
            time_columns = ['time', 'datetime', 'date', 'timestamp']
            time_col = None
            
            for col in time_columns:
                if col in df.columns:
                    time_col = col
                    break
            
            if time_col is None:
                # 没有时间列，使用索引或基础时间戳
                return self._create_time_range_from_timestamp(base_timestamp)
            
            # 分析时间列的范围
            time_series = df[time_col]
            
            if pd.api.types.is_numeric_dtype(time_series):
                # 数值时间戳
                min_time = time_series.min()
                max_time = time_series.max()
                
                # 转换为datetime
                if min_time > 1e12:  # 毫秒时间戳
                    start_dt = smart_to_datetime(min_time, unit='ms')
                    end_dt = smart_to_datetime(max_time, unit='ms')
                else:  # 秒时间戳
                    start_dt = smart_to_datetime(min_time, unit='s')
                    end_dt = smart_to_datetime(max_time, unit='s')
                    
            elif pd.api.types.is_datetime64_any_dtype(time_series):
                # datetime类型
                start_dt = time_series.min().to_pydatetime()
                end_dt = time_series.max().to_pydatetime()
                
            else:
                # 其他类型，尝试转换
                start_dt = smart_to_datetime(time_series.iloc[0])
                end_dt = smart_to_datetime(time_series.iloc[-1])
            
            return self._create_time_range_info(start_dt, end_dt)
            
        except Exception as e:
            logger.debug(LogTarget.FILE, f"分析数据时间范围失败: {e}")
            return self._create_time_range_from_timestamp(base_timestamp)
    
    def _create_time_range_info(self, start_dt: datetime, end_dt: datetime) -> TimeRangeInfo:
        """创建时间范围信息对象"""
        start_timestamp = start_dt.strftime('%Y%m%d%H%M%S')
        end_timestamp = end_dt.strftime('%Y%m%d%H%M%S')
        
        # 计算日期范围
        start_date = start_dt.date()
        end_date = end_dt.date()
        
        date_range = []
        current_date = start_date
        while current_date <= end_date:
            date_range.append(current_date.strftime('%Y%m%d'))
            current_date = date(current_date.year, current_date.month, current_date.day + 1)
        
        span_days = len(date_range)
        
        return TimeRangeInfo(
            start_time=start_dt,
            end_time=end_dt,
            start_timestamp=start_timestamp,
            end_timestamp=end_timestamp,
            date_range=date_range,
            span_days=span_days
        )
    
    def _create_time_range_from_timestamp(self, timestamp_str: str) -> Optional[TimeRangeInfo]:
        """从时间戳字符串创建时间范围信息"""
        try:
            if not timestamp_str:
                return None
                
            if len(timestamp_str) >= 14:
                dt = datetime.strptime(timestamp_str[:14], '%Y%m%d%H%M%S')
            elif len(timestamp_str) >= 8:
                dt = datetime.strptime(timestamp_str[:8], '%Y%m%d')
            else:
                return None
                
            return self._create_time_range_info(dt, dt)
            
        except Exception as e:
            logger.debug(LogTarget.FILE, f"从时间戳创建时间范围失败: {e}")
            return None
    
    def _create_fallback_time_range(self, fallback_timestamp: Optional[str]) -> Optional[TimeRangeInfo]:
        """创建备选时间范围"""
        if fallback_timestamp:
            return self._create_time_range_from_timestamp(fallback_timestamp)
        else:
            # 使用当前时间
            now = datetime.now()
            return self._create_time_range_info(now, now)
    
    def _group_data_by_date(self, df: pd.DataFrame, time_range: TimeRangeInfo) -> Dict[str, pd.DataFrame]:
        """按日期分组数据"""
        try:
            if time_range.span_days == 1:
                return {time_range.get_primary_date(): df}

            # 对于跨日期数据，实现真正的按日期分组
            logger.info(LogTarget.FILE, f"检测到跨日期数据，跨越{time_range.span_days}天，执行按日期分组")

            # 查找时间列
            time_columns = ['time', 'datetime', 'date', 'timestamp']
            time_col = None

            for col in time_columns:
                if col in df.columns:
                    time_col = col
                    break

            if time_col is None:
                logger.warning(LogTarget.FILE, "未找到时间列，使用主要日期分组")
                return {time_range.get_primary_date(): df}

            # 转换时间列为datetime格式并提取日期
            time_series = df[time_col]

            if pd.api.types.is_numeric_dtype(time_series):
                # 数值时间戳，转换为datetime
                if time_series.max() > 1e12:  # 毫秒时间戳
                    datetime_series = pd.to_datetime(time_series, unit='ms')
                else:  # 秒时间戳
                    datetime_series = pd.to_datetime(time_series, unit='s')
            elif pd.api.types.is_datetime64_any_dtype(time_series):
                # 已经是datetime格式
                datetime_series = time_series
            else:
                # 其他格式，尝试转换
                from utils.smart_time_converter import smart_to_datetime
                datetime_series = smart_to_datetime(time_series)

            # 提取日期部分
            date_series = datetime_series.dt.date

            # 按日期分组
            grouped_data = {}
            for date, group in df.groupby(date_series):
                date_str = date.strftime('%Y%m%d')
                grouped_data[date_str] = group.copy()
                logger.debug(LogTarget.FILE, f"分组 {date_str}: {len(group)} 行数据")

            logger.info(LogTarget.FILE, f"按日期分组完成，共 {len(grouped_data)} 个分组")
            return grouped_data

        except Exception as e:
            logger.error(LogTarget.FILE, f"按日期分组数据失败: {e}")
            # 回退到主要日期分组
            fallback_date = time_range.get_primary_date() if time_range else datetime.now().strftime('%Y%m%d')
            logger.warning(LogTarget.FILE, f"回退到主要日期分组: {fallback_date}")
            return {fallback_date: df}


# 全局实例
_smart_timestamp_processor = None


def get_smart_timestamp_processor() -> SmartTimestampProcessor:
    """获取全局智能时间戳处理器实例"""
    global _smart_timestamp_processor
    if _smart_timestamp_processor is None:
        _smart_timestamp_processor = SmartTimestampProcessor()
    return _smart_timestamp_processor


# 便捷函数
def extract_partition_timestamp(
    df: pd.DataFrame,
    period: str = "tick",
    fallback_timestamp: Optional[str] = None,
    force_single_partition: bool = False
) -> str:
    """
    从数据中提取分区时间戳的便捷函数（修复版本）

    Args:
        df: 数据DataFrame
        period: 数据周期
        fallback_timestamp: 备选时间戳
        force_single_partition: 是否强制单分区（用于向后兼容）

    Returns:
        str: YYYYMMDD格式的时间戳

    Raises:
        ValueError: 当检测到跨日期数据但未强制单分区时抛出异常
    """
    processor = get_smart_timestamp_processor()
    return processor.get_partition_timestamp(df, period, fallback_timestamp, force_single_partition)


def analyze_data_time_range(df: pd.DataFrame) -> Optional[TimeRangeInfo]:
    """
    分析数据时间范围的便捷函数
    
    Args:
        df: 数据DataFrame
        
    Returns:
        TimeRangeInfo: 时间范围信息
    """
    processor = get_smart_timestamp_processor()
    return processor.extract_time_range_from_data(df)
