#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
控制台显示功能模块

提供控制台界面相关功能，如进度条、菜单等
该模块已被重构，现在从其他模块导入函数以避免重复实现
"""

import os
import sys

# 将项目根目录添加到Python路径
root_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, root_path)

# 从其他模块导入函数
from utils.data_display.formatting import print_progress_bar, print_dict_as_table
from utils.data_display.menus import show_menu

# 为了向后兼容，重新导出这些函数
__all__ = [
    'print_progress_bar',
    'print_dict_as_table',
    'show_menu'
]