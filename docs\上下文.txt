- **【P9XGPS】2025-08-04 22:22:26,037 | ...anager.unified_path_manager【                      __init__】(  101)|      DEBUG | 路径管理器v2.0初始化完成，数据根目录: D:\data
【P9XGPS】2025-08-04 22:22:27,670 | ...ta_processor.period_support【             register_strategy】(   56)|      DEBUG | 已注册数据源策略: xtquant
【P9XGPS】2025-08-04 22:22:28,133 |   data.storage.parquet_storage【                              】(    0)|       INFO | 已加载全局进程池模块
【P9XGPS】2025-08-04 22:22:28,290 |                   data.storage【                              】(    0)|      DEBUG | 初始化数据存储模块
【P9XGPS】2025-08-04 22:22:28,364 |                   data.storage【                              】(    0)|      DEBUG | 已导入路径管理模块
【P9XGPS】2025-08-04 22:22:28,443 |                   data.storage【                              】(    0)|      DEBUG | 已导入Parquet读取模块
【P9XGPS】2025-08-04 22:22:28,520 |                   data.storage【                              】(    0)|      DEBUG | 已导入Parquet存储模块
【P9XGPS】2025-08-04 22:22:28,595 |                   data.storage【                              】(    0)|      DEBUG | 设置模块导出的符号
【P9XGPS】2025-08-04 22:22:28,672 |                   data.storage【                              】(    0)|      DEBUG | 数据存储模块版本: 1.0.0
【P9XGPS】2025-08-04 22:22:28,747 |                   data.storage【                              】(    0)|       INFO | 数据存储模块初始化完成，提供数据存储和读取功能
【P9XGPS】2025-08-04 22:22:28,846 | ...ent.dividend_factor_storage【                      __init__】(   50)|      DEBUG | 复权因子存储管理器初始化完成，数据根目录: D:\data
【P9XGPS】2025-08-04 22:22:28,926 | ...ent.dividend_factor_storage【                      __init__】(   51)|      DEBUG | 使用统一路径管理器工厂动态获取路径管理器
【P9XGPS】2025-08-04 22:22:29,006 | ...tment.field_type_classifier【          _init_field_mappings】(  125)|      DEBUG | 字段映射初始化完成:
【P9XGPS】2025-08-04 22:22:29,123 | ...tment.field_type_classifier【          _init_field_mappings】(  126)|      DEBUG |   价格字段: 32 个
【P9XGPS】2025-08-04 22:22:29,238 | ...tment.field_type_classifier【          _init_field_mappings】(  127)|      DEBUG |   数量字段: 25 个
【P9XGPS】2025-08-04 22:22:29,353 | ...tment.field_type_classifier【          _init_field_mappings】(  128)|      DEBUG |   时间字段: 7 个
【P9XGPS】2025-08-04 22:22:29,467 | ...tment.field_type_classifier【          _init_field_mappings】(  129)|      DEBUG |   比率字段: 15 个
【P9XGPS】2025-08-04 22:22:29,584 | ...tment.field_type_classifier【          _init_field_mappings】(  130)|      DEBUG |   计数字段: 8 个
【P9XGPS】2025-08-04 22:22:29,702 | ...tment.field_type_classifier【                      __init__】(   46)|      DEBUG | 字段类型分类器初始化完成
【P9XGPS】2025-08-04 22:22:29,820 | ...stment.data_quality_monitor【                      __init__】(   56)|      DEBUG | 复权数据质量监控器初始化完成
【P9XGPS】2025-08-04 22:22:29,939 | ...t.forward_adjustment_engine【                      __init__】(   38)|      DEBUG | 前复权计算引擎初始化完成
【P9XGPS】2025-08-04 22:22:30,026 | data.storage.vectorized_reader【                      __init__】(  117)|       INFO | Pandas向量化读取器初始化成功
【P9XGPS】2025-08-04 22:22:30,199 | ...orage.unified_data_accessor【                      __init__】(   57)|       INFO | 统一数据访问器初始化完成
【P9XGPS】2025-08-04 22:22:30,375 | ...ment.adjustment_synthesizer【                      __init__】(   42)|      DEBUG | 复权数据合成器初始化完成（使用统一数据访问器）
【P9XGPS】2025-08-04 22:22:30,468 |                       __main__【  process_tick_adjustment_data】(  126)|       INFO | 开始合成 600000.SH 的tick复权数据，复权类型: front
【P9XGPS】2025-08-04 22:22:30,476 |                       __main__【  process_tick_adjustment_data】(  129)|      DEBUG | 读取 600000.SH 的原始tick数据
【P9XGPS】2025-08-04 22:22:30,481 | data.storage.vectorized_reader【...partitioned_data_vectorized】(  983)|      DEBUG | 便捷函数接收参数: 20250715145100 至 20250716093500 dividend_type=none
【P9XGPS】2025-08-04 22:22:30,487 | data.storage.vectorized_reader【                      __init__】(  117)|       INFO | Pandas向量化读取器初始化成功
【P9XGPS】2025-08-04 22:22:30,496 | data.storage.vectorized_reader【...partitioned_data_vectorized】(  333)|       INFO | 向量化读取器接收参数: 600000.SH tick 时间范围 20250715145100 至 20250716093500 复权类型 none
【P9XGPS】2025-08-04 22:22:30,505 | data.storage.vectorized_reader【...partitioned_data_vectorized】(  337)|      DEBUG | 向量化读取分区数据: 600000.SH tick 20250715145100-20250716093500 dividend_type=none
【P9XGPS】2025-08-04 22:22:30,511 | data.storage.vectorized_reader【...partitioned_data_vectorized】(  346)|      DEBUG | 向量化读取器使用智能文件选择: 600000.SH tick 20250715145100-20250716093500
【P9XGPS】2025-08-04 22:22:30,518 | data.storage.vectorized_reader【...partitioned_data_vectorized】(  356)|      DEBUG | 向量化读取器路径参数: data_type=raw, adj_type=None (来自dividend_type=none)
【P9XGPS】2025-08-04 22:22:30,525 |    data.storage.parquet_reader【   _get_target_partition_files】(  102)|      DEBUG | 使用智能文件选择: 600000.SH tick 时间范围 20250715145100 至 20250716093500
【P9XGPS】2025-08-04 22:22:30,532 |    data.storage.parquet_reader【   _get_target_partition_files】(  129)|      DEBUG | 解析时间范围: 20250715 至 20250716
【P9XGPS】2025-08-04 22:22:30,538 | ...anager.unified_path_manager【        build_partitioned_path】(  263)|      DEBUG | 构建分区路径: 600000.SH tick raw -> D:\data\raw\SH\600000\tick\2025\07\15.parquet
【P9XGPS】2025-08-04 22:22:30,545 |    data.storage.parquet_reader【   _get_target_partition_files】(  147)|      DEBUG | 找到目标文件: D:\data\raw\SH\600000\tick\2025\07\15.parquet
【P9XGPS】2025-08-04 22:22:30,552 | ...anager.unified_path_manager【        build_partitioned_path】(  263)|      DEBUG | 构建分区路径: 600000.SH tick raw -> D:\data\raw\SH\600000\tick\2025\07\16.parquet
【P9XGPS】2025-08-04 22:22:30,560 |    data.storage.parquet_reader【   _get_target_partition_files】(  147)|      DEBUG | 找到目标文件: D:\data\raw\SH\600000\tick\2025\07\16.parquet
【P9XGPS】2025-08-04 22:22:30,567 |    data.storage.parquet_reader【   _get_target_partition_files】(  156)|       INFO | 智能文件选择完成: 时间范围内找到 2 个目标文件
【P9XGPS】2025-08-04 22:22:30,578 |    data.storage.parquet_reader【   _get_target_partition_files】(  161)|       INFO | 性能优化: 时间跨度 2 天，精确选择 2 个文件
【P9XGPS】2025-08-04 22:22:30,588 | data.storage.vectorized_reader【...partitioned_data_vectorized】(  372)|      DEBUG | 找到 2 个分区文件
【P9XGPS】2025-08-04 22:22:30,595 | data.storage.vectorized_reader【         read_files_vectorized】(  178)|      DEBUG | 使用Pandas向量化读取 2 个文件
【P9XGPS】2025-08-04 22:22:30,666 | ...ata_processor.index_manager【         validate_index_format】(   98)|      DEBUG | 索引格式验证通过: 4994行数据，样本索引['20250715091502', '20250715091505', '20250715091508', '20250715091511', '20250715091514']
【P9XGPS】2025-08-04 22:22:30,676 | ...ata_processor.index_manager【                   safe_concat】(  190)|      DEBUG | 成功合并2个DataFrame，索引格式正确
【P9XGPS】2025-08-04 22:22:30,685 | data.storage.vectorized_reader【         read_files_vectorized】(  220)|      DEBUG | 数据索引已按时间排序
【P9XGPS】2025-08-04 22:22:30,741 | data.storage.vectorized_reader【         read_files_vectorized】(  225)|      DEBUG | Pandas向量化读取源数据成功: 9994 行数据，耗时 0.089998 秒，数据预览: 
                         time  lastPrice   open  high    low  lastClose  ...                             bidPrice                         askVol                      bidVol  settlementPrice  transactionNum   pe
20250715091502  1752542102000       0.00   0.00   0.0   0.00      14.11  ...          [14.99, 0.0, 0.0, 0.0, 0.0]              [301, 0, 0, 0, 0]           [301, 7, 0, 0, 0]              0.0               0  0.0
20250715091505  1752542105000       0.00   0.00   0.0   0.00      14.11  ...          [14.28, 0.0, 0.0, 0.0, 0.0]              [308, 0, 0, 0, 0]           [308, 0, 0, 0, 0]              0.0               0  0.0
20250715091508  1752542108000       0.00   0.00   0.0   0.00      14.11  ...          [14.26, 0.0, 0.0, 0.0, 0.0]              [319, 2, 0, 0, 0]           [319, 0, 0, 0, 0]              0.0               0  0.0
20250715091511  1752542111000       0.00   0.00   0.0   0.00      14.11  ...          [14.24, 0.0, 0.0, 0.0, 0.0]              [320, 0, 0, 0, 0]           [320, 0, 0, 0, 0]              0.0               0  0.0
20250715091514  1752542114000       0.00   0.00   0.0   0.00      14.11  ...          [14.16, 0.0, 0.0, 0.0, 0.0]             [330, 41, 0, 0, 0]           [330, 0, 0, 0, 0]              0.0               0  0.0
...                       ...        ...    ...   ...    ...        ...  ...                                  ...                            ...                         ...              ...             ...  ...
20250716145949  1752649189000      13.48  13.54  13.6  13.26      13.52  ...          [13.44, 0.0, 0.0, 0.0, 0.0]             [9648, 0, 0, 0, 0]         [9648, 64, 0, 0, 0]              0.0           74197  0.0
20250716145952  1752649192000      13.48  13.54  13.6  13.26      13.52  ...          [13.45, 0.0, 0.0, 0.0, 0.0]            [10483, 0, 0, 0, 0]       [10483, 363, 0, 0, 0]              0.0           74197  0.0
20250716145955  1752649195000      13.48  13.54  13.6  13.26      13.52  ...          [13.48, 0.0, 0.0, 0.0, 0.0]            [11011, 0, 0, 0, 0]       [11011, 239, 0, 0, 0]              0.0           74197  0.0
20250716145958  1752649198000      13.48  13.54  13.6  13.26      13.52  ...          [13.48, 0.0, 0.0, 0.0, 0.0]            [11197, 0, 0, 0, 0]       [11197, 529, 0, 0, 0]              0.0           74197  0.0
20250716150001  1752649201000      13.48  13.54  13.6  13.26      13.52  ...  [13.48, 13.47, 13.46, 13.45, 13.44]  [224, 1751, 2578, 1249, 1606]  [542, 405, 583, 1840, 647]              0.0           74643  0.0

[9994 rows x 20 columns]
【P9XGPS】2025-08-04 22:22:30,751 | data.storage.vectorized_reader【                       wrapper】(   70)|      DEBUG | 向量化数据读取: read_files_vectorized 处理 9994 条数据，耗时 0.156500 秒
【P9XGPS】2025-08-04 22:22:30,758 | data.storage.vectorized_reader【...partitioned_data_vectorized】(  383)|      DEBUG | 向量化读取原始数据: 9994 行
【P9XGPS】2025-08-04 22:22:30,763 | data.storage.vectorized_reader【...partitioned_data_vectorized】(  388)|      DEBUG | 开始时间过滤: 20250715145100 至 20250716093500
【P9XGPS】2025-08-04 22:22:30,775 | data.storage.vectorized_reader【...partitioned_data_vectorized】(  393)|      DEBUG | 时间过滤完成: 9994 -> 480 行
【P9XGPS】2025-08-04 22:22:30,782 | data.storage.vectorized_reader【...partitioned_data_vectorized】(  394)|       INFO | 向量化读取时间过滤: 从 9994 行过滤到 480 行
【P9XGPS】2025-08-04 22:22:30,792 | data.storage.vectorized_reader【...partitioned_data_vectorized】(  415)|      DEBUG | Pandas向量化读取完成: 480 行数据，dividend_type=none
【P9XGPS】2025-08-04 22:22:30,799 | data.storage.vectorized_reader【                       wrapper】(   70)|      DEBUG | 向量化数据读取: read_partitioned_data_vectorized 处理 480 条数据，耗时 0.302477 秒
【P9XGPS】2025-08-04 22:22:30,805 | ...ata_processor.index_monitor【    _validate_output_dataframe】(  116)|      DEBUG | 函数 read_partitioned_data_vectorized 输出索引格式正确
【P9XGPS】2025-08-04 22:22:30,810 |                       __main__【  process_tick_adjustment_data】(  148)|       INFO | 600000.SH 原始tick数据读取成功，数据行数: 480
【P9XGPS】2025-08-04 22:22:30,818 |                       __main__【  process_tick_adjustment_data】(  157)|      DEBUG | 开始 600000.SH 的复权计算，类型: front
【P9XGPS】2025-08-04 22:22:30,823 | ...ment.adjustment_synthesizer【      synthesize_adjusted_data】(   77)|       INFO | 开始合成股票 600000.SH 的复权数据，类型: front, 方法: ratio
【P9XGPS】2025-08-04 22:22:30,832 | ...ment.adjustment_synthesizer【      synthesize_adjusted_data】(   82)|      DEBUG | 股票 600000.SH 复权数据将使用VectorizedDataReader内置缓存
【P9XGPS】2025-08-04 22:22:30,838 |      data.storage.path_manager【                      __init__】(  110)|      DEBUG | 路径管理器初始化，数据根目录: D:\data
【P9XGPS】2025-08-04 22:22:30,846 | ...ent.dividend_factor_storage【         load_dividend_factors】(  155)|      DEBUG | 股票 600000.SH 的复权因子数据文件不存在: D:\data\raw\SH\600000\dividend_factors.parquet
【P9XGPS】2025-08-04 22:22:30,853 | ...ment.adjustment_synthesizer【         _get_dividend_factors】(  155)|       INFO | 本地无股票 600000.SH 复权因子数据，尝试从xtquant获取
【P9XGPS】2025-08-04 22:22:30,861 |      data.storage.path_manager【                      __init__】(  110)|      DEBUG | 路径管理器初始化，数据根目录: D:\data
【P9XGPS】2025-08-04 22:22:30,869 | ...ent.dividend_factor_storage【         load_dividend_factors】(  155)|      DEBUG | 股票 600000.SH 的复权因子数据文件不存在: D:\data\raw\SH\600000\dividend_factors.parquet
【P9XGPS】2025-08-04 22:22:30,875 | ...ent.dividend_factor_storage【       update_dividend_factors】(  200)|       INFO | 本地无股票 600000.SH 复权因子数据，获取全部数据
【P9XGPS】2025-08-04 22:22:31,741 | ...ent.dividend_factor_storage【...vidend_factors_from_xtquant】(   96)|      DEBUG | 从xtquant获取复权因子: 600000.SH, 时间范围: 20250715145100 - 20250716093500
【P9XGPS】2025-08-04 22:22:31,756 | ...ent.dividend_factor_storage【...vidend_factors_from_xtquant】(  109)|       INFO | 成功获取股票 600000.SH 的复权因子数据，共 1 条记录
【P9XGPS】2025-08-04 22:22:31,767 |      data.storage.path_manager【                      __init__】(  110)|      DEBUG | 路径管理器初始化，数据根目录: D:\data
【P9XGPS】2025-08-04 22:22:31,780 | ...ent.dividend_factor_storage【         save_dividend_factors】(  135)|       INFO | 成功保存股票 600000.SH 的复权因子数据到: D:\data\raw\SH\600000\dividend_factors.parquet
【P9XGPS】2025-08-04 22:22:31,791 |      data.storage.path_manager【                      __init__】(  110)|      DEBUG | 路径管理器初始化，数据根目录: D:\data
【P9XGPS】2025-08-04 22:22:31,803 | ...ent.dividend_factor_storage【         load_dividend_factors】(  159)|      DEBUG | 成功加载股票 600000.SH 的复权因子数据，共 1 条记录
【P9XGPS】2025-08-04 22:22:31,811 | ...ent.dividend_factor_storage【        query_dividend_factors】(  296)|      DEBUG | 查询股票 600000.SH 复权因子数据，返回 1 条记录
【P9XGPS】2025-08-04 22:22:31,817 | ...ment.adjustment_synthesizer【 _calculate_forward_adjustment】(  204)|      DEBUG | 复权因子数据索引转换完成: <class 'pandas.core.indexes.datetimes.DatetimeIndex'> -> 1 条记录
【P9XGPS】2025-08-04 22:22:31,823 | ...ment.adjustment_synthesizer【 _calculate_forward_adjustment】(  218)|      DEBUG | 复权因子数据索引已转换为字符串格式以匹配价格数据
【P9XGPS】2025-08-04 22:22:31,829 | ...ment.adjustment_synthesizer【 _calculate_forward_adjustment】(  230)|      DEBUG | 索引类型统一完成 - 价格数据: <class 'pandas.core.indexes.base.Index'>, 复权因子: <class 'pandas.core.indexes.base.Index'>
【P9XGPS】2025-08-04 22:22:31,835 | ...t.forward_adjustment_engine【         process_forward_ratio】(  160)|      DEBUG | 开始等比前复权计算，价格数据: 480 行，复权因子: 1 行
【P9XGPS】2025-08-04 22:22:31,841 | ...t.forward_adjustment_engine【               gen_divid_ratio】(   67)|      DEBUG | 索引类型检查 - 价格数据: <class 'str'>, 复权因子: <class 'str'>
【P9XGPS】2025-08-04 22:22:31,884 | ...t.forward_adjustment_engine【               gen_divid_ratio】(  127)|      DEBUG | 生成复权比例数据完成，数据量: 480
【P9XGPS】2025-08-04 22:22:31,892 | ...t.forward_adjustment_engine【         process_forward_ratio】(  176)|      DEBUG | 原始time列信息: 类型=int64, 样本值=1752562262000
【P9XGPS】2025-08-04 22:22:31,903 | ...tment.field_type_classifier【     classify_dataframe_fields】(  264)|      DEBUG | 执行字段分类，共 20 个字段
【P9XGPS】2025-08-04 22:22:31,911 | ...tment.field_type_classifier【     classify_dataframe_fields】(  266)|      DEBUG | 字段 'time' 分类为: time
【P9XGPS】2025-08-04 22:22:31,920 | ...tment.field_type_classifier【     classify_dataframe_fields】(  266)|      DEBUG | 字段 'lastPrice' 分类为: price
【P9XGPS】2025-08-04 22:22:31,928 | ...tment.field_type_classifier【     classify_dataframe_fields】(  266)|      DEBUG | 字段 'open' 分类为: price
【P9XGPS】2025-08-04 22:22:31,936 | ...tment.field_type_classifier【     classify_dataframe_fields】(  266)|      DEBUG | 字段 'high' 分类为: price
【P9XGPS】2025-08-04 22:22:31,945 | ...tment.field_type_classifier【     classify_dataframe_fields】(  266)|      DEBUG | 字段 'low' 分类为: price
【P9XGPS】2025-08-04 22:22:31,953 | ...tment.field_type_classifier【     classify_dataframe_fields】(  266)|      DEBUG | 字段 'lastClose' 分类为: price
【P9XGPS】2025-08-04 22:22:31,961 | ...tment.field_type_classifier【     classify_dataframe_fields】(  266)|      DEBUG | 字段 'amount' 分类为: volume
【P9XGPS】2025-08-04 22:22:31,969 | ...tment.field_type_classifier【     classify_dataframe_fields】(  266)|      DEBUG | 字段 'volume' 分类为: volume
【P9XGPS】2025-08-04 22:22:31,976 | ...tment.field_type_classifier【     classify_dataframe_fields】(  266)|      DEBUG | 字段 'pvolume' 分类为: volume
【P9XGPS】2025-08-04 22:22:31,983 | ...tment.field_type_classifier【     classify_dataframe_fields】(  266)|      DEBUG | 字段 'tickvol' 分类为: volume
【P9XGPS】2025-08-04 22:22:31,990 | ...tment.field_type_classifier【     classify_dataframe_fields】(  266)|      DEBUG | 字段 'stockStatus' 分类为: ratio
【P9XGPS】2025-08-04 22:22:31,996 | ...tment.field_type_classifier【     classify_dataframe_fields】(  266)|      DEBUG | 字段 'openInt' 分类为: price
【P9XGPS】2025-08-04 22:22:32,003 | ...tment.field_type_classifier【     classify_dataframe_fields】(  266)|      DEBUG | 字段 'lastSettlementPrice' 分类为: price
【P9XGPS】2025-08-04 22:22:32,010 | ...tment.field_type_classifier【     classify_dataframe_fields】(  266)|      DEBUG | 字段 'askPrice' 分类为: price
【P9XGPS】2025-08-04 22:22:32,017 | ...tment.field_type_classifier【     classify_dataframe_fields】(  266)|      DEBUG | 字段 'bidPrice' 分类为: price
【P9XGPS】2025-08-04 22:22:32,023 | ...tment.field_type_classifier【     classify_dataframe_fields】(  266)|      DEBUG | 字段 'askVol' 分类为: volume
【P9XGPS】2025-08-04 22:22:32,029 | ...tment.field_type_classifier【     classify_dataframe_fields】(  266)|      DEBUG | 字段 'bidVol' 分类为: volume
【P9XGPS】2025-08-04 22:22:32,036 | ...tment.field_type_classifier【     classify_dataframe_fields】(  266)|      DEBUG | 字段 'settlementPrice' 分类为: price
【P9XGPS】2025-08-04 22:22:32,043 | ...tment.field_type_classifier【     classify_dataframe_fields】(  266)|      DEBUG | 字段 'transactionNum' 分类为: count
【P9XGPS】2025-08-04 22:22:32,050 | ...tment.field_type_classifier【     classify_dataframe_fields】(  266)|      DEBUG | 字段 'pe' 分类为: ratio
【P9XGPS】2025-08-04 22:22:32,057 | ...tment.field_type_classifier【     classify_dataframe_fields】(  275)|       INFO | 字段分类完成，共 20 个字段:
【P9XGPS】2025-08-04 22:22:32,068 | ...tment.field_type_classifier【     classify_dataframe_fields】(  277)|       INFO |   time: 1 个
【P9XGPS】2025-08-04 22:22:32,077 | ...tment.field_type_classifier【     classify_dataframe_fields】(  277)|       INFO |   price: 10 个
【P9XGPS】2025-08-04 22:22:32,088 | ...tment.field_type_classifier【     classify_dataframe_fields】(  277)|       INFO |   volume: 6 个
【P9XGPS】2025-08-04 22:22:32,098 | ...tment.field_type_classifier【     classify_dataframe_fields】(  277)|       INFO |   ratio: 2 个
【P9XGPS】2025-08-04 22:22:32,108 | ...tment.field_type_classifier【     classify_dataframe_fields】(  277)|       INFO |   count: 1 个
【P9XGPS】2025-08-04 22:22:32,118 | ...t.forward_adjustment_engine【         process_forward_ratio】(  185)|       INFO | 需要复权的字段: ['lastPrice', 'open', 'high', 'low', 'lastClose', 'openInt', 'lastSettlementPrice', 'askPrice', 'bidPrice', 'settlementPrice']
【P9XGPS】2025-08-04 22:22:32,127 | ...t.forward_adjustment_engine【         process_forward_ratio】(  195)|      DEBUG | 字段分类验证通过
【P9XGPS】2025-08-04 22:22:32,134 | ...t.forward_adjustment_engine【         process_forward_ratio】(  199)|      DEBUG | 开始对 10 个价格字段进行复权计算
【P9XGPS】2025-08-04 22:22:32,141 | ...t.forward_adjustment_engine【         process_forward_ratio】(  200)|      DEBUG | 复权比例样本: {'lastPrice': 0.9709426007862693, 'open': 0.9709426007862693, 'high': 0.9709426007862693, 'low': 0.9709426007862693, 'lastClose': 0.9709426007862693, 'openInt': 0.9709426007862693, 'lastSettlementPrice': 0.9709426007862693, 'askPrice': 0.9709426007862693, 'bidPrice': 0.9709426007862693, 'settlementPrice': 0.9709426007862693}
【P9XGPS】2025-08-04 22:22:32,152 | ...t.forward_adjustment_engine【         process_forward_ratio】(  209)|      DEBUG | 处理数组字段: askPrice
【P9XGPS】2025-08-04 22:22:32,235 | ...t.forward_adjustment_engine【         process_forward_ratio】(  209)|      DEBUG | 处理数组字段: bidPrice
【P9XGPS】2025-08-04 22:22:32,321 | ...t.forward_adjustment_engine【         process_forward_ratio】(  215)|       INFO | 复权计算完成，调整了 10 个价格字段
【P9XGPS】2025-08-04 22:22:32,332 | ...t.forward_adjustment_engine【         process_forward_ratio】(  223)|      DEBUG | 复权后time列信息: 类型=int64, 样本值=1752562262000
【P9XGPS】2025-08-04 22:22:32,339 | ...t.forward_adjustment_engine【         process_forward_ratio】(  224)|      DEBUG | time列是否保持不变: True
【P9XGPS】2025-08-04 22:22:32,345 | ...t.forward_adjustment_engine【         process_forward_ratio】(  231)|      DEBUG | 复权计算完成，time列保持原始格式
【P9XGPS】2025-08-04 22:22:32,353 | ...t.forward_adjustment_engine【         process_forward_ratio】(  238)|      DEBUG | 非价格字段 time 保持不变
【P9XGPS】2025-08-04 22:22:32,360 | ...t.forward_adjustment_engine【         process_forward_ratio】(  238)|      DEBUG | 非价格字段 amount 保持不变
【P9XGPS】2025-08-04 22:22:32,367 | ...t.forward_adjustment_engine【         process_forward_ratio】(  238)|      DEBUG | 非价格字段 volume 保持不变
【P9XGPS】2025-08-04 22:22:32,374 | ...t.forward_adjustment_engine【         process_forward_ratio】(  238)|      DEBUG | 非价格字段 pvolume 保持不变
【P9XGPS】2025-08-04 22:22:32,381 | ...t.forward_adjustment_engine【         process_forward_ratio】(  238)|      DEBUG | 非价格字段 tickvol 保持不变
【P9XGPS】2025-08-04 22:22:32,389 | ...t.forward_adjustment_engine【         process_forward_ratio】(  238)|      DEBUG | 非价格字段 stockStatus 保持不变
【P9XGPS】2025-08-04 22:22:32,400 | ...t.forward_adjustment_engine【         process_forward_ratio】(  238)|      DEBUG | 非价格字段 askVol 保持不变
【P9XGPS】2025-08-04 22:22:32,412 | ...t.forward_adjustment_engine【         process_forward_ratio】(  238)|      DEBUG | 非价格字段 bidVol 保持不变
【P9XGPS】2025-08-04 22:22:32,420 | ...t.forward_adjustment_engine【         process_forward_ratio】(  238)|      DEBUG | 非价格字段 transactionNum 保持不变
【P9XGPS】2025-08-04 22:22:32,428 | ...t.forward_adjustment_engine【         process_forward_ratio】(  238)|      DEBUG | 非价格字段 pe 保持不变
【P9XGPS】2025-08-04 22:22:32,437 | ...stment.data_quality_monitor【                  clear_issues】(   61)|      DEBUG | 数据质量问题记录已清空
【P9XGPS】2025-08-04 22:22:32,447 | ...stment.data_quality_monitor【    monitor_adjustment_quality】(   94)|       INFO | 开始监控复权数据质量，原始数据: 480 行，复权后: 480 行
【P9XGPS】2025-08-04 22:22:32,460 | ...tment.field_type_classifier【     classify_dataframe_fields】(  250)|      DEBUG | 字段分类缓存机制已启用
【P9XGPS】2025-08-04 22:22:32,486 | ...stment.data_quality_monitor【                     add_issue】(   78)|       INFO | [INFO] lastSettlementPrice: 结算价字段未发生复权调整，这在期货数据中是正常的
【P9XGPS】2025-08-04 22:22:32,521 | ...stment.data_quality_monitor【                     add_issue】(   78)|       INFO | [INFO] settlementPrice: 结算价字段未发生复权调整，这在期货数据中是正常的
【P9XGPS】2025-08-04 22:22:32,533 | ...stment.data_quality_monitor【                     add_issue】(   78)|       INFO | [INFO] openInt: 数据类型发生变化: int32 -> float64
【P9XGPS】2025-08-04 22:22:32,548 | ...stment.data_quality_monitor【      _summarize_quality_check】(  399)|       INFO | 复权数据质量检查完成，发现 3 个问题:
【P9XGPS】2025-08-04 22:22:32,562 | ...stment.data_quality_monitor【      _summarize_quality_check】(  401)|       INFO |   info: 3 个
【P9XGPS】2025-08-04 22:22:32,576 | ...t.forward_adjustment_engine【         process_forward_ratio】(  248)|       INFO | 复权数据质量检查通过
【P9XGPS】2025-08-04 22:22:32,588 | ...t.forward_adjustment_engine【         process_forward_ratio】(  253)|       INFO | 等比前复权计算完成，处理数据量: 480
【P9XGPS】2025-08-04 22:22:32,601 | ...ment.adjustment_synthesizer【      synthesize_adjusted_data】(  120)|      DEBUG | 股票 600000.SH 复权数据将由VectorizedDataReader自动缓存
【P9XGPS】2025-08-04 22:22:32,608 | ...ment.adjustment_synthesizer【      synthesize_adjusted_data】(  122)|       INFO | 股票 600000.SH 复权数据合成成功
【P9XGPS】2025-08-04 22:22:32,618 |                       __main__【  process_tick_adjustment_data】(  172)|       INFO | 600000.SH 复权计算成功，数据行数: 480
【P9XGPS】2025-08-04 22:22:32,626 |                       __main__【  process_tick_adjustment_data】(  176)|      DEBUG | 保存 600000.SH 的复权数据到存储系统
【P9XGPS】2025-08-04 22:22:32,631 |   data.storage.parquet_storage【             save_to_partition】(  233)|      DEBUG | 开始保存 600000.SH 的 tick 数据到分区 (480 行)
【P9XGPS】2025-08-04 22:22:32,637 |   data.storage.parquet_storage【             save_to_partition】(  253)|      DEBUG | 数据时间戳范围: 1752562262000 至 1752629698000
【P9XGPS】2025-08-04 22:22:32,643 | ...anager.unified_path_manager【        build_partitioned_path】(  263)|      DEBUG | 构建分区路径: 600000.SH tick adjusted -> D:\data\adjusted\front\SH\600000\tick\2025\08\04.parquet
【P9XGPS】2025-08-04 22:22:32,651 |   data.storage.parquet_storage【             save_to_partition】(  310)|      DEBUG | 文件不存在，直接保存: D:\data\adjusted\front\SH\600000\tick\2025\08\04.parquet
【P9XGPS】2025-08-04 22:22:32,659 |   data.storage.parquet_storage【...mize_numeric_table_creation】(  762)|      DEBUG | 索引格式正确，保持原有格式
【P9XGPS】2025-08-04 22:22:32,667 |   data.storage.parquet_storage【...mize_numeric_table_creation】(  778)|      DEBUG | time列保持数值格式: int64
【P9XGPS】2025-08-04 22:22:32,689 |   data.storage.parquet_storage【          save_data_to_parquet】(  137)|      DEBUG | 数据成功保存到: D:\data\adjusted\front\SH\600000\tick\2025\08\04.parquet
【P9XGPS】2025-08-04 22:22:32,695 |   data.storage.parquet_storage【             save_to_partition】(  331)|      DEBUG | 已保存 600000.SH 的 tick 数据: D:\data\adjusted\front\SH\600000\tick\2025\08\04.parquet (480 行, 36.5 KB)

【P9XGPS】2025-08-04 22:22:32,702 |                       __main__【  process_tick_adjustment_data】(  187)|       INFO | 600000.SH 复权数据保存成功

**日志内容
- **D:\quant\data\批量处理tick复权数据.py
**这个脚本只能合成tick数据，我需要支持全部周期，包括期货连续合约的数据
- 复权数据保存成功后怎么没显示保存数据？我需要了解保存的数据是否有没有问题
- 阅读 D:\quant\项目常见问题.md
- 先查看项目中是否有问题相关实现，避免重复造轮子
- 遵循DRY原则
- 遵循**核心指导思维**原则
- 研究