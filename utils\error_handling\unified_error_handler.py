#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一错误处理系统 - 标准化错误处理和报告机制

该模块提供统一的错误处理、分类、报告和恢复机制，确保系统错误信息的一致性和可操作性。

作者: Augment Agent
创建时间: 2025-07-31
版本: 1.0.0
"""

import traceback
from typing import Dict, List, Optional, Any, Callable
from enum import Enum
from dataclasses import dataclass
import datetime

from utils.logger import get_unified_logger, LogTarget

class ErrorCategory(Enum):
    """错误分类"""
    DATA_TYPE = "data_type"           # 数据类型错误
    DATA_VALIDATION = "data_validation"  # 数据验证错误
    FILE_IO = "file_io"              # 文件读写错误
    NETWORK = "network"              # 网络错误
    CONFIGURATION = "configuration"  # 配置错误
    BUSINESS_LOGIC = "business_logic"  # 业务逻辑错误
    SYSTEM = "system"                # 系统错误
    UNKNOWN = "unknown"              # 未知错误

class ErrorSeverity(Enum):
    """错误严重程度"""
    CRITICAL = "critical"  # 严重错误，系统无法继续
    ERROR = "error"        # 错误，功能无法正常执行
    WARNING = "warning"    # 警告，功能可以执行但有问题
    INFO = "info"          # 信息，仅供参考

@dataclass
class ErrorContext:
    """错误上下文信息"""
    function_name: str
    module_name: str
    operation: str
    parameters: Dict[str, Any]
    timestamp: datetime.datetime
    
class ErrorCode(Enum):
    """错误代码"""
    # 数据类型相关错误 (DT_xxx)
    DT_TYPE_MISMATCH = "DT_001"           # 数据类型不匹配
    DT_STRING_TIMESTAMP = "DT_002"        # 字符串时间戳问题
    DT_CONVERSION_FAILED = "DT_003"       # 类型转换失败
    DT_INCONSISTENT_TYPES = "DT_004"      # 类型不一致
    
    # 数据验证相关错误 (DV_xxx)
    DV_VALIDATION_FAILED = "DV_001"       # 验证失败
    DV_INVALID_FORMAT = "DV_002"          # 格式无效
    DV_OUT_OF_RANGE = "DV_003"           # 数值超出范围
    DV_MISSING_REQUIRED = "DV_004"       # 缺少必需字段
    
    # 文件IO相关错误 (FIO_xxx)
    FIO_FILE_NOT_FOUND = "FIO_001"       # 文件未找到
    FIO_READ_FAILED = "FIO_002"          # 读取失败
    FIO_WRITE_FAILED = "FIO_003"         # 写入失败
    FIO_PERMISSION_DENIED = "FIO_004"    # 权限拒绝

@dataclass
class ErrorInfo:
    """错误信息"""
    code: ErrorCode
    category: ErrorCategory
    severity: ErrorSeverity
    message: str
    details: str
    context: ErrorContext
    suggestions: List[str]
    recovery_actions: List[str]
    
class UnifiedErrorHandler:
    """统一错误处理器"""
    
    def __init__(self):
        self.error_history = []
        self.error_handlers = {}  # 自定义错误处理器
        self.logger = get_unified_logger(__name__, enhanced=True)
        self.recovery_strategies = {}  # 恢复策略
        
    def register_error_handler(self, error_code: ErrorCode, 
                             handler: Callable[[ErrorInfo], Any]):
        """注册自定义错误处理器"""
        self.error_handlers[error_code] = handler
        
    def register_recovery_strategy(self, error_code: ErrorCode,
                                 strategy: Callable[[ErrorInfo], bool]):
        """注册错误恢复策略"""
        self.recovery_strategies[error_code] = strategy
        
    def handle_error(self, error_code: ErrorCode, message: str,
                    context: ErrorContext, details: str = "",
                    suggestions: List[str] = None,
                    recovery_actions: List[str] = None) -> ErrorInfo:
        """
        处理错误
        
        Args:
            error_code: 错误代码
            message: 错误消息
            context: 错误上下文
            details: 详细信息
            suggestions: 修复建议
            recovery_actions: 恢复操作
            
        Returns:
            ErrorInfo: 错误信息对象
        """
        # 确定错误分类和严重程度
        category = self._get_error_category(error_code)
        severity = self._get_error_severity(error_code)
        
        # 创建错误信息
        error_info = ErrorInfo(
            code=error_code,
            category=category,
            severity=severity,
            message=message,
            details=details,
            context=context,
            suggestions=suggestions or [],
            recovery_actions=recovery_actions or []
        )
        
        # 添加默认建议和恢复操作
        self._add_default_suggestions(error_info)
        
        # 记录错误
        self._log_error(error_info)
        
        # 保存到历史记录
        self.error_history.append(error_info)
        
        # 尝试自动恢复
        if error_code in self.recovery_strategies:
            try:
                recovery_success = self.recovery_strategies[error_code](error_info)
                if recovery_success:
                    self.logger.info(LogTarget.FILE, f"错误 {error_code.value} 自动恢复成功")
            except Exception as e:
                self.logger.warning(LogTarget.FILE, f"错误恢复失败: {e}")
                
        # 调用自定义处理器
        if error_code in self.error_handlers:
            try:
                self.error_handlers[error_code](error_info)
            except Exception as e:
                self.logger.warning(LogTarget.FILE, f"自定义错误处理器执行失败: {e}")
                
        return error_info
        
    def _get_error_category(self, error_code: ErrorCode) -> ErrorCategory:
        """根据错误代码确定错误分类"""
        code_str = error_code.value
        
        if code_str.startswith("DT_"):
            return ErrorCategory.DATA_TYPE
        elif code_str.startswith("DV_"):
            return ErrorCategory.DATA_VALIDATION
        elif code_str.startswith("FIO_"):
            return ErrorCategory.FILE_IO
        else:
            return ErrorCategory.UNKNOWN
            
    def _get_error_severity(self, error_code: ErrorCode) -> ErrorSeverity:
        """根据错误代码确定严重程度"""
        # 根据错误代码的特征确定严重程度
        critical_codes = [
            ErrorCode.FIO_PERMISSION_DENIED,
            ErrorCode.DT_CONVERSION_FAILED
        ]
        
        warning_codes = [
            ErrorCode.DT_STRING_TIMESTAMP,
            ErrorCode.DV_OUT_OF_RANGE
        ]
        
        if error_code in critical_codes:
            return ErrorSeverity.CRITICAL
        elif error_code in warning_codes:
            return ErrorSeverity.WARNING
        else:
            return ErrorSeverity.ERROR
            
    def _add_default_suggestions(self, error_info: ErrorInfo):
        """添加默认的修复建议"""
        if error_info.code == ErrorCode.DT_STRING_TIMESTAMP:
            error_info.suggestions.extend([
                "使用pd.to_numeric()将字符串时间戳转换为数值类型",
                "检查数据源是否正确设置了数据类型",
                "使用DataTypeManager.ensure_type_consistency()确保类型一致性"
            ])
            error_info.recovery_actions.extend([
                "自动转换字符串时间戳为数值类型",
                "记录转换过程以便审查"
            ])
            
        elif error_info.code == ErrorCode.DT_TYPE_MISMATCH:
            error_info.suggestions.extend([
                "检查数据合并前的类型一致性",
                "使用IndexManager.safe_concat()进行安全合并",
                "避免直接使用pd.concat()"
            ])
            
        elif error_info.code == ErrorCode.DV_VALIDATION_FAILED:
            error_info.suggestions.extend([
                "检查输入数据的格式和范围",
                "使用SmartValidationSystem进行详细验证",
                "查看验证失败的具体原因"
            ])
            
    def _log_error(self, error_info: ErrorInfo):
        """记录错误到日志"""
        log_message = (
            f"[{error_info.code.value}] {error_info.message}\n"
            f"模块: {error_info.context.module_name}\n"
            f"函数: {error_info.context.function_name}\n"
            f"操作: {error_info.context.operation}"
        )
        
        if error_info.details:
            log_message += f"\n详细信息: {error_info.details}"
            
        if error_info.suggestions:
            log_message += f"\n修复建议: {'; '.join(error_info.suggestions)}"
            
        # 根据严重程度选择日志级别
        if error_info.severity == ErrorSeverity.CRITICAL:
            self.logger.critical(LogTarget.FILE, log_message)
        elif error_info.severity == ErrorSeverity.ERROR:
            self.logger.error(LogTarget.FILE, log_message)
        elif error_info.severity == ErrorSeverity.WARNING:
            self.logger.warning(LogTarget.FILE, log_message)
        else:
            self.logger.info(LogTarget.FILE, log_message)
            
    def create_context(self, function_name: str, module_name: str,
                      operation: str, **parameters) -> ErrorContext:
        """创建错误上下文"""
        return ErrorContext(
            function_name=function_name,
            module_name=module_name,
            operation=operation,
            parameters=parameters,
            timestamp=datetime.datetime.now()
        )
        
    def get_error_summary(self, hours: int = 24) -> Dict[str, Any]:
        """获取错误摘要"""
        cutoff_time = datetime.datetime.now() - datetime.timedelta(hours=hours)
        recent_errors = [
            error for error in self.error_history
            if error.context.timestamp > cutoff_time
        ]
        
        # 按类别统计
        category_counts = {}
        severity_counts = {}
        
        for error in recent_errors:
            category = error.category.value
            severity = error.severity.value
            
            category_counts[category] = category_counts.get(category, 0) + 1
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
            
        return {
            "total_errors": len(recent_errors),
            "time_range_hours": hours,
            "category_breakdown": category_counts,
            "severity_breakdown": severity_counts,
            "most_recent_errors": [
                {
                    "code": error.code.value,
                    "message": error.message,
                    "timestamp": error.context.timestamp.isoformat()
                }
                for error in recent_errors[-5:]  # 最近5个错误
            ]
        }
        
    def clear_error_history(self, older_than_days: int = 7):
        """清理错误历史记录"""
        cutoff_time = datetime.datetime.now() - datetime.timedelta(days=older_than_days)
        self.error_history = [
            error for error in self.error_history
            if error.context.timestamp > cutoff_time
        ]
        
# 全局错误处理器实例
error_handler = UnifiedErrorHandler()
