# 文件管理规范指南

## 🎯 目标
建立清晰的文件分类和存放规范，区分核心文件和临时文件，确保项目结构清晰、易于维护。

## 📁 文件分类体系

### 1. **核心业务文件** (永久保留)
- **定义**: 生产环境使用的核心功能代码
- **位置**: 对应的功能模块目录
- **示例**: `data/`, `utils/`, `backtest/`, `strategy/`
- **命名**: 功能描述性命名
- **生命周期**: 永久保留，长期维护

### 2. **配置文件** (永久保留)
- **定义**: 系统配置和设置文件
- **位置**: `config/` 目录
- **示例**: `settings.py`, `symbols.py`
- **命名**: `config_*` 或 `settings_*`
- **生命周期**: 永久保留

### 3. **测试文件** (长期保留)
- **定义**: 正式的单元测试和性能测试
- **位置**: `tests/` 目录根级
- **示例**: `test_*.py`, `benchmark_*.py`
- **命名**: `test_*` 或 `benchmark_*`
- **生命周期**: 长期保留

### 4. **临时测试文件** (可删除)
- **定义**: 开发过程中的临时验证脚本
- **位置**: `tests/temp/` 目录
- **示例**: `temp_test_*.py`, `debug_*.py`
- **命名**: `temp_*`, `debug_*`
- **生命周期**: 任务完成后删除

### 5. **开发工具** (任务完成后删除)
- **定义**: 开发过程中使用的辅助工具
- **位置**: `tests/temp/` 目录
- **示例**: `*_fixer.py`, `*_checker.py`, `*_tool.py`
- **命名**: `*_fixer`, `*_checker`, `*_tool`
- **生命周期**: 任务完成后删除

### 6. **修复脚本** (任务完成后删除)
- **定义**: 一次性问题修复脚本
- **位置**: `tests/temp/` 目录
- **示例**: `fix_*.py`, `repair_*.py`
- **命名**: `fix_*`, `repair_*`
- **生命周期**: 修复完成后删除

### 7. **演示脚本** (任务完成后删除)
- **定义**: 功能演示和验证脚本
- **位置**: `tests/temp/` 目录
- **示例**: `demo_*.py`, `show_*.py`
- **命名**: `demo_*`, `show_*`
- **生命周期**: 演示完成后删除

### 8. **示例代码** (长期保留)
- **定义**: 用户使用参考的示例代码
- **位置**: `examples/` 目录
- **示例**: `basic_usage.py`, `advanced_features.py`
- **命名**: 描述性命名
- **生命周期**: 长期保留

### 9. **文档文件** (长期保留)
- **定义**: 正式的技术文档
- **位置**: `docs/` 目录根级
- **示例**: `README.md`, `API.md`
- **命名**: 描述性命名
- **生命周期**: 长期保留

### 10. **临时文档** (任务完成后删除)
- **定义**: 开发过程中的临时文档
- **位置**: `docs/temp/` 目录
- **示例**: `*_plan.md`, `temp_*.md`
- **命名**: `*_plan`, `temp_*`
- **生命周期**: 任务完成后删除

## 🗂️ 目录结构规范

```
project/
├── 📂 data/                    # 数据处理核心模块
│   ├── *.py                    # 核心业务代码
│   └── README.md               # 模块文档
│
├── 📂 utils/                   # 工具函数库
│   ├── *.py                    # 工具函数
│   └── */                      # 子模块
│
├── 📂 tests/                   # 测试目录
│   ├── test_*.py               # 单元测试（保留）
│   ├── benchmark_*.py          # 性能测试（保留）
│   └── temp/                   # 临时测试目录
│       ├── temp_test_*.py      # 临时测试
│       ├── fix_*.py            # 修复脚本
│       ├── demo_*.py           # 演示脚本
│       ├── *_tool.py           # 开发工具
│       └── debug_*.py          # 调试脚本
│
├── 📂 examples/                # 示例目录
│   ├── basic_*.py              # 基础示例
│   └── advanced_*.py           # 高级示例
│
├── 📂 docs/                    # 文档目录
│   ├── *.md                    # 正式文档
│   └── temp/                   # 临时文档目录
│       ├── *_plan.md           # 计划文档
│       └── temp_*.md           # 临时文档
│
└── 📂 config/                  # 配置目录
    ├── settings.py             # 系统设置
    └── *.py                    # 其他配置
```

## 🧹 清理规则

### 自动清理 (通过 .gitignore)
```gitignore
# 临时目录
tests/temp/
docs/temp/

# 备份文件
*.backup*
*.bak

# 临时文件
temp_*.py
temp_*.md
*_plan.md

# 工具文件
*_fixer.py
*_checker.py
*_tool.py

# 修复文件
fix_*.py
repair_*.py

# 演示文件
demo_*.py
show_*.py

# 报告文件
*_fix_report.txt
*_report.txt
```

### 手动清理时机
1. **任务完成后**: 删除相关的临时文件和工具脚本
2. **版本发布前**: 清理所有临时文件和文档
3. **定期维护**: 每月检查并清理过期的临时文件

## ⚠️ 注意事项

### 删除前确认
- 确保功能已经集成到核心代码中
- 确保不再需要该工具或脚本
- 备份重要的临时代码片段到文档中

### 保留原则
- 有教育价值的演示脚本可以移到 `examples/`
- 可能重复使用的工具可以重构为正式工具函数
- 重要的临时文档可以整理为正式文档

## 🎯 最佳实践

### 开发时
1. 临时文件直接创建在 `tests/temp/` 或 `docs/temp/`
2. 使用规范的命名前缀
3. 在文件头部注释说明用途和生命周期

### 完成时
1. 将有价值的代码集成到核心模块
2. 将有价值的文档整理为正式文档
3. 删除不再需要的临时文件

### 示例注释
```python
#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
临时修复脚本 - 修复时间处理问题

用途: 一次性修复项目中的pd.to_datetime问题
生命周期: 修复完成后删除
创建时间: 2025-07-21
预期删除: 2025-07-21 (修复完成后)
"""
```

通过这个规范，确保项目结构清晰，核心代码和临时文件分离明确！
