#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一索引处理管理器
提供项目级别的数据索引处理标准和工具
"""

import pandas as pd
from typing import Optional, Union, List, Any
import logging
from datetime import datetime
import time

# 配置日志
logger = logging.getLogger(__name__)

# 日志频率控制
_last_validation_log_time = 0
_validation_log_interval = 5  # 5秒内不重复输出相同的验证日志

class IndexManager:
    """
    统一索引处理管理器

    负责：
    1. 支持多种时间索引格式：
       - 8位日期格式(YYYYMMDD)：适用于日线数据
       - 14位时间戳格式(YYYYMMDDHHMMSS)：适用于分钟和tick数据
    2. 提供智能索引格式验证和转换机制
    3. 确保所有数据操作保持索引格式一致性
    4. 提供索引完整性检查工具
    5. 支持DatetimeIndex格式的自动识别
    """
    
    # 支持的索引格式
    DATE_INDEX_FORMAT = "YYYYMMDD"           # 8位日期格式，适用于日线数据
    TIMESTAMP_INDEX_FORMAT = "YYYYMMDDHHMMSS" # 14位时间戳格式，适用于分钟和tick数据
    DATE_INDEX_PATTERN = r'^\d{8}$'          # 8位数字格式
    TIMESTAMP_INDEX_PATTERN = r'^\d{14}$'    # 14位数字格式
    
    @classmethod
    def validate_index_format(cls, df: pd.DataFrame) -> bool:
        """
        验证DataFrame的索引是否符合标准时间戳格式
        支持两种格式：
        - 8位日期格式(YYYYMMDD)：适用于日线数据
        - 14位时间戳格式(YYYYMMDDHHMMSS)：适用于分钟和tick数据

        Args:
            df: 要验证的DataFrame

        Returns:
            bool: True表示索引格式正确，False表示格式错误
        """
        if df is None or df.empty:
            logger.warning("DataFrame为空，无法验证索引格式")
            return False

        # 检查索引类型
        if isinstance(df.index, pd.DatetimeIndex):
            logger.debug("检测到DatetimeIndex，索引格式正确")
            return True

        # 检查是否为时间戳字符串格式
        if df.index.dtype == 'object':
            # 检查前几个索引值是否符合支持的格式
            sample_indices = df.index[:min(5, len(df.index))]
            for idx in sample_indices:
                if not isinstance(idx, str):
                    logger.warning(f"索引格式不正确: {idx}，期望字符串格式")
                    return False

                idx_str = str(idx)
                idx_len = len(idx_str)

                # 支持8位日期格式(YYYYMMDD)和14位时间戳格式(YYYYMMDDHHMMSS)
                if idx_len == 8:
                    # 8位日期格式验证
                    try:
                        datetime.strptime(idx_str, '%Y%m%d')
                    except ValueError:
                        logger.warning(f"8位索引无法解析为日期: {idx}")
                        return False
                elif idx_len == 14:
                    # 14位时间戳格式验证
                    try:
                        datetime.strptime(idx_str, '%Y%m%d%H%M%S')
                    except ValueError:
                        logger.warning(f"14位索引无法解析为时间戳: {idx}")
                        return False
                else:
                    logger.warning(f"索引格式不正确: {idx}，期望8位日期格式(YYYYMMDD)或14位时间戳格式(YYYYMMDDHHMMSS)")
                    return False
            # 频率控制：避免大量重复的验证日志
            global _last_validation_log_time
            current_time = time.time()
            if current_time - _last_validation_log_time > _validation_log_interval:
                logger.debug(f"索引格式验证通过: {len(df.index)}行数据，样本索引{list(sample_indices)}")
                _last_validation_log_time = current_time
            return True
            
        # 检查是否为数字索引（错误格式）
        if pd.api.types.is_integer_dtype(df.index):
            logger.error("检测到数字索引，这是错误的格式！应该使用时间戳索引")
            return False
            
        logger.warning(f"未知的索引类型: {type(df.index)}")
        return False
    
    @classmethod
    def ensure_proper_index(cls, df: pd.DataFrame, 
                          time_column: Optional[str] = 'time') -> pd.DataFrame:
        """
        确保DataFrame具有正确的时间戳索引格式
        
        Args:
            df: 要处理的DataFrame
            time_column: 时间列名，如果索引不正确时用作索引
            
        Returns:
            pd.DataFrame: 具有正确索引格式的DataFrame
        """
        if df is None or df.empty:
            return df
            
        # 如果索引已经正确，直接返回
        if cls.validate_index_format(df):
            logger.debug("索引格式已正确，无需处理")
            return df
            
        # 如果索引是数字序列，尝试使用time列作为索引
        if pd.api.types.is_integer_dtype(df.index) and time_column in df.columns:
            logger.info(f"检测到数字索引，使用{time_column}列重建索引")
            df_copy = df.copy()
            
            # 将time列设置为索引
            df_copy = df_copy.set_index(time_column)
            
            # 验证新索引格式
            if cls.validate_index_format(df_copy):
                logger.info("成功重建时间戳索引")
                return df_copy
            else:
                logger.error("重建索引失败，time列格式不正确")
                return df
        
        logger.warning("无法修复索引格式，返回原始DataFrame")
        return df
    
    @classmethod
    def safe_concat(cls, dfs: List[pd.DataFrame], 
                   axis: int = 0, 
                   **kwargs) -> Optional[pd.DataFrame]:
        """
        安全的DataFrame合并，确保索引格式正确
        
        Args:
            dfs: 要合并的DataFrame列表
            axis: 合并轴向
            **kwargs: 其他pandas.concat参数（ignore_index将被强制设为False）
            
        Returns:
            pd.DataFrame: 合并后的DataFrame，保持正确的索引格式
        """
        if not dfs:
            logger.warning("没有DataFrame需要合并")
            return None
            
        # 过滤空DataFrame
        valid_dfs = [df for df in dfs if df is not None and not df.empty]
        if not valid_dfs:
            logger.warning("所有DataFrame都为空")
            return None
            
        # 强制设置ignore_index=False以保持索引
        kwargs['ignore_index'] = False
        
        # 验证所有DataFrame的索引格式
        for i, df in enumerate(valid_dfs):
            if not cls.validate_index_format(df):
                logger.warning(f"DataFrame[{i}]索引格式不正确，尝试修复")
                valid_dfs[i] = cls.ensure_proper_index(df)
        
        try:
            # 执行合并
            result_df = pd.concat(valid_dfs, axis=axis, **kwargs)
            
            # 验证合并结果的索引格式
            if cls.validate_index_format(result_df):
                logger.debug(f"成功合并{len(valid_dfs)}个DataFrame，索引格式正确")
                return result_df
            else:
                logger.error("合并后索引格式不正确")
                return result_df
                
        except Exception as e:
            logger.error(f"DataFrame合并失败: {e}")
            return None
    
    @classmethod
    def detect_data_type(cls, df: pd.DataFrame) -> str:
        """
        根据DataFrame的列名检测数据类型

        Args:
            df: 要检测的DataFrame

        Returns:
            str: 数据类型 ('tick', 'kline', 'unknown')
        """
        if df is None or df.empty:
            return 'unknown'

        columns = set(df.columns.str.lower())

        # tick数据特征列
        tick_indicators = {
            'lastprice', 'bidprice', 'askprice', 'bidvol', 'askvol',
            'transactionnum', 'lastclose', 'settlementprice', 'pe'
        }

        # K线数据特征列
        kline_indicators = {
            'open', 'high', 'low', 'close', 'volume'
        }

        # 检测tick数据
        tick_matches = len(tick_indicators.intersection(columns))
        kline_matches = len(kline_indicators.intersection(columns))

        if tick_matches >= 3:  # 至少匹配3个tick特征
            return 'tick'
        elif kline_matches >= 4:  # 至少匹配4个K线特征
            return 'kline'
        else:
            return 'unknown'

    @classmethod
    def get_index_info(cls, df: pd.DataFrame) -> dict:
        """
        获取DataFrame索引的详细信息
        
        Args:
            df: 要分析的DataFrame
            
        Returns:
            dict: 索引信息字典
        """
        if df is None or df.empty:
            return {"error": "DataFrame为空"}
            
        # 检测数据类型
        data_type = cls.detect_data_type(df)

        # 计算重复索引统计
        duplicated_mask = df.index.duplicated()
        duplicate_count = duplicated_mask.sum()

        info = {
            "index_type": type(df.index).__name__,
            "index_dtype": str(df.index.dtype),
            "index_length": len(df.index),
            "is_valid_format": cls.validate_index_format(df),
            "sample_indices": list(df.index[:5]) if len(df.index) > 0 else [],
            "has_duplicates": df.index.duplicated().any(),
            "duplicate_count": int(duplicate_count),
            "is_monotonic": df.index.is_monotonic_increasing,
            "data_type": data_type
        }
        
        return info
    
    @classmethod
    def log_index_info(cls, df: pd.DataFrame, context: str = ""):
        """
        记录DataFrame索引信息到日志（智能化分级处理）

        Args:
            df: 要分析的DataFrame
            context: 上下文信息
        """
        info = cls.get_index_info(df)
        context_str = f"[{context}] " if context else ""
        data_type = info.get("data_type", "unknown")

        # 基础索引信息（DEBUG级别）- 只在有问题或特殊情况时输出
        has_issues = (not info.get("is_valid_format", False) or
                     info.get("has_duplicates", False) or
                     data_type == "unknown")

        if has_issues and logger.isEnabledFor(logging.DEBUG):
            index_summary = {
                "type": info.get("index_type"),
                "length": info.get("index_length"),
                "data_type": data_type,
                "valid_format": info.get("is_valid_format"),
                "has_duplicates": info.get("has_duplicates")
            }
            logger.debug(f"{context_str}索引摘要: {index_summary}")

        # 索引格式验证
        if not info.get("is_valid_format", False):
            logger.warning(f"{context_str}索引格式不正确！")

        # 智能化重复索引处理
        if info.get("has_duplicates", False):
            duplicate_count = info.get("duplicate_count", 0)
            total_count = info.get("index_length", 0)

            if data_type == "tick":
                # Tick数据：索引重复是正常现象，使用DEBUG级别
                logger.debug(f"{context_str}Tick数据索引重复正常，共{total_count}行数据，{duplicate_count}个重复索引")
            elif data_type == "kline":
                # K线数据：索引重复是异常，使用WARNING级别
                duplicate_indices = df.index[df.index.duplicated(keep=False)].unique()[:5]  # 最多显示5个
                logger.warning(f"{context_str}K线数据索引重复异常！重复索引: {list(duplicate_indices)}")
            else:
                # 未知数据类型：使用INFO级别，提供建议
                logger.info(f"{context_str}检测到索引重复({duplicate_count}/{total_count})，数据类型未知，建议检查数据结构")

    @classmethod
    def get_index_statistics(cls, df: pd.DataFrame) -> dict:
        """
        获取详细的索引统计信息

        Args:
            df: 要分析的DataFrame

        Returns:
            dict: 详细的索引统计信息
        """
        if df is None or df.empty:
            return {"error": "DataFrame为空"}

        info = cls.get_index_info(df)

        # 扩展统计信息
        stats = {
            **info,
            "unique_indices": len(df.index.unique()),
            "duplicate_ratio": info["duplicate_count"] / info["index_length"] if info["index_length"] > 0 else 0,
        }

        # 如果有重复索引，提供更详细的分析
        if info["has_duplicates"]:
            duplicated_mask = df.index.duplicated(keep=False)
            duplicate_indices = df.index[duplicated_mask].unique()

            stats.update({
                "duplicate_indices_sample": list(duplicate_indices[:10]),  # 最多显示10个重复索引
                "max_duplicate_count": df.index.value_counts().max(),
                "duplicate_indices_count": len(duplicate_indices)
            })

        return stats


class IndexValidator:
    """
    索引格式验证器
    提供更详细的索引验证和报告功能
    """
    
    @staticmethod
    def comprehensive_validation(df: pd.DataFrame) -> dict:
        """
        全面的索引验证
        
        Args:
            df: 要验证的DataFrame
            
        Returns:
            dict: 详细的验证报告
        """
        report = {
            "is_valid": False,
            "issues": [],
            "recommendations": [],
            "index_info": {}
        }
        
        if df is None or df.empty:
            report["issues"].append("DataFrame为空")
            return report
            
        # 获取基本索引信息
        report["index_info"] = IndexManager.get_index_info(df)
        
        # 检查索引格式
        if not IndexManager.validate_index_format(df):
            report["issues"].append("索引格式不符合YYYYMMDDHHMMSS标准")
            report["recommendations"].append("使用IndexManager.ensure_proper_index()修复索引")
        
        # 检查重复索引
        if report["index_info"].get("has_duplicates", False):
            report["issues"].append("索引存在重复值")
            report["recommendations"].append("使用df.index.duplicated()检查并处理重复索引")
        
        # 检查索引排序
        if not report["index_info"].get("is_monotonic", False):
            report["issues"].append("索引未按时间排序")
            report["recommendations"].append("使用df.sort_index()对数据排序")
        
        # 如果没有问题，标记为有效
        if not report["issues"]:
            report["is_valid"] = True
        
        return report
