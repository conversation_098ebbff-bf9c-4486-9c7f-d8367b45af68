"""
前复权计算引擎

基于xtquant算法实现高性能的前复权计算功能，包括：
1. 等比前复权算法（process_forward_ratio）
2. 标准前复权算法（process_forward）
3. 复权比例生成算法（gen_divid_ratio）
4. 向量化计算优化

算法来源：xtquant官方文档示例
"""

import pandas as pd
import numpy as np
from typing import Union, List, Optional
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed

from utils.logger import get_unified_logger
from .field_type_classifier import field_classifier, FieldType
from .data_quality_monitor import quality_monitor

logger = get_unified_logger(__name__, enhanced=True)


class ForwardAdjustmentEngine:
    """前复权计算引擎
    
    基于xtquant算法实现的高性能前复权计算引擎，支持：
    - 等比前复权计算
    - 标准前复权计算
    - 批量数据处理
    - 向量化计算优化
    """
    
    def __init__(self):
        """初始化前复权计算引擎"""
        logger.debug("前复权计算引擎初始化完成")
    
    def gen_divid_ratio(self, quote_datas: pd.DataFrame, divid_datas: pd.DataFrame) -> pd.DataFrame:
        """生成复权比例数据
        
        基于xtquant的gen_divid_ratio算法实现
        
        Args:
            quote_datas: 原始价格数据
            divid_datas: 复权因子数据
            
        Returns:
            复权比例数据
        """
        try:
            if quote_datas.empty or divid_datas.empty:
                logger.warning("价格数据或复权因子数据为空，返回原始数据")
                return pd.DataFrame(1.0, index=quote_datas.index, columns=quote_datas.columns)

            # 索引类型统一处理：确保价格数据和复权因子数据索引类型一致
            from utils.smart_time_converter import smart_to_datetime

            quote_datas_processed = quote_datas.copy()
            divid_datas_processed = divid_datas.copy()

            # 检查并统一索引类型
            quote_index_type = type(quote_datas_processed.index[0]) if len(quote_datas_processed) > 0 else None
            divid_index_type = type(divid_datas_processed.index[0]) if len(divid_datas_processed) > 0 else None

            logger.debug(f"索引类型检查 - 价格数据: {quote_index_type}, 复权因子: {divid_index_type}")

            # 如果索引类型不一致，统一转换为字符串类型进行比较
            if quote_index_type != divid_index_type:
                logger.debug("检测到索引类型不一致，统一转换为字符串格式")

                # 转换价格数据索引为字符串
                if pd.api.types.is_datetime64_any_dtype(quote_datas_processed.index):
                    quote_datas_processed.index = quote_datas_processed.index.strftime('%Y%m%d%H%M%S')
                else:
                    quote_datas_processed.index = quote_datas_processed.index.astype(str)

                # 转换复权因子数据索引为字符串
                if pd.api.types.is_datetime64_any_dtype(divid_datas_processed.index):
                    divid_datas_processed.index = divid_datas_processed.index.strftime('%Y%m%d%H%M%S')
                else:
                    divid_datas_processed.index = divid_datas_processed.index.astype(str)

                logger.debug("索引类型统一完成，使用字符串格式进行比较")

            drl = []
            dr = 1.0
            qi = 0
            qdl = len(quote_datas_processed)
            di = 0
            ddl = len(divid_datas_processed)

            # 按时间顺序处理复权比例
            while qi < qdl and di < ddl:
                qd = quote_datas_processed.iloc[qi]
                dd = divid_datas_processed.iloc[di]

                # 安全的索引比较，确保类型一致
                try:
                    if qd.name >= dd.name:
                        dr *= dd['dr']
                        di += 1
                    if qd.name <= dd.name:
                        drl.append(dr)
                        qi += 1
                except TypeError as e:
                    logger.error(f"索引比较失败: {e}, 价格索引类型: {type(qd.name)}, 复权因子索引类型: {type(dd.name)}")
                    # 如果比较失败，跳过当前记录
                    qi += 1
                    continue
            
            # 处理剩余的价格数据
            while qi < qdl:
                drl.append(dr)
                qi += 1

            # 修复DataFrame构造问题：drl是一维列表，需要正确构造DataFrame
            if len(drl) == len(quote_datas):
                # 将一维比例列表扩展为与价格数据相同形状的DataFrame
                result_data = []
                for ratio in drl:
                    result_data.append([ratio] * len(quote_datas.columns))

                # 使用原始价格数据的索引和列名构造结果DataFrame
                result = pd.DataFrame(result_data, index=quote_datas.index, columns=quote_datas.columns)
                logger.debug(f"生成复权比例数据完成，数据量: {len(result)}")

                return result
            else:
                logger.error(f"复权比例数据长度不匹配: {len(drl)} vs {len(quote_datas)}")
                return pd.DataFrame(1.0, index=quote_datas.index, columns=quote_datas.columns)
            
        except Exception as e:
            logger.error(f"生成复权比例数据失败: {e}")
            # 返回全1的DataFrame作为降级处理
            return pd.DataFrame(1.0, index=quote_datas.index, columns=quote_datas.columns)
    
    def process_forward_ratio(self, quote_datas: pd.DataFrame, divid_datas: pd.DataFrame) -> pd.DataFrame:
        """等比前复权计算
        
        基于xtquant的process_forward_ratio算法实现
        
        Args:
            quote_datas: 原始价格数据
            divid_datas: 复权因子数据
            
        Returns:
            等比前复权后的价格数据
        """
        try:
            if quote_datas.empty:
                logger.warning("价格数据为空，返回空DataFrame")
                return quote_datas.copy()
            
            if divid_datas.empty:
                logger.info("复权因子数据为空，返回原始价格数据")
                return quote_datas.copy()
            
            logger.debug(f"开始等比前复权计算，价格数据: {len(quote_datas)} 行，复权因子: {len(divid_datas)} 行")
            
            # 生成复权比例
            drl = self.gen_divid_ratio(quote_datas, divid_datas)
            
            # 计算等比前复权：除以最后一个复权比例
            if not drl.empty and len(drl) > 0:
                drlf = drl / drl.iloc[-1]

                # 保护time列不被复权计算影响
                result = quote_datas.copy()

                # 记录原始time列信息
                if 'time' in quote_datas.columns:
                    original_time_dtype = quote_datas['time'].dtype
                    original_time_sample = quote_datas['time'].iloc[0] if len(quote_datas) > 0 else None
                    logger.debug(f"原始time列信息: 类型={original_time_dtype}, 样本值={original_time_sample}")

                # 统一执行字段分类（使用缓存机制）
                field_types = field_classifier.classify_dataframe_fields(quote_datas)

                # 获取需要复权的字段（基于已分类的结果）
                adjustment_fields = [field for field, ftype in field_types.items()
                                   if ftype == FieldType.PRICE_FIELD]

                logger.info(f"需要复权的字段: {adjustment_fields}")

                # 验证字段分类的合理性（不重复分类）
                unknown_fields = [field for field, ftype in field_types.items()
                                if ftype == FieldType.UNKNOWN_FIELD]
                if unknown_fields:
                    logger.warning(f"发现未知字段类型: {unknown_fields}")
                elif not adjustment_fields:
                    logger.warning("未发现任何价格字段，可能存在分类错误")
                else:
                    logger.debug("字段分类验证通过")

                # 只对价格字段进行复权计算
                if adjustment_fields:
                    logger.debug(f"开始对 {len(adjustment_fields)} 个价格字段进行复权计算")
                    logger.debug(f"复权比例样本: {drlf[adjustment_fields].iloc[0].to_dict() if len(drlf) > 0 else 'N/A'}")

                    # 处理数组字段（如bidPrice, askPrice）
                    for field in adjustment_fields:
                        if field in quote_datas.columns:
                            original_data = quote_datas[field]

                            # 检查是否为数组字段
                            if self._is_array_field(original_data):
                                logger.debug(f"处理数组字段: {field}")
                                result[field] = self._adjust_array_field(original_data, drlf[field])
                            else:
                                # 普通数值字段
                                result[field] = (original_data * drlf[field]).round(2)

                    logger.info(f"复权计算完成，调整了 {len(adjustment_fields)} 个价格字段")

                    # 验证time列是否保持不变
                    if 'time' in result.columns:
                        result_time_dtype = result['time'].dtype
                        result_time_sample = result['time'].iloc[0] if len(result) > 0 else None
                        time_unchanged = (result['time'] == quote_datas['time']).all() if len(result) > 0 else True

                        logger.debug(f"复权后time列信息: 类型={result_time_dtype}, 样本值={result_time_sample}")
                        logger.debug(f"time列是否保持不变: {time_unchanged}")

                        if not time_unchanged:
                            logger.error("time列在复权计算中发生了意外变化！")
                        elif result_time_dtype != original_time_dtype:
                            logger.warning(f"time列类型发生变化: {original_time_dtype} -> {result_time_dtype}")

                    logger.debug(f"复权计算完成，time列保持原始格式")
                # 验证非价格字段是否保持不变
                non_price_fields = [col for col in quote_datas.columns if col not in adjustment_fields]
                for field in non_price_fields:
                    if not quote_datas[field].equals(result[field]):
                        logger.error(f"非价格字段 {field} 发生了意外变化")
                    else:
                        logger.debug(f"非价格字段 {field} 保持不变")

                # 执行数据质量监控
                quality_passed = quality_monitor.monitor_adjustment_quality(quote_datas, result)
                if not quality_passed:
                    logger.error("复权数据质量检查未通过，请检查质量监控报告")
                    # 获取质量报告
                    quality_report = quality_monitor.get_quality_report()
                    logger.error(f"质量问题统计: {quality_report['level_counts']}")
                else:
                    logger.info("复权数据质量检查通过")
            else:
                logger.warning("未找到需要复权的价格列，返回原始数据")
                result = quote_datas.copy()

            logger.info(f"等比前复权计算完成，处理数据量: {len(result)}")

            return result
            
        except Exception as e:
            logger.error(f"等比前复权计算失败: {e}")
            return quote_datas.copy()
    
    def process_forward(self, quote_datas: pd.DataFrame, divid_datas: pd.DataFrame) -> pd.DataFrame:
        """标准前复权计算
        
        基于xtquant的process_forward算法实现
        
        Args:
            quote_datas: 原始价格数据
            divid_datas: 复权因子数据
            
        Returns:
            标准前复权后的价格数据
        """
        try:
            if quote_datas.empty:
                logger.warning("价格数据为空，返回空DataFrame")
                return quote_datas.copy()
            
            if divid_datas.empty:
                logger.info("复权因子数据为空，返回原始价格数据")
                return quote_datas.copy()
            
            logger.debug(f"开始标准前复权计算，价格数据: {len(quote_datas)} 行，复权因子: {len(divid_datas)} 行")
            
            # 复制数据避免修改原始数据
            result = quote_datas.copy()
            
            def calc_front(v, d):
                """前复权计算公式"""
                return ((v - d['interest'] + d['allotPrice'] * d['allotNum'])
                       / (1 + d['allotNum'] + d['stockBonus'] + d['stockGift']))
            
            # 对每个价格数据点进行前复权计算
            for qi in range(len(result)):
                for di in range(len(divid_datas)):
                    d = divid_datas.iloc[di]
                    # 只处理除权日期在价格日期之后的复权因子
                    if d.name <= result.index[qi]:
                        continue
                    # 对第一列（通常是价格列）进行前复权计算
                    if len(result.columns) > 0:
                        result.iloc[qi, 0] = calc_front(result.iloc[qi, 0], d)
            
            logger.info(f"标准前复权计算完成，处理数据量: {len(result)}")
            return result
            
        except Exception as e:
            logger.error(f"标准前复权计算失败: {e}")
            return quote_datas.copy()
    
    def calculate_forward_adjustment(
        self, 
        price_data: pd.DataFrame, 
        dividend_factors: pd.DataFrame,
        method: str = "ratio"
    ) -> pd.DataFrame:
        """计算前复权数据（统一接口）
        
        Args:
            price_data: 原始价格数据
            dividend_factors: 复权因子数据
            method: 计算方法，"ratio"（等比前复权）或"standard"（标准前复权）
            
        Returns:
            前复权后的价格数据
        """
        try:
            if method == "ratio":
                return self.process_forward_ratio(price_data, dividend_factors)
            elif method == "standard":
                return self.process_forward(price_data, dividend_factors)
            else:
                logger.error(f"不支持的前复权计算方法: {method}")
                return price_data.copy()
                
        except Exception as e:
            logger.error(f"前复权计算失败: {e}")
            return price_data.copy()
    
    def batch_calculate_forward_adjustment(
        self, 
        stock_price_data: dict, 
        stock_dividend_factors: dict,
        method: str = "ratio",
        max_workers: int = 4
    ) -> dict:
        """批量计算前复权数据
        
        Args:
            stock_price_data: 股票价格数据字典 {stock_code: price_df}
            stock_dividend_factors: 股票复权因子数据字典 {stock_code: dividend_df}
            method: 计算方法
            max_workers: 最大并发数
            
        Returns:
            前复权后的价格数据字典 {stock_code: adjusted_price_df}
        """
        try:
            results = {}
            
            # 获取需要处理的股票列表
            stock_codes = list(stock_price_data.keys())
            logger.info(f"开始批量前复权计算，股票数量: {len(stock_codes)}")
            
            if max_workers == 1:
                # 单线程处理
                for stock_code in stock_codes:
                    if stock_code in stock_dividend_factors:
                        price_data = stock_price_data[stock_code]
                        dividend_factors = stock_dividend_factors[stock_code]
                        results[stock_code] = self.calculate_forward_adjustment(
                            price_data, dividend_factors, method
                        )
                    else:
                        # 如果没有复权因子，返回原始数据
                        results[stock_code] = stock_price_data[stock_code].copy()
            else:
                # 多线程处理
                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    future_to_stock = {}
                    
                    for stock_code in stock_codes:
                        if stock_code in stock_dividend_factors:
                            price_data = stock_price_data[stock_code]
                            dividend_factors = stock_dividend_factors[stock_code]
                            future = executor.submit(
                                self.calculate_forward_adjustment,
                                price_data, dividend_factors, method
                            )
                            future_to_stock[future] = stock_code
                        else:
                            # 如果没有复权因子，直接返回原始数据
                            results[stock_code] = stock_price_data[stock_code].copy()
                    
                    # 收集结果
                    for future in as_completed(future_to_stock):
                        stock_code = future_to_stock[future]
                        try:
                            results[stock_code] = future.result()
                        except Exception as e:
                            logger.error(f"股票 {stock_code} 前复权计算失败: {e}")
                            results[stock_code] = stock_price_data[stock_code].copy()
            
            logger.info(f"批量前复权计算完成，成功处理 {len(results)} 只股票")
            return results
            
        except Exception as e:
            logger.error(f"批量前复权计算失败: {e}")
            return {}

    def _is_array_field(self, series: pd.Series) -> bool:
        """
        检查字段是否为数组字段（如bidPrice, askPrice等五档行情数据）

        Args:
            series: pandas Series

        Returns:
            bool: 是否为数组字段
        """
        if series.empty:
            return False

        # 检查第一个非空值是否为列表或数组
        for value in series.dropna():
            if isinstance(value, (list, tuple, np.ndarray)):
                return True
            break

        return False

    def _adjust_array_field(self, series: pd.Series, adjustment_ratios: pd.Series) -> pd.Series:
        """
        对数组字段进行复权调整

        Args:
            series: 原始数组字段数据
            adjustment_ratios: 复权比例

        Returns:
            pd.Series: 调整后的数组字段数据
        """
        def adjust_array_element(array_data, ratio):
            """调整单个数组元素"""
            if not isinstance(array_data, (list, tuple, np.ndarray)):
                return array_data

            # 对数组中的每个元素进行复权调整
            adjusted_array = []
            for value in array_data:
                if isinstance(value, (int, float)) and value != 0:
                    adjusted_value = round(value * ratio, 2)
                    adjusted_array.append(adjusted_value)
                else:
                    adjusted_array.append(value)  # 保持0值不变

            return adjusted_array

        # 对整个Series进行调整
        result = series.copy()
        for idx in series.index:
            if idx in adjustment_ratios.index:
                result.loc[idx] = adjust_array_element(series.loc[idx], adjustment_ratios.loc[idx])

        return result


# 创建全局实例
forward_adjustment_engine = ForwardAdjustmentEngine()
