#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
菜单显示模块

提供控制台菜单显示和交互功能
"""

import os
import sys
from typing import List, Tuple, Optional, Any, Dict, Union

# 将项目根目录添加到Python路径
root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, root_dir)

from utils.data_display.formatting import print_boxed_text


def show_menu(
    title: str, 
    options: Optional[List[Tuple[int, str]]] = None,
    default: Optional[int] = None,
    min_value: Optional[int] = None,
    max_value: Optional[int] = None
) -> Optional[int]:
    """
    显示菜单并获取用户选择
    
    Args:
        title: 菜单标题
        options: 选项列表，每个选项是一个(值, 文本)元组，可以为None表示只显示标题
        default: 默认选项，如果为None则没有默认选项
        min_value: 允许的最小值，默认为None
        max_value: 允许的最大值，默认为None
        
    Returns:
        用户选择的选项值，如果options为None则返回None
    """
    # 打印菜单标题
    print("\n" + "=" * 60)
    print(f" {title} ".center(60))
    print("=" * 60)
    
    # 如果没有选项，仅显示标题后返回
    if not options:
        return None
    
    # 打印选项
    for value, text in options:
        # 标记默认选项
        if value == default:
            print(f"  {value}. {text} [默认]")
        else:
            print(f"  {value}. {text}")
    
    # 获取用户输入
    while True:
        try:
            # 构建提示信息
            prompt = "请选择: "
            if default is not None:
                prompt = f"请选择 (默认: {default}): "
            
            user_input = input(prompt)
            
            # 处理空输入
            if not user_input and default is not None:
                return default
            
            # 转换为整数
            choice = int(user_input)
            
            # 验证是否在有效范围内
            if min_value is not None and choice < min_value:
                print(f"请输入不小于 {min_value} 的数字")
                continue
                
            if max_value is not None and choice > max_value:
                print(f"请输入不大于 {max_value} 的数字")
                continue
            
            # 验证是否在选项列表中
            valid_values = [val for val, _ in options]
            if choice not in valid_values:
                print(f"请输入有效的选项: {', '.join(map(str, valid_values))}")
                continue
            
            return choice
            
        except ValueError:
            print("请输入有效的数字")


def show_submenu(title: str, options: List[str], back_option: bool = True) -> int:
    """
    显示子菜单并获取用户选择
    
    Args:
        title: 菜单标题
        options: 选项文本列表
        back_option: 是否添加"返回上级菜单"选项
        
    Returns:
        用户选择的索引，0表示返回，1-n表示选项1到选项n
    """
    menu_options = [(i+1, option) for i, option in enumerate(options)]
    
    if back_option:
        menu_options.append((0, "返回上级菜单"))
    
    return show_menu(
        title=title,
        options=menu_options,
        min_value=0 if back_option else 1,
        max_value=len(options) + (1 if back_option else 0) - 1
    )


def show_paged_menu(
    title: str,
    items: List[str],
    page_size: int = 10,
    show_numbers: bool = True,
    back_option: bool = True
) -> Optional[int]:
    """
    显示分页菜单
    
    Args:
        title: 菜单标题
        items: 所有项目列表
        page_size: 每页显示的项目数
        show_numbers: 是否显示项目序号
        back_option: 是否添加返回选项
        
    Returns:
        用户选择的项目索引，None表示返回
    """
    if not items:
        print(f"\n{title}\n\n没有可显示的项目")
        input("按Enter键继续...")
        return None
    
    total_pages = (len(items) + page_size - 1) // page_size
    current_page = 1
    
    while True:
        # 计算当前页显示的项目
        start_idx = (current_page - 1) * page_size
        end_idx = min(start_idx + page_size, len(items))
        current_items = items[start_idx:end_idx]
        
        # 打印标题
        print("\n" + "=" * 60)
        print(f" {title} (第 {current_page}/{total_pages} 页) ".center(60))
        print("=" * 60)
        
        # 打印项目
        for i, item in enumerate(current_items):
            if show_numbers:
                print(f"  {start_idx + i + 1}. {item}")
            else:
                print(f"  {item}")
        
        # 导航选项
        nav_options = []
        if current_page > 1:
            nav_options.append(("P", "上一页"))
        if current_page < total_pages:
            nav_options.append(("N", "下一页"))
        nav_options.append(("S", "指定页码"))
        if back_option:
            nav_options.append(("Q", "返回"))
        
        # 打印导航选项
        print("\n导航：")
        for key, text in nav_options:
            print(f"  {key} - {text}")
        
        if show_numbers:
            print("  或输入项目编号选择特定项目")
        
        # 获取用户输入
        user_input = input("\n请选择: ").strip().upper()
        
        # 处理导航命令
        if user_input == "P" and current_page > 1:
            current_page -= 1
        elif user_input == "N" and current_page < total_pages:
            current_page += 1
        elif user_input == "S":
            try:
                page_input = input(f"请输入页码 (1-{total_pages}): ")
                page = int(page_input)
                if 1 <= page <= total_pages:
                    current_page = page
                else:
                    print(f"页码必须在 1 和 {total_pages} 之间")
            except ValueError:
                print("请输入有效的页码")
        elif user_input == "Q" and back_option:
            return None
        elif show_numbers and user_input.isdigit():
            # 尝试解析为项目编号
            try:
                item_idx = int(user_input) - 1
                if 0 <= item_idx < len(items):
                    return item_idx
                else:
                    print(f"项目编号必须在 1 和 {len(items)} 之间")
            except ValueError:
                print("请输入有效的项目编号")
        else:
            print("无效的选择，请重试")


def show_multi_select_menu(
    title: str,
    options: List[str],
    default_selected: Optional[List[int]] = None
) -> List[int]:
    """
    显示多选菜单
    
    Args:
        title: 菜单标题
        options: 选项列表
        default_selected: 默认选中的选项索引列表
        
    Returns:
        用户选择的选项索引列表
    """
    if not options:
        print(f"\n{title}\n\n没有可选择的选项")
        return []
    
    if default_selected is None:
        default_selected = []
    
    selected = [idx in default_selected for idx in range(len(options))]
    
    while True:
        # 打印标题
        print("\n" + "=" * 60)
        print(f" {title} ".center(60))
        print("=" * 60)
        
        # 打印选项
        for i, option in enumerate(options):
            status = "[X]" if selected[i] else "[ ]"
            print(f"  {i+1}. {status} {option}")
        
        # 打印命令
        print("\n命令：")
        print("  数字 - 切换选项状态")
        print("  A - 全选")
        print("  N - 全不选")
        print("  I - 反选")
        print("  D - 完成选择")
        print("  Q - 取消并返回")
        
        # 获取用户输入
        user_input = input("\n请选择: ").strip().upper()
        
        # 处理命令
        if user_input == "A":
            selected = [True] * len(options)
        elif user_input == "N":
            selected = [False] * len(options)
        elif user_input == "I":
            selected = [not s for s in selected]
        elif user_input == "D":
            return [i for i, s in enumerate(selected) if s]
        elif user_input == "Q":
            return default_selected
        elif user_input.isdigit():
            try:
                idx = int(user_input) - 1
                if 0 <= idx < len(options):
                    selected[idx] = not selected[idx]
                else:
                    print(f"选项编号必须在 1 和 {len(options)} 之间")
            except ValueError:
                print("请输入有效的选项编号")
        else:
            print("无效的命令，请重试")


def clear_screen():
    """
    清除控制台屏幕
    """
    os.system('cls' if os.name == 'nt' else 'clear')


def progress_menu(
    title: str,
    steps: List[str],
    current_step: int = 0,
    completed_steps: Optional[List[bool]] = None
) -> None:
    """
    显示进度菜单
    
    Args:
        title: 菜单标题
        steps: 步骤列表
        current_step: 当前执行的步骤索引
        completed_steps: 已完成步骤的布尔列表
    """
    if completed_steps is None:
        completed_steps = [False] * len(steps)
    
    # 确保completed_steps长度与steps相同
    while len(completed_steps) < len(steps):
        completed_steps.append(False)
    
    # 打印标题
    print("\n" + "=" * 60)
    print(f" {title} ".center(60))
    print("=" * 60)
    
    # 打印步骤
    for i, step in enumerate(steps):
        if completed_steps[i]:
            status = "[✓]"
        elif i == current_step:
            status = "[>]"
        else:
            status = "[ ]"
        
        print(f"  {status} {i+1}. {step}")
    
    print("=" * 60) 