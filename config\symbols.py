#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
交易品种配置模块，定义常用指数、行业板块和个股列表
"""

# ===== 指数成分股 =====

# 上证50成分股
SH50_SYMBOLS = [
    "600000.SH",  # 浦发银行
    "600016.SH",  # 民生银行
    "600019.SH",  # 宝钢股份
    "600028.SH",  # 中国石化
    "600030.SH",  # 中信证券
    "600036.SH",  # 招商银行
    "600048.SH",  # 保利发展
    "600050.SH",  # 中国联通
    "600104.SH",  # 上汽集团
    # ... 其他成分股
]

# 沪深300成分股（示例仅列部分）
HS300_SYMBOLS = [
    # 上证部分
    "600000.SH",  # 浦发银行
    "600015.SH",  # 华夏银行
    "600016.SH",  # 民生银行
    "600018.SH",  # 上港集团
    "600019.SH",  # 宝钢股份
    # 深证部分
    "000001.SZ",  # 平安银行
    "000002.SZ",  # 万科A
    "000063.SZ",  # 中兴通讯
    "000069.SZ",  # 华侨城A
    "000100.SZ",  # TCL科技
    # ... 其他成分股
]

# 中证500成分股（示例仅列部分）
ZZ500_SYMBOLS = [
    "000008.SZ",  # 神州高铁
    "000009.SZ",  # 中国宝安
    "000012.SZ",  # 南玻A
    "000021.SZ",  # 深科技
    # ... 其他成分股
]

# 创业板指数成分股（示例仅列部分）
GEM_SYMBOLS = [
    "300001.SZ",  # 特锐德
    "300002.SZ",  # 神州泰岳
    "300003.SZ",  # 乐普医疗
    "300004.SZ",  # 南风股份
    # ... 其他成分股
]

# 科创50指数成分股（示例仅列部分）
STAR50_SYMBOLS = [
    "688001.SH",  # 华兴源创
    "688002.SH",  # 睿创微纳
    "688003.SH",  # 天准科技
    "688004.SH",  # 博汇科技
    # ... 其他成分股
]

# ===== 行业板块 =====

# 银行板块
BANK_SYMBOLS = [
    "600000.SH",  # 浦发银行
    "600015.SH",  # 华夏银行
    "600016.SH",  # 民生银行
    "600036.SH",  # 招商银行
    "601166.SH",  # 兴业银行
    "601328.SH",  # 交通银行
    "601398.SH",  # 工商银行
    "601288.SH",  # 农业银行
    "601988.SH",  # 中国银行
    "601998.SH",  # 中信银行
    "000001.SZ",  # 平安银行
    # ... 其他银行股
]

# 券商板块
BROKER_SYMBOLS = [
    "600030.SH",  # 中信证券
    "600837.SH",  # 海通证券
    "601066.SH",  # 中信建投
    "601099.SH",  # 太平洋
    "601198.SH",  # 东兴证券
    "601211.SH",  # 国泰君安
    "601377.SH",  # 兴业证券
    "601688.SH",  # 华泰证券
    "601788.SH",  # 光大证券
    "601901.SH",  # 方正证券
    # ... 其他券商股
]

# 新能源板块
NEW_ENERGY_SYMBOLS = [
    "601012.SH",  # 隆基绿能
    "600438.SH",  # 通威股份
    "002459.SZ",  # 晶澳科技
    "002594.SZ",  # 比亚迪
    "300014.SZ",  # 亿纬锂能
    "300015.SZ",  # 爱尔眼科
    "300274.SZ",  # 阳光电源
    "300750.SZ",  # 宁德时代
    # ... 其他新能源股
]

# 医药板块
PHARMACEUTICAL_SYMBOLS = [
    "600276.SH",  # 恒瑞医药
    "600196.SH",  # 复星医药
    "300015.SZ",  # 爱尔眼科
    "300122.SZ",  # 智飞生物
    "300142.SZ",  # 沃森生物
    "300347.SZ",  # 泰格医药
    "300760.SZ",  # 迈瑞医疗
    # ... 其他医药股
]

# 消费板块
CONSUMER_SYMBOLS = [
    "600519.SH",  # 贵州茅台
    "600887.SH",  # 伊利股份
    "601888.SH",  # 中国中免
    "603288.SH",  # 海天味业
    "000568.SZ",  # 泸州老窖
    "000858.SZ",  # 五粮液
    "002714.SZ",  # 牧原股份
    # ... 其他消费股
]

# ===== 自定义股票组合 =====

# 自定义高成长组合
HIGH_GROWTH_SYMBOLS = [
    "300750.SZ",  # 宁德时代
    "002594.SZ",  # 比亚迪
    "600276.SH",  # 恒瑞医药
    "601012.SH",  # 隆基绿能
    "300760.SZ",  # 迈瑞医疗
    # ... 其他高成长股
]

# 自定义高股息组合
HIGH_DIVIDEND_SYMBOLS = [
    "600036.SH",  # 招商银行
    "601398.SH",  # 工商银行
    "601288.SH",  # 农业银行
    "600028.SH",  # 中国石化
    "601166.SH",  # 兴业银行
    # ... 其他高股息股
]

# ===== 股票代码转换函数 =====

def normalize_symbol(symbol: str) -> str:
    """
    标准化股票代码为交易所格式（带.SH或.SZ后缀）
    
    Args:
        symbol: 输入的股票代码（如：600000或600000.SH）
        
    Returns:
        标准化后的股票代码（如：600000.SH）
    """
    # 已经包含后缀的情况
    if "." in symbol:
        return symbol
    
    # 根据前缀判断交易所
    if symbol.startswith(("6", "5", "9")):
        return f"{symbol}.SH"
    elif symbol.startswith(("0", "1", "2", "3")):
        return f"{symbol}.SZ"
    else:
        raise ValueError(f"无法识别的股票代码格式: {symbol}")

def batch_normalize_symbols(symbols: list) -> list:
    """
    批量标准化股票代码列表
    
    Args:
        symbols: 股票代码列表
        
    Returns:
        标准化后的股票代码列表
    """
    return [normalize_symbol(symbol) for symbol in symbols]

# ===== 指数映射 =====

# 指数代码与成分股列表的映射
INDEX_SYMBOLS_MAP = {
    "000001.SH": [],  # 上证指数
    "000016.SH": SH50_SYMBOLS,  # 上证50
    "000300.SH": HS300_SYMBOLS,  # 沪深300
    "000905.SH": ZZ500_SYMBOLS,  # 中证500
    "399001.SZ": [],  # 深证成指
    "399006.SZ": GEM_SYMBOLS,  # 创业板指
    "000688.SH": STAR50_SYMBOLS,  # 科创50
    # 其他指数
} 