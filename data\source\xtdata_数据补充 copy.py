import time
from xtquant import xtdata as xt_data
xt_data.enable_hello = False

# 创建一个下载状态跟踪器
class DownloadTracker:
    """
    用于跟踪异步下载进度的类
    """
    def __init__(self):
        self.completed = False    # 下载是否完成
        self.progress = 0         # 下载进度百分比
        self.error = None         # 错误信息（如果有）
        self.finished = 0         # 已完成的单位数
        self.total = 0            # 总单位数
    
    def reset(self):
        """重置下载状态"""
        self.completed = False
        self.progress = 0
        self.error = None
        self.finished = 0
        self.total = 0

# 全局下载状态跟踪器
download_tracker = DownloadTracker()

def on_xt_download_progress(data):
    """
    处理下载进度回调
    
    参数:
        data: 回调数据，格式示例: 
              {'finished': 10, 'total': 334, 'stockcode': '', 'message': '600845.SH'}
    """
    print(f"[回调] 下载进度: {data}")
    
    # 更新下载状态
    if 'finished' in data and 'total' in data:
        download_tracker.finished = data['finished']
        download_tracker.total = data['total']
        
        # 计算进度百分比
        if download_tracker.total > 0:
            download_tracker.progress = int(download_tracker.finished / download_tracker.total * 100)
        
        # 检查是否下载完成
        if download_tracker.finished >= download_tracker.total:
            download_tracker.completed = True
            print(f"[回调] 下载完成! 总共下载了 {download_tracker.finished}/{download_tracker.total} 单位")


def download_data2(field_list, period, start_time, end_time, stock_codes):
    """
    使用异步方式下载历史数据，并等待下载完成后返回数据
    
    参数:
        field_list: 需要获取的字段列表
        period: 周期，如'1d'表示日线，'tick'表示逐笔
        start_time: 开始时间
        end_time: 结束时间
        stock_codes: 股票代码列表
        
    返回:
        包含历史数据的字典，如果下载失败则返回None
    """
    # 重置下载状态
    download_tracker.reset()
    
    # 开始异步下载
    xt_data.download_history_data2(
        stock_list=stock_codes,
        period=period,
        start_time=start_time,
        end_time=end_time,
        incrementally=True,
        callback=on_xt_download_progress
    )
    
    # 等待下载完成或出错
    print("等待下载完成...")
    timeout = 60  # 设置超时时间为60秒
    start_wait_time = time.time()
    last_progress_time = time.time()
    last_progress_count = 0

    while not download_tracker.completed:
        time.sleep(0.5)  # 每0.5秒检查一次状态

        # 每5秒打印一次当前进度
        current_time = time.time()
        if current_time - last_progress_time >= 5:
            if download_tracker.total > 0:
                print(f"⏳ 当前进度: {download_tracker.finished}/{download_tracker.total} ({download_tracker.progress}%)")
            last_progress_time = current_time

        # 检查进度是否有更新，如果有则重置超时时间
        if download_tracker.finished > last_progress_count:
            start_wait_time = current_time  # 重置超时计时器
            last_progress_count = download_tracker.finished

        # 检查是否超时
        if current_time - start_wait_time > timeout:
            print(f"下载超时（{timeout}秒）")
            return None
    
    # 下载完成
    print(f"下载完成 (耗时: {time.time() - start_wait_time:.2f}秒)")


def get_local_data_with_limit(field_list, stock_codes, period, start_time, end_time):
    """
    参数:
        field_list: 字段列表
        stock_codes: 股票代码列表
        period: 周期
        start_time: 开始时间
        end_time: 结束时间

    返回:
        str: 时间段内所有数据
    """
    # 获取所有数据
    result = xt_data.get_local_data(
        field_list=field_list,
        stock_list=stock_codes,
        period=period,
        start_time=start_time,
        end_time=end_time,
        count=-1  # 获取所有数据
    )
    return result


def get_local_data(field_list, stock_codes, period_type, current_start_time, current_end_time):
    """
    循环打印每只股票的本地数据

    参数:
        field_list: 字段列表
        stock_codes: 股票代码列表
        period_type: 周期类型
        current_start_time: 开始时间
        current_end_time: 结束时间
    """
    for index, stock_code in enumerate(stock_codes, 1):
        print(f"\n📊 [{index}/{len(stock_codes)}] {stock_code} {period_type}数据:")
        result = None # 重置变量，释放内存

        # 直接打印get_local_data_with_limit返回结果
        start_time_get = time.time()
        result = get_local_data_with_limit(field_list, [stock_code], period_type, current_start_time, current_end_time)
        end_time_get = time.time()
        print(result)
        print(f"⏱️  数据获取耗时: {end_time_get - start_time_get:.2f}秒")
        print("-" * 50)

if __name__ == "__main__":
    # 添加项目根目录到Python路径
    import sys
    import os
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    sys.path.insert(0, project_root)

    # 从硬编码路径的文件中读取股票数据
    from utils.text_parser import parse_instrument_input

    # 硬编码文件路径
    stock_file_path = "d:\\data\\补充历史数据代码.txt"

    try:
        # 读取文件内容
        with open(stock_file_path, 'r', encoding='utf-8') as f:
            file_content = f.read()

        # 直接使用通用工具解析整个文件内容
        stock_codes = parse_instrument_input(file_content)

        if not stock_codes:
            print("未找到需要下载的股票数据，请检查文件内容")
            exit(1)

    except FileNotFoundError:
        print(f"文件未找到: {stock_file_path}")
        exit(1)
    except Exception as e:
        print(f"读取文件失败: {e}")
        exit(1)

    print(f"  - 股票代码: {stock_codes}")


    field_list = [
    ]

    # 不同周期的时间范围设置
    period_time_ranges = {
        '1d': {'start_time': "20150101", 'end_time': ""},
        '1m': {'start_time': "20240101", 'end_time': ""},
        'tick': {'start_time': "20250601", 'end_time': ""}
    }


    # 周期下载循环：先下载日线数据，再下载1m数据，最后下载tick数据
    periods = [
        '1d',
        '1m',
        'tick'
        ]
    period_names = {'1d': '日线', '1m': '1分钟', 'tick': 'tick'}
    total_periods = len(periods)

    print(f"\n🚀 开始批量下载数据，共 {total_periods} 个周期")
    print(f"📊 合约数量: {len(stock_codes)}")

    for i, period_type in enumerate(periods, 1):
        # 获取当前周期的时间范围
        time_range = period_time_ranges[period_type]
        current_start_time = time_range['start_time']
        current_end_time = time_range['end_time']

        print(f"\n{'='*60}")
        print(f"📈 [{i}/{total_periods}] 开始下载 {period_names[period_type]} 数据...")
        print(f"⏰ 时间范围: {current_start_time} ~ {current_end_time}")
        print(f"{'='*60}")

        # 使用download_data2下载数据
        download_data2(field_list, period_type, current_start_time, current_end_time, stock_codes)
        print(f"✅ {period_names[period_type]}数据下载完成")

        # 数据下载完成后，循环打印每只股票数据
        print(f"\n📋 显示 {period_names[period_type]} 数据预览:")
        get_local_data(field_list, stock_codes, period_type, current_start_time, current_end_time)



        # 添加延时，避免请求过于频繁
        if i < total_periods:  # 最后一个周期不需要延时
            print(f"⏳ 等待2秒后继续下载下一个周期...")
            time.sleep(2)

    # 下载完成总结
    print(f"\n🎉 {'='*60}")
    print(f"🎉 所有数据下载完成！")
    print(f"📊 已下载周期: {', '.join([period_names[p] for p in periods])}")
    print(f"📈 合约数量: {len(stock_codes)}")
    print(f"📅 各周期时间范围:")
    for period in periods:
        time_range = period_time_ranges[period]
        print(f"   - {period_names[period]}: {time_range['start_time']} ~ {time_range['end_time']}")
    print(f"🎉 {'='*60}")
